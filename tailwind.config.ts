import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          "100": "#E5F6F6",
          "200": "#D2F0F0",
          "300": "#B0DFDF",
          "400": "#00797D",
          "500": "#016366",
          "600": "#001F2A",
          DEFAULT: "#E5F6F6",
          foreground: "#001F2A",
        },
        toggle: {
          active: "#00797D",
          inactive: "#e0e0e0",
          thumb: "#ffffff",
        },
        neutral: {
          "100": "#91909A29",
          "200": "#91909A29",
          "300": "#91909A52",
          DEFAULT: "#91909A29",
        },
        white: {
          "100": "#FFFFFF",
          "150": "#F9F9F9",
          "200": "#F4F4F4",
          "300": "#F0F0F0",
          DEFAULT: "#FFFFFF",
        },
        grey: {
          "100": "#E1E1E1",
          "200": "#B9B9B9",
          "300": "#989898",
          DEFAULT: "#E1E1E1",
        },
        dark: {
          "100": "#575757",
          "200": "#3C3C3C",
          "300": "#282828",
          DEFAULT: "#575757",
        },
        red: {
          "100": "#FF71711F",
          "200": "#F55D5D",
          "300": "#E05252",
          DEFAULT: "#FF71711F",
        },
        yellow: {
          "100": "#FFB2361F",
          "200": "#FFA931",
          "300": "#F19413",
          DEFAULT: "#FFB2361F",
        },
        green: {
          "100": "#37FF6329",
          "200": "#49B380",
          "300": "#309665",
          DEFAULT: "#37FF6329",
        },
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        approval: {
          selected: "#006C5F",
          border: "#F0F0F1",
          text: {
            primary: "#333333",
            secondary: "#6B7280",
          },
        },
      },
      boxShadow: {
        "shadow-1": "0px 0px 1px 0px #3031330D, 0px 2px 4px 0px #3031331A",
        "shadow-2": "0px 0px 1px 0px #3031330D, 0px 4px 8px 0px #3031331A",
        "shadow-3": "0px 0px 1px 0px #3031330D, 0px 8px 16px 0px #3031331A",
        "shadow-4": "0px 0px 1px 0px #3031330D, 0px 16px 24px 0px #30313317",
        "shadow-5": "0px 0px 1px 0px #3031330D, 0px 24px 40px 0px #30313314",
        "shadow-6": "0px 0px 1px 0px rgba(48,49,51,0.05)",
        "shadow-7": "0px 2px 4px 0px rgba(48,49,51,0.10)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        "fade-in-out": {
          "0%": {
            opacity: "0",
          },
          "10%": {
            opacity: "1",
          },
          "90%": {
            opacity: "1",
          },
          "100%": {
            opacity: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "fade-in-out": "fade-in-out 5s ease-in-out forwards",
      },
      fontFamily: {
        inter: "Inter, Inter Fallback",
      },
    },
  },
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
