export enum WorkOrderStatus {
  PastDue = 'Past due',
  WorkInProgress = 'Work in progress',
  Upcoming = 'Upcoming',
  Completed = 'Completed',
}

export enum WorkOrderStepStatus {
  YetToStart = 'Yet to start',
  Ongoing = 'Ongoing',
  PendingApproval = 'Pending approval',
  Completed = 'Completed',
}

export enum WorkOrderStepActions {
  SendForApproval = 'Send for approval',
  Approved = 'Approved',
  Complete = 'Complete',
}
