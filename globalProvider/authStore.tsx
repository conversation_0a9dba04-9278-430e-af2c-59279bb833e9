import { TCurrentUser } from '@/interfaces/user';
import { IOrganization } from '@/interfaces/org';
import { create } from 'zustand';
import { ORGANIZATION_SESSION_KEY } from '@/constants/common';

export interface AuthState {
  orgInfo: string | null;
  user: TCurrentUser | null;
  accessToken: string | null;
  isLoading: boolean;
  organizations: IOrganization[];
  sideBarExpanded: boolean;
  complianceExpanded: boolean;
  operationsExpanded: boolean;
  setOrg: (orgInfo: string) => void;
  setAuth: (user: TCurrentUser, accessToken: string) => void;
  clearAuth: () => void;
  setIsLoading: (loading: boolean) => void;
  setOrganizations: (orgs: IOrganization[]) => void;
  setSideBarExpanded: (expanded: boolean) => void;
  setComplianceExpanded: (expanded: boolean) => void;
  setOperationsExpanded: (expanded: boolean) => void;
}

export const useAuthStore = create<AuthState>((set) => ({
  orgInfo: null,
  user: null,
  accessToken: null,
  isLoading: true,
  organizations: [],
  sideBarExpanded:
    typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem('sideBarExpanded') || 'true')
      : true,
  complianceExpanded:
    typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem('complianceExpanded') || 'true')
      : true,
  operationsExpanded:
    typeof window !== 'undefined'
      ? JSON.parse(localStorage.getItem('operationsExpanded') || 'false')
      : false,
  setOrg: (orgInfo: string) => set({ orgInfo }),
  setAuth: (user, accessToken) => set({ user, accessToken }),
  clearAuth: () => {
    if (typeof window !== 'undefined') {
      sessionStorage.removeItem(ORGANIZATION_SESSION_KEY);
    }
    set({ user: null, accessToken: null, orgInfo: null });
  },
  setIsLoading: (loading: boolean) => set({ isLoading: loading }),
  setOrganizations: (orgs) => set({ organizations: orgs }),
  setSideBarExpanded: (expanded) => {
    set({ sideBarExpanded: expanded });
    if (typeof window !== 'undefined') {
      localStorage.setItem('sideBarExpanded', JSON.stringify(expanded));
    }
  },
  setComplianceExpanded: (expanded) => {
    set({ complianceExpanded: expanded });
    if (typeof window !== 'undefined') {
      localStorage.setItem('complianceExpanded', JSON.stringify(expanded));
    }
  },
  setOperationsExpanded: (expanded) => {
    set({ operationsExpanded: expanded });
    if (typeof window !== 'undefined') {
      localStorage.setItem('operationsExpanded', JSON.stringify(expanded));
    }
  },
}));
