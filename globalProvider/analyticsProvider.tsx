import React, { createContext, useContext, useEffect } from 'react';
import { mixpanelService } from '@/services/analytics/mixpanel-service';

const AnalyticsContext = createContext(mixpanelService);

export const useAnalytics = () => useContext(AnalyticsContext);

export const AnalyticsProvider: React.FC<React.PropsWithChildren> = ({ children }) => {
    useEffect(() => {
        mixpanelService.init();
    }, []);

    return (
        <AnalyticsContext.Provider value={mixpanelService}>
            {children}
        </AnalyticsContext.Provider>
    );
};