import { GET_CURRENT_USER } from "@/utils/api";
import { useRouter } from "next/router";
import React, { useEffect, useState } from "react";
import { useAuthStore } from "./authStore";
import { ORGANIZATION_SESSION_KEY } from "@/constants/common";
import { usePostLoginAnalytics } from "@/hooks/usePostLoginAnalytics";
import { useAnalytics } from "./analyticsProvider";

interface GlobalProviderProps {
  children: React.ReactNode;
}

const unAuthPages = ["/privacy", "/terms"];

const GlobalProvider: React.FC<GlobalProviderProps> = ({ children }) => {
  const setAuth = useAuthStore((state) => state.setAuth);
  const clearAuth = useAuthStore((state) => state.clearAuth);
  const analytics = useAnalytics();
  const router = useRouter();
  const [error, setError] = useState<string | null>(null);
  const { isLoading, setIsLoading, user, accessToken } = useAuthStore();

  useEffect(() => {
    const fetchSessionAndToken = async () => {
      setIsLoading(true);
      try {
        const refreshResponse = await fetch("/api/auth/refresh");

        if (refreshResponse.status === 401 || refreshResponse.status === 500) {
          if (!unAuthPages.includes(router.pathname)) {
            router.push("/login");
          }
          setIsLoading(false);
          return;
        }

        const { accessToken } = await refreshResponse.json();

        const mockUser =
          typeof window !== "undefined"
            ? localStorage.getItem("x-mock-user")
            : null;

        const orgId =
          typeof window !== "undefined" ? sessionStorage.getItem("oid") : null;

        const headers: Record<string, string> = {
          Authorization: `Bearer ${accessToken}`,
          "Content-Type": "application/json",
          ...(mockUser ? { "x-mock-user": mockUser } : {}),
          ...(!!orgId ? { "x-org": orgId } : {}),
        };
        if (accessToken) {
          const userResponse = await fetch(GET_CURRENT_USER, { headers });
          if (userResponse.ok) {
            const user = await userResponse.json();
            if (user?.record && user.record.id) {
              setAuth(user.record, accessToken);
              if (typeof window !== "undefined") {
                const urlParams = new URLSearchParams(window.location.search);
                const tokenParam = urlParams.get("_t");

                if (tokenParam) {
                  // Set the organization session
                  sessionStorage.setItem(ORGANIZATION_SESSION_KEY, tokenParam);
                  // Remove _t from URL and reload
                  urlParams.delete("_t");
                  const newUrl = `${window.location.pathname}${
                    urlParams.toString() ? "?" + urlParams.toString() : ""
                  }`;
                  setIsLoading(false);
                  window.location.replace(newUrl);
                  return;
                } else {
                  sessionStorage.setItem(
                    ORGANIZATION_SESSION_KEY,
                    user?.record?.company?.id,
                  );
                  setIsLoading(false);
                }
                
                // Initialize Analytics
                if (!!user?.record) {
                  usePostLoginAnalytics(analytics, user.record)
                } 
              }
            } else {
              clearAuth();
              setIsLoading(false);
              if (!unAuthPages.includes(router.pathname)) {
                router.push("/login");
              }
            }
          } else {
            clearAuth();
            setIsLoading(false);
            if (!unAuthPages.includes(router.pathname)) {
              router.push("/login");
            }
          }
        }
      } catch (err: any) {
        clearAuth();
        console.error("Error fetching session or token:", err);
        setError(err.message);
        setIsLoading(false);
      }
    };

    fetchSessionAndToken();
  }, []);

  return <>{children}</>;
};

export default GlobalProvider;
