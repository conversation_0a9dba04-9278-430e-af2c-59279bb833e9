const EditIcon = ({
  height = "20",
  width = "20",
  color = "#282828",
  className = "",
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    fill="none"
    className={className}
    viewBox="0 0 20 20"
  >
    <path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeMiterlimit={10}
      strokeWidth={1.5}
      d="M2.5 18h15M9.908 4.33c.359 2.226 2.225 3.928 4.542 4.153m-3.4-5.322-6.842 7.008c-.258.266-.508.79-.558 1.153l-.308 2.613c-.109.944.591 1.59 1.558 1.428l2.683-.444c.375-.064.9-.33 1.159-.605l6.841-7.008c1.184-1.21 1.717-2.589-.125-4.274-1.833-1.67-3.225-1.081-4.408.129Z"
    />
  </svg>
);
export default EditIcon;
