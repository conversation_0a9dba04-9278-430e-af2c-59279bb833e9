const NotesIcon = ({
  height = '32',
  width = '32',
  color = '#282828',
  className = '',
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    fill="none"
    className={className}
    viewBox="0 0 32 32"
  >
    <path
      stroke={color}
      strokeLinecap="round"
      strokeLinejoin="round"
      strokeWidth={2}
      d="M10.667 5.667c-2.947 0-5.333 1-5.333 5.333v13c0 4 2.386 5.333 5.333 5.333h10.667c2.946 0 5.333-1.333 5.333-5.333V11c0-4.333-2.387-5.333-5.334-5.333m-10.666 0c0 .826.333 1.573.88 2.12.546.546 1.293.88 2.12.88h4.667c1.653 0 3-1.347 3-3m-10.667 0c0-1.654 1.347-3 3-3h4.667c.826 0 1.573.333 2.12.88.546.546.88 1.293.88 2.12M10.667 17.332H16m-5.333 5.334h10.667"
    />
  </svg>
);
export default NotesIcon;
