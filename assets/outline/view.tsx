import { TSVGProps } from "@/interfaces/misc";
import React from "react";

const View = ({ color, height, width }: TSVGProps) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.9269 10.5039C12.9269 12.0592 11.6188 13.316 10 13.316C8.3812 13.316 7.07307 12.0592 7.07307 10.5039C7.07307 8.94864 8.3812 7.69184 10 7.69184C11.6188 7.69184 12.9269 8.94864 12.9269 10.5039Z"
        stroke={color ? color : "#016366"}
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M10 17C12.8861 17 15.5759 15.3662 17.4481 12.5384C18.184 11.4308 18.184 9.56918 17.4481 8.46163C15.5759 5.63384 12.8861 4 10 4C7.11395 4 4.42412 5.63384 2.55187 8.46163C1.81604 9.56918 1.81604 11.4308 2.55187 12.5384C4.42412 15.3662 7.11395 17 10 17Z"
        stroke={color ? color : "#016366"}
        stroke-width="1.5"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export default View;
