const UserIcon = ({
  height = '24',
  width = '24',
  color = '#171717',
  className = '',
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 32 32"
    fill="none"
    className={className}
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21.6502 6.42302C24.0847 6.42302 26.0424 8.32509 26.0424 10.6633C26.0424 12.9531 24.16 14.8188 21.8133 14.9036C21.7129 14.8915 21.6 14.8915 21.4871 14.9036M24.0722 25.8072C24.9757 25.6254 25.829 25.2741 26.5318 24.7532C28.4894 23.3357 28.4894 20.9975 26.5318 19.58C25.8416 19.0712 25.0008 18.732 24.1098 18.5381M12.5522 14.7461C12.4267 14.734 12.2761 14.734 12.138 14.7461C9.15137 14.6492 6.77961 12.2867 6.77961 9.3791C6.77961 6.4109 9.26431 4 12.3514 4C15.4259 4 17.9231 6.4109 17.9231 9.3791C17.9106 12.2867 15.5388 14.6492 12.5522 14.7461ZM6.27765 19.2166C3.24078 21.1792 3.24078 24.3776 6.27765 26.3281C9.72863 28.5573 15.3882 28.5573 18.8392 26.3281C21.8761 24.3655 21.8761 21.1671 18.8392 19.2166C15.4008 16.9995 9.74118 16.9995 6.27765 19.2166Z"
      stroke={color}
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </svg>
);
export default UserIcon;
