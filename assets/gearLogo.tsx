const BPRGearLogo = ({
  height = '370',
  width = '1655',
  color = '#00797D',
  className = '',
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 36 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clip-path="url(#clip0_1323_1887)">
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M32.6934 28.6993C32.8537 28.539 32.8537 28.2791 32.6934 28.1189L28.248 23.6734C27.9352 23.3606 27.4082 23.4406 27.1705 23.8138C25.2416 26.8415 21.8466 28.8509 17.9805 28.8509C11.9733 28.8509 7.10353 23.9995 7.10353 18.015C7.10353 12.0769 11.898 7.25444 17.8409 7.17998C17.8873 7.17939 17.9338 7.1791 17.9804 7.1791C20.5985 7.1791 23.0007 8.10065 24.8778 9.63593C25.1568 9.86416 25.5681 9.85108 25.8148 9.58824L28.2718 6.97121C28.4269 6.80594 28.419 6.54572 28.2498 6.395C26.0407 4.42882 23.3112 3.13609 20.3842 2.67496C17.3219 2.19252 14.1853 2.64268 11.3824 3.9669C10.8509 4.21802 10.3359 4.49838 9.83984 4.80608C9.675 4.90827 9.46096 4.88633 9.32384 4.74922L7.87989 3.30526C7.7196 3.14497 7.45972 3.14497 7.29942 3.30526L3.35297 7.25172C3.19268 7.41201 3.19268 7.6719 3.35297 7.83219L4.79691 9.27612C4.93402 9.41323 4.95596 9.62729 4.85377 9.79207C4.79063 9.89391 4.72861 9.99652 4.66775 10.0999C3.85818 11.4759 3.27147 12.9609 2.92061 14.5002C2.8773 14.6902 2.70987 14.8274 2.515 14.8274H0.454151C0.227464 14.8274 0.0437015 15.0111 0.0437015 15.2378L0.0437012 20.9841C0.0437012 21.2108 0.227464 21.3945 0.454151 21.3945H2.60049C2.79173 21.3945 2.95707 21.5268 3.00386 21.7122C3.39199 23.2502 4.01533 24.7216 4.85363 26.0734C4.95583 26.2382 4.93388 26.4522 4.79677 26.5894L3.35304 28.033C3.19276 28.1934 3.19276 28.4533 3.35304 28.6135L7.29951 32.56C7.45981 32.7203 7.71968 32.7203 7.87998 32.56L9.32367 31.1163C9.46078 30.9792 9.67483 30.9573 9.83964 31.0594C10.9087 31.7224 12.0604 32.256 13.2715 32.6436C16.2241 33.5887 19.3926 33.6235 22.3651 32.7434C23.694 32.35 24.9563 31.7819 26.1211 31.0595C26.286 30.9573 26.5 30.9792 26.6371 31.1164L28.1665 32.6458C28.3268 32.806 28.5867 32.806 28.7469 32.6458L32.6934 28.6993Z"
        fill={color}
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M21.0243 29.2887C21.0243 28.8512 20.6041 28.5361 20.1755 28.6243C19.4535 28.7728 18.7056 28.8509 17.9393 28.8509C17.0572 28.8509 16.1993 28.7474 15.3772 28.552C14.9426 28.4487 14.5056 28.7654 14.5056 29.2121V35.4182C14.5056 35.6449 14.6894 35.8286 14.9161 35.8286H20.6139C20.8406 35.8286 21.0243 35.6449 21.0243 35.4182V29.2887ZM20.1755 7.15939C20.6041 7.24757 21.0243 6.93249 21.0243 6.49497V0.447804C21.0243 0.221118 20.8406 0.0373537 20.6139 0.0373537L14.9161 0.0373535C14.6894 0.0373535 14.5056 0.221118 14.5056 0.447804V6.57154C14.5056 7.01823 14.9426 7.33494 15.3772 7.23165C16.1993 7.03625 17.0572 6.93279 17.9393 6.93279C18.7056 6.93279 19.4535 7.01084 20.1755 7.15939Z"
        fill={color}
      />
      <path
        fill-rule="evenodd"
        clip-rule="evenodd"
        d="M19.4573 17.0696C19.7458 17.3581 20.2136 17.3581 20.5022 17.0696L31.0557 6.51608C31.3441 6.22755 31.8119 6.22755 32.1005 6.51608L34.9171 9.33269C35.2056 9.62121 35.2056 10.089 34.9171 10.3775L20.5029 24.7917C20.2143 25.0803 19.7466 25.0803 19.458 24.7917L17.5806 22.9143C17.5804 22.9141 17.5801 22.9141 17.5799 22.9143C17.5797 22.9145 17.5793 22.9145 17.5791 22.9143L10.7972 16.1324C10.5087 15.8438 10.5087 15.3761 10.7972 15.0876L13.6138 12.271C13.9024 11.9824 14.3701 11.9824 14.6586 12.271L19.4573 17.0696Z"
        fill={color}
      />
    </g>
    <defs>
      <clipPath id="clip0_1323_1887">
        <rect width="36" height="36" fill="white" />
      </clipPath>
    </defs>
  </svg>

  // <svg
  //   xmlns="http://www.w3.org/2000/svg"
  //   width={width}
  //   height={height}
  //   fill="none"
  //   viewBox="0 0 1655 370"
  //   className={className}
  // >
  //   <path
  //     fill={color}
  //     fillRule="evenodd"
  //     d="M337.691 296.185a4.234 4.234 0 0 0 0-5.99l-45.878-45.879c-3.228-3.228-8.666-2.402-11.12 1.449-19.907 31.247-54.944 51.985-94.844 51.985-61.996 0-112.253-50.068-112.253-111.83 0-61.283 49.481-111.052 110.812-111.82.48-.007.959-.01 1.44-.01h.001c27.02 0 51.811 9.511 71.183 25.355 2.879 2.356 7.123 2.22 9.669-.492l25.357-27.008c1.602-1.706 1.52-4.391-.227-5.947A159.41 159.41 0 0 0 101.835 49.6c-1.701 1.055-3.91.828-5.325-.587L81.608 34.111a4.236 4.236 0 0 0-5.99 0L34.889 74.84a4.236 4.236 0 0 0 0 5.99l14.902 14.902c1.415 1.415 1.641 3.624.587 5.325a159.408 159.408 0 0 0-19.95 48.589c-.448 1.961-2.176 3.377-4.187 3.377H4.973a4.236 4.236 0 0 0-4.236 4.236v59.303a4.236 4.236 0 0 0 4.236 4.236h22.15c1.974 0 3.68 1.365 4.163 3.278a159.418 159.418 0 0 0 19.09 45.009c1.055 1.701.829 3.91-.587 5.325l-14.9 14.899a4.236 4.236 0 0 0 0 5.991l40.73 40.729a4.236 4.236 0 0 0 5.99 0l14.9-14.9c1.414-1.415 3.623-1.641 5.324-.587a159.408 159.408 0 0 0 168.03.001c1.701-1.055 3.91-.829 5.325.587l15.784 15.784a4.237 4.237 0 0 0 5.991 0l40.728-40.729Z"
  //     clipRule="evenodd"
  //   />
  //   <path
  //     fill={color}
  //     fillRule="evenodd"
  //     d="M217.262 302.268c0-4.515-4.337-7.767-8.759-6.857a114.448 114.448 0 0 1-23.079 2.339c-9.104 0-17.958-1.068-26.442-3.085-4.485-1.066-8.994 2.203-8.994 6.813v64.048a4.235 4.235 0 0 0 4.236 4.236h58.802a4.235 4.235 0 0 0 4.236-4.236v-63.258Zm-8.759-228.38c4.422.91 8.759-2.342 8.759-6.858V4.623a4.236 4.236 0 0 0-4.236-4.236h-58.802a4.236 4.236 0 0 0-4.236 4.236V67.82c0 4.61 4.509 7.878 8.994 6.812a114.236 114.236 0 0 1 26.442-3.084c7.908 0 15.627.805 23.079 2.339ZM201.09 176.164a7.624 7.624 0 0 0 10.783 0L320.789 67.249a7.625 7.625 0 0 1 10.783 0l29.068 29.068a7.626 7.626 0 0 1 0 10.783L211.881 255.859a7.624 7.624 0 0 1-10.783 0l-19.376-19.376c-.002-.002-.005-.002-.008 0a.005.005 0 0 1-.007 0l-69.991-69.991a7.624 7.624 0 0 1 0-10.783l29.068-29.068a7.624 7.624 0 0 1 10.783 0l49.523 49.523Z"
  //     clipRule="evenodd"
  //   />
  //   <path
  //     fill={color}
  //     d="M1509.79 69.35h26.86v81.805c6.03-7.878 13.25-13.864 21.64-17.957 8.39-4.195 17.49-6.293 27.32-6.293 20.46 0 37.04 7.061 49.72 21.181 12.79 14.017 19.19 34.737 19.19 62.159 0 25.989-6.29 47.579-18.88 64.768-12.59 17.19-30.03 25.785-52.34 25.785-12.48 0-23.02-3.018-31.61-9.055-5.12-3.582-10.59-9.311-16.43-17.19v21.027h-25.47V69.35Zm71.83 207.045c14.93 0 26.09-5.935 33.45-17.804 7.47-11.869 11.21-27.524 11.21-46.965 0-17.292-3.74-31.617-11.21-42.974-7.36-11.358-18.26-17.036-32.69-17.036-12.58 0-23.63 4.655-33.15 13.966-9.41 9.311-14.12 24.659-14.12 46.044 0 15.451 1.94 27.985 5.83 37.603 7.27 18.11 20.83 27.166 40.68 27.166ZM1380.31 131.202v109.124c0 8.39 1.33 15.246 3.99 20.566 4.91 9.823 14.07 14.735 27.47 14.735 19.24 0 32.34-8.595 39.3-25.785 3.78-9.209 5.67-21.845 5.67-37.91v-80.73h27.63v164.377h-26.09l.31-24.25c-3.58 6.242-8.04 11.511-13.36 15.808-10.54 8.595-23.33 12.893-38.37 12.893-23.43 0-39.39-7.828-47.88-23.483-4.61-8.39-6.91-19.594-6.91-33.612V131.202h28.24ZM1145.7 70.12h30.85v93.161h117.26V70.119h30.85v225.462h-30.85V190.14h-117.26v105.441h-30.85V70.119ZM964.099 206.868h-48.806v88.712h-46.198V69.35h110.966c15.86.308 28.039 2.252 36.529 5.833 8.59 3.581 15.86 8.85 21.79 15.808a70.219 70.219 0 0 1 11.67 19.032c2.86 6.957 4.3 14.887 4.3 23.789 0 10.744-2.72 21.334-8.14 31.77-5.42 10.335-14.37 17.651-26.86 21.948 10.44 4.195 17.81 10.181 22.1 17.957 4.4 7.674 6.6 19.441 6.6 35.3v15.195c0 10.334.41 17.343 1.23 21.027 1.23 5.832 4.09 10.129 8.6 12.892v5.679h-52.03c-1.44-5.014-2.46-9.056-3.07-12.125a110.974 110.974 0 0 1-2-19.492l-.31-21.027c-.2-14.427-2.861-24.045-7.977-28.854-5.014-4.809-14.478-7.214-28.394-7.214Zm28.701-41.132c9.41-4.298 14.12-12.79 14.12-25.478 0-13.711-4.55-22.919-13.66-27.626-5.115-2.66-12.789-3.991-23.021-3.991h-54.946v60.778h53.564c10.641 0 18.622-1.227 23.943-3.683ZM760.732 214.235h-48.039v81.345h-46.965V69.35h98.534c22.715 0 40.826 5.833 54.332 17.497 13.506 11.665 20.259 29.724 20.259 54.179 0 26.705-6.753 45.583-20.259 56.634-13.506 11.05-32.794 16.575-57.862 16.575Zm22.101-47.118c6.139-5.423 9.209-14.018 9.209-25.784 0-11.767-3.121-20.157-9.362-25.171-6.14-5.014-14.786-7.521-25.939-7.521h-44.048v66.611h44.048c11.153 0 19.851-2.712 26.092-8.135ZM547.083 295.58H444.558V69.35H554.45c27.728.41 47.374 8.442 58.936 24.097 6.958 9.618 10.436 21.129 10.436 34.533 0 13.813-3.478 24.915-10.436 33.305-3.888 4.707-9.618 9.004-17.19 12.892 11.562 4.195 20.259 10.846 26.092 19.953 5.934 9.106 8.901 20.157 8.901 33.151 0 13.404-3.376 25.427-10.129 36.068-4.298 7.06-9.669 12.995-16.116 17.804-7.264 5.525-15.859 9.311-25.784 11.357-9.823 2.046-20.515 3.07-32.077 3.07Zm-1.075-99.609h-56.327v60.318h55.56c9.925 0 17.65-1.33 23.175-3.991 10.028-4.911 15.041-14.324 15.041-28.24 0-11.767-4.86-19.85-14.58-24.25-5.423-2.455-13.046-3.734-22.869-3.837Zm23.483-43.127c6.139-3.684 9.208-10.284 9.208-19.799 0-10.539-4.092-17.497-12.278-20.874-7.06-2.353-16.064-3.53-27.012-3.53h-49.728v49.881h55.56c9.925 0 18.008-1.893 24.25-5.678Z"
  //   />
  // </svg>
);
export default BPRGearLogo;
