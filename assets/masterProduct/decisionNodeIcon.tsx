
const DecisionNodeIcon = ({ color }: { color?: string }) => {
  return (
    <svg
      width="22"
      height="22"
      viewBox="0 0 22 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.80797 2.46482C10.0186 1.2542 11.9814 1.2542 13.192 2.46482L19.5352 8.80797C20.7458 10.0186 20.7458 11.9814 19.5352 13.192L13.192 19.5352C11.9814 20.7458 10.0186 20.7458 8.80797 19.5352L2.46482 13.192C1.2542 11.9814 1.2542 10.0186 2.46482 8.80797L8.80797 2.46482Z"
        stroke={color ? color : '#7784FF'}
        stroke-width="1.8"
      />
    </svg>
  );
};

export default DecisionNodeIcon
