import * as React from "react";

const InventoryHubIcon = ({
  height = "24",
  width = "24",
  color = "#575757",
  className = "",
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={width}
    height={height}
    fill="none"
    className={className}
    viewBox="0 0 24 24"
  >
    <path
      fill={color}
      d="M20.107 6.94c0 .54-.29 1.03-.75 1.28l-1.74.94-1.48.79-3.07 1.66c-.33.18-.69.27-1.06.27-.37 0-.73-.09-1.06-.27l-6.29-3.39c-.46-.25-.75-.74-.75-1.28s.29-1.03.75-1.28l1.97-1.06 1.57-.85 2.75-1.48c.66-.36 1.46-.36 2.12 0l6.29 3.39c.46.25.75.74.75 1.28Z"
      opacity={0.2}
    />
    <path
      fill={color}
      d="m9.907 12.79-5.85-2.93c-.45-.23-.97-.2-1.4.06-.43.26-.68.72-.68 1.22v5.53c0 .96.53 1.82 1.39 2.25l5.85 2.92c.2.1.42.15.64.15.26 0 .52-.07.75-.22.43-.26.68-.72.68-1.22v-5.53c.01-.94-.52-1.8-1.38-2.23ZM22.037 11.15v5.53c0 .95-.53 1.81-1.39 2.24l-5.85 2.93a1.432 1.432 0 0 1-1.4-.07c-.42-.26-.68-.72-.68-1.22v-5.52c0-.96.53-1.82 1.39-2.25l2.15-1.07 1.5-.75 2.2-1.1c.45-.23.97-.21 1.4.06.42.26.68.72.68 1.22Z"
      opacity={0.4}
    />
    <path
      fill={color}
      d="m17.617 9.16-1.48.79-9.51-5.35 1.57-.85 9.18 5.18c.1.06.18.14.24.23ZM17.757 10.97v2.27c0 .41-.34.75-.75.75s-.75-.34-.75-.75v-1.52l1.5-.75Z"
    />
  </svg>
);
export default InventoryHubIcon;
