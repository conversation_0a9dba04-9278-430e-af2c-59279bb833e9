export type FieldType =
  | 'text'
  | 'textarea'
  | 'email'
  | 'number'
  | 'dropdown'
  | 'radio'
  | 'checkbox'
  | 'boolean'
  | 'date'
  | 'file'
  | 'section-header';

export interface FieldOption {
  id: string;
  label: string;
  value: string;
}

export interface ValidationRules {
  minLength?: number;
  maxLength?: number;
  pattern?: string; // regex string
  min?: number; // for numeric fields
  max?: number; // for numeric fields
}

export interface FieldBase {
  id: string;
  type: FieldType;
  label: string;
  required?: boolean;
  placeholder?: string;
  helpText?: string;
  defaultValue?: FormResponseValue;
  validation?: ValidationRules;
  scoreWeight?: number; // for scoring/evaluations
  allowAttachments?: boolean; // for evidence
  enableReviewerNotes?: boolean;
}

export interface ChoiceField extends FieldBase {
  type: 'dropdown' | 'radio' | 'checkbox';
  options: FieldOption[];
}

export interface SectionHeaderField extends FieldBase {
  type: 'section-header';
  description?: string;
}

export type Field =
  | FieldBase // text, textarea, email, number, boolean, date, file
  | ChoiceField
  | SectionHeaderField;

export interface Section {
  id: string;
  title: string;
  description?: string;
  fields: Field[];
}

export type FormStatus = 'draft' | 'published' | 'archived';

export interface FormSchema {
  id?: string;
  name: string;
  description?: string;
  sections: Section[];
  status: FormStatus;
  created_at?: string;
  updated_at?: string;
}

export interface FormSummary {
  id: string;
  name: string;
  status: FormStatus;
  updated_at?: string;
}

export type FormResponseValue = string | number | boolean | string[] | null;

export interface FormResponsePayload {
  form_id: string;
  answers: Record<string, FormResponseValue>; // key is field.id
}

export const defaultNewForm = (): FormSchema => ({
  name: 'Untitled Form',
  description: '',
  status: 'draft',
  sections: [
    {
      id: `${Date.now()}-section-1`,
      title: 'New Section',
      description: '',
      fields: [],
    },
  ],
});
