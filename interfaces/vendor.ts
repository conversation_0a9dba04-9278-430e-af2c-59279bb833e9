export enum VendorStatus {
  Approved = 'Approved',
  Rejected = 'Rejected',
  UnderEvaluation = 'Under evaluation',
}

export enum ReviewPeriod {
  Monthly = 'Monthly',
  Quarterly = 'Quarterly',
  HalfYearly = 'Half-yearly',
  Yearly = 'Yearly',
}

export interface VendorDocument {
  document_type: string;
  document_path: string;
  file_name: string;
  vendor: string;
  company: string;
  created_on: string;
  last_modified_on: string;
  id: string;
}

export interface VendorEvaluation {
  file_name: string;
  document_path: string;
  vendor: string;
  company: string;
  created_on: string;
  last_modified_on: string;
  document_type: string;
  evaluation_result: string;
  id: string;
}

export interface TVendorData {
  id?: string;
  name?: string;
  service?: string;
  assignee_details: { full_name: string; id: string; name: string };
  contact_date: string;
  contact_name: string;
  contact_email: string;
  contact_phone: string | number;
  address: string;
  assignee: string;
  status: VendorStatus;
  review_period: number | string;
  description: string;
  next_evaluation_date?: string;
  company?: string;
  created_by?: string;
  created_on?: string;
  last_modified_on?: string;
  documents?: VendorDocument[];
  evaluations?: VendorEvaluation[];
}
