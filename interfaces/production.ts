import { IAttachment } from './misc';

export interface TWorkOrder {
  id: string;
  order_no: string;
  reference_no: string;
  customer_name: string;
  status: string;
  uom: string;
  quantity: number;
  priority: 'Low' | 'Medium' | 'High'; // Enum-like typing for priority
  start_date: string; // ISO 8601 date string
  delivery_date: string; // ISO 8601 date string
  name: string; // UUID as a string
  managers: {
    full_name: string;
    id: string;
    name: string;
  }[]; // Array of UUIDs
  attachments: IAttachment[];
}

export interface TStep {
  id: string;
  name: string;
  status?: string;
  action?: string;
  instruction?: string;
  remark?: string;
  is_enabled?: boolean;
  requires_approval?: boolean;
  assignees?: string[];
  approvers?: string[];
}

export interface IWorkInstructionDetails {
  id: string;
  instruction_name: string;
  description?: string;
  requires_evidence?: boolean;
  requires_approval?: boolean;
}

export interface IWorkInstruction {
  sequence_no: number;
  description: string;
  compliant: boolean;
  remark: string;
  attachments?: IAttachment[];
  completion_date: string;
  done_by: string;
  id: string;
  work_instruction?: IWorkInstructionDetails;
}

// Validation Rule Interfaces
export interface IValidationRule {
  id: string;
  rule_name?: string;
  type: 'ValueMatch' | 'ValueRange' | 'ConditionalStep';
  error_message: string;
  condition: string; // Expected value/condition
  master_rule_id?: string;
  work_order_checklist_id: string;
  work_order_step: string;
  work_order: string;
}

export interface IValidationResponse {
  id?: string;
  user_response: string;
  is_correct: boolean;
  validation_rule_id: string;
}

export interface IWorkInstructionWithValidation extends IWorkInstruction {
  validation_rules: IValidationRule[];
  validation_responses: IValidationResponse[];
}

// Flow Conditions Interface
export interface IFlowCondition {
  id: string;
  step: string;
  work_order: string;
  condition?: string;
  next_step?: string;
  validation_rule_id?: string;
  master_flow_condition_id?: string;
}

export interface IValidationRuleWithResponse {
  rule: IValidationRule;
  user_response?: IValidationResponse;
  flow_conditions?: IFlowCondition[];
}
