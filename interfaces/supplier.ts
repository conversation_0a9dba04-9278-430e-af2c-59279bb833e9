export type SupplierStatus =
  | 'draft'
  | 'prospect'
  | 'active'
  | 'inactive'
  | 'blocked';
export type RiskTier = 'low' | 'medium' | 'high';

export interface SupplierListItem {
  id: string;
  legal_name: string;
  website?: string | null;
  // Legacy single supplier_type may be present; new multi-types are in 'types'
  supplier_type?: string | null;
  status: SupplierStatus;
  risk_tier: RiskTier;
  criticality_level: number;
}

export interface SupplierContact {
  id?: string;
  name: string;
  email?: string | null;
  phone?: string | null;
  is_primary: boolean;
}

export interface SupplierAddress {
  id?: string;
  country: string;
  address_line1: string;
  address_line2?: string | null;
  city: string;
  state_province?: string | null;
  postal_code?: string | null;
  is_primary: boolean;
}

export interface SupplyCategoryRef {
  id: string;
  name: string;
}

export interface SupplierDetail extends SupplierListItem {
  notes?: string | null;
  contacts: SupplierContact[];
  addresses: SupplierAddress[];
  categories: SupplyCategoryRef[];
  types: Array<{ id: string; name: string }>; // new multi-types
}

export interface SupplierCreatePayload {
  legal_name: string;
  website?: string | null;
  supplier_type_ids?: string[]; // new multi-type IDs
  status?: SupplierStatus; // ignored on create (always Draft)
  risk_tier: RiskTier;
  criticality_level: number;
  notes?: string | null;
  primary_contact: {
    name: string;
    email?: string | null;
    phone?: string | null;
  };
  additional_contacts?: Array<{
    name: string;
    email?: string | null;
    phone?: string | null;
  }>;
  primary_address: {
    country: string;
    address_line1: string;
    address_line2?: string | null;
    city: string;
    state_province?: string | null;
    postal_code?: string | null;
  };
  additional_addresses?: Array<{
    country: string;
    address_line1: string;
    address_line2?: string | null;
    city: string;
    state_province?: string | null;
    postal_code?: string | null;
  }>;
  category_ids?: string[];
}

export type SupplierUpdatePayload = Partial<SupplierCreatePayload>;
