// interfaces/risk.ts
import { IAttachment } from './misc';

export interface TRiskData {
  id: string;
  risk_id: string;
  title: string;
  description: string;
  departments: [{ id: string; name: string }];
  processes: [{ id: string; name: string }];
  categories: [{ id: string; name: string }];
  types: [{ id: string; name: string }];
  impact: string;
  probability: number;
  severity: number;
  detectability: number;
  risk_score: number;
  risk_level: string;
  mitigation_description: string;
  target_completion_date: string;
  status: string;
  attachments?: IAttachment[];
  risk_owner_id?: string;
  risk_owner?: { full_name: string; id: string; name: string };
  status_history?: {
    date: string;
    status: string;
    updated_by: string;
    comments: string;
  }[];
  created_at: string;
  updated_at: string;
}