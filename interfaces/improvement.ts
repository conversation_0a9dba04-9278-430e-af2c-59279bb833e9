export interface ICapa {
  id: string;
  capa_id: string;
  created_on: string;
  last_modified_on: string;
  description: string;
  source: string;
  status: "Open" | "Closed" | "In Progress";
}

export type ICapaInfo = {
  id: string;
  capa_id: string;
  created_by: string;
  last_modified_by: string;
  created_on: string;
  last_modified_on: string;
  description: string;
  status: "Open" | "Closed" | "In Progress";
  root_cause: string;
  corrective_action_description: string;
  corrective_action_due_date: string;
  corrective_action_verification_date: string;
  corrective_action_users: User[];
  corrective_action_verification_users: User[];
  corrective_action_documents: Document[];
  preventive_action_description: string;
  preventive_action_due_date: string;
  preventive_action_verification_date: string;
  preventive_action_users: User[];
  preventive_action_verification_users: User[];
  preventive_action_documents: Document[];
  source: string;
  feedback: string;
  action: string;
  audit_info: AuditInfo;
};

type User = {
  id: string;
  full_name: string;
  name: string;
};

type Document = {
  id: string;
  file_path: string;
  file_extension: string;
};

export type AuditInfo = {
  audit: {
    id: string;
    created_on: string;
    last_modified_on: string;
    name: string;
  };
  audit_nc: {
    id: string;
    last_modified_on: string;
    nc_id: string;
    category: string;
  };
  sub_clause: {
    id: string;
    clause_no: string;
    title: string;
  };
  auditors: User[];
  auditees: User[];
};
