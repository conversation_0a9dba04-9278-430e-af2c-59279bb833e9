import { Position } from "@xyflow/react";

export interface TProductData {
    id: string;
    product_id: string;
    name: string;
    assignees: {name:string;id:string}[];
    approvers: {name:string;id:string}[];
    status: string;
    launch_date: string;
    version: number;
  }

export interface TNodeProps {
  id:string;
  data:{onChange: (event: any) => void,label:string, disabled?:boolean, selected?:boolean};
  sourcePosition?:Position,
  targetPosition?:Position
}