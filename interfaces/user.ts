export interface IUser {
  email: string;
  full_name: string;
  id: string;
  roles: string[];
}

export interface TCurrentUser {
  company: {
    id: string;
    name: string;
    logo_url: string;
    primary_color: string;
    is_cfr11_required: boolean;
  };
  created_by: string;
  created_on: string;
  email: string;
  first_name: string;
  full_name: string;
  id: string;
  is_active: boolean;
  last_modified_by: string;
  last_modified_on: string;
  last_name: string;
  roles: string[];
  sub: string;
  job_title: string;
  is_admin: boolean;
}
