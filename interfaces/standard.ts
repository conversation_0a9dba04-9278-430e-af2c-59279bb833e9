export interface IStandardData {
  id: string;
  title: string;
  description: string;
  percent_complete: number;
}

type ISummary = {
  total_document_count: number;
  compliant_document_count: number;
  non_compliant_document_count: number;
};

export type IDocument = {
  id: string;
  last_modified_by?: User;
  created_on: string;
  last_modified_on: string;
  doc_id?: string;
  title: string;
  assignee?: User;
  approver?: User;
  department?: Department;
  category?: Category;
  review_period: number;
  origin: string;
  status: string;
  is_compliant: boolean;
  publish_date?: string;
  next_review_date?: string;
};

type User = {
  id: string;
  full_name: string;
  name: string;
};

type Department = {
  id: string;
  name: string;
};

type Category = {
  id: string;
  name: string;
};
export type Process = {
  id: string;
  name: string;
  documents: IDocument[];
};

type SubClause = {
  id: string;
  clause_no: string;
  title: string;
  description: string;
  question: string;
  is_compliant: boolean;
  processes: Process[];
};

type Clauses = {
  id: string;
  clause_no: string;
  title: string;
  is_compliant: boolean;
  sub_clauses: SubClause[];
};

export type IStandardRecord = {
  id: string;
  clause_no: string;
  title: string;
  clauses: Clauses[];
  sub_clauses: SubClause[];
  is_compliant: boolean;
};

export type IStandardView = {
  summary: ISummary;
  standard: IStandardData;
  records: IStandardRecord[];
};
