{"name": "bprhub-fe-v2", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ag-grid-community/client-side-row-model": "^32.3.3", "@ag-grid-community/core": "^32.3.3", "@ag-grid-community/react": "^32.3.3", "@ag-grid-enterprise/menu": "^32.3.3", "@ag-grid-enterprise/multi-filter": "^32.3.3", "@ag-grid-enterprise/set-filter": "^32.3.3", "@auth0/nextjs-auth0": "^3.5.0", "@ckeditor/ckeditor5-react": "^9.4.0", "@headlessui/react": "^2.2.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.1.4", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/toolbar": "^3.12.0", "@xyflow/react": "^12.3.6", "ag-grid-community": "^32.3.3", "axios": "^1.7.9", "ckeditor5": "^43.3.1", "ckeditor5-premium-features": "^43.3.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "framer-motion": "^11.14.4", "html2canvas": "^1.4.1", "jspdf": "^3.0.0", "lodash": "^4.17.21", "lucide-react": "^0.463.0", "mixpanel-browser": "^2.67.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "next": "15.0.3", "pdfjs-dist": "^3.4.120", "react": "18.2.0", "react-datepicker": "^7.6.0", "react-day-picker": "^9.5.0", "react-dom": "18.2.0", "react-draggable": "^4.5.0", "react-dropzone": "^14.3.5", "react-lottie": "^1.2.10", "react-markdown": "^10.1.0", "react-mentions": "4.4.10", "react-resizable-panels": "^3.0.3", "react-select": "^5.8.3", "react-toastify": "^10.0.6", "react-zoom-pan-pinch": "^3.7.0", "reactjs-otp-input": "^2.0.10", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "uuid": "^11.0.5", "zod": "^3.24.1", "zustand": "^5.0.2"}, "devDependencies": {"@types/lodash": "^4.17.14", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-lottie": "^1.2.10", "@types/react-mentions": "^4.4.1", "depcheck": "^1.4.7", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}