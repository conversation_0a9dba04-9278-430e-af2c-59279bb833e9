import { NextApiRequest, NextApiResponse } from 'next';
import auth0 from './auth0';

// TypeScript type for the response payload
interface TokenResponse {
  accessToken: string;
}



const handler = auth0.withApiAuthRequired(async (req: NextApiRequest, res: NextApiResponse<TokenResponse | { message: string }>) => {

  console.log('AUTH0_SECRET_token = ', process.env.AUTH0_SECRET)

  try {
    const { accessToken } = await auth0.getAccessToken(req, res);

    if (!accessToken) {
      return res.status(500).json({ message: 'Access token not found' });
    }

    res.status(200).json({ accessToken });
  } catch (error) {
    console.error("Error fetching access token:", error);
    res.status(500).json({ message: 'Failed to fetch access token' });
  }
});

export default handler;
