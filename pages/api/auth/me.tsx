import { NextApiRequest, NextApiResponse } from "next";
import auth0 from "./auth0";

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  try {
    // Validate the session
    const session = auth0.getSession(req, res);
    if (!session) {
      return res.redirect(302, "/login");
    }

    // Fetch and return the profile
    await auth0.handleProfile(req, res);
  } catch (error) {
    console.error("Error fetching user profile:", error);
    return res.redirect(302, "/login");
  }
}
