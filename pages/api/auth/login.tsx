import { NextApiRequest, NextApiResponse } from "next";
import auth0 from "./auth0";

export default async function login(
  req: NextApiRequest,
  res: NextApiResponse
): Promise<void> {
  try {
    const { organization, connection, login_hint } = req.query;
    let AUTH0_BASE_URL = process.env.AUTH0_BASE_URL
    let baseUrl = AUTH0_BASE_URL && !/^https?:\/\//.test(AUTH0_BASE_URL as string) ? `https://${AUTH0_BASE_URL}` : AUTH0_BASE_URL;
    console.log(`base url: ${baseUrl}`)
    console.log(`scope: ${process.env.AUTH0_SCOPE}`)
    let audience = process.env.AUTH0_AUDIENCE
    console.log(`audience: ${audience}`)
    await auth0.handleLogin(req, res, {
      authorizationParams: {
        audience: process.env.AUTH0_AUDIENCE, // API Identifier
        ...(organization && { organization: organization as string }),
        ...(connection && { connection: connection as string }),
        ...(login_hint && { login_hint: login_hint as string }),
        scope: 'openid profile email offline_access',
      },
    });
  } catch (error: any) {
    res.status(error.status || 500).end(error.message);
  }
}
