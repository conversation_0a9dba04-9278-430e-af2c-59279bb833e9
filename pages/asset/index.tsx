import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { Upload } from 'lucide-react';

import PlusIcon from '@/assets/outline/plus';
import CreateAssetModal from '@/components/asset/modals/createAssetModal';
import BulkUploadModal from '@/components/asset/modals/bulkUploadModal';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import Status from '@/components/common/status';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TAssetData } from '@/interfaces/asset';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import { formatDate, getPeriodFromMonth } from '@/utils/time';

const AssetHub = () => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);
  const router = useRouter();

  const { data, isLoading, error, reFetch } = useFetch(accessToken, 'assets');

  const [activeTab, setActiveTab] = useState<number>(0);
  const [createAsset, setCreateAsset] = useState(false);
  const [editAsset, setEditAsset] = useState(false);
  const [bulkUpload, setBulkUpload] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<undefined | TAssetData>(
    undefined,
  );
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { deleteData, response: onDelete } = useDelete();

  useEffect(() => {
    if (onDelete) {
      setShowDeleteModal(false);
      reFetch();
    }
  }, [onDelete]);

  const handleEdit = (rowData: TAssetData) => {
    setSelectedAsset(rowData);
    setEditAsset(true);
  };

  const handleDeleteModal = (rowData: TAssetData) => {
    setSelectedAsset(rowData);
    setShowDeleteModal(true);
  };

  const handleDelete = () => {
    async function fetch() {
      accessToken &&
        (await deleteData(accessToken, `assets/${selectedAsset?.id}`));
    }
    fetch();
  };

  const getAssetColumns = useCallback(() => {
    const assetColumns: any = [
      {
        headerName: 'ID',
        field: 'asset_id',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: any) => params.value,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'asset_id'),
        filter: false,
      },
      {
        headerName: 'Asset name',
        field: 'name',
        resizable: true,
        valueFormatter: (params: any) => getValueOrDefault(params.data, 'name'),
        filter: false,
      },
      {
        headerName: 'Owner',
        field: 'owner',
        sortable: false,
        resizable: true,
        filter: 'agMultiColumnFilter',
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data.owner, 'name'),
      },
      {
        headerName: 'Location',
        field: 'location',
        resizable: true,
        filter: 'agMultiColumnFilter',
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'location'),
      },
      {
        headerName: 'Status',
        field: 'status',
        resizable: true,
        filter: 'agMultiColumnFilter',
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'status'),
      },
      {
        headerName: 'Calibration',
        field: 'calibration_status',
        resizable: true,
        sortable: false,
        filter: false,
        cellRenderer: (params: any) => (
          <div className="flex items-center h-full">
            <Status type={params.value} />
          </div>
        ),
      },
    ];

    hasAccess(AccessActions.CanAddOrEditAssets, user) &&
      assetColumns.push({
        headerName: 'Manage',
        field: 'manage',
        cellRenderer: (params: any) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={handleEdit}
            handleDelete={handleDeleteModal}
            hideDelete={!hasAccess(AccessActions.IsAssetAdmin, user)}
          />
        ),
        width: 100,
        pinned: 'right',
        filter: false,
      });

    return assetColumns;
  }, [user]);

  const breadcrumbData = [
    {
      name: 'Asset hub',
      link: '/asset',
    },
  ];

  return (
    <Layout>
      {createAsset && (
        <Dialog open={createAsset} onOpenChange={setCreateAsset}>
          <CreateAssetModal setOpenEdit={setCreateAsset} reFetch={reFetch} />
        </Dialog>
      )}
      {bulkUpload && (
        <Dialog open={bulkUpload} onOpenChange={setBulkUpload}>
          <BulkUploadModal setOpenModal={setBulkUpload} onSuccess={reFetch} />
        </Dialog>
      )}
      {editAsset && selectedAsset && (
        <Dialog
          open={editAsset}
          onOpenChange={() => {
            setEditAsset(false);
          }}
        >
          <CreateAssetModal
            edit={true}
            assetData={selectedAsset}
            setOpenEdit={setEditAsset}
            reFetch={reFetch}
          />
        </Dialog>
      )}
      {showDeleteModal && (
        <Dialog
          open={showDeleteModal}
          onOpenChange={() => setShowDeleteModal(false)}
        >
          <DeleteModal
            title={`Delete Asset`}
            infoText={`Are you sure you want to delete this asset?`}
            btnText={'Delete'}
            onClick={handleDelete}
          />
        </Dialog>
      )}
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Asset Hub
          </div>
        </div>
      </div>
      <div className="mb-5">
        <CommonTable
          data={data}
          columnDefs={getAssetColumns()}
          searchPlaceholder="Search by ID, name, location, owner or status"
          isLoading={isLoading}
          handleRowClick={(e: any) => {
            if (e?.data?.id) {
              router.push(`/asset/${e.data.id}`);
            }
          }}
          searchRightSideElement={
            hasAccess(AccessActions.CanAddOrEditAssets, user) ? (
              <div className="flex gap-4">
                <PrimaryButton
                  text="Add Asset"
                  buttonClasses="!px-5 !py-2"
                  onClick={() => {
                    setCreateAsset(true);
                  }}
                  icon={<PlusIcon color="white" />}
                />
                {hasAccess(AccessActions.IsAssetAdmin, user) && (
                  <PrimaryButton
                    text="Bulk Upload"
                    buttonClasses="!px-5 !py-2"
                    onClick={() => {
                      setBulkUpload(true);
                    }}
                    icon={<Upload className="h-4 w-4" color="white" />}
                  />
                )}
              </div>
            ) : (
              <></>
            )
          }
        />
      </div>
    </Layout>
  );
};

export default AssetHub;
