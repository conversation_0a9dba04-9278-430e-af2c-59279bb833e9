import React, { useCallback } from 'react';
import Breadcrumb from '@/components/common/breadcrumb';
import Loader from '@/components/common/loader';
import Layout from '@/components/common/sidebar/layout';
import ToggleSwitch from '@/components/common/toogleSwitch';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePut } from '@/hooks/usePut';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import InfoCircle from '@/assets/outline/infoCircle';

interface CompanyData {
  record: {
    company: {
      is_cfr11_required: boolean;
    };
  };
}

const BREADCRUMB_DATA = [
  {
    name: 'Settings',
    link: '/setting',
  },
  {
    name: 'Workflows',
    link: '#',
  },
];

const WorkFlowPage: React.FC = () => {
  const { accessToken } = useAuthStore();

  const { data, isLoading, reFetch } = useFetch<CompanyData>(
    accessToken,
    '/users/me',
  );

  const { putData, response: updateResponse } = usePut();

  const handleChangeCfr = useCallback(
    (value: boolean) => {
      putData(accessToken as string, 'company', {
        is_cfr11_required: value,
      }).then(() => window.location.reload());
    },
    [accessToken, putData, reFetch],
  );

  if (isLoading) {
    return <Loader className="h-[80vh]" />;
  }

  return (
    <Layout>
      <div className="my-5">
        <div className="flex flex-col">
          <Breadcrumb data={BREADCRUMB_DATA} />

          <h1 className="text-dark-300 font-semibold text-3xl leading-10">
            Workflows
          </h1>

          <div className="my-5 flex gap-4 items-center p-4 border border-white-300 rounded-lg mb-3">
            <ToggleSwitch
              size="lg"
              initialState={data?.record?.company?.is_cfr11_required}
              onChange={handleChangeCfr}
            />

            <div>
              <h5 className="text-base font-medium leading-6 mb-1 flex items-center gap-2">
                CFR Part 11{' '}
                <Tooltip>
                  <TooltipTrigger>
                    <InfoCircle height="24" width="24" color="#989898" />
                  </TooltipTrigger>
                  <TooltipContent className="mx-4 py-4" side="bottom">
                    <div className=" text-sm text-dark-300 px-2">
                      <h4 className="text-base font-medium leading-6 mb-1">
                        <strong>Enable CFR Part 11 Compliance</strong>
                      </h4>
                      <p className="text-base leading-6 font-medium mb-1">
                        When enabled, BPRHub enforces compliance with FDA 21 CFR
                        Part 11 regulations. This includes:
                      </p>
                      <ul className="list-disc mx-4 text-base leading-6 font-medium mb-2">
                        <li>
                          Enhanced access control with role-based permissions.
                        </li>
                        <li>Automated audit trails for critical actions.</li>
                        <li>
                          Electronic signatures for approvals and verifications.
                        </li>
                        <li>
                          Data integrity safeguards for document storage and
                          processing.
                        </li>
                      </ul>
                      <p className="text-base leading-6 font-medium mb-1">
                        <strong>Note:</strong> Disabling this feature may affect
                        workflows that rely on compliance features. Ensure
                        compliance requirements are reviewed before making
                        changes.
                      </p>
                    </div>
                  </TooltipContent>
                </Tooltip>
              </h5>
              <p className="text-base font-medium leading-6 text-grey-300 mb-1">
                Enable CFR Part 11 Compliance to enforce audit trails,
                electronic signatures, and data integrity for regulatory
                compliance.
              </p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default React.memo(WorkFlowPage);
