// pages/risk/[riskId]/edit.tsx
import React from 'react';
import { useRouter } from 'next/router';

import Layout from '@/components/common/sidebar/layout';
import CreateRiskForm from '@/components/risk/createRiskForm';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { TRiskData } from '@/interfaces/risk';
import Loader from '@/components/common/loader';

const EditRisk = () => {
  const router = useRouter();
  const { riskId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);

  const { data: riskData, isLoading } = useFetch<TRiskData>(
    accessToken,
    `risks/${riskId}`,
    {},
  );

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-screen">
          <Loader />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="px-6 py-6">
        {riskData && (
          <CreateRiskForm
            riskData={riskData}
            isEdit={true}
            onSuccess={() => router.push('/risk')}
          />
        )}
      </div>
    </Layout>
  );
};

export default EditRisk;
