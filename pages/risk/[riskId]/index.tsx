// pages/risk/[riskId]/index.tsx
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState } from 'react';

import { ArrowLeft } from 'lucide-react';
import EditIcon from '@/assets/outline/edit';
import PrimaryButton from '@/components/common/button/primaryButton';
import {
  DetailsText,
  DetailsTextNew,
  IDetailsText,
} from '@/components/common/infoDetail';
import Loader from '@/components/common/loader';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import RiskActions from '@/components/risk/riskActions';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TRiskData } from '@/interfaces/risk';
import { hasAccess } from '@/utils/roleAccessConfig';
import { formatDate } from '@/utils/time';
import { Dialog } from '@radix-ui/react-dialog';
import DeleteIcon from '@/assets/outline/delete';
import { DialogTrigger } from '@/components/common/dialog';
import SecondaryButton from '@/components/common/button/secondaryButton';
import DeleteButton from '@/components/common/button/deleteButton';

const RiskDetails = () => {
  const router = useRouter();
  const { riskId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);
  const currentUser = useAuthStore((state) => state.user);

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);

  const {
    data: riskData,
    isLoading,
    reFetch: refetchRiskData,
  } = useFetch<TRiskData>(accessToken, `risks/${riskId}`, {});

  const { deleteData, isLoading: isDeleting } = useDelete();

  const handleDelete = async () => {
    if (!riskId) return;

    await deleteData(accessToken, `risks/${riskId}`);
    setShowDeleteModal(false);
    router.push('/risk');
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-screen">
          <Loader />
        </div>
      </Layout>
    );
  }

  if (!riskData) {
    return (
      <Layout>
        <div className="px-6 py-6">
          <div className="text-center">
            <h2 className="text-xl font-semibold">Risk not found</h2>
            <Link
              href="/risk"
              className="text-blue-600 hover:underline mt-2 inline-block"
            >
              Return to Risk Hub
            </Link>
          </div>
        </div>
      </Layout>
    );
  }

  const getRiskLevelColor = (level: string) => {
    switch (level?.toLowerCase()) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Layout>
      <div className="flex flex-col flex-1">
        <div className=" my-5">
          <div className="flex items-center gap-4">
            <SecondaryButton
              icon={<ArrowLeft />}
              onClick={() => router.push('/risk')}
              text=""
              size="medium"
              buttonClasses="!px-2.5"
            />
            <div>
              <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5">
                {riskData?.title}
              </div>
            </div>
          </div>

          <div className="mt-6 flex items-center justify-between"></div>
          {!isLoading ? (
            <div className=" flex-1">
              <>
                <div className="border border-grey-100 bg-white p-2 rounded-lg">
                  {/* Row 1: RiskID, Status, Target Completion date, edit button */}
                  <div className="flex items-start">
                    <div className="flex-1 grid grid-cols-3 gap-4">
                      <div className="p-2 flex flex-col gap-3">
                        <DetailsTextNew
                          label="Risk ID"
                          value={riskData?.risk_id || '--'}
                        />
                      </div>
                      <div className="p-2 flex flex-col gap-3">
                        <DetailsTextNew
                          label="Status"
                          value={riskData?.status || '--'}
                        />
                      </div>
                      <div className="p-2 flex flex-col gap-3">
                        <DetailsTextNew
                          label="Target Completion Date"
                          value={
                            riskData?.target_completion_date
                              ? formatDate(riskData?.target_completion_date)
                              : '--'
                          }
                        />
                      </div>
                    </div>
                    <div className="flex-none">
                      {hasAccess(AccessActions.IsRiskAdmin, currentUser) && (
                        <div className="flex items-center gap-3">
                          <Dialog
                            open={openEditModal}
                            onOpenChange={setOpenEditModal}
                          >
                            <DialogTrigger asChild>
                              <SecondaryButton
                                size="medium"
                                icon={
                                  <EditIcon
                                    color="#016366"
                                    className="h-5 w-5"
                                  />
                                }
                                text="Edit"
                                onClick={() => {
                                  router.push(`/risk/${riskId}/edit`);
                                }}
                              />
                            </DialogTrigger>
                          </Dialog>

                          <Dialog>
                            <DialogTrigger asChild>
                              <DeleteButton />
                            </DialogTrigger>
                            <DeleteModal
                              title="Delete Risk"
                              infoText="Are you sure you want to delete this risk?"
                              btnText="Delete"
                              onClick={handleDelete}
                              btnLoading={isDeleting}
                              dialogContentClass="min-w-[28.5rem]"
                            >
                              <div className="p-2 border flex flex-col gap-4 border-white-300 bg-white-100 px-2.5 py-2 rounded-lg">
                                <div className="flex justify-between items-center">
                                  <div className="text-sm font-medium leading-5 text-grey-300">
                                    Risk Title
                                  </div>
                                  <div className="text-base font-medium leading-6 text-dark-300">
                                    {riskData?.title || '--'}
                                  </div>
                                </div>
                                <div className="flex justify-between items-center">
                                  <div className="text-sm font-medium leading-5 text-grey-300">
                                    Risk ID
                                  </div>
                                  <div className="text-base font-medium leading-6 text-dark-300">
                                    {riskData?.risk_id || '--'}
                                  </div>
                                </div>
                              </div>
                            </DeleteModal>
                          </Dialog>
                        </div>
                      )}
                    </div>
                    {!hasAccess(AccessActions.IsRiskAdmin, currentUser) &&
                      hasAccess(AccessActions.EDIT_RISK, currentUser) && (
                        <div className="flex-none">
                          <div className="flex items-center gap-3">
                            <Dialog
                              open={openEditModal}
                              onOpenChange={setOpenEditModal}
                            >
                              <DialogTrigger asChild>
                                <SecondaryButton
                                  size="medium"
                                  icon={
                                    <EditIcon
                                      color="#016366"
                                      className="h-5 w-5"
                                    />
                                  }
                                  text="Edit"
                                  onClick={() =>
                                    router.push(`/risk/${riskId}/edit`)
                                  }
                                />
                              </DialogTrigger>
                            </Dialog>
                          </div>
                        </div>
                      )}
                  </div>
                  {/* Row 2: Department, Process, Category */}
                  <div className="mt-4">
                    <div className="grid grid-cols-3 gap-4 w-[62vw]">
                      <div className="p-2 flex flex-col gap-3">
                        <DetailsTextNew
                          label="Department"
                          value={riskData?.departments?.[0]?.name || '--'}
                        />
                      </div>
                      <div className="p-2 flex flex-col gap-3">
                        <DetailsTextNew
                          label="Process"
                          value={riskData?.processes?.[0]?.name || '--'}
                        />
                      </div>
                      <div className="p-2 flex flex-col gap-3">
                        <DetailsTextNew
                          label="Category"
                          value={riskData?.categories?.[0]?.name || '--'}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Row 3: Risk Owner, Risk Score, Risk level */}
                  <div className="mt-4">
                    <div className="grid grid-cols-3 gap-4 w-[62vw]">
                      <div className="p-2 flex flex-col gap-3">
                        <DetailsTextNew
                          label="Risk Owner"
                          value={riskData?.risk_owner?.full_name || '--'}
                        />
                      </div>
                      <div className="p-2 flex flex-col gap-3">
                        <DetailsTextNew
                          label="Risk Score"
                          value={riskData?.risk_score?.toString() || '--'}
                        />
                      </div>
                      <div className="p-2 flex flex-col gap-3">
                        <DetailsTextNew
                          label="Risk Type"
                          value={riskData?.types?.[0]?.name || '--'}
                        />
                      </div>
                    </div>
                  </div>

                  {/* Row 4: Risk description */}
                  <div className="pl-2 mt-4">
                    <DetailsTextNew
                      label="Description"
                      value={riskData?.description || '--'}
                    />
                  </div>

                  {/* Row 5: Impact Description */}
                  <div className="pl-2 mt-4">
                    <DetailsTextNew
                      label="Impact"
                      value={riskData?.impact || '--'}
                    />
                  </div>

                  {/* Row 6: Mitigation description */}
                  <div className="pl-2 mt-4 pb-2">
                    <DetailsTextNew
                      label="Mitigation Description"
                      value={riskData?.mitigation_description || '--'}
                    />
                  </div>
                </div>
              </>
            </div>
          ) : (
            <Loader className="h-[400px]" />
          )}
        </div>
      </div>
      {/* Delete Confirmation Modal */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DeleteModal
          title="Delete Risk"
          infoText="Are you sure you want to delete this risk?"
          btnText="Delete"
          btnLoading={isDeleting}
          onClick={handleDelete}
        />
      </Dialog>
    </Layout>
  );
};

export default RiskDetails;
