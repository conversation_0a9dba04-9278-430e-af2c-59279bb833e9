import { Upload } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/router';
// pages/risk/index.tsx
import { useCallback, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import SettingIcon from '@/assets/outline/settting';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import BulkUploadModal from '@/components/risk/bulkUploadModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TRiskData } from '@/interfaces/risk';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import { formatDate } from '@/utils/time';
import { Dialog } from '@radix-ui/react-dialog';
import { useParams } from 'next/navigation';

const RiskHub = () => {
  const router = useRouter();
  const accessToken = useAuthStore((state) => state.accessToken);
  const currentUser = useAuthStore((state) => state.user);
  const [selectedRisk, setSelectedRisk] = useState<TRiskData | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showBulkUploadModal, setShowBulkUploadModal] = useState(false);

  // Fetch risks data from API
  const {
    data: risksData,
    isLoading,
    error,
    reFetch,
  } = useFetch<{
    records: TRiskData[];
    total: number;
  }>(accessToken, 'risks');

  const { deleteData, isLoading: isDeleting } = useDelete<any>();
  const param = useParams();

  const handleDelete = useCallback(async () => {
    if (selectedRisk) {
      await deleteData(accessToken, `risks/${selectedRisk.id}`);
      setShowDeleteModal(false);
      setSelectedRisk(null);
      reFetch(); // Refresh the data after deletion
    }
  }, [selectedRisk, deleteData]);

  const handleDeleteModal = useCallback((rowData: TRiskData) => {
    setSelectedRisk(rowData);
    setShowDeleteModal(true);
  }, []);

  const columnDefs = [
    {
      headerName: 'Risk ID',
      field: 'risk_id',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'risk_id');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.risk_id || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'risk_id');
      },
      filter: false,
    },
    {
      headerName: 'Risk Title',
      field: 'title',
      cellRenderer: (params: any) => {
        return (
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              router.push(`/risk/${params.data.id}`);
            }}
          >
            {getValueOrDefault(params.data, 'title')}
          </button>
        );
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.title || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'title');
      },
      filter: false,
      minWidth: 250,
      flex: 2,
    },
    {
      headerName: 'Department',
      field: 'departments',
      cellRenderer: (params: any) => {
        const departments = params.data.departments;
        if (departments && departments.length > 0) {
          return departments.map((dept: any) => dept.name).join(', ');
        }
        return '-';
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        const departments = params.data.departments;
        if (departments && departments.length > 0) {
          return departments.map((dept: any) => dept.name).join(' ');
        }
        return '';
      },
      valueFormatter: (params: any) => {
        const departments = params.data.departments;
        if (departments && departments.length > 0) {
          return departments.map((dept: any) => dept.name).join(', ');
        }
        return '-';
      },
      filter: false,
      minWidth: 150,
    },
    {
      headerName: 'Process',
      field: 'processes',
      cellRenderer: (params: any) => {
        const processes = params.data.processes;
        if (processes && processes.length > 0) {
          return processes.map((process: any) => process.name).join(', ');
        }
        return '-';
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        const processes = params.data.processes;
        if (processes && processes.length > 0) {
          return processes.map((process: any) => process.name).join(' ');
        }
        return '';
      },
      valueFormatter: (params: any) => {
        const processes = params.data.processes;
        if (processes && processes.length > 0) {
          return processes.map((process: any) => process.name).join(', ');
        }
        return '-';
      },
      filter: false,
      minWidth: 150,
    },
    {
      headerName: 'Status',
      field: 'status',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'status');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.status || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'status');
      },
      filter: false,
      minWidth: 150,
    },
    {
      headerName: 'Risk Score',
      field: 'risk_score',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'risk_score');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.risk_score?.toString() || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'risk_score');
      },
      filter: false,
    },
    {
      headerName: 'Owner',
      field: 'risk_owner.full_name',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data?.risk_owner?.full_name || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data?.risk_owner, 'full_name');
      },
      filter: false,
    },
    {
      headerName: 'Updated At',
      field: 'updated_at',
      cellRenderer: (params: any) => {
        return formatDate(params.data.last_modified_on);
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return formatDate(params.data.last_modified_on);
      },
      valueFormatter: (params: any) => {
        return formatDate(params.data.last_modified_on);
      },
      filter: false,
    },
    {
      headerName: 'Manage',
      field: 'manage',
      cellRenderer: (params: any) => (
        <ManageCellRenderer
          rowData={params.data}
          handleEdit={(rowData) => {
            router.push(`/risk/${rowData.id}/edit`);
          }}
          handleDelete={(rowData) => {
            setSelectedRisk(rowData);
            setShowDeleteModal(true);
          }}
        />
      ),

      sortable: false,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return '';
      },
      valueFormatter: (params: any) => {
        return '';
      },
      filter: false,
    },
  ];

  return (
    <Layout>
      <div className="my-5">
        <div className="flex flex-col">
          <Breadcrumb
            data={[
              { name: 'Home', link: '/' },
              { name: 'Risk Hub', link: '/risk' },
            ]}
          />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Risk Hub
          </div>
        </div>

        <div className="mt-5 mb-5">
          <CommonTable
            data={risksData}
            columnDefs={columnDefs}
            isLoading={isLoading}
            searchRightSideElement={
              <div className="flex gap-4">
                {/* {hasAccess(AccessActions.IsRiskAdmin, currentUser) && ( */}
                <Tooltip>
                  <TooltipTrigger>
                    <Link href={'/risk/risk-administration'}>
                      <SecondaryButton
                        icon={<SettingIcon />}
                        text=""
                        size="medium"
                        buttonClasses="!p-2.5"
                      />
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent>Risk Administration</TooltipContent>
                </Tooltip>
                {/* )} */}

                {/* Only show bulk upload for Admin users */}
                {(currentUser?.roles?.includes('Admin') ||
                  currentUser?.roles?.includes('SuperAdmin')) && (
                  <Tooltip>
                    <TooltipTrigger>
                      <SecondaryButton
                        text="Bulk Upload"
                        size="medium"
                        buttonClasses="!px-4 !py-2"
                        onClick={() => setShowBulkUploadModal(true)}
                        icon={<Upload className="w-4 h-4" />}
                      />
                    </TooltipTrigger>
                    <TooltipContent>
                      Bulk Upload Risks from Excel (Admin Only)
                    </TooltipContent>
                  </Tooltip>
                )}

                {hasAccess(AccessActions.CREATE_RISK, currentUser) && (
                  <>
                    <PrimaryButton
                      text="Add Risk"
                      buttonClasses="!px-5 !py-2"
                      onClick={() => {
                        router.push('/risk/create');
                      }}
                      icon={<PlusIcon color="white" />}
                    />
                  </>
                )}
              </div>
            }
          />
        </div>
      </div>
      <Dialog
        open={showDeleteModal}
        onOpenChange={() => setShowDeleteModal(false)}
      >
        <DeleteModal
          title="Delete Risk"
          infoText="Are you sure you want to delete this risk? This action cannot be undone."
          btnText="Delete"
          onClick={handleDelete}
          btnLoading={isDeleting}
        />
      </Dialog>

      <Dialog
        open={showBulkUploadModal}
        onOpenChange={(open) => {
          if (!open) {
            setShowBulkUploadModal(false);
          }
        }}
      >
        <BulkUploadModal
          setOpenModal={setShowBulkUploadModal}
          onSuccess={() => {
            reFetch(); // Refresh the risk list
          }}
        />
      </Dialog>
    </Layout>
  );
};

export default RiskHub;
