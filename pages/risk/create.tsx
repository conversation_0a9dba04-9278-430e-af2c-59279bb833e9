// pages/risk/create.tsx
import React from 'react';
import { useRouter } from 'next/router';

import Layout from '@/components/common/sidebar/layout';
import CreateRiskForm from '@/components/risk/createRiskForm';

const CreateRisk = () => {
  const router = useRouter();

  return (
    <Layout>
      <div className="px-6 py-6">
        <CreateRiskForm onSuccess={() => router.push('/risk')} />
      </div>
    </Layout>
  );
};

export default CreateRisk;
