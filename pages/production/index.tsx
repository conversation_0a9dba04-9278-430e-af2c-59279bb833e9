import { filter } from 'lodash';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { DialogContent, DialogTrigger } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import AddWOModal from '@/components/production/modals/addWOModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TWorkOrder } from '@/interfaces/production';
import { hasAccess } from '@/utils/roleAccessConfig';
import { getValueOrDefault } from '@/utils/table';
import { formatDate } from '@/utils/time';
import { truncateWithEllipsis } from '@/utils/truncateText';
import { Dialog } from '@radix-ui/react-dialog';

const ProductionHub = () => {
  const { accessToken, user } = useAuthStore();
  const { data, reFetch, isLoading } = useFetch(
    accessToken,
    'production/work-orders',
  );
  const [createWO, setCreateWO] = useState(false);
  const [editWO, setEditWO] = useState(false);
  const [selectedWO, setSelectedWO] = useState<undefined | TWorkOrder>(
    undefined,
  );
  const router = useRouter();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const {
    deleteData,
    response: onDelete,
    isLoading: deleteLoading,
  } = useDelete();

  useEffect(() => {
    if (onDelete) {
      setShowDeleteModal(false);
      reFetch();
    }
  }, [onDelete]);

  const handleEdit = (rowData: TWorkOrder) => {
    setSelectedWO(rowData);
    setEditWO(true);
  };

  const handleDeleteModal = (rowData: TWorkOrder) => {
    setSelectedWO(rowData);
    setShowDeleteModal(true);
  };

  const handleDelete = () => {
    async function fetch() {
      await deleteData(
        accessToken as string,
        `production/work-orders/${selectedWO?.id}`,
      ).then(() => {
        reFetch();
      });
    }
    fetch();
  };

  const getWOColumns = useCallback(() => {
    const WOColumns: any = [
      {
        headerName: 'Work order no.',
        field: 'order_no',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: any) => {
          return params.value;
        },
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'order_no'),
        filter: false,
      },
      {
        headerName: 'Product',
        field: 'product',
        resizable: true,
        sortable: false,
        valueFormatter: (params: any) => getValueOrDefault(params.data, 'name'),
        filter: false,
      },
      {
        headerName: 'Customer Name',
        field: 'customer_name',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'customer_name'),
        filter: false,
      },
      {
        headerName: 'Status',
        field: 'status',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'status'),
        filter: true,
        sortable: true,
      },
      {
        headerName: 'Managers',
        field: 'managers.full_name',
        filter: 'agMultiColumnFilter',
        sortable: false,
        filterValueGetter: (params: any) => {
          return params?.data?.managers
            ? params?.data?.assignees?.map((assignee: any, index: number) => {
                return `${assignee.full_name}`;
              })
            : '-';
        },
        getQuickFilterText: (params: any) => {
          return params?.data?.managers?.map((process: any) => {
            return `${process.full_name}`;
          });
        },
        valueFormatter: (params: any) => {
          return params?.data?.managers
            ? params?.data?.managers
                ?.map((process: any) => {
                  return `${process.full_name}`;
                })
                .join(', ')
            : '-';
        },
      },
      {
        headerName: 'Quantity',
        field: 'quantity',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'quantity'),
        filter: false,
        sortable: true,
      },
      {
        headerName: 'UOM',
        field: 'units',
        resizable: true,
        valueFormatter: (params: any) => getValueOrDefault(params.data, 'uom'),
        filter: false,
        sortable: false,
      },
      {
        headerName: 'Expected start date',
        field: 'start_date',
        resizable: true,
        valueFormatter: (params: any) =>
          formatDate(getValueOrDefault(params.data, 'start_date'), false),
        filter: false,
        minWidth: 150,
        sortable: true,
      },
      {
        headerName: 'Expected delivery date',
        field: 'delivery_date',
        resizable: true,
        valueFormatter: (params: any) =>
          formatDate(getValueOrDefault(params.data, 'delivery_date'), false),
        filter: false,
        minWidth: 150,
        sortable: true,
      },
    ];

    if (hasAccess(AccessActions.CanDeleteSpecificWorkOrder, user)) {
      WOColumns.push({
        headerName: 'Manage',
        field: 'manage',
        cellRenderer: (params: any) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={handleEdit}
            handleDelete={handleDeleteModal}
            hideDelete={
              !hasAccess(AccessActions.CanDeleteSpecificWorkOrder, user)
            }
          />
        ),
        width: 100,
        pinned: 'right',
        filter: false,
      });
    }

    return WOColumns;
  }, [user]);

  const breadcrumbData = [
    {
      name: 'Production hub',
      link: '/production',
    },
  ];

  return (
    <Layout>
      {editWO && selectedWO && (
        <Dialog
          open={editWO}
          onOpenChange={() => {
            setEditWO(false);
          }}
        >
          <AddWOModal
            edit={true}
            orderData={selectedWO}
            reFetch={reFetch}
            setOpenEdit={setEditWO}
          />
        </Dialog>
      )}
      {showDeleteModal && (
        <Dialog
          open={showDeleteModal}
          onOpenChange={() => setShowDeleteModal(false)}
        >
          <DeleteModal
            title={`Delete work order`}
            infoText={`Are you sure you want to delete this work order?`}
            btnText={'Delete'}
            onClick={handleDelete}
            btnLoading={deleteLoading}
          >
            <div className="p-2 border flex flex-col gap-4 border-white-300 bg-white-100 px-2.5 py-2 rounded-lg">
              <div className="flex justify-between items-center">
                <div className="text-sm font-medium leading-5 text-grey-300">
                  Work order no.
                </div>
                <div className="text-base font-medium leading-6 text-dark-300">
                  {selectedWO?.order_no || '-'}
                </div>
              </div>
              <div className="flex justify-between items-center">
                <div className="text-sm font-medium leading-5 text-grey-300">
                  Product
                </div>
                <div className="text-base font-medium leading-6 text-dark-300">
                  {selectedWO?.name
                    ? truncateWithEllipsis(selectedWO?.name as string, 30)
                    : '-'}
                </div>
              </div>
            </div>
          </DeleteModal>
        </Dialog>
      )}

      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Production hub
          </div>
        </div>
      </div>
      <div className="mb-5 relative">
        <CommonTable
          data={data}
          columnDefs={getWOColumns()}
          handleRowClick={(e) => {
            router.push(`production/${e.data?.id}`);
          }}
          isLoading={isLoading}
        />
        {hasAccess(AccessActions.CanAddOrEditWorkOrders, user) && (
          <div className="flex gap-4 absolute top-0 right-0">
            <Dialog open={createWO} onOpenChange={setCreateWO}>
              <DialogTrigger asChild>
                <PrimaryButton
                  text="Add work order"
                  buttonClasses="!px-5 !py-2"
                  icon={<PlusIcon color="white" />}
                />
              </DialogTrigger>
              <DialogContent>
                <AddWOModal
                  edit={false}
                  reFetch={reFetch}
                  setOpenEdit={setCreateWO}
                />
              </DialogContent>
            </Dialog>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ProductionHub;
