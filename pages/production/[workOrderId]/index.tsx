// import { useParams } from "react-router-dom";

import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import Breadcrumb from '@/components/common/breadcrumb';
import Loader from '@/components/common/loader';
import Layout from '@/components/common/sidebar/layout';
import Status from '@/components/common/status';
import Tabs from '@/components/common/tabs';
import WOInfoTabContent from '@/components/production/infoTab';
import WOProcessTab from '@/components/production/processTab/wOprocessTab';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { TWorkOrder } from '@/interfaces/production';

export const WorkOrderStatus = {
  Draft: 'Upcoming',
  'In progress': 'In progress',
  Completed: 'Completed',
};

const WorkOrderView = () => {
  const router = useRouter();
  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);

  const { data, reFetch, isLoading } = useFetch<{
    record: TWorkOrder;
  }>(
    accessToken,
    router.query.workOrderId
      ? `production/work-orders/${router.query.workOrderId}`
      : undefined,
  );

  const [activeTab, setActiveTab] = useState<number>(0);

  const breadcrumbData = [
    {
      name: 'Production hub',
      link: '/production',
    },
    {
      name: 'Work order',
      link: '#',
    },
  ];

  const tabsData = [
    { name: 'Info', textColor: 'text-dark-100' },
    { name: 'Process', textColor: 'text-dark-100' },
  ];

  useEffect(() => {
    if (router.query.tab) {
      if (router.query.tab === 'info') {
        setActiveTab(0);
      } else if (router.query.tab === 'process') {
        setActiveTab(1);
      } else {
        setActiveTab(0);
      }
    }
  }, [router.query.tab]);

  return (
    <>
      <Layout>
        {!accessToken || isLoading ? (
          <Loader className="h-[600px]" />
        ) : (
          <div className="flex flex-col flex-1">
            <div className=" my-5">
              <div className="relative">
                <Breadcrumb data={breadcrumbData} />
                <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5 relative">
                  {data?.record.name}{' '}
                  {data?.record.status && (
                    <Status type={data?.record.status.toLowerCase()} />
                  )}
                </div>
              </div>
              <div className="mt-4 ">
                <Tabs
                  tabsData={tabsData}
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                />
              </div>
            </div>
            <div className=" flex-1">
              {activeTab === 0 && data?.record ? (
                <WOInfoTabContent data={data?.record} reFetch={reFetch} />
              ) : activeTab === 1 ? (
                <>
                  <WOProcessTab data={data?.record} />
                </>
              ) : (
                ''
              )}
            </div>
          </div>
        )}
      </Layout>
    </>
  );
};

export default WorkOrderView;
