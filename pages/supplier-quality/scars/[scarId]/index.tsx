import axios from 'axios';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useMemo, useState } from 'react';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import Layout from '@/components/common/sidebar/layout';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { formatDate } from '@/utils/time';

 type SCARDetail = {
  id: string;
  title: string;
  description?: string;
  category?: string;
  severity: 'High' | 'Medium' | 'Low';
  status: 'Draft' | 'Buyer Review' | 'Supplier Response' | 'Implementation' | 'Verification' | 'Closed';
  root_cause?: string;
  raised_date?: string;
  target_close_date?: string | null;
  supplier_id?: string;
  nc?: string | null;
  created_on: string;
};

const statusOptions = [
  'Draft',
  'Buyer Review',
  'Supplier Response',
  'Implementation',
  'Verification',
  'Closed',
] as const;

const severityOptions = ['High', 'Medium', 'Low'] as const;

const SCARDetailPage = () => {
  const router = useRouter();
  const { scarId } = router.query as { scarId?: string };
  const accessToken = useAuthStore((s) => s.accessToken);

  const { data, isLoading, reFetch } = useFetch<SCARDetail>(
    accessToken,
    scarId ? `supplier-quality/scars/${scarId}` : undefined,
  );

  const rec = data;

  const [form, setForm] = useState({
    status: undefined as SCARDetail['status'] | undefined,
    severity: undefined as SCARDetail['severity'] | undefined,
    target_close_date: undefined as string | undefined,
    root_cause: undefined as string | undefined,
  });

  const initialised = useMemo(() => !!rec, [rec]);

  React.useEffect(() => {
    if (rec) {
      setForm({
        status: rec.status,
        severity: rec.severity,
        target_close_date: rec.target_close_date || undefined,
        root_cause: rec.root_cause || undefined,
      });
    }
  }, [rec]);

  const updateSCAR = async () => {
    if (!scarId) return;
    try {
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const mockUser = typeof window !== 'undefined' ? localStorage.getItem('x-mock-user') : null;
      const orgId = typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;

      const payload: any = {};
      if (form.status && form.status !== rec?.status) payload.status = form.status;
      if (form.severity && form.severity !== rec?.severity) payload.severity = form.severity;
      if (form.target_close_date !== undefined && form.target_close_date !== (rec?.target_close_date || undefined)) payload.target_close_date = form.target_close_date || null;
      if (form.root_cause !== undefined && form.root_cause !== (rec?.root_cause || undefined)) payload.root_cause = form.root_cause || null;

      const res = await axios.request({
        method: 'PUT',
        url: `${baseUrl}/${productVersion}/supplier-quality/scars/${scarId}`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          ...(mockUser ? { 'x-mock-user': mockUser } : {}),
          ...(orgId ? { 'x-org': orgId } : {}),
        },
        data: payload,
      });
      if (res.status === 200) {
        toast.success('SCAR updated');
        reFetch();
      }
    } catch (err: any) {
      toast.error(err?.response?.data?.detail || 'Failed to update SCAR');
    }
  };

  return (
    <Layout>
      <div className="flex flex-col flex-1 my-5">
        <div className="mb-6 flex items-center gap-4">
          <SecondaryButton
            icon={<ArrowLeft />}
            onClick={() => router.push('/supplier-quality/scars')}
            text=""
            size="medium"
            buttonClasses="!px-2.5"
          />
          <h1 className="text-2xl font-semibold text-dark-300">SCAR Detail</h1>
        </div>

        {!rec ? (
          <div className="text-gray-500">{isLoading ? 'Loading...' : 'Not found'}</div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-6">
              <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                <h2 className="text-lg font-semibold mb-4">Basic Information</h2>
                <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Title</label>
                    <p className="text-sm text-gray-900">{rec.title}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Raised Date</label>
                    <p className="text-sm text-gray-900">{rec.raised_date ? formatDate(rec.raised_date) : '-'}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Severity</label>
                    <p className="text-sm text-gray-900">{rec.severity}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status</label>
                    <p className="text-sm text-gray-900">{rec.status}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">NC Link</label>
                    <p className="text-sm text-primary">
                      {rec.nc ? (
                        <button
                          className="underline"
                          onClick={() => router.push(`/supplier-quality/${rec.nc}`)}
                        >
                          View NC
                        </button>
                      ) : (
                        '-'
                      )}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                <h2 className="text-lg font-semibold mb-4">Description</h2>
                <p className="text-gray-700 whitespace-pre-wrap">{rec.description || '-'}</p>
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-white rounded-lg border border-gray-200 p-6">
                <h3 className="font-semibold mb-3">Update</h3>

                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Status</label>
                    <select
                      className="mt-1 w-full border rounded px-3 py-2"
                      value={form.status || ''}
                      onChange={(e) => setForm((f) => ({ ...f, status: e.target.value as SCARDetail['status'] }))}
                    >
                      {statusOptions.map((s) => (
                        <option key={s} value={s}>
                          {s}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">Severity</label>
                    <select
                      className="mt-1 w-full border rounded px-3 py-2"
                      value={form.severity || ''}
                      onChange={(e) => setForm((f) => ({ ...f, severity: e.target.value as SCARDetail['severity'] }))}
                    >
                      {severityOptions.map((s) => (
                        <option key={s} value={s}>
                          {s}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">Target Close Date</label>
                    <input
                      type="date"
                      className="mt-1 w-full border rounded px-3 py-2"
                      value={form.target_close_date || ''}
                      onChange={(e) => setForm((f) => ({ ...f, target_close_date: e.target.value }))}
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">Root Cause</label>
                    <textarea
                      className="mt-1 w-full border rounded px-3 py-2"
                      rows={4}
                      value={form.root_cause || ''}
                      onChange={(e) => setForm((f) => ({ ...f, root_cause: e.target.value }))}
                    />
                  </div>

                  <PrimaryButton text="Save Changes" onClick={updateSCAR} />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default SCARDetailPage;

