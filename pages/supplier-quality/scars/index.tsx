import { useRouter } from 'next/router';
import React from 'react';

import Breadcrumb from '@/components/common/breadcrumb';
import Layout from '@/components/common/sidebar/layout';
import CommonTable from '@/components/common/table';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { getValueOrDefault } from '@/utils/general';
import { formatDate } from '@/utils/time';

type SCARRow = {
  id: string;
  title: string;
  severity: string; // High | Medium | Low
  status: string; // Draft | Buyer Review | ...
  raised_date?: string;
};

const SCARListPage = () => {
  const accessToken = useAuthStore((s) => s.accessToken);
  const router = useRouter();

  const breadcrumbData = [
    {
      name: 'SCAR Management',
      link: '#',
    },
  ];

  const { data, isLoading } = useFetch<{ records: SCARRow[] }>(
    accessToken,
    'supplier-quality/scars',
  );

  const columnDefs = [
    {
      headerName: 'Title',
      field: 'title',
      cellRenderer: (params: any) => (
        <button
          className="text-primary hover:underline"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            router.push(`/supplier-quality/scars/${params.data.id}`);
          }}
        >
          {getValueOrDefault(params.data, 'title')}
        </button>
      ),
      sortable: true,
      resizable: true,
      minWidth: 220,
      flex: 2,
    },
    {
      headerName: 'Status',
      field: 'status',
      cellRenderer: (p: any) => getValueOrDefault(p.data, 'status'),
      sortable: true,
      resizable: true,
      minWidth: 160,
    },
    {
      headerName: 'Severity',
      field: 'severity',
      cellRenderer: (p: any) => getValueOrDefault(p.data, 'severity'),
      sortable: true,
      resizable: true,
      minWidth: 140,
    },
    {
      headerName: 'Raised Date',
      field: 'raised_date',
      cellRenderer: (p: any) =>
        p.data.raised_date ? formatDate(p.data.raised_date) : '-',
      sortable: true,
      resizable: true,
      minWidth: 160,
    },
  ];

  return (
    <div>
      <Layout>
        <div className="my-5">
          <div className="flex flex-col">
            <Breadcrumb data={breadcrumbData} />
            <div className="text-dark-300 font-semibold text-3xl leading-10">
              SCARs
            </div>
          </div>
          <div className="mt-5 mb-5">
            <CommonTable
              data={{
                records: data?.records || [],
                total: data?.records?.length || 0,
              }}
              columnDefs={columnDefs as any}
              isLoading={isLoading}
            />
          </div>
        </div>
      </Layout>
    </div>
  );
};

export default SCARListPage;
