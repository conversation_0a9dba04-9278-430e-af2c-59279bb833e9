import { useRouter } from 'next/router';
import React, { useMemo } from 'react';

import Breadcrumb from '@/components/common/breadcrumb';
import Loader from '@/components/common/loader';
import Layout from '@/components/common/sidebar/layout';
import CreateNCFormEnhanced, {
  FormData,
} from '@/components/supplier/nc_scar/CreateNCFormEnhanced';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';

const EditNCPage = () => {
  const router = useRouter();
  const { ncId } = router.query;
  const accessToken = useAuthStore((s) => s.accessToken);

  const { data, isLoading } = useFetch<any>(
    accessToken,
    ncId ? `supplier-quality/non-conformances/${ncId}` : undefined,
  );
  const rec = data?.record;

  const breadcrumbData = [
    { name: 'Home', link: '/' },
    { name: 'Supplier Quality', link: '/supplier-quality' },
    {
      name: 'Non-Conformances',
      link: '/supplier-quality/supplier-non-conformance',
    },
    { name: 'Edit', link: '#' },
  ];

  const initialData: Partial<FormData> = useMemo(() => {
    if (!rec) return {};
    const toDateInput = (d?: string) => (d ? d.slice(0, 10) : '');
    return {
      nc_title: rec?.title || '',
      supplier: rec?.supplier_id ? String(rec.supplier_id) : '',
      person_responsible: rec?.responsible_person || {},
      issue_description: rec?.issue_description || '',
      severity: (rec?.severity as any) || 'Minor',
      repeat_count: rec?.repeat_count ?? 1,
      nc_type: rec?.type || '',
      categories: rec?.categories || [],
      // Product
      part_number: rec?.part_number || '',
      lot_number: rec?.lot_number || '',
      batch_id: rec?.batch_id || '',
      // Service
      service_category: rec?.service_category || '',
      // Process
      process_name: rec?.process_step || '',
      process_stage: rec?.process_stage || '',
      // Documentation
      document_id: rec?.document_type || '',
      revision_number: rec?.revision_number || '',
      // Delivery
      delivery_date: rec?.type === 'Delivery' ? toDateInput(rec?.due_date) : '',
      po_number: rec?.delivery_reference || '',
      // System
      system_module_name: rec?.system_area || '',
      impacted_users: rec?.impacted_users || '',
      // Other
      type_description: rec?.type_description || '',
      other_information: rec?.other_information || '',
      // Additional
      additional_notes: rec?.additional_notes || '',
      due_date: rec?.type === 'Delivery' ? '' : toDateInput(rec?.due_date),

      custom_fields: rec?.custom_fields || {},
    };
  }, [rec]);

  return (
    <div>
      <Layout>
        <div className="my-5">
          <div className="flex flex-col">
            <Breadcrumb data={breadcrumbData} />
            {/* <div className="text-dark-300 font-semibold text-3xl leading-10">
              Edit Non-Conformance
            </div> */}
          </div>
          <div className="mt-5">
            {isLoading ? (
              <Loader />
            ) : (
              <CreateNCFormEnhanced
                isEdit
                initialData={initialData}
                ncId={ncId as string}
              />
            )}
          </div>
        </div>
      </Layout>
    </div>
  );
};

export default EditNCPage;
