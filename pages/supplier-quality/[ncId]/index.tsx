import axios from 'axios';
import {
  AlertTriangle,
  Edit,
  ExternalLink,
  Eye,
  FileText,
  Link2,
  Pencil,
  Trash2,
  Upload,
  Clock,
  Users,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import React, { use, useState } from 'react';
import { toast } from 'react-toastify';

import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import Layout from '@/components/common/sidebar/layout';
import Status from '@/components/common/status';
import Tabs from '@/components/common/tabs';
import { Textarea } from '@/components/common/textarea';
import AddInvestigatorModal from '@/components/supplier/modals/addInvestigatorModal';
import CloseNcModal from '@/components/supplier/modals/closeNcModal';
import EscalateToScarModal from '@/components/supplier/modals/escalateToScarModal';
import LinkExistingScarModal from '@/components/supplier/modals/linkExistingScarModal';
import RaiseInternalCapaModal from '@/components/supplier/modals/raiseInternalCapaModal';
import UploadComponent from '@/components/common/uploadComponent';
import * as role from '@/constants/role';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { IAttachment } from '@/interfaces/misc';
import { formatDate } from '@/utils/time';
import ConfirmModal from '@/components/common/modals/confirmModal';
import AddResponseModal from '@/components/document/components/modals/addResponseModal';
import moment from 'moment';
import DocumentUploadModal from '@/components/modals/evidenceUploadModal';
import { DetailsTextNew } from '@/components/common/infoDetail';
import ReactMention from '@/components/react-mention';
import LinkButton from '@/components/common/button/linkButton';
import DocumentIcon from '@/assets/outline/document';
import DocumentViewModal from '@/components/common/modals/documentViewModal';
import EvidenceUploadModal from '@/components/modals/evidenceUploadModal';
import { usePost } from '@/hooks/usePost';
import Image from 'next/image';
import LogsCard from '@/components/document/components/logsCard';
import ZeroStateLog from '@/assets/document/zeroStateLog.svg';

const NCDetailPage = () => {
  const router = useRouter();
  const accessToken = useAuthStore((s) => s.accessToken);
  const { ncId } = router.query as { ncId?: string };
  const [addedFiles, setAddedFiles] = React.useState<IAttachment[]>([]);
  const [activeTab, setActiveTab] = React.useState<number>(0);
  const [activeLogTab, setActiveLogTab] = React.useState<number>(0);

  // Modal states
  const [showEscalateModal, setShowEscalateModal] = React.useState(false);
  const [showLinkModal, setShowLinkModal] = React.useState(false);
  const [showCapaModal, setShowCapaModal] = React.useState(false);
  const [showCloseModal, setShowCloseModal] = React.useState(false);
  const [showInvestigatorModal, setShowInvestigatorModal] =
    React.useState(false);
  const [showUploadDocumentModal, setShowUploadDocumentModal] =
    React.useState(false);
  // Cancel / Archive modal
  const [showCancelModal, setShowCancelModal] = React.useState(false);
  const [cancelAction, setCancelAction] = React.useState('');
  const [cancelReason, setCancelReason] = React.useState('');
  const [cancelActionError, setCancelActionError] = React.useState('');
  const [cancelReasonError, setCancelReasonError] = React.useState('');

  // Role-based visibility
  const currentUser = useAuthStore((s) => s.user);
  const userRoles = currentUser?.roles || [];
  const canManageNC = userRoles.some((r) =>
    [
      role.SUPER_ADMIN,
      role.ADMIN,
      role.SUPPLIER_HUB_ADMIN,
      role.SUPPLIER_HUB_EDITOR,
    ].includes(r),
  );
  const canBuyerEscalate = canManageNC;

  // Status modal and loading flags
  const [showStatusModal, setShowStatusModal] = React.useState(false);
  const [nextStatus, setNextStatus] = React.useState<
    'Open' | 'InReview' | null
  >(null);
  const [statusReason, setStatusReason] = React.useState('');
  const [openResponse, setOpenResponse] = useState(false);

  const [cancelLoading, setCancelLoading] = React.useState(false);
  const [statusLoading, setStatusLoading] = React.useState(false);
  const [comment, setComment] = React.useState('');
  const [editNotes, setEditNotes] = React.useState(false);

  const [selectedDocument, setSelectedDocument] = useState(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const {
    postData: createCapa,
    response: createCapaResponse,
    isLoading: createCapaLoading,
  } = usePost();

  const handleViewDocument = (
    file: string,
    fileName: string,
    fileExtension: string,
  ) => {
    setSelectedDocument({
      title: fileName,
      filePath: file.file_path,
      extension: fileExtension,
    });
    setIsDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsDialogOpen(false);
    setSelectedDocument(null);
  };

  const [selectedEvidence, setSelectedEvidence] = useState(null);
  const [isEvidenceDialogOpen, setIsEvidenceDialogOpen] = useState(false);

  const handleViewEvidence = (
    file: string,
    fileName: string,
    fileExtension: string,
  ) => {
    setSelectedEvidence({
      title: fileName,
      filePath: file.file_path,
      extension: fileExtension,
    });
    setIsEvidenceDialogOpen(true);
  };

  const activities = [
    {
      id: 1,
      type: 'NC Created',
      date: '05 Jan 2025',
      icon: FileText,
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-600',
      user: {
        initials: 'JS',
        name: 'John Smith',
        role: 'QA',
        roleColor: 'bg-blue-100 text-blue-700',
      },
      description:
        'Non-conformance reported for defective parts in batch LOT-2025-001',
    },
    {
      id: 2,
      type: 'Investigator Assigned',
      date: '05 Jan 2025',
      icon: Users,
      iconBg: 'bg-purple-100',
      iconColor: 'text-purple-600',
      user: {
        initials: 'SW',
        name: 'Sarah Wilson',
        role: 'Quality Manager',
        roleColor: 'bg-orange-100 text-orange-700',
      },
      description: 'Mike Johnson assigned as primary investigator',
      reason: 'Engineering expertise required for root cause analysis',
    },
    {
      id: 3,
      type: 'Evidence Uploaded',
      date: '06 Jan 2025',
      icon: Upload,
      iconBg: 'bg-green-100',
      iconColor: 'text-green-600',
      user: {
        initials: 'MJ',
        name: 'Mike Johnson',
        role: 'Engineer',
        roleColor: 'bg-purple-100 text-purple-700',
      },
      description: 'Investigation report and supplier communication uploaded',
    },
    {
      id: 4,
      type: 'Escalated to SCAR',
      date: '07 Jan 2025',
      icon: AlertTriangle,
      iconBg: 'bg-orange-100',
      iconColor: 'text-orange-600',
      user: {
        initials: 'SW',
        name: 'Sarah Wilson',
        role: 'Quality Manager',
        roleColor: 'bg-orange-100 text-orange-700',
      },
      description: 'NC escalated due to critical severity and supplier impact',
      reason: 'Multiple suppliers affected, systematic issue identified',
    },
  ];

  const { data, isLoading, reFetch } = useFetch<{
    record: {
      id: string;
      nc_number: string;
      title: string;
      supplier_name: string;
      supplier_id?: string;
      created_by?: { full_name?: string };
      issue_description: string;
      status: string;
      severity: string;
      type?: string;
      repeat_count: number;
      date_reported?: string;
      due_date?: string;
      // type-specific optional fields
      lot_number?: string;
      part_number?: string;
      batch_id?: string;
      process_step?: string;
      process_stage?: string;
      document_type?: string;
      revision_number?: string;
      delivery_reference?: string;
      service_category?: string;
      system_area?: string;
      impacted_users?: string;
      type_description?: string;
      other_information?: string;
      scar_id?: string;
    };
    linked_scars: Array<{ id: string; title?: string; status: string }>;
    linked_capas?: Array<{ id: string; capa_id: string; status: string }>;
    audit_logs: Array<{
      id: string;
      description?: string;
      created_on?: string;
    }>;
    evidence_files?: Array<{ file_path: string; file_extension?: string }>;
  }>(accessToken, `supplier-quality/non-conformances/${ncId}`);

  // Logs tabs data and SCAR logs fetch (like Document Hub tabs)
  const firstScarId = data?.linked_scars?.[0]?.id;
  const scarLogsEndpoint =
    activeLogTab === 1 && firstScarId
      ? `supplier-quality/scars/${firstScarId}/logs`
      : undefined;

  const { data: scarLogs, isLoading: scarLogsLoading } = useFetch<{
    records: Array<{ id: string; description?: string; created_on?: string }>;
  }>(accessToken, scarLogsEndpoint);

  const tabsData = [
    { name: 'Details & Evidence', textColor: 'text-dark-100' },
    { name: 'Activity Timeline', textColor: 'text-dark-100' },
  ];

  const logTabsData = [
    {
      name: `NC Log (${data?.audit_logs?.length || 0})`,
      textColor: 'text-dark-100',
    },
    {
      name: `SCAR Log (${scarLogs?.records?.length || 0})`,
      textColor: 'text-dark-100',
    },
  ];

  const breadcrumbData = [
    { name: 'Home', link: '/' },
    { name: 'Supplier Quality', link: '/supplier-quality' },
    {
      name: 'Non-Conformances',
      link: '/supplier-quality/supplier-non-conformance',
    },
    { name: 'NC Detail', link: '#' },
  ];

  const doUploadEvidence = async () => {
    if (!ncId) return;
    if (!addedFiles.length) {
      toast.error('Please upload files first');
      return;
    }
    try {
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const mockUser =
        typeof window !== 'undefined'
          ? localStorage.getItem('x-mock-user')
          : null;
      const orgId =
        typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;
      const res = await axios.request({
        method: 'POST',
        url: `${baseUrl}/${productVersion}/supplier-quality/non-conformances/${ncId}/evidence`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          ...(mockUser ? { 'x-mock-user': mockUser } : {}),
          ...(orgId ? { 'x-org': orgId } : {}),
        },
        data: { files: addedFiles },
      });
      if (res.status === 200) {
        toast.success('Evidence added');
        setAddedFiles([]);
        reFetch();
      }
    } catch (err: any) {
      toast.error(err?.response?.data?.detail || 'Failed to add evidence');
    }
  };

  const handleConfirmCancelArchive = async () => {
    if (!ncId) return;
    let hasErr = false;
    if (!['Cancelled', 'Archived', 'Withdrawn'].includes(cancelAction)) {
      setCancelActionError('Please select a valid action');
      hasErr = true;
    } else {
      setCancelActionError('');
    }
    if (!cancelReason.trim()) {
      setCancelReasonError('Reason is required');
      hasErr = true;
    } else {
      setCancelReasonError('');
    }
    if (hasErr) return;
    setCancelLoading(true);
    try {
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const mockUser =
        typeof window !== 'undefined'
          ? localStorage.getItem('x-mock-user')
          : null;
      const orgId =
        typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;

      const res = await axios.request({
        method: 'POST',
        url: `${baseUrl}/${productVersion}/supplier-quality/non-conformances/${ncId}/cancel`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          ...(mockUser ? { 'x-mock-user': mockUser } : {}),
          ...(orgId ? { 'x-org': orgId } : {}),
        },
        data: { action: cancelAction, reason: cancelReason },
      });
      if (res.status === 200) {
        toast.success(`${cancelAction} successfully`);
        setShowCancelModal(false);
        reFetch();
      }
    } catch (err: any) {
      toast.error(err?.response?.data?.detail || 'Failed to update status');
    } finally {
      setCancelLoading(false);
    }
  };

  const doSetStatus = async (statusVal: 'Open' | 'InReview') => {
    setNextStatus(statusVal);
    setStatusReason('');
    setShowStatusModal(true);
  };

  const handleConfirmSetStatus = async () => {
    if (!ncId || !nextStatus) return;
    setStatusLoading(true);
    try {
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const mockUser =
        typeof window !== 'undefined'
          ? localStorage.getItem('x-mock-user')
          : null;
      const orgId =
        typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;
      const res = await axios.request({
        method: 'POST',
        url: `${baseUrl}/${productVersion}/supplier-quality/non-conformances/${ncId}/status`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          ...(mockUser ? { 'x-mock-user': mockUser } : {}),
          ...(orgId ? { 'x-org': orgId } : {}),
        },
        data: { status: nextStatus, reason: statusReason },
      });
      if (res.status === 200) {
        toast.success(`Status updated to ${nextStatus}`);
        setShowStatusModal(false);
        setNextStatus(null);
        setStatusReason('');
        reFetch();
      }
    } catch (err: any) {
      toast.error(err?.response?.data?.detail || 'Failed to update status');
    } finally {
      setStatusLoading(false);
    }
  };

  const handleCreateCapa = () => {
    const body = {
      description: rec?.issue_description,
      audit_info: {
        nc_id: ncId,
      },
    };

    async function fetch() {
      await createCapa(accessToken as string, `capas`, body);
    }
    fetch();
  };

  const rec = data?.record;
  const logs = data?.audit_logs;
  const evidence_files = data?.evidence_files;
  const linked_scars = data?.linked_scars;
  const linked_capas = data?.linked_capas;

  return (
    <Layout>
      <div className="flex flex-1 gap-6">
        {/* Main Content */}
        <div className="flex flex-col flex-1">
          <div className="my-5">
            <div className="mb-6">
              <Breadcrumb data={breadcrumbData} />
              <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5">
                {rec?.title}
                <Status type={rec?.status?.toLowerCase() as string} />
              </div>

              <div className="text-dark-100 font-medium text-lg flex items-center gap-2 mt-1">
                <p>{rec?.supplier_name}</p>
              </div>
            </div>
            <div className="mt-4 flex justify-between items-center">
              <Tabs
                tabsData={tabsData}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
              />
              {/* <div className="flex gap-2">
                <PrimaryButton
                  text="Edit"
                  icon={<Edit className="h-4 w-4" />}
                  onClick={() => router.push(`/supplier-quality/${ncId}/edit`)}
                  size="medium"
                />
              </div> */}
            </div>
          </div>
          <div className="flex flex-1 gap-6">
            <div className="flex-1">
              {activeTab === 0 ? (
                <div>
                  {/* Basic Information Grid */}
                  <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                    <div className="grid grid-cols-4 gap-x-8 gap-y-4">
                      <DetailsTextNew
                        label="NC Number"
                        value={rec?.nc_number || '-'}
                      />

                      <DetailsTextNew
                        label="Detected On"
                        value={formatDate(rec?.date_reported) || '-'}
                      />

                      <DetailsTextNew
                        label="Due Date"
                        value={formatDate(rec?.due_date) || '-'}
                      />

                      <DetailsTextNew
                        label="Repeat Count"
                        value={(rec?.repeat_count ?? 0).toString()}
                      />

                      <DetailsTextNew
                        label="Created By"
                        value={
                          rec?.created_by?.first_name +
                            ' ' +
                            rec?.created_by?.last_name || '-'
                        }
                      />
                      <DetailsTextNew
                        label="Severity"
                        value={rec?.severity || '-'}
                      />

                      <DetailsTextNew
                        label="Assigned To"
                        value={
                          rec?.responsible_person?.first_name +
                            ' ' +
                            rec?.responsible_person?.last_name || '-'
                        }
                      />
                      <DetailsTextNew
                        label="Investigators"
                        value={rec?.investigators || '-'}
                      />

                      <DetailsTextNew
                        label="Categories"
                        value={
                          rec?.categories.length && rec?.categories.length > 0
                            ? rec?.categories
                            : '-'
                        }
                        multiValue={
                          rec?.categories?.length && rec?.categories?.length > 0
                            ? true
                            : false
                        }
                      />

                      <DetailsTextNew
                        label="Department"
                        value={rec?.investigators || '-'}
                      />
                    </div>
                  </div>

                  {rec?.custom_fields && (
                    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                      <h2 className="text-lg font-semibold mb-4">
                        Custom Fields
                      </h2>
                      {rec?.custom_fields &&
                        Object.keys(rec?.custom_fields).length > 0 && (
                          <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                            {Object.entries(rec.custom_fields).map(
                              ([key, value]) => (
                                <DetailsTextNew
                                  key={key}
                                  label={key}
                                  value={value || '-'}
                                />
                              ),
                            )}
                          </div>
                        )}
                    </div>
                  )}

                  {/* Additional Fields (by NC Type) */}
                  <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                    <h2 className="text-lg font-semibold mb-4">
                      Additional Fields
                    </h2>

                    {/* Product */}

                    <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                      {rec?.part_number && (
                        <DetailsTextNew
                          label="Part Number"
                          value={rec?.part_number || '-'}
                        />
                      )}

                      {rec?.lot_number && (
                        <DetailsTextNew
                          label="Lot Number"
                          value={rec?.lot_number || '-'}
                        />
                      )}

                      {rec?.batch_id && (
                        <DetailsTextNew
                          label="Batch ID"
                          value={rec?.batch_id || '-'}
                        />
                      )}
                    </div>

                    {/* Service */}

                    {rec?.service_category && (
                      <DetailsTextNew
                        label="Service Category"
                        value={rec?.service_category || '-'}
                      />
                    )}

                    {/* Process */}

                    <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                      {rec?.process_step && (
                        <DetailsTextNew
                          label="Process Name"
                          value={rec?.process_step || '-'}
                        />
                      )}

                      {rec?.process_stage && (
                        <DetailsTextNew
                          label="Process Stage"
                          value={rec?.process_stage || '-'}
                        />
                      )}
                    </div>

                    {/* Documentation */}
                    <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                      {rec?.document_type && (
                        <DetailsTextNew
                          label="Document ID"
                          value={rec?.document_type || '-'}
                        />
                      )}

                      {rec?.revision_number && (
                        <DetailsTextNew
                          label="Revision Number"
                          value={rec?.revision_number || '-'}
                        />
                      )}
                    </div>

                    {/* Delivery */}

                    {rec?.delivery_reference && (
                      <DetailsTextNew
                        label="PO Number"
                        value={rec?.delivery_reference || '-'}
                      />
                    )}

                    {/* System */}
                    <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                      {rec?.system_area && (
                        <DetailsTextNew
                          label="System/Module Name"
                          value={rec?.system_area || '-'}
                        />
                      )}

                      {rec?.impacted_users && (
                        <DetailsTextNew
                          label="Impacted Users"
                          value={rec?.impacted_users || '-'}
                        />
                      )}
                    </div>

                    {/* Other */}
                    <div className="grid grid-cols-2 gap-x-8 gap-y-4">
                      {rec?.type_description && (
                        <DetailsTextNew
                          label=" Type Description"
                          value={rec?.type_description || '-'}
                        />
                      )}

                      {rec?.other_information && (
                        <DetailsTextNew
                          label="Other Information"
                          value={rec?.other_information || '-'}
                        />
                      )}
                    </div>
                  </div>

                  {/* Notes */}
                  <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                    <div className="flex justify-between items-center mb-2">
                      <h2 className="text-lg font-semibold mb-4">Notes</h2>
                      <SecondaryButton
                        text="Edit"
                        onClick={() => setEditNotes(true)}
                        size="small"
                        icon={<Edit className="h-4 w-4" />}
                      />
                    </div>
                    <Textarea
                      value={rec?.issue_description || ''}
                      onChange={(e) => setComment(e.target.value)}
                      placeholder="Notes"
                      disabled={!editNotes}
                    />
                    {editNotes && (
                      <div className="flex justify-end mt-5 gap-4">
                        <LinkButton
                          text="Cancel"
                          onClick={() => setEditNotes(false)}
                          size="medium"
                        />
                        <PrimaryButton
                          size="medium"
                          text="Update"
                          isLoading={false}
                          onClick={() => console.log('test')}
                          disabled={comment === ''}
                        />
                      </div>
                    )}
                  </div>

                  {/* Attachments */}
                  <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-semibold">
                        Supporting Documents (
                        {rec?.supporting_documents?.length || 0})
                      </h2>
                    </div>

                    <div className="space-y-3">
                      {rec?.supporting_documents?.length === 0 && (
                        <p className="text-gray-500">No attachments found</p>
                      )}

                      {(rec?.supporting_documents || []).map((f, idx) => {
                        const fileName =
                          f.file_name ||
                          f.file_path?.split('/').pop() ||
                          'Unnamed file';
                        const fileExtension = fileName
                          .split('.')
                          .pop()
                          ?.toLowerCase();
                        const uploadedDate = new Date(
                          f.uploaded_at,
                        ).toLocaleDateString();

                        const viewableExtensions = [
                          'pdf',
                          'docx',
                          'doc',
                          'jpg',
                          'png',
                          'jpeg',
                        ];
                        const isViewable =
                          viewableExtensions.includes(fileExtension);

                        return (
                          <div
                            key={idx}
                            className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                          >
                            {/* Left side: file icon, name, extension, date */}
                            <div className="flex items-center gap-3">
                              <FileText className="h-5 w-5 text-primary-500" />
                              <div className="min-w-0">
                                <p className="font-medium text-gray-900 break-all">
                                  {fileName}{' '}
                                  <span className="text-sm text-gray-500">
                                    ({fileExtension})
                                  </span>
                                </p>
                                <p className="text-xs text-gray-500">
                                  Uploaded: {uploadedDate}
                                </p>
                              </div>
                            </div>

                            {/* Right side: View and Delete icons */}
                            <div className="flex items-center gap-3 ml-4">
                              <button
                                type="button"
                                onClick={() =>
                                  handleViewDocument(f, fileName, fileExtension)
                                }
                                className={`${
                                  isViewable
                                    ? 'text-gray-600 hover:text-gray-800 cursor-pointer'
                                    : 'text-gray-300 cursor-not-allowed'
                                }`}
                                title="View"
                                disabled={!isViewable}
                              >
                                <Eye height={20} />
                              </button>

                              <button
                                onClick={() =>
                                  console.log('Delete file at index:', idx)
                                }
                                className="text-red-600 hover:text-red-800"
                                title="Delete"
                              >
                                <Trash2 height={20} />
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    {/* Dialog Modal - Only renders DocumentViewModal when open */}
                    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                      {isDialogOpen && selectedDocument && (
                        <DocumentViewModal
                          title={selectedDocument.title}
                          filePath={selectedDocument.filePath}
                          extension={selectedDocument.extension}
                          dialogClass="min-w-[95%]"
                        />
                      )}
                    </Dialog>
                  </div>

                  <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-lg font-semibold">
                        Evidence ({evidence_files?.length || 0})
                      </h2>
                      <SecondaryButton
                        text="Upload Evidence"
                        onClick={() => setShowUploadDocumentModal(true)}
                        size="medium"
                        icon={<Upload className="h-4 w-4" />}
                      />
                    </div>

                    <div className="space-y-3">
                      {evidence_files?.length === 0 && (
                        <p className="text-gray-500">No attachments found</p>
                      )}
                      {(evidence_files || []).map((f, idx) => {
                        const fileName =
                          f.file_name ||
                          f.file_path?.split('/').pop() ||
                          'Unnamed file';
                        const fileExtension = fileName
                          .split('.')
                          .pop()
                          ?.toLowerCase();
                        const uploadedDate = new Date(
                          f.uploaded_at,
                        ).toLocaleDateString();

                        const viewableExtensions = [
                          'pdf',
                          'docx',
                          'doc',
                          'jpg',
                          'png',
                          'jpeg',
                        ];
                        const isViewable =
                          viewableExtensions.includes(fileExtension);

                        return (
                          <div
                            key={idx}
                            className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                          >
                            {/* Left side: file icon, name, extension, date */}
                            <div className="flex items-center gap-3">
                              <FileText className="h-5 w-5 text-primary-500" />
                              <div className="min-w-0">
                                <p className="font-medium text-gray-900 break-all">
                                  {fileName}{' '}
                                  <span className="text-sm text-gray-500">
                                    ({fileExtension})
                                  </span>
                                </p>
                                <p className="text-xs text-gray-500">
                                  Uploaded: {uploadedDate}
                                </p>
                              </div>
                            </div>

                            {/* Right side: View and Delete icons */}
                            <div className="flex items-center gap-3 ml-4">
                              <button
                                type="button"
                                onClick={() =>
                                  handleViewEvidence(f, fileName, fileExtension)
                                }
                                className={`${
                                  isViewable
                                    ? 'text-gray-600 hover:text-gray-800 cursor-pointer'
                                    : 'text-gray-300 cursor-not-allowed'
                                }`}
                                title="View"
                                disabled={!isViewable}
                              >
                                <Eye height={20} />
                              </button>

                              <button
                                onClick={() =>
                                  console.log('Delete evidence at index:', idx)
                                }
                                className="text-red-600 hover:text-red-800"
                                title="Delete"
                              >
                                <Trash2 height={20} />
                              </button>
                            </div>
                          </div>
                        );
                      })}
                    </div>

                    {/* Dialog Modal - Only renders DocumentViewModal when open */}
                    <Dialog
                      open={isEvidenceDialogOpen}
                      onOpenChange={setIsEvidenceDialogOpen}
                    >
                      {isEvidenceDialogOpen && selectedEvidence && (
                        <DocumentViewModal
                          title={selectedEvidence.title}
                          filePath={selectedEvidence.filePath}
                          extension={selectedEvidence.extension}
                          dialogClass="min-w-[95%]"
                        />
                      )}
                    </Dialog>
                  </div>

                  <div className="p-6 border rounded-lg border-gray-200">
                    <div className="text-lg leading-5 font-medium text-black">
                      Comments
                    </div>

                    <div className="mt-4">
                      <ReactMention
                        comment={comment}
                        setComment={setComment}
                        // data={users?.records?.map((user) => ({
                        //   id: user.id,
                        //   display: user.full_name,
                        // }))}
                      />
                      <div className="flex justify-end mt-5">
                        <PrimaryButton
                          size="medium"
                          text="Comment"
                          isLoading={false}
                          onClick={() => console.log('test')}
                          disabled={comment === ''}
                        />
                      </div>
                    </div>

                    <div className="mt-5 min-h-64 bg-white-150 rounded-lg p-5">
                      {!rec?.documentComments?.length ? (
                        <div className="w-full h-24 bg-white-100 border border-white-300 rounded-lg p-4">
                          <p className="text-base text-dark-300 leading-6 font-medium">
                            No response has been added yet
                          </p>
                        </div>
                      ) : (
                        rec?.documentComments?.map((e, i) => (
                          <div
                            className="px-4 mb-2 py-4 border border-white-300 rounded-lg bg-white-100"
                            key={i}
                          >
                            <div className="flex mb-1 gap-2 items-center text-base leading-6 font-medium text-dark-300">
                              <div>{e.created_by.full_name}</div>
                              <div className="text-grey-300">
                                {e.created_by.role}
                              </div>
                            </div>
                            <div className="text-sm flex items-center mb-1 gap-2 leading-5 font-semibold text-grey-200">
                              <span>
                                {moment(e.created_on).format('hh:mm')},
                              </span>{' '}
                              <span>
                                {moment(e.created_on).format('MMM Do, YYYY')}
                              </span>
                            </div>
                            <p
                              onClick={(e) => e.preventDefault()}
                              dangerouslySetInnerHTML={{
                                __html: e?.description,
                              }}
                              className="text-base font-medium leading-6 text-dark-300"
                            />
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                </div>
              ) : activeTab === 1 ? (
                <div className="">
                  {/* <div className="border-b border-gray-200 mb-4">
                    <Tabs
                      tabsData={logTabsData}
                      activeTab={activeLogTab}
                      setActiveTab={setActiveLogTab}
                      tabGroupName="nc-logs"
                    />
                  </div> */}

                  <>
                    {logs?.length && logs?.length > 0 ? (
                      logs?.map((e, i) => <LogsCard logs={e} key={i} />)
                    ) : (
                      <>
                        <div className="w-full h-80 bg-white-100 border border-white-300 rounded-lg flex flex-col justify-center items-center p-4">
                          <div className="mb-5">
                            <Image src={ZeroStateLog} alt="" />
                          </div>
                          <p className="text-base text-dark-300 leading-6 font-medium">
                            No logs have been added yet
                          </p>
                        </div>
                      </>
                    )}
                  </>

                  {/* {activeLogTab === 0 ? (
                    <div className="space-y-3">
                      {(data?.audit_logs || []).length ? (
                        (data?.audit_logs || []).map((l) => (
                          <div
                            key={l.id}
                            className="border-l-2 border-primary-400 pl-3"
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium text-sm">
                                {l.description || '-'}
                              </span>
                              <span className="text-xs text-grey-300">
                                {l.created_on ? formatDate(l.created_on) : ''}
                              </span>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="w-full h-24 bg-gray-50 border border-gray-200 rounded-lg p-4">
                          <p className="text-base text-gray-600 leading-6 font-medium">
                            No logs have been added yet
                          </p>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {scarLogsLoading ? (
                        <div className="text-sm text-gray-500">
                          Loading SCAR logs...
                        </div>
                      ) : (scarLogs?.records || []).length ? (
                        (scarLogs?.records || []).map((l) => (
                          <div
                            key={l.id}
                            className="border-l-2 border-primary-400 pl-3"
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium text-sm">
                                {l.description || '-'}
                              </span>
                              <span className="text-xs text-gray-500">
                                {l.created_on ? formatDate(l.created_on) : ''}
                              </span>
                            </div>
                          </div>
                        ))
                      ) : (
                        <div className="w-full h-24 bg-gray-50 border border-gray-200 rounded-lg p-4">
                          <p className="text-base text-gray-600 leading-6 font-medium">
                            {firstScarId
                              ? 'No logs have been added yet'
                              : 'No linked SCAR found'}
                          </p>
                        </div>
                      )}
                    </div>
                  )} */}
                </div>
              ) : null}
            </div>
            {activeTab === 0 && (
              <div className="w-80 flex-shrink-0">
                <div className="sticky top-6 space-y-4">
                  {/* Linked Records Section */}
                  <div className="bg-white rounded-lg border border-gray-200 p-4">
                    <h2 className="text-lg font-semibold mb-4">Actions</h2>

                    <div className="space-y-2">
                      {rec?.status === 'Draft' && canManageNC && (
                        <SecondaryButton
                          text="Mark as Open"
                          onClick={() => doSetStatus('Open')}
                          size="medium"
                          buttonClasses="w-full justify-start"
                          icon={<Eye className="h-4 w-4" />}
                        />
                      )}

                      {rec?.status === 'Open' && canManageNC && (
                        <SecondaryButton
                          text="Move to In Review"
                          onClick={() => doSetStatus('InReview')}
                          size="medium"
                          buttonClasses="w-full justify-start"
                          icon={<Eye className="h-4 w-4" />}
                        />
                      )}

                      {(rec?.status === 'InReview' ||
                        rec?.status === 'In Review') &&
                        canManageNC && (
                          <SecondaryButton
                            text="Move back to Open"
                            onClick={() => doSetStatus('Open')}
                            size="medium"
                            buttonClasses="w-full justify-start"
                            icon={<Eye className="h-4 w-4" />}
                          />
                        )}

                      {rec?.status === 'Draft' && (
                        <PrimaryButton
                          text="Edit"
                          icon={<Edit className="h-4 w-4" />}
                          buttonClasses="w-full justify-start"
                          onClick={() =>
                            router.push(`/supplier-quality/${ncId}/edit`)
                          }
                          size="medium"
                        />
                      )}

                      <Dialog
                        open={showCapaModal}
                        onOpenChange={setShowCapaModal}
                      >
                        <DialogTrigger asChild>
                          <SecondaryButton
                            buttonClasses="w-full"
                            text="Raise a CAPA"
                            size="medium"
                          />
                        </DialogTrigger>
                        <ConfirmModal
                          title={'Confirm'}
                          infoText={
                            'Are you sure, you want to raise a CAPA for this NC?'
                          }
                          btnText={'Confirm'}
                          onClick={() => handleCreateCapa()}
                          btnLoading={createCapaLoading}
                          dialogClass="min-w-[28.5rem]"
                        ></ConfirmModal>
                      </Dialog>

                      {canManageNC && (
                        <SecondaryButton
                          text="Close NC"
                          onClick={() => setShowCloseModal(true)}
                          size="medium"
                          buttonClasses="w-full justify-start"
                          icon={<FileText className="h-4 w-4" />}
                        />
                      )}

                      <SecondaryButton
                        text="Add Investigator"
                        onClick={() => setShowInvestigatorModal(true)}
                        size="medium"
                        buttonClasses="w-full justify-start"
                        icon={<Upload className="h-4 w-4" />}
                      />
                      <SecondaryButton
                        text="Escalate to SCAR"
                        onClick={() => setShowInvestigatorModal(true)}
                        size="medium"
                        buttonClasses="w-full justify-start"
                        icon={<AlertTriangle className="h-4 w-4" />}
                      />
                    </div>
                  </div>

                  {(linked_capas || []).length > 0 && (
                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <h2 className="text-lg font-semibold mb-2">
                          Linked Records
                        </h2>
                      </div>

                      <div className="space-y-4">
                        {/* CAPAs Section */}
                        {(linked_capas || []).length > 0 && (
                          <div className="space-y-2">
                            {(linked_capas || []).map((c) => (
                              <div
                                key={c.id}
                                className="bg-gray-50 rounded-md p-3"
                              >
                                <div className="flex items-center justify-between mb-1">
                                  <span className="text-sm font-medium text-gray-600">
                                    {c.capa_id || 'CAPA'}
                                  </span>
                                  <Status type={c.status} />
                                </div>
                                <Link
                                  href={`/improvement/${c.id}`}
                                  className="text-sm text-primary-500 hover:underline inline-flex items-center gap-1"
                                >
                                  View CAPA
                                </Link>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {(linked_scars || []).length > 0 && (
                    <div className="bg-white rounded-lg border border-gray-200 p-4">
                      <div className="flex items-center gap-2 mb-2">
                        <h2 className="text-lg font-semibold mb-2">
                          Linked Scars
                        </h2>
                      </div>

                      <div className="space-y-4">
                        {/* CAPAs Section */}
                        {(linked_scars || []).length > 0 && (
                          <div className="space-y-2">
                            {(linked_scars || []).map((c) => (
                              <div
                                key={c.id}
                                className="bg-gray-50 rounded-md p-3"
                              >
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-sm font-medium text-gray-600">
                                    {c.title || 'SCAR'}
                                  </span>
                                  <Status type={c.status} />
                                </div>
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-sm font-medium text-gray-600">
                                    Raised Date:
                                  </span>
                                  {formatDate(c.raised_date, false)}
                                </div>
                                <Link
                                  href={`/scars/${c.id}`}
                                  className="text-sm text-primary-500 hover:underline inline-flex items-center gap-1"
                                >
                                  View Scar
                                </Link>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Actions Section */}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Modals */}

        <Dialog
          open={showInvestigatorModal}
          onOpenChange={setShowInvestigatorModal}
        >
          <AddInvestigatorModal
            open={showInvestigatorModal}
            onOpenChange={setShowInvestigatorModal}
            ncId={ncId as string}
            onSuccess={reFetch}
          />
        </Dialog>

        <Dialog
          open={showUploadDocumentModal}
          onOpenChange={setShowUploadDocumentModal}
        >
          <EvidenceUploadModal
            open={showUploadDocumentModal}
            onOpenChange={setShowUploadDocumentModal}
            title="Upload Evidence"
            onSuccess={reFetch}
            isMulti={false}
          />
        </Dialog>

        {/* <Dialog open={showEscalateModal} onOpenChange={setShowEscalateModal}>
        <EscalateToScarModal
          open={showEscalateModal}
          onOpenChange={setShowEscalateModal}
          ncId={ncId as string}
          onSuccess={reFetch}
        />
      </Dialog> */}

        {/* {showLinkModal && (
        <LinkExistingScarModal
          open={showLinkModal}
          onOpenChange={setShowLinkModal}
          ncId={ncId as string}
          supplierId={rec?.supplier_id}
          onSuccess={reFetch}
        />
      )} */}

        {/* {showCloseModal && (
        <CloseNcModal
          open={showCloseModal}
          onOpenChange={setShowCloseModal}
          ncId={ncId as string}
          addedFiles={addedFiles}
          onSuccess={reFetch}
        />
      )} */}

        {/* Status Change Modal */}
        {/* <Dialog
        open={showStatusModal}
        onOpenChange={(open) => {
          setShowStatusModal(open);
          if (!open) setStatusReason('');
        }}
      >
        <div className="w-[520px] bg-white rounded-lg p-6 shadow-xl">
          <h3 className="text-lg font-semibold mb-2">Change Status</h3>
          <p className="text-sm text-gray-600 mb-4">
            {nextStatus === 'InReview'
              ? 'Move to In Review.'
              : nextStatus === 'Open'
              ? 'Move to Open.'
              : 'Update status.'}
          </p>
          <div className="space-y-2">
            <Label htmlFor="status_reason">Reason (optional)</Label>
            <Textarea
              id="status_reason"
              rows={3}
              value={statusReason}
              onChange={(e) => setStatusReason(e.target.value)}
              placeholder="Provide context for this status change"
            />
          </div>
          <div className="mt-5 flex justify-end gap-2">
            <button
              className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
              onClick={() => setShowStatusModal(false)}
            >
              Cancel
            </button>
            <PrimaryButton
              text="Update Status"
              onClick={handleConfirmSetStatus}
              isLoading={statusLoading}
            />
          </div>
        </div>
      </Dialog> */}

        {/* Cancel / Archive Modal */}
        {/* <Dialog
        open={showCancelModal}
        onOpenChange={(open) => {
          setShowCancelModal(open);
          if (!open) {
            setCancelActionError('');
            setCancelReasonError('');
          }
        }}
      >
        <div className="w-[520px] bg-white rounded-lg p-6 shadow-xl">
          <h3 className="text-lg font-semibold mb-2">Cancel / Archive NC</h3>
          <p className="text-sm text-gray-600 mb-4">
            Choose an action and provide a reason. This will remove the NC from
            active views while preserving history.
          </p>
          <div className="space-y-4">
            <div>
              <Label>Action</Label>
              <Select
                value={cancelAction}
                onValueChange={(v) => {
                  setCancelAction(v);
                  setCancelActionError('');
                }}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select action" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Cancelled">Cancelled</SelectItem>
                  <SelectItem value="Archived">Archived</SelectItem>
                  <SelectItem value="Withdrawn">Withdrawn</SelectItem>
                </SelectContent>
              </Select>
              {cancelActionError ? (
                <p className="text-sm text-red-600 mt-1">{cancelActionError}</p>
              ) : null}
            </div>
            <div>
              <Label htmlFor="cancel_reason">Reason</Label>
              <Textarea
                id="cancel_reason"
                rows={3}
                value={cancelReason}
                onChange={(e) => {
                  setCancelReason(e.target.value);
                  setCancelReasonError('');
                }}
                placeholder="Provide a reason"
              />
              {cancelReasonError ? (
                <p className="text-sm text-red-600 mt-1">{cancelReasonError}</p>
              ) : null}
            </div>
          </div>
          <div className="mt-5 flex justify-end gap-2">
            <button
              className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
              onClick={() => setShowCancelModal(false)}
            >
              Cancel
            </button>
            <PrimaryButton
              text="Confirm"
              onClick={handleConfirmCancelArchive}
              isLoading={cancelLoading}
            />
          </div>
        </div>
      </Dialog> */}
      </div>
    </Layout>
  );
};

export default NCDetailPage;
