import SecondaryButton from '@/components/common/button/secondaryButton';
import Loader from '@/components/common/loader';
import Layout from '@/components/common/sidebar/layout';

import Tabs from '@/components/common/tabs';
import Document from '@/components/supplier/supplierDetail/document';
import LogDetails from '@/components/supplier/supplierDetail/logDetails';
import OverView from '@/components/supplier/supplierDetail/overView';
import QualityIssues from '@/components/supplier/supplierDetail/qualityIssue';
import Questionnaires from '@/components/supplier/supplierDetail/questionnaries';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/router';
import { useState } from 'react';

export interface Supplier {
  id: string;
  legal_name: string;
  website: string | null;
  supplier_type: string;
  status: string;
  risk_tier: string;
  criticality_level: number;
  notes: string | null;
  contacts: {
    id: string;
    name: string;
    email: string;
    phone: string;
    is_primary: boolean;
  }[];
  addresses: {
    id: string;
    country: string;
    address_line1: string;
    address_line2: string | null;
    city: string;
    state_province: string;
    postal_code: string;
    is_primary: boolean;
  }[];
  categories: {
    id: string;
    name: string;
  }[];
}

const SupplierDetailPage = () => {
  const router = useRouter();
  const { supplierId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);

  const [activeTab, setActiveTab] = useState(0);

  const tabsData = [
    {
      name: 'Overview',
      textColor: 'text-gray-600',
    },
    {
      name: 'Document',
      textColor: 'text-gray-600',
    },
    {
      name: 'Quality Issue',
      textColor: 'text-gray-600',
    },
    {
      name: 'Questionnaires',
      textColor: 'text-gray-600',
    },
    {
      name: 'Logs',
      textColor: 'text-gray-600',
    },
  ];

  // fetch supplier details using supplierId
  const { data: supplierData, isLoading } = useFetch<Supplier>(
    accessToken,
    `suppliers/${supplierId}`,
    {},
  );

  return (
    <Layout>
      {isLoading ? (
        <Loader className="h-[80vh]" />
      ) : (
        <div className="flex flex-col flex-1 my-5">
          {/* Header */}
          <div className="mb-6 flex items-center gap-4">
            <SecondaryButton
              icon={<ArrowLeft />}
              onClick={() => router.push('/supplier-quality/supplier')}
              text=""
              size="medium"
              buttonClasses="!px-2.5"
            />
            <h1 className="text-2xl font-semibold text-dark-300">
              Supplier Details
            </h1>
          </div>

          {/* Company Name and Basic Info */}
          <div className="mb-8">
            <h1 className="text-xl font-bold mb-2">
              {supplierData?.legal_name}
            </h1>
            <p className="text-base font-medium text-gray-600">
              {supplierData?.supplier_type}
            </p>
          </div>

          <div className="mb-6">
            <Tabs
              tabsData={tabsData}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              tabGroupName="supplier-details"
            />
          </div>

          <div>
            {activeTab === 0 && (
              <OverView supplierData={supplierData ?? undefined} />
            )}
            {activeTab === 1 && <Document />}
            {activeTab === 2 && <QualityIssues />}
            {activeTab === 3 && <Questionnaires />}
            {activeTab === 4 && <LogDetails />}
          </div>
        </div>
      )}
    </Layout>
  );
};

export default SupplierDetailPage;
