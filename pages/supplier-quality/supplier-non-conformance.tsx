import axios from 'axios';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useMemo, useState } from 'react';
import { toast } from 'react-toastify';

import PlusIcon from '@/assets/outline/plus';
import SettingIcon from '@/assets/outline/settting';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Dialog } from '@/components/common/dialog';
// Live data will be fetched via API
import { Label } from '@/components/common/label';
import DeleteModal from '@/components/common/modals/deleteModal';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import { Textarea } from '@/components/common/textarea';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import { formatDate } from '@/utils/time';

const SupplierNonConformance = () => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const [selectedRisk, setSelectedRisk] = useState<NCRow | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const currentUser = useAuthStore((state) => state.user);
  const [deleteAction, setDeleteAction] = useState<
    'Cancelled' | 'Archived' | 'Withdrawn'
  >('Cancelled');
  const [deleteReason, setDeleteReason] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const router = useRouter();

  const breadcrumbData = [
    { name: 'Home', link: '/' },
    { name: 'Supplier Quality', link: '/supplier-quality' },
    {
      name: 'Non-Conformances',
      link: '/supplier-quality/supplier-non-conformance',
    },
  ];
  type NCRow = {
    id: string;
    title: string;
    supplier_name: string;
    status: string;
    severity: string;
    repeat_count: number;
    created_on: string;
    due_date?: string;
  };

  const {
    data: ncData,
    isLoading,
    reFetch,
  } = useFetch<{
    records: NCRow[];
    stats: any;
  }>(accessToken, 'supplier-quality/non-conformances');

  const columnDefs = [
    {
      headerName: 'NC Number',
      field: 'nc_number',
      cellRenderer: (params: any) => {
        console.log('params', params.data);
        return getValueOrDefault(params.data, 'nc_number');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.nc_number || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'nc_number');
      },
      filter: false,
    },
    {
      headerName: 'Supplier',
      field: 'supplier_name',
      cellRenderer: (params: any) => {
        return (
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              router.push(`/supplier-quality/${params.data.id}`);
            }}
          >
            {getValueOrDefault(params.data, 'supplier_name')}
          </button>
        );
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.supplier_name || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'supplier_name');
      },
      filter: false,
      minWidth: 250,
      flex: 2,
    },
    {
      headerName: 'Title',
      field: 'title',
      cellRenderer: (params: any) => getValueOrDefault(params.data, 'title'),
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        const departments = params.data.departments;
        if (departments && departments.length > 0) {
          return departments.map((dept: any) => dept.name).join(' ');
        }
        return '';
      },
      valueFormatter: (params: any) => {
        const departments = params.data.departments;
        if (departments && departments.length > 0) {
          return departments.map((dept: any) => dept.name).join(', ');
        }
        return '-';
      },
      filter: false,
      minWidth: 150,
    },
    {
      headerName: 'Status',
      field: 'status',
      cellRenderer: (params: any) => {
        const processes = params.data.status || '-';

        return processes;
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        const processes = params.data.processes;
        if (processes && processes.length > 0) {
          return processes.map((process: any) => process.name).join(' ');
        }
        return '';
      },
      valueFormatter: (params: any) => {
        const processes = params.data.processes;
        if (processes && processes.length > 0) {
          return processes.map((process: any) => process.name).join(', ');
        }
        return '-';
      },
      filter: false,
      minWidth: 150,
    },
    {
      headerName: 'Severity',
      field: 'severity',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'severity');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.severity || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'severity');
      },
      filter: false,
      minWidth: 150,
    },
    // CAPA linked is not part of list item response; omit for now
    {
      headerName: 'Created On',
      field: 'created_on',
      cellRenderer: (params: any) => formatDate(params.data.created_on),
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => formatDate(params.data.created_on),
      valueFormatter: (params: any) => formatDate(params.data.created_on),
      filter: false,
    },
    {
      headerName: 'Due Date',
      field: 'due_date',
      cellRenderer: (params: any) =>
        params.data.due_date ? formatDate(params.data.due_date) : '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) =>
        params.data.due_date ? formatDate(params.data.due_date) : '',
      valueFormatter: (params: any) =>
        params.data.due_date ? formatDate(params.data.due_date) : '-',
      filter: false,
      minWidth: 140,
    },
    // SCAR link not part of list response; open in detail page

    {
      headerName: 'Repeat',
      field: 'repeat_count',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'repeat_count');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.repeat_count || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'repeat_count');
      },
      filter: false,
      minWidth: 150,
    },
    {
      headerName: 'Action',
      field: 'action',
      cellRenderer: (params: any) => (
        <ManageCellRenderer
          rowData={params.data}
          handleEdit={(rowData) => {
            router.push(`/supplier-quality/${rowData.id}/edit`);
          }}
          handleDelete={(rowData) => {
            setSelectedRisk(rowData);
            setShowDeleteModal(true);
          }}
        />
      ),

      sortable: false,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return '';
      },
      valueFormatter: (params: any) => {
        return '';
      },
      filter: false,
    },
  ];

  const handleConfirmDelete = async () => {
    if (!selectedRisk) return;
    try {
      setIsDeleting(true);
      const mockUser =
        typeof window !== 'undefined'
          ? localStorage.getItem('x-mock-user')
          : null;
      const orgId =
        typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;

      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${accessToken || ''}`,
        ...(mockUser ? { 'x-mock-user': mockUser } : {}),
        ...(orgId ? { 'x-org': orgId } : {}),
      };
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const url = `${baseUrl}/${productVersion}/supplier-quality/non-conformances/${selectedRisk.id}/cancel`;

      const res = await axios.post(
        url,
        { action: deleteAction, reason: deleteReason },
        { headers },
      );
      if (res.status === 200) {
        toast.success('Non-Conformance deleted successfully');
        setShowDeleteModal(false);
        setSelectedRisk(null);
        setDeleteReason('');
        setDeleteAction('Cancelled');
        reFetch();
      } else {
        toast.error('Failed to delete Non-Conformance');
      }
    } catch (e: any) {
      const msg =
        e?.response?.data?.detail ||
        e?.response?.data?.error ||
        e?.message ||
        'Failed to delete';
      toast.error(msg);
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div>
      <Layout>
        <div className=" my-5">
          <div className="flex flex-col">
            <Breadcrumb data={breadcrumbData} />
            <div className="text-dark-300 font-semibold text-3xl leading-10">
              Non-Conformances
            </div>
          </div>
          <div className="mt-5 mb-5 space-y-4">
            <CommonTable
              data={{
                records: ncData?.records || [],
                total: ncData?.records?.length || 0,
              }}
              columnDefs={columnDefs}
              isLoading={isLoading}
              searchRightSideElement={
                <div className="flex gap-4">
                  <>
                    <Tooltip>
                      <TooltipTrigger>
                        <Link href={'/supplier-quality/administration'}>
                          <SecondaryButton
                            icon={<SettingIcon />}
                            text=""
                            size="medium"
                            buttonClasses="!p-2.5"
                          />
                        </Link>
                      </TooltipTrigger>
                      <TooltipContent>NC Administration</TooltipContent>
                    </Tooltip>
                    <PrimaryButton
                      text="Log New NC"
                      buttonClasses="!px-5 !py-2"
                      onClick={() => {
                        router.push('/supplier-quality/create-nc');
                      }}
                      icon={<PlusIcon color="white" />}
                    />
                  </>
                </div>
              }
            />
          </div>

          {/* Delete Confirmation Modal */}
          <Dialog
            open={showDeleteModal}
            onOpenChange={(open) => {
              if (!open) setShowDeleteModal(false);
            }}
          >
            <DeleteModal
              title="Delete Non-Conformance"
              infoText="This will mark the NC as Cancelled/Archived/Withdrawn based on your selection. It will be removed from active views."
              btnText="Confirm"
              onClick={handleConfirmDelete}
              btnLoading={isDeleting}
            >
              <div className="space-y-3">
                <div className="text-sm">
                  Selected:{' '}
                  <span className="font-medium">
                    {selectedRisk?.title || selectedRisk?.id}
                  </span>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div>
                    <Label
                      htmlFor="delete_action"
                      className="text-xs text-gray-500 mb-1"
                    >
                      Action
                    </Label>
                    <Select
                      value={deleteAction}
                      onValueChange={(v) =>
                        setDeleteAction(
                          v as 'Cancelled' | 'Archived' | 'Withdrawn',
                        )
                      }
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select action" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Cancelled">Cancelled</SelectItem>
                        <SelectItem value="Archived">Archived</SelectItem>
                        <SelectItem value="Withdrawn">Withdrawn</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="md:col-span-2">
                    <Label
                      htmlFor="delete_reason"
                      className="text-xs text-gray-500 mb-1"
                    >
                      Reason
                    </Label>
                    <Textarea
                      id="delete_reason"
                      rows={3}
                      placeholder="Provide a reason"
                      value={deleteReason}
                      onChange={(e) => setDeleteReason(e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </DeleteModal>
          </Dialog>
        </div>
      </Layout>
    </div>
  );
};

export default SupplierNonConformance;
