import { Upload } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Dialog } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer, TCustomColDef } from '@/components/common/table';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/common/tooltip';
import BulkUploadModal from '@/components/inventory/modals/bulkUploadModal';
import CreateMaterialModal from '@/components/inventory/modals/createInventoryModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TMaterialData } from '@/interfaces/material';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';

export interface ISelectboxData {
  id?: string;
  name: string;
}

// Use the exported TCustomColDef interface from CommonTable

const InventoryHub = () => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);

  const { data, isLoading, reFetch } = useFetch(accessToken, 'materials');

  const { data: materialType } = useFetch<{ records: ISelectboxData[] }>(
    accessToken,
    'materials/types',
  );
  const { data: materialCategories } = useFetch<{ records: ISelectboxData[] }>(
    accessToken,
    'materials/categories',
  );
  const { data: materialUnits } = useFetch<{ records: ISelectboxData[] }>(
    accessToken,
    'materials/units',
  );

  const [bulkUpload, setBulkUpload] = useState(false);
  const [createMaterial, setCreateMaterial] = useState(false);
  const [editMaterial, setEditMaterial] = useState(false);
  const [selectedMaterial, setSelectedMaterial] = useState<
    undefined | TMaterialData
  >(undefined);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { deleteData, response: onDelete } = useDelete();

  useEffect(() => {
    if (onDelete) {
      setShowDeleteModal(false);
      reFetch();
    }
  }, [onDelete, reFetch]);

  const handleEdit = (rowData: TMaterialData) => {
    setSelectedMaterial(rowData);
    setEditMaterial(true);
  };

  const handleDeleteModal = (rowData: TMaterialData) => {
    setSelectedMaterial(rowData);
    setShowDeleteModal(true);
  };

  const handleDelete = () => {
    async function fetch() {
      await deleteData(accessToken, `materials/${selectedMaterial?.id}`);
    }
    fetch();
  };

  const getInventoryColumns = useCallback(() => {
    const inventoryColumns: TCustomColDef[] = [
      {
        headerName: 'ID',
        field: 'material_id',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: { value: string }) => params.value,
        valueFormatter: (params: { data: TMaterialData; value: unknown }) =>
          getValueOrDefault(params.data, 'material_id'),
        filter: true,
      } as TCustomColDef,
      {
        headerName: 'Material name',
        field: 'name',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: { value: unknown }) =>
          String(params.value || ''),
        valueFormatter: (params: { data: TMaterialData; value: unknown }) =>
          getValueOrDefault(params.data, 'name'),
        filter: true,
      } as TCustomColDef,
      {
        headerName: 'Type',
        field: 'type',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: { value: unknown }) =>
          String(params.value || ''),
        valueFormatter: (params: { data: TMaterialData; value: unknown }) =>
          getValueOrDefault(params.data, 'type'),
        filter: true,
      } as TCustomColDef,
      {
        headerName: 'Category',
        field: 'category',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: { value: unknown }) =>
          String(params.value || ''),
        valueFormatter: (params: { data: TMaterialData; value: unknown }) =>
          getValueOrDefault(params.data, 'category'),
        filter: true,
      } as TCustomColDef,
      {
        headerName: 'UOM',
        field: 'units',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: { value: unknown }) =>
          String(params.value || ''),
        filter: true,
        valueFormatter: (params: { data: TMaterialData; value: unknown }) =>
          getValueOrDefault(params.data, 'units'),
      } as TCustomColDef,
    ];

    if (hasAccess(AccessActions.CanAddOrEditInventory, user)) {
      inventoryColumns.push({
        headerName: 'Manage',
        field: 'manage',
        sortable: false,
        resizable: false,
        getQuickFilterText: () => '',
        valueFormatter: () => '',
        cellRenderer: (params: { data: TMaterialData }) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={handleEdit}
            handleDelete={handleDeleteModal}
            hideDelete={!hasAccess(AccessActions.IsInventoryAdmin, user)}
          />
        ),
        width: 100,
        pinned: 'right',
        filter: false,
      } as TCustomColDef);
    }

    return inventoryColumns;
  }, [user]);

  const breadcrumbData = [
    {
      name: 'Inventory hub',
      link: '/inventory',
    },
  ];

  return (
    <Layout>
      {editMaterial && selectedMaterial && (
        <Dialog
          open={editMaterial}
          onOpenChange={() => {
            setEditMaterial(false);
          }}
        >
          <CreateMaterialModal
            edit
            materialData={selectedMaterial}
            setOpenEdit={setEditMaterial}
            reFetch={reFetch}
            materialType={materialType?.records}
            materialCategories={materialCategories?.records}
            materialUnits={materialUnits?.records}
          />
        </Dialog>
      )}
      {createMaterial && (
        <Dialog
          open={createMaterial}
          onOpenChange={() => {
            setCreateMaterial(false);
          }}
        >
          <CreateMaterialModal
            setOpenEdit={setCreateMaterial}
            reFetch={reFetch}
            materialCategories={materialCategories?.records}
            materialType={materialType?.records}
            materialUnits={materialUnits?.records}
          />
        </Dialog>
      )}
      {bulkUpload && (
        <Dialog open={bulkUpload} onOpenChange={setBulkUpload}>
          <BulkUploadModal setOpenModal={setBulkUpload} onSuccess={reFetch} />
        </Dialog>
      )}
      {showDeleteModal && (
        <Dialog
          open={showDeleteModal}
          onOpenChange={() => setShowDeleteModal(false)}
        >
          <DeleteModal
            title={`Delete Material`}
            infoText={`Are you sure you want to delete this material?`}
            btnText={'Delete'}
            onClick={handleDelete}
          />
        </Dialog>
      )}
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Inventory Hub
          </div>
        </div>
      </div>
      <div className="mb-5">
        <CommonTable
          data={data}
          columnDefs={getInventoryColumns()}
          isLoading={isLoading}
          searchRightSideElement={
            <div className="flex gap-4">
              {/* Only show bulk upload for Admin users */}
              {(user?.roles?.includes('Admin') ||
                user?.roles?.includes('SuperAdmin')) && (
                <Tooltip>
                  <TooltipTrigger>
                    <SecondaryButton
                      text="Bulk Upload"
                      size="medium"
                      buttonClasses="!px-4 !py-2"
                      onClick={() => setBulkUpload(true)}
                      icon={<Upload className="w-4 h-4" />}
                    />
                  </TooltipTrigger>
                  <TooltipContent>
                    Bulk Upload Materials from Excel (Admin Only)
                  </TooltipContent>
                </Tooltip>
              )}

              {hasAccess(AccessActions.CanAddOrEditInventory, user) && (
                <PrimaryButton
                  text="Add Material"
                  buttonClasses="!px-5 !py-2"
                  onClick={() => setCreateMaterial(true)}
                  icon={<PlusIcon color="white" />}
                />
              )}
            </div>
          }
        />
      </div>
    </Layout>
  );
};

export default InventoryHub;
