import Loader from '@/components/common/loader';
import { useAuthStore } from '@/globalProvider/authStore';

const GlobalLoader = () => {
  const isLoading = useAuthStore((state) => state.isLoading);

  if (!isLoading) return null;

  return (
    <div
      style={{ width: '100%' }}
      className="fixed w-full inset-0 z-50 bg-black bg-opacity-50 flex items-center justify-center"
    >
      {' '}
      <Loader className="w-full" />
    </div>
  );
};

export default GlobalLoader;
