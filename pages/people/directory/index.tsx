import { useCallback, useEffect, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import AdvancedTable from '@/components/common/advancedTable';
import { ManageCellRenderer } from '@/components/common/table';
import CreateEmployeeModal from '@/components/people/modals/createEmployeeModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TEmployeeData } from '@/interfaces/people';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import { useRouter } from 'next/router';
import { Upload } from 'lucide-react';
import BulkUploadModal from '@/components/people/bulkUploadModal';

const PeopleHub = () => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);

  const { data, isLoading, error, reFetch } = useFetch<{
    records: TEmployeeData[];
  }>(accessToken, 'employees');

  const { data: employeeTitle } = useFetch<{
    records: {
      id: string;
      name: string;
    }[];
  }>(accessToken, 'employee/titles');

  const [activeTab, setActiveTab] = useState<number>(0);
  const router = useRouter();

  const [createEmployee, setCreateEmployee] = useState(false);
  const [editEmployee, setEditEmployee] = useState(false);
  const [selectedEmployee, setSelectedEmployee] = useState<
    undefined | TEmployeeData
  >(undefined);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [bulkUpload, setBulkUpload] = useState(false);

  const {
    deleteData,
    response: onDelete,
    isLoading: deleteLoading,
  } = useDelete();

  useEffect(() => {
    if (onDelete) {
      setShowDeleteModal(false);
      reFetch();
    }
  }, [onDelete]);

  const handleEdit = (rowData: TEmployeeData) => {
    setSelectedEmployee(rowData);
    setEditEmployee(true);
  };

  const handleDeleteModal = (rowData: TEmployeeData) => {
    setSelectedEmployee(rowData);
    setShowDeleteModal(true);
  };

  const handleDelete = () => {
    async function fetch() {
      await deleteData(accessToken, `employees/${selectedEmployee?.id}`);
    }
    fetch();
  };

  const getPeopleColumns = useCallback(() => {
    const peopleColumns = [
      {
        key: 'name',
        label: 'Name',
        sortable: true,
        resizable: true,
        width: 350,
        render: (value: any, row: TEmployeeData) => (
          <div
            className="flex flex-col"
            onClick={() =>
              router.push({
                pathname: `/people/directory/${row?.id}`,
                query: router.query,
              })
            }
          >
            <span className="text-[#0C66E4] font-semibold cursor-pointer">
              {getValueOrDefault(row, 'name')}
            </span>
            <span className="text-gray-500 text-xs font-semibold overflow-hidden text-ellipsis">
              ID: {getValueOrDefault(row, 'employee_id')}
            </span>
          </div>
        ),
      },
      {
        key: 'job_title',
        label: 'Job Title',
        sortable: false,
        resizable: true,
        render: (value: any, row: TEmployeeData) => row.job_title?.name || '',
      },
      {
        key: 'email',
        label: 'Email',
        sortable: true,
        resizable: true,
        render: (value: any, row: TEmployeeData) =>
          getValueOrDefault(row, 'email'),
      },
      {
        key: 'status',
        label: 'Status',
        sortable: true,
        resizable: true,
        render: (value: any, row: TEmployeeData) =>
          getValueOrDefault(row, 'status'),
      },
      {
        key: 'departments',
        label: 'Departments',
        sortable: false,
        resizable: true,
        render: (value: any, row: TEmployeeData) => {
          return row?.departments
            ? row.departments
                .map((department: any) => department.name)
                .join(', ')
            : '-';
        },
      },
      {
        key: 'processes',
        label: 'Processes',
        sortable: false,
        resizable: true,
        render: (value: any, row: TEmployeeData) => {
          return row?.processes
            ? row.processes.map((process: any) => process.name).join(', ')
            : '-';
        },
      },
    ];

    if (hasAccess(AccessActions.CanAddOrEditPeople, user)) {
      peopleColumns.push({
        key: 'manage',
        label: 'Manage',
        sortable: false,
        resizable: false,
        render: (value: any, row: TEmployeeData) => (
          <ManageCellRenderer
            rowData={row}
            handleEdit={handleEdit}
            handleDelete={handleDeleteModal}
            hideDelete={!hasAccess(AccessActions.IsPeopleAdmin, user)}
          />
        ),
      });
    }

    return peopleColumns;
  }, [user]);

  const breadcrumbData = [
    {
      name: 'People hub',
      link: '/people',
    },
    {
      name: 'Directory',
      link: '#',
    },
  ];

  return (
    <Layout>
      {bulkUpload && (
        <Dialog open={bulkUpload} onOpenChange={setBulkUpload}>
          <BulkUploadModal setOpenModal={setBulkUpload} onSuccess={reFetch} />
        </Dialog>
      )}

      {editEmployee && selectedEmployee && (
        <Dialog open={editEmployee} onOpenChange={setEditEmployee}>
          <CreateEmployeeModal
            edit
            employeeData={selectedEmployee}
            setOpenEdit={setEditEmployee}
            reFetch={reFetch}
            employeeTitle={employeeTitle?.records}
          />
        </Dialog>
      )}
      {createEmployee && (
        <Dialog open={createEmployee} onOpenChange={setCreateEmployee}>
          <CreateEmployeeModal
            setOpenEdit={setCreateEmployee}
            reFetch={reFetch}
            employeeTitle={employeeTitle?.records}
          />
        </Dialog>
      )}
      {showDeleteModal && (
        <Dialog
          open={showDeleteModal}
          onOpenChange={() => setShowDeleteModal(false)}
        >
          <DeleteModal
            title={`Delete Employee`}
            infoText={`Are you sure you want to delete this employee?`}
            btnText={'Delete'}
            onClick={handleDelete}
            btnLoading={deleteLoading}
          />
        </Dialog>
      )}
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            People Hub
          </div>
        </div>
      </div>
      <div className="mb-5 relative">
        <AdvancedTable
          data={data?.records || []}
          columns={getPeopleColumns()}
          handleRowClicked={(row) => {
            setSelectedEmployee(row);
          }}
          loading={isLoading}
          searchRightSideElement={
            hasAccess(AccessActions.CanAddOrEditPeople, user) ? (
              <div className="flex gap-4">
                <PrimaryButton
                  text="Bulk Upload"
                  buttonClasses="!px-5 !py-2"
                  onClick={() => {
                    setBulkUpload(true);
                  }}
                  icon={<Upload className="h-4 w-4" color="white" />}
                />
                <PrimaryButton
                  icon={<PlusIcon color="white" />}
                  text="Add Employee"
                  buttonClasses="!px-5 !py-2"
                  onClick={() => setCreateEmployee(true)}
                />
              </div>
            ) : undefined
          }
        />
      </div>
    </Layout>
  );
};

export default PeopleHub;
