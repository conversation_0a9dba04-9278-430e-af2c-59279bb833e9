import React, { useState, useMemo } from 'react';
import AssignSkillModal from './assignSkillModal';
import { Input } from '@/components/common/input';
import {
  getSkillLevelColor,
  getSkillLevelName,
} from '@/utils/skillLevelColors';
import SkillLevelLegend from '@/components/skills/skillLevelLegend';
import { Dialog } from '@/components/common/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import ResetFilterIcon from '@/assets/outline/resetFilter';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import TertiaryButton from '@/components/common/button/tertiaryButton';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';
import { AccessActions } from '@/constants/access';

// Type definitions
interface Employee {
  id: string;
  name: string;
}

interface Skill {
  id: string;
  name: string;
}

interface SkillCategory {
  category: string;
  skills: Skill[];
}

interface SkillLevel {
  employeeId: string;
  skillId: string;
  level: string;
}

interface SkillRecord {
  id: string;
  skill_id: string;
  skill_name: string;
  employee_id: string;
  employee_name: string;
  category_id: string;
  category_name: string;
  current_skill_level_id: string;
  current_skill_level_name: string;
}

const transformSkillsData = (
  data?: { records?: SkillRecord[] },
  allSkillsData?: { records?: any[] },
  allEmployeesData?: any[],
) => {
  const employees = new Map<string, Employee>();
  const skillCategories = new Map<
    string,
    { category: string; skills: Map<string, Skill> }
  >();
  const skillLevels: SkillLevel[] = [];

  (allEmployeesData ?? []).forEach((employee) => {
    employees.set(employee.id, {
      id: employee.id,
      name: employee.name,
    });
  });

  (data?.records ?? []).forEach((record) => {
    employees.set(record.employee_id, {
      id: record.employee_id,
      name: record.employee_name,
    });

    if (!skillCategories.has(record.category_name)) {
      skillCategories.set(record.category_name, {
        category: record.category_name,
        skills: new Map<string, Skill>(),
      });
    }

    const category = skillCategories.get(record.category_name)!;
    category.skills.set(record.skill_id, {
      id: record.skill_id,
      name: record.skill_name,
    });

    skillLevels.push({
      employeeId: record.employee_id,
      skillId: record.skill_id,
      level: record.current_skill_level_name,
    });
  });

  (allSkillsData?.records ?? []).forEach((skill) => {
    const categoryName = skill.category?.name || 'Uncategorized';

    if (!skillCategories.has(categoryName)) {
      skillCategories.set(categoryName, {
        category: categoryName,
        skills: new Map<string, Skill>(),
      });
    }

    const category = skillCategories.get(categoryName)!;
    if (!category.skills.has(skill.id)) {
      category.skills.set(skill.id, {
        id: skill.id,
        name: skill.name,
      });
    }
  });

  return {
    employees: Array.from(employees.values()),
    skillCategories: Array.from(skillCategories.values()).map((cat) => ({
      category: cat.category,
      skills: Array.from(cat.skills.values()),
    })),
    skillLevels,
  };
};

export default function SkillsMatrix({
  skillsData,
  skillLevels = [], // Provide default empty array
  employeesData,
  reFetch,
  allSkillsData,
}: {
  skillsData: any;
  skillLevels: any[];
  employeesData: any[];
  reFetch: () => void;
  allSkillsData?: any; // Add this type
}) {
  const {
    employees: mockEmployees,
    skillCategories: mockSkillCategories,
    skillLevels: mockSkillLevels,
  } = transformSkillsData(
    skillsData ?? { records: [] },
    allSkillsData,
    employeesData,
  );

  const { accessToken, user } = useAuthStore();

  const [selected, setSelected] = useState<{
    employeeId: string;
    skillId: string;
    employeeName?: string;
    skillName?: string;
  } | null>(null);
  const [search, setSearch] = useState('');
  const [searchType, setSearchType] = useState<
    'employee' | 'skill' | 'category' | ''
  >('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  const getLevel = (employeeId: string, skillId: string) => {
    const found = mockSkillLevels.find(
      (l) => l.employeeId === employeeId && l.skillId === skillId,
    );
    return found ? found.level : '+';
  };

  const getLevelDisplay = (level: string) => {
    if (level === '+') return { level: '+', name: 'Not Assigned' };
    // Keep the "L" prefix for display
    return { level, name: getSkillLevelName(level.replace('L', '')) };
  };

  const getLevelStyles = (level: string, isSelected: boolean = false) => {
    if (isSelected) {
      return 'bg-primary-50 text-black border rounded-full';
    }
    // Uniform styling for all levels - white background with black text and rounded border
    return 'bg-white text-black border border-gray-300 rounded-full';
  };

  const filteredEmployees = useMemo((): Employee[] => {
    if (searchType !== 'employee' || !search.trim()) return mockEmployees || [];
    return (mockEmployees || []).filter((e) =>
      e.name.toLowerCase().includes(search.toLowerCase()),
    );
  }, [mockEmployees, search, searchType]);

  const filteredSkillCategories = useMemo((): SkillCategory[] => {
    let categories = mockSkillCategories || [];

    if (selectedCategory && selectedCategory !== '') {
      categories = categories.filter(
        (cat) => cat.category === selectedCategory,
      );
    }

    if (!search.trim() || !searchType) return categories;

    if (searchType === 'employee') {
      return categories;
    } else if (searchType === 'skill') {
      return categories
        .map((cat) => ({
          ...cat,
          skills: (cat.skills || []).filter((s: Skill) =>
            s.name.toLowerCase().includes(search.toLowerCase()),
          ),
        }))
        .filter((cat) => cat.skills.length > 0);
    } else {
      const lowerSearch = search.toLowerCase();
      return categories.filter((cat) =>
        cat.category.toLowerCase().includes(lowerSearch),
      );
    }
  }, [mockSkillCategories, search, searchType, selectedCategory]);

  const allCategories = useMemo((): string[] => {
    return (mockSkillCategories || []).map((cat) => cat.category);
  }, [mockSkillCategories]);

  const handleSearchTypeChange = (type: 'employee' | 'skill' | 'category') => {
    setSearchType(type);
    setSearch('');
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
  };

  const handleReset = () => {
    setSearchType('');
    setSelectedCategory('');
    setSearch('');
  };

  const isSelected = (employeeId: string, skillId: string) => {
    return selected?.employeeId === employeeId && selected?.skillId === skillId;
  };

  return (
    <div className="space-y-5">
      <div className="mt-4">
        <SkillLevelLegend skillLevels={skillLevels || []} />
      </div>
      <div className="flex flex-col sm:flex-row justify-between mb-4 gap-2 mt-4">
        <div className="flex items-center justify-between relative w-full">
          <div className="flex gap-4 justify-between  w-full">
            <div className="flex gap-4">
              <Select
                value={searchType}
                onValueChange={(value: string) => {
                  handleSearchTypeChange(
                    value as 'employee' | 'skill' | 'category',
                  );
                }}
              >
                <SelectTrigger id="status">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="employee">Search by Employees</SelectItem>
                  <SelectItem value="skill">Search by Skills</SelectItem>
                </SelectContent>
              </Select>

              <Input
                placeholder={
                  searchType
                    ? `Search by ${searchType}...`
                    : 'Select search type first...'
                }
                className="flex-1 w-[40vw] bg-white rounded-lg border border-grey-100 py-3 px-4 text-black outline-none transition focus:border-primary active:border-primary"
                onChange={(e) => setSearch(e.target.value)}
                value={search}
                disabled={!searchType}
              />
              <Tooltip>
                <TooltipTrigger>
                  <TertiaryButton
                    icon={<ResetFilterIcon />}
                    text=""
                    size="medium"
                    buttonClasses="!p-2"
                    onClick={() => {
                      handleReset();
                    }}
                  />
                </TooltipTrigger>
                <TooltipContent>Reset filters</TooltipContent>
              </Tooltip>
            </div>

            <div className="flex gap-2 items-center">
              <Select
                value={selectedCategory}
                onValueChange={(value: string) => {
                  handleCategoryChange(value);
                }}
              >
                <SelectTrigger id="status" className="w-[200px]">
                  <SelectValue placeholder="Select Department" />
                </SelectTrigger>
                <SelectContent>
                  {allCategories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop/Tablet Table */}
      <div className="hidden md:block rounded-lg border border-white-200 bg-white-100 shadow-sm max-h-[70vh] overflow-auto">
        <table className="min-w-full text-base border-collapse table-auto">
          <thead>
            {/* Header row: sticky at top */}
            <tr className="bg-white-200 text-left border border-grey-100 sticky top-0 z-40">
              {/* Skills column header (auto width) */}
              <th className="px-3 py-3 sticky left-0 z-50 bg-white-200">
                <span className="text-base font-semibold text-grey-300 truncate">
                  Skills
                </span>
              </th>
              {/* Employee columns */}
              {filteredEmployees.length === 0 ? (
                <th className="px-3 py-3 bg-white-200 border-b border-grey-100 min-w-[120px]">
                  <span className="text-base font-semibold text-grey-300 truncate">
                    No employees found
                  </span>
                </th>
              ) : (
                filteredEmployees.map((emp) => (
                  <th
                    key={emp.id}
                    className="px-3 py-3 bg-white-200 border-b border-grey-100 min-w-[120px] text-center"
                    title={emp.name}
                  >
                    <span className="text-base font-semibold text-grey-300 truncate block max-w-[10rem] lg:max-w-[14rem]">
                      {emp.name}
                    </span>
                  </th>
                ))
              )}
            </tr>
          </thead>

          <tbody>
            {filteredSkillCategories.length === 0 ? (
              <tr>
                <td
                  colSpan={filteredEmployees.length + 1}
                  className="p-4 text-center text-gray-400"
                >
                  No skills available
                </td>
              </tr>
            ) : (
              filteredSkillCategories.map((cat) => (
                <React.Fragment key={cat.category}>
                  {/* Sticky Category Row */}
                  <tr className="border-t border-white-200 bg-gray-50">
                    {/* Sticky first column */}
                    <td
                      className="px-3 py-2 text-dark-300 font-semibold sticky left-0 z-40 bg-white-50"
                      style={{ top: '48px' }}
                    >
                      {cat.category}
                    </td>
                    <td
                      colSpan={filteredEmployees.length}
                      className="bg-white-50"
                      style={{ top: '48px' }}
                    />
                  </tr>

                  {/* Skills rows */}
                  {(cat.skills || []).length === 0 ? (
                    <tr>
                      <td
                        colSpan={filteredEmployees.length + 1}
                        className="p-4 text-center text-gray-400"
                      >
                        No skills in this category
                      </td>
                    </tr>
                  ) : (
                    (cat.skills || []).map((skill: Skill) => (
                      <tr key={skill.id} className="border-t border-white-200">
                        {/* Sticky skill name */}
                        <td className="py-3 px-2 sticky left-0 z-30 bg-white-100">
                          <span className="inline-block rounded-full bg-primary-100 text-primary-400 px-3 py-1 text-sm font-medium whitespace-nowrap">
                            {skill.name}
                          </span>
                        </td>

                        {/* Employee skill levels */}
                        {filteredEmployees.map((emp) => {
                          const level = getLevel(emp.id, skill.id);
                          const levelDisplay = getLevelDisplay(level);
                          const cellIsSelected = isSelected(emp.id, skill.id);
                          const levelStyles = getLevelStyles(
                            level,
                            cellIsSelected,
                          );

                          return (
                            <td
                              key={emp.id}
                              className={`p-2 cursor-pointer text-center ${
                                cellIsSelected ? 'bg-primary-50' : ''
                              }`}
                              onClick={() => {
                                if (
                                  hasAccess(
                                    AccessActions.CanAddOrEditPeople,
                                    user,
                                  )
                                ) {
                                  setSelected({
                                    employeeId: emp.id,
                                    skillId: skill.id,
                                    employeeName: emp.name,
                                    skillName: skill.name,
                                  });
                                }
                              }}
                              title={
                                hasAccess(
                                  AccessActions.CanAddOrEditPeople,
                                  user,
                                )
                                  ? `${emp.name} - ${levelDisplay.name}`
                                  : 'Admin access required to assign skills'
                              }
                            >
                              <span
                                className={`px-2 py-1 text-xs font-medium ${levelStyles}`}
                              >
                                {levelDisplay.level}
                              </span>
                            </td>
                          );
                        })}
                      </tr>
                    ))
                  )}
                </React.Fragment>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Mobile List */}
      <div className="md:hidden space-y-3">
        {filteredSkillCategories.map((cat) => (
          <div
            key={cat.category}
            className="rounded-lg border border-white-200 bg-white-100 shadow-sm"
          >
            <div className="px-3 py-2 bg-white-50 text-dark-300 font-semibold rounded-t-lg sticky top-0 z-10">
              {cat.category}
            </div>
            <div className="divide-y divide-white-200">
              {(cat.skills || []).map((skill: Skill) => (
                <div key={skill.id} className="p-3 space-y-2">
                  <div>
                    <span className="inline-block rounded-full bg-primary-100 text-primary-400 px-3 py-1 text-xs font-medium">
                      {skill.name}
                    </span>
                  </div>
                  <div className="flex gap-2 overflow-x-auto pb-1">
                    {filteredEmployees.map((emp) => {
                      const level = getLevel(emp.id, skill.id);
                      const levelDisplay = getLevelDisplay(level);
                      const cellIsSelected = isSelected(emp.id, skill.id);
                      const levelStyles = getLevelStyles(level, cellIsSelected);

                      return (
                        <button
                          key={emp.id}
                          className={`flex-shrink-0 inline-flex items-center gap-2 px-3 py-2 rounded-lg border ${
                            cellIsSelected
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-white-200 bg-white-100 hover:bg-white-50'
                          }`}
                          onClick={() =>
                            setSelected({
                              employeeId: emp.id,
                              skillId: skill.id,
                              employeeName: emp.name,
                              skillName: skill.name,
                            })
                          }
                          title={`${emp.name} - ${levelDisplay.name}`}
                        >
                          <span className="max-w-[8rem] truncate text-sm text-dark-200">
                            {emp.name}
                          </span>
                          <span
                            className={`px-2 py-0.5 text-sm font-medium ${levelStyles}`}
                          >
                            {levelDisplay.level}
                          </span>
                        </button>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {selected && (
        <Dialog open={!!selected} onOpenChange={() => setSelected(null)}>
          <AssignSkillModal
            employeeId={selected.employeeId}
            skillId={selected.skillId}
            employeeName={selected.employeeName}
            skillName={selected.skillName}
            onClose={() => setSelected(null)}
            skillLevels={skillLevels || []}
            skillsData={skillsData}
            reFetch={reFetch}
            open
          />
        </Dialog>
      )}
    </div>
  );
}
