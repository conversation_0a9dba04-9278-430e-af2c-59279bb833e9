import React, { useState, useEffect, useMemo, useCallback } from 'react';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Label } from '@/components/common/label';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import {
  getSkillLevelColor,
  getSkillLevelName,
} from '@/utils/skillLevelColors';
import { usePost } from '@/hooks/usePost';

import moment from 'moment';
import Calendar from '@/components/common/calendar';
import UploadComponent from '@/components/common/uploadComponent';
import { IAttachment } from '@/interfaces/misc';
import { useAuthStore } from '@/globalProvider/authStore';
import { toast } from 'react-toastify';
import { useDropzone } from 'react-dropzone';
import { Upload, AlertCircle } from 'lucide-react';
import { FileCard } from '@/components/common/fileCard';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import axios from 'axios';

const assessmentMethods = [
  { value: 'Self Assesment', label: 'Self Assesment' },
  { value: 'Manager Review', label: 'Manager Review' },
  { value: 'Peer Review', label: 'Peer Review' },
  { value: 'Formal Assesment', label: 'Formal Assesment' },
];

interface AssignSkillModalProps {
  employeeId: string;
  skillId: string;
  employeeName?: string;
  skillName?: string;
  onClose: () => void;
  skillLevels: any[];
  skillsData: any;
  reFetch: () => void;
  open: boolean;
}

interface ValidationErrors {
  currentLevel?: string;
  targetLevel?: string;
  assessor?: string;
  method?: string;
  evidence?: string;
  nextReviewDate?: string;
}

const accepted_file_types: any = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
  ],
  'application/msword': ['.doc'],
  'image/jpeg': ['.jpeg', '.jpg'],
  'image/png': ['.png'],
};

const AssignSkillModal = ({
  employeeId,
  skillId,
  employeeName,
  skillName,
  onClose,
  skillLevels = [],
  skillsData = { records: [] },
  reFetch,
  open = true,
}: AssignSkillModalProps) => {
  const [currentLevel, setCurrentLevel] = useState('');
  const [targetLevel, setTargetLevel] = useState('');
  const [assessor, setAssessor] = useState('');
  const [method, setMethod] = useState('');
  const [evidence, setEvidence] = useState('');
  const [notes, setNotes] = useState('');
  const [nextReviewDate, setNextReviewDate] = useState('');
  const [addedFiles, setAddedFiles] = useState<File[]>([]);
  const [trainingIds, setTrainingIds] = useState<string[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {},
  );
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const { postData, response, isLoading, error } = usePost();
  const accessToken = useAuthStore((state) => state.accessToken);

  // Get current skill and employee information
  const currentSkillRecord = useMemo(() => {
    return skillsData?.records?.find(
      (record: any) =>
        record.employee_id === employeeId && record.skill_id === skillId,
    );
  }, [skillsData, employeeId, skillId]);

  // Use fallback values if record doesn't exist (skill not assigned yet)
  const finalEmployeeName =
    currentSkillRecord?.employee_name || employeeName || 'Unknown Employee';
  const finalSkillName =
    currentSkillRecord?.skill_name || skillName || 'Unknown Skill';
  const currentSkillLevelName = currentSkillRecord?.current_skill_level_name;

  // Set initial current level when component mounts or when current skill record changes
  useEffect(() => {
    if (currentSkillLevelName && skillLevels?.length > 0) {
      // Find the skill level object that matches the current level name
      const currentLevelObj = skillLevels.find(
        (level) => level.name === currentSkillLevelName,
      );
      if (currentLevelObj) {
        setCurrentLevel(currentLevelObj.id);
        setTouched((prev) => ({ ...prev, currentLevel: false }));
      }
    }
  }, [currentSkillLevelName, skillLevels]);

  const validateField = (
    fieldName: string,
    value: string,
  ): string | undefined => {
    switch (fieldName) {
      case 'currentLevel':
        if (!value) return 'Current level is required';
        break;
      case 'targetLevel':
        // Target level is now optional, only validate if a value is provided
        if (value && value === currentLevel)
          return 'Target level must be different from current level';
        break;
      case 'assessor':
        if (!value.trim()) return 'Assessor name is required';
        if (value.trim().length < 2)
          return 'Assessor name must be at least 2 characters';
        if (value.trim().length > 100)
          return 'Assessor name must be less than 100 characters';
        break;
      case 'method':
        if (!value) return 'Assessment method is required';
        break;
      case 'evidence':
        if (!value.trim()) return 'Evidence is required';
        if (value.trim().length < 10)
          return 'Evidence must be at least 10 characters';
        if (value.trim().length > 1000)
          return 'Evidence must be less than 1000 characters';
        break;
      case 'nextReviewDate':
        if (!value) return 'Next review date is required';
        const selectedDate = moment(value);
        const today = moment().startOf('day');
        if (selectedDate.isBefore(today))
          return 'Next review date cannot be in the past';
        if (selectedDate.isAfter(moment().add(2, 'years')))
          return 'Next review date cannot be more than 2 years from now';
        break;
      default:
        return undefined;
    }
    return undefined;
  };

  const validateAllFields = (): ValidationErrors => {
    const errors: ValidationErrors = {};

    const currentLevelError = validateField('currentLevel', currentLevel);
    if (currentLevelError) errors.currentLevel = currentLevelError;

    const targetLevelError = validateField('targetLevel', targetLevel);
    if (targetLevelError) errors.targetLevel = targetLevelError;

    const assessorError = validateField('assessor', assessor);
    if (assessorError) errors.assessor = assessorError;

    const methodError = validateField('method', method);
    if (methodError) errors.method = methodError;

    const evidenceError = validateField('evidence', evidence);
    if (evidenceError) errors.evidence = evidenceError;

    const dateError = validateField('nextReviewDate', nextReviewDate);
    if (dateError) errors.nextReviewDate = dateError;

    // Supporting document validation

    return errors;
  };

  // Real-time validation
  useEffect(() => {
    const errors = validateAllFields();
    setValidationErrors(errors);
  }, [currentLevel, targetLevel, assessor, method, evidence, nextReviewDate]);

  // Check if form is valid
  const isFormValid = useMemo(() => {
    const errors = validateAllFields();
    return Object.keys(errors).length === 0;
  }, [currentLevel, targetLevel, assessor, method, evidence, nextReviewDate]);

  const handleFieldBlur = (fieldName: string) => {
    setTouched((prev) => ({ ...prev, [fieldName]: true }));
  };

  const handleFileUpload = async (
    file: File,
  ): Promise<{ file_path: string; file_extension: string } | null> => {
    try {
      setIsUploading(true);
      const formData = new FormData();
      formData.append('file', file);

      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=people_hub&sub_path=/employees/${employeeId}/${Date.now()}/documents/skills/${skillId}`;
      const response = await axios.post(url, formData, config);

      setIsUploading(false);
      return {
        file_path: response.data.file_path,
        file_extension: response.data.file_ext,
      };
    } catch (error) {
      setIsUploading(false);
      toast.error('File upload failed');
      return null;
    }
  };

  const handleSave = async () => {
    setTouched({
      currentLevel: true,
      targetLevel: true,
      assessor: true,
      method: true,
      evidence: true,
      nextReviewDate: true,
    });

    const errors = validateAllFields();
    if (Object.keys(errors).length > 0) {
      toast.error('Please fix the validation errors before saving');
      return;
    }

    let response;

    if (addedFiles.length > 0) {
      const result = await handleFileUpload(addedFiles[0]);
      if (result) {
        response = result;
      }
    }

    const currentLevelObj = skillLevels.find(
      (level) => level.id === currentLevel,
    );
    const targetLevelObj = skillLevels.find(
      (level) => level.name === targetLevel,
    );

    const payload = {
      employee_id: employeeId,
      current_level_id: currentLevel || currentLevelObj?.id,
      target_level_id: targetLevelObj?.id || null, // Allow null for optional target level
      assessor_name: assessor.trim(),
      assessment_method: method,
      evidence: {
        evidence_type: 'document',
        documents: response
          ? [
              {
                file_path: response?.file_path,
                file_extension: response?.file_extension,
              },
            ]
          : [],
        training_ids: trainingIds,
      },
      notes: notes.trim(),
      next_review_date: nextReviewDate,
    };

    await postData(
      accessToken as string,
      `employee/skills/${skillId}/assign`,
      payload,
    );
  };

  // Get available skill levels for target (excluding current if it exists)
  const availableTargetLevels = (skillLevels || []).filter(
    (level) => level.id < currentLevel,
  );

  const onDrop = useCallback((acceptedFiles: File[]) => {
    if (acceptedFiles.length > 0) {
      setAddedFiles([acceptedFiles[0]]);
    }
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: accepted_file_types,
    multiple: false,
  });

  const removeFile = () => {
    setAddedFiles([]);
  };

  const resetForm = () => {
    setAddedFiles([]);
    setTrainingIds([]);
    setNotes('');
    setNextReviewDate('');
    setTargetLevel('');
    setAssessor('');
    setMethod('');
    setEvidence('');
    setIsUploading(false);
    setCurrentLevel('');
    setTargetLevel('');
    setValidationErrors({});
    setTouched({});
  };

  useEffect(() => {
    if (response) {
      onClose();
      resetForm();
      toast.success('Skill assigned successfully');
      reFetch();
    }

    if (error) {
      toast.error('Failed to assign skill');
    }
  }, [response, error, onClose, reFetch]);

  const ErrorMessage = ({ message }: { message?: string }) => {
    if (!message) return null;
    return (
      <div className="flex items-center gap-1 mt-1 text-red-600 text-sm">
        <AlertCircle className="w-4 h-4" />
        <span>{message}</span>
      </div>
    );
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Assign Skill</DialogTitle>
        </DialogHeader>

        <div className="space-y-5">
          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="current_level"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Current Level <span className="text-red-500">*</span>
              {currentSkillLevelName
                ? ` (Currently: ${currentSkillLevelName})`
                : ' (Not Assigned)'}
            </Label>
            <div className="flex flex-wrap gap-2">
              {(skillLevels || []).map((lvl) => {
                const levelNumber = lvl.name.replace('L', '');
                const isSelected = currentLevel === lvl.id;
                const isCurrentAssigned = lvl.name === currentSkillLevelName;

                return (
                  <button
                    key={lvl.id}
                    onClick={() => {
                      setCurrentLevel(lvl.id);
                      handleFieldBlur('currentLevel');
                    }}
                    className={`px-2.5 py-1.5 rounded-full border text-sm font-medium transition-colors relative ${
                      isSelected
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-black border-gray-300 hover:bg-blue-50'
                    } ${isCurrentAssigned ? 'ring-2 ring-blue-300' : ''} ${
                      touched.currentLevel && validationErrors.currentLevel
                        ? 'border-red-300'
                        : ''
                    }`}
                    title={`${getSkillLevelName(levelNumber)}${
                      isCurrentAssigned ? ' (Currently Assigned)' : ''
                    }`}
                  >
                    {lvl.name}
                    {isCurrentAssigned && (
                      <span className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full border-2 border-white"></span>
                    )}
                  </button>
                );
              })}
            </div>
            {touched.currentLevel && (
              <ErrorMessage message={validationErrors.currentLevel} />
            )}
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="target_level"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Target Level (Optional)
            </Label>
            <div className="flex flex-wrap gap-2">
              {availableTargetLevels.map((lvl) => {
                const levelNumber = lvl.name.replace('L', '');
                const isSelected = targetLevel === lvl.name;

                return (
                  <button
                    key={lvl.id}
                    onClick={() => {
                      setTargetLevel(lvl.name);
                      handleFieldBlur('targetLevel');
                    }}
                    className={`px-2.5 py-1.5 rounded-full border text-sm font-medium transition-colors ${
                      isSelected
                        ? 'bg-blue-500 text-white border-blue-500'
                        : 'bg-white text-black border-gray-300 hover:bg-blue-50'
                    } ${
                      touched.targetLevel && validationErrors.targetLevel
                        ? 'border-red-300'
                        : ''
                    }`}
                    title={getSkillLevelName(levelNumber)}
                  >
                    {lvl.name}
                  </button>
                );
              })}
              {availableTargetLevels.length === 0 && (
                <p className="text-sm text-gray-500">
                  No target levels available
                </p>
              )}
            </div>
            {touched.targetLevel && (
              <ErrorMessage message={validationErrors.targetLevel} />
            )}
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="assessor"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Assessor Name <span className="text-red-500">*</span>
            </Label>
            <Input
              id="assessor"
              placeholder="Enter assessor name"
              value={assessor}
              onChange={(e) => setAssessor(e.target.value)}
              onBlur={() => handleFieldBlur('assessor')}
              className={
                touched.assessor && validationErrors.assessor
                  ? 'border-red-300 focus:border-red-500'
                  : ''
              }
            />
            {touched.assessor && (
              <ErrorMessage message={validationErrors.assessor} />
            )}
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="method"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Assessment Method <span className="text-red-500">*</span>
            </Label>
            <Select
              value={method}
              onValueChange={(value) => {
                setMethod(value);
                handleFieldBlur('method');
              }}
            >
              <SelectTrigger
                className={
                  touched.method && validationErrors.method
                    ? 'border-red-300 focus:border-red-500'
                    : ''
                }
              >
                <SelectValue placeholder="Select assessment method" />
              </SelectTrigger>
              <SelectContent>
                {assessmentMethods.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {touched.method && (
              <ErrorMessage message={validationErrors.method} />
            )}
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="evidence"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Evidence <span className="text-red-500">*</span>
            </Label>
            <Textarea
              id="evidence"
              placeholder="Describe the evidence supporting this skill level (minimum 10 characters)"
              rows={3}
              value={evidence}
              onChange={(e) => setEvidence(e.target.value)}
              onBlur={() => handleFieldBlur('evidence')}
              className={
                touched.evidence && validationErrors.evidence
                  ? 'border-red-300 focus:border-red-500'
                  : ''
              }
            />
            <div className="flex justify-between items-center">
              <div>
                {touched.evidence && (
                  <ErrorMessage message={validationErrors.evidence} />
                )}
              </div>
              <span
                className={`text-xs ${
                  evidence.length > 1000 ? 'text-red-500' : 'text-gray-500'
                }`}
              >
                {evidence.length}/1000
              </span>
            </div>
          </div>

          <div>
            <div className="flex flex-col gap-2.5">
              <Label className="text-base font-medium leading-6 text-dark-100">
                Supporting Documents (Optional)
              </Label>
              <div
                {...getRootProps()}
                className={`min-h-32 border-2 border-dashed rounded-lg flex items-center justify-center flex-col gap-2 p-6 cursor-pointer transition-colors ${
                  addedFiles.length > 0
                    ? 'bg-blue-50 border-blue-300'
                    : 'bg-gray-50 border-gray-300 hover:bg-gray-100'
                }`}
              >
                <input {...getInputProps()} />
                <Upload className="w-8 h-8 text-gray-400" />
                <div className="text-center">
                  <p className="text-sm font-medium text-gray-700">
                    {addedFiles.length > 0
                      ? addedFiles[0].name
                      : 'Upload supporting document (optional)'}
                  </p>
                  <p className="text-xs text-gray-500">
                    Drag and drop a file here, or click to select
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    Supports PDF, DOC, DOCX, JPG, PNG, JPEG
                  </p>
                </div>
              </div>

              {addedFiles.length > 0 && (
                <div className="mt-4 space-y-2">
                  <Label className="text-sm font-medium">Selected File:</Label>
                  <FileCard
                    filepath={addedFiles[0].name}
                    file_extension={addedFiles[0].type}
                    handleDelete={() => removeFile()}
                  />
                </div>
              )}
            </div>
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="notes"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Notes (Optional)
            </Label>
            <Textarea
              id="notes"
              placeholder="Additional notes or comments"
              rows={3}
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
            />
          </div>

          <div>
            <Label
              htmlFor="next_review_date"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Next Review Date <span className="text-red-500">*</span>
            </Label>
            <Calendar
              selectedDate={nextReviewDate}
              onDateChange={(date) => {
                if (date) {
                  setNextReviewDate(
                    moment(date as string).format('YYYY-MM-DD'),
                  );
                } else {
                  setNextReviewDate('');
                }
                handleFieldBlur('nextReviewDate');
              }}
              allowPastDates={false}
              className={`mt-2 ${
                touched.nextReviewDate && validationErrors.nextReviewDate
                  ? 'border-red-300'
                  : ''
              }`}
            />
            {touched.nextReviewDate && (
              <ErrorMessage message={validationErrors.nextReviewDate} />
            )}
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t mt-6">
          <SecondaryButton text="Cancel" size="medium" onClick={onClose} />
          <PrimaryButton
            text="Save"
            size="medium"
            isLoading={isLoading || isUploading}
            disabled={!isFormValid || isLoading || isUploading}
            onClick={handleSave}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AssignSkillModal;
