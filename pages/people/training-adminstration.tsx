import { Check, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

import DeleteIcon from '@/assets/outline/delete';
import EditIcon from '@/assets/outline/edit';
import PlusIcon from '@/assets/outline/plus';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/common/accordion';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import { ICategory } from '@/interfaces/category';
import { IDepartment } from '@/interfaces/department';
import { IProcess } from '@/interfaces/process';
import { hasAccess } from '@/utils/roleAccessConfig';
import { cn } from '@/utils/styleUtils';

import Breadcrumb from '@/components/common/breadcrumb';
import LinkButton from '@/components/common/button/linkButton';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import Loader from '@/components/common/loader';
import DeleteModal from '@/components/common/modals/deleteModal';
import SideBarWrapper from '@/components/common/sidebar/layout';

type IData = {
  value: string;
  id: string;
};
const TrainingAdministrator = () => {
  const [category, setCategory] = React.useState<IData[]>([]);
  const [department, setDepartment] = React.useState<IData[]>([]);
  const [process, setProcess] = React.useState<IData[]>([]);
  const { accessToken, user } = useAuthStore();

  const {
    data: categories,
    // error: categoriesError,
    reFetch: reFetchCategories,
    isLoading: categoryLoading,
  } = useFetch<{ records: ICategory[] }>(
    accessToken,
    `training/categories`,
    {},
  );

  useEffect(() => {
    if (categories) {
      setCategory(
        categories.records.map((item) => ({ value: item.name, id: item.id })),
      );
    }
  }, [categories]);

  const breadcrumbData = [
    {
      name: 'Training Hub',
      link: '/people/training',
    },
    {
      name: 'Training Administration',
      link: '#',
    },
  ];

  return (
    <SideBarWrapper>
      <div className="flex flex-col flex-1">
        <div className=" my-5">
          <div>
            <Breadcrumb data={breadcrumbData} />
          </div>
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 ">
            Training Administration
          </div>
        </div>

        <div>
          <Accordion type="multiple" className="w-full">
            <AccordionItem value="item-1" className="">
              <AccordionTrigger>
                View all Category{' '}
                {category.length > 0 ? `(${category.length})` : ''}
              </AccordionTrigger>
              <AccordionContent className="p-5">
                {categoryLoading ? (
                  <Loader className="h-[150px]" />
                ) : (
                  <div className="grid grid-cols-2 gap-5">
                    {category.map((item, index) => (
                      <EditedInput
                        key={index}
                        data={item}
                        title="category"
                        setState={setCategory}
                        index={index}
                        refetch={reFetchCategories}
                      />
                    ))}
                    {hasAccess(AccessActions.IsDocumentAdmin, user) && (
                      <AddCategory
                        text="Add Category"
                        onClick={() =>
                          setCategory((pre) => [...pre, { value: '', id: '' }])
                        }
                      />
                    )}
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </SideBarWrapper>
  );
};

const mapUrl = (title: string) => {
  switch (title) {
    case 'category':
      return 'training/categories';
    default:
      break;
  }
};

const EditedInput = ({
  data,
  title,
  setState,
  index,
  refetch,
}: {
  data: { value: string; id: string };
  title: string;
  setState: React.Dispatch<React.SetStateAction<IData[]>>;
  index: number;
  refetch: () => void;
}) => {
  const [isEdit, setIsEdit] = React.useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const [name, setName] = useState(data.value || '');
  const [originalValue, setOriginalValue] = useState(data.value || '');
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const { postData, response, error: postError } = usePost<{ id: string }>();
  const {
    deleteData,
    response: deletedResponse,
    error: deleteError,
    isLoading: deleteLoading,
  } = useDelete();
  const {
    putData,
    response: putResponse,
    error: putError,
  } = usePut<{ ids: string[] }, { id: string; name: string }[]>();

  const { accessToken } = useAuthStore();
  React.useEffect(() => {
    if (isEdit) {
      inputRef.current?.focus();
      // Store original value when entering edit mode
      setOriginalValue(data.value);
    }
  }, [isEdit, data.value]);

  // Accessiblity ENTER/ESC key
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isSubmitting) {
      handleSubmit(title);
    } else if (e.key === 'Escape' && !isSubmitting) {
      handleCancel();
    }
  };

  const handleSubmit = async (title: string) => {
    if (name === originalValue) {
      setIsEdit(false);
      return;
    }

    if (name.trim() === '') {
      if (data.id === '') {
        setState((pre) => pre.filter((_, i) => i !== index));
      } else {
        setName(originalValue); // For existing, revert to original value
      }
      setIsEdit(false);
      return;
    }

    setIsSubmitting(true);
    const url = mapUrl(title);

    if (!url) {
      setIsSubmitting(false);
      return;
    }

    try {
      if (data.id === '') {
        const body = { name };
        await postData(accessToken as string, url, body);
      } else {
        const body = [{ name, id: data.id }];
        await putData(accessToken as string, url, body);
      }
    } catch (error) {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (response) {
      setState((pre) => {
        const newState = [...pre];
        newState[index] = {
          ...newState[index],
          id: response.id,
          value: name,
        };
        return newState;
      });
      setOriginalValue(name);
      setIsSubmitting(false);
      setIsEdit(false);
      refetch();
    } else if (putResponse) {
      setState((pre) => {
        const newState = [...pre];
        newState[index] = {
          ...newState[index],
          value: name,
        };
        return newState;
      });
      setOriginalValue(name);
      setIsSubmitting(false);
      setIsEdit(false);
    } else if (postError != null) {
      setIsSubmitting(false);
      setIsEdit(true);
    } else if (
      putError != null &&
      typeof putError === 'object' &&
      Object.keys(putError).length > 0
    ) {
      setIsSubmitting(false);
      setIsEdit(true);
    }
  }, [response, putResponse, postError, putError]);

  useEffect(() => {
    if (data.value === '' && data.id === '') {
      setIsEdit(true);
      setOriginalValue('');
    }
    if (!isEdit && !isSubmitting) {
      setName(data.value);
      setOriginalValue(data.value);
    }
  }, [data, isEdit, isSubmitting]);

  useEffect(() => {
    if (deletedResponse && !deleteError) {
      refetch();
      setOpenDeleteModal(false);
    } else if (deleteError) {
      setOpenDeleteModal(false);
    }
  }, [deletedResponse, deleteError]);

  const handleDelete = async () => {
    const url = mapUrl(title);
    if (url && data.id) {
      try {
        await deleteData(accessToken as string, `${url}/${data.id}`);
      } catch (error) {
        console.error('Error deleting item:', error);
      }
    }
  };

  const handleCancel = () => {
    setName(originalValue);
    setIsEdit(false);

    if (data.id === '' && originalValue === '') {
      setState((pre) => pre.filter((_, i) => i !== index));
    }
  };

  return (
    <div className="flex items-center gap-2.5 flex-1">
      <Input
        placeholder={'Add ' + title}
        value={name}
        containerClass="flex-1"
        disabled={!isEdit || isSubmitting}
        ref={inputRef}
        onChange={(e) => setName(e.target.value)}
        onKeyDown={handleKeyDown}
      />
      {isEdit ? (
        <>
          <div
            className={cn(
              'h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer',
              name === originalValue || isSubmitting || name.trim() === ''
                ? 'cursor-not-allowed opacity-50'
                : '',
            )}
            onClick={() =>
              !isSubmitting &&
              name.trim() !== '' &&
              name !== originalValue &&
              handleSubmit(title)
            }
          >
            <Check className="h-5 w-5" />
          </div>
          <div
            className={cn(
              'h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer',
              isSubmitting ? 'cursor-not-allowed opacity-50' : '',
            )}
            onClick={() => !isSubmitting && handleCancel()}
          >
            <X className="h-5 w-5" />
          </div>
        </>
      ) : (
        <>
          <div
            className="h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer"
            onClick={() => {
              setIsEdit(true);
            }}
          >
            <EditIcon />
          </div>
          <Dialog open={openDeleteModal} onOpenChange={setOpenDeleteModal}>
            <DialogTrigger asChild>
              <div className="h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer">
                <DeleteIcon height="20" width="20" />
              </div>
            </DialogTrigger>
            <DeleteModal
              title={'Delete '}
              infoText={'Are you sure you want to delete "' + data.value + '"'}
              btnText={'Delete'}
              onClick={handleDelete}
              btnLoading={deleteLoading}
            />
          </Dialog>
        </>
      )}
    </div>
  );
};

const AddCategory = ({
  text,
  onClick,
}: {
  text: string;
  onClick?: () => void;
}) => {
  return (
    <div className="flex items-center">
      <LinkButton
        size="large"
        text={text}
        icon={<PlusIcon />}
        iconPosition="left"
        onClick={onClick}
      />
    </div>
  );
};

export default TrainingAdministrator;
