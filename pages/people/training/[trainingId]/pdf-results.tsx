import React from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/common/sidebar/layout';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { CheckCircle, Download, Calendar, FileText, Clock } from 'lucide-react';
import { useAuthStore } from '@/globalProvider/authStore';
import Link from 'next/link';
import Image from 'next/image';
import BackButton from '@/assets/backButton.svg';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import { DetailsText } from '@/components/common/infoDetail';
import SecondaryButton from '@/components/common/button/secondaryButton';

interface PdfCompletionResult {
  id: string;
  trainingTitle: string;
  completedAt: string;
  submissionNotes: string;
  downloadCount: number;
  status: 'completed';
  fileName: string;
  category: string;
  duration: number;
}

const PdfResults = () => {
  const router = useRouter();
  const { trainingId } = router.query;
  const { accessToken } = useAuthStore();

  // Mock data - replace with actual API call
  const resultData: PdfCompletionResult = {
    id: trainingId as string,
    trainingTitle: 'Emergency Response Procedures Manual',
    completedAt: '2024-01-25T14:30:00Z',
    submissionNotes:
      'Reviewed all sections thoroughly. Have questions about the new evacuation routes for Building C.',
    downloadCount: 2,
    status: 'completed',
    fileName: 'Emergency_Response_Manual_v2.1.pdf',
    category: 'Safety',
    duration: 45,
  };

  const breadcrumbData = [
    { name: 'People hub', link: '/people' },
    { name: 'My Trainings', link: '/people/training/my-trainings' },
    { name: resultData.trainingTitle, link: `/people/training/${trainingId}` },
    { name: 'Results', link: '#' },
  ];

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Layout>
      <div className="flex flex-col flex-1">
        <div className="my-5">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-5">
            <Tooltip>
              <TooltipTrigger>
                <Link
                  className="w-10 h-10 flex items-center justify-center  bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                  href={`/people/training/my-trainings`}
                >
                  <Image src={BackButton} alt="" />
                </Link>
              </TooltipTrigger>
              <TooltipContent>
                <div className="text-sm text-dark-300">Back</div>
              </TooltipContent>
            </Tooltip>
            {resultData.trainingTitle}
          </div>
        </div>

        <div className="max-w-4xl">
          {/* Success Header */}
          <div className="bg-green-50 border border-green-200 rounded-lg py-4 px-6 mb-6">
            <div className="flex items-center">
              <CheckCircle className="w-8 h-8 text-green-600 mr-4" />
              <div>
                <h2 className="text-xl font-semibold text-green-800">
                  Training Completed Successfully!
                </h2>
                <p className="text-green-700 mt-1">
                  You have successfully completed the PDF training module.
                </p>
              </div>
            </div>
          </div>

          {/* Training Details */}
          <div className="border border-gray-200 rounded-lg bg-white mb-6">
            <div className="py-4 px-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-dark-300">
                Training Details
              </h2>
            </div>
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <DetailsText
                  label="Training Title"
                  value={resultData.trainingTitle || '-'}
                  newLine
                />
                <DetailsText
                  label="Category"
                  value={resultData.category || '-'}
                  newLine
                />
                <DetailsText
                  label=" Completion Date"
                  value={formatDate(resultData.completedAt) || '-'}
                  newLine
                />
                <DetailsText
                  label=" Duration"
                  value={resultData.duration + ' ' + 'minutes' || '-'}
                  newLine
                />
                <DetailsText
                  label=" Document"
                  value={resultData.fileName || '-'}
                  newLine
                />
                <DetailsText
                  label=" Document"
                  value={resultData.downloadCount.toString() || '-'}
                  newLine
                />
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="border border-gray-200 rounded-lg bg-white">
            <div className="py-4 px-6 border-b border-gray-100">
              <h3 className="text-lg font-medium text-dark-300">Actions</h3>
            </div>
            <div className="py-4 px-6">
              <div className="flex flex-wrap gap-3">
                <PrimaryButton
                  text="Download Certificate"
                  icon={<Download className="w-4 h-4" />}
                  size="medium"
                  onClick={() => {
                    // Implement certificate download
                    console.log('Download certificate');
                  }}
                />
                <SecondaryButton
                  text="Re-download PDF"
                  icon={<FileText className="w-4 h-4" />}
                  size="medium"
                  onClick={() => {
                    // Re-download the original PDF
                    console.log('Re-download PDF');
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default PdfResults;
