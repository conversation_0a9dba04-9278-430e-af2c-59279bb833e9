import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Layout from '@/components/common/sidebar/layout';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import Status from '@/components/common/status';
import Tabs from '@/components/common/tabs';
import AdvancedTable from '@/components/common/advancedTable';
import { Play } from 'lucide-react';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import Loader from '@/components/common/loader';
import { usePost } from '@/hooks/usePost';
import { toast } from 'react-toastify';
import { formatDate } from '@/utils/time';

interface AssignedTraining {
  id: string;
  title: string;
  description: string;
  type: 'quiz' | 'pdf';
  status: 'ongoing' | 'In Progress' | 'completed' | 'past_due';
  dueDate: string;
  assignedDate: string;
  completedDate?: string;
  duration: number;
  passingScore: number;
  category: string;
  progress: number;
  score?: number;
}

const MyTrainings = () => {
  const router = useRouter();
  const { accessToken, user } = useAuthStore();
  const [activeTab, setActiveTab] = useState(0);
  const [filter, setFilter] = useState<
    'all' | 'ongoing' | 'completed' | 'past_due'
  >('all');

  const breadcrumbData = [
    { name: 'People hub', link: '/people' },
    { name: 'My Trainings', link: '#' },
  ];

  const { data, isLoading, error, reFetch } = useFetch<any>(
    accessToken as string,
    `trainings/me/`,
    {},
  );

  const {
    postData,
    isLoading: isPosting,
    response: postResponse,
    error: postError,
  } = usePost<any, Record<string, string>>();

  const trainingData: AssignedTraining[] = data?.records
    ? data.records.map((rec: any) => ({
        id: rec.training.id,
        title: rec.training.title,
        description: rec.training.description,
        type: 'quiz', // fallback if API doesn't provide type
        status: (() => {
          if (rec.status === 'Assigned' || rec.status === 'Not Started')
            return 'ongoing';
          if (rec.status === 'In Progress') return 'In Progress';
          if (rec.status === 'Completed') return 'completed';
          if (rec.status === 'Overdue') return 'past_due';
          return 'ongoing'; // fallback
        })(),
        dueDate: rec.due_date,
        assignedDate: rec.assigned_on,
        passingScore: 70,
        category: 'General',
        progress: (() => {
          if (rec.status === 'Completed') return 100;
          if (rec.status === 'In Progress') return 50; // or get actual progress from API
          return 0;
        })(),
      }))
    : [];

  const handleTabChange = (index: number) => {
    setActiveTab(index);
    const filterMap = ['all', 'ongoing', 'completed', 'past_due'] as const;
    setFilter(filterMap[index]);
  };

  const tabsData = [
    {
      name: `All Trainings (${trainingData.length})`,
      textColor: 'text-dark-100',
      onClick: () => handleTabChange(0),
    },
    {
      name: `Ongoing (${
        trainingData.filter((t) =>
          ['ongoing', 'In Progress'].includes(t.status),
        ).length
      })`,
      textColor: 'text-dark-100',
      onClick: () => handleTabChange(1),
    },
    {
      name: `Completed (${
        trainingData.filter((t) => t.status === 'completed').length
      })`,
      textColor: 'text-dark-100',
      onClick: () => handleTabChange(2),
    },
    {
      name: `Past Due (${
        trainingData.filter((t) => t.status === 'past_due').length
      })`,
      textColor: 'text-[#E05252]',
      onClick: () => handleTabChange(3),
    },
  ];

  const filteredTrainings = trainingData.filter((training) => {
    if (filter === 'all') return true;
    if (filter === 'ongoing')
      return ['ongoing', 'In Progress'].includes(training.status);
    if (filter === 'completed') return training.status === 'completed';
    if (filter === 'past_due') return training.status === 'past_due';
    return true;
  });

  const trainingColumns: any[] = [
    {
      key: 'title',
      label: 'Training Title',
      sortable: true,
      resizable: true,
      width: 450,
      showFilter: false,
      render: (_value: any, row: AssignedTraining) => {
        return (
          <div className="flex flex-col">
            <div className="flex items-center gap-2">
              <span className="text-[#0C66E4] font-semibold cursor-pointer">
                {row.title}
              </span>
              <span className="rounded-full text-xs capitalize font-bold px-2 py-1 bg-gray-100 text-gray-500">
                {row.type.toUpperCase()}
              </span>
            </div>
            {row.description && (
              <span className="text-gray-500 text-xs font-semibold overflow-hidden text-ellipsis">
                {row.description}
              </span>
            )}
          </div>
        );
      },
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      resizable: true,
      showFilter: true,
      render: (_value: any, row: AssignedTraining) => {
        return <Status type={row.status} />;
      },
    },
    {
      key: 'category',
      label: 'Category',
      sortable: true,
      resizable: true,
      showFilter: true,
      render: (_value: any, row: AssignedTraining) => {
        return <span className="text-dark-300">{row.category}</span>;
      },
    },
    {
      key: 'assignedDate',
      label: 'Assigned Date',
      sortable: true,
      resizable: true,
      showFilter: false,
      render: (_value: any, row: AssignedTraining) => {
        return (
          <span className="text-dark-300">{formatDate(row.assignedDate)}</span>
        );
      },
    },
    {
      key: 'dueDate',
      label: 'Due Date',
      sortable: true,
      resizable: true,
      showFilter: false,
      render: (_value: any, row: AssignedTraining) => {
        const isOverdueDate =
          isOverdue(row.dueDate) && row.status !== 'completed';
        return (
          <span
            className={`text-base leading-6 font-medium ${
              isOverdueDate ? 'text-red-600' : 'text-dark-300'
            }`}
          >
            {formatDate(row.dueDate)}
          </span>
        );
      },
    },
    {
      key: 'actions',
      label: 'Actions',
      sortable: false,
      resizable: true,
      width: 200,
      showFilter: false,
      render: (_value: any, row: AssignedTraining) => {
        return getActionButton(row);
      },
    },
  ];

  const handleStartTraining = (training: AssignedTraining) => {
    if (training.type === 'quiz') {
      router.push(`/people/training/${training.id}/take-quiz`);
    } else if (training.type === 'pdf') {
      router.push(`/people/training/${training.id}/take-pdf-test`);
    } else {
      router.push(`/people/training/${training.id}`);
    }
  };

  const handleStartTrainingAction = async (trainingId: string) => {
    await postData(accessToken || '', `trainings/${trainingId}/me/start`, {});
  };

  const isOverdue = (dueDate: string) => {
    return new Date(dueDate) < new Date();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getActionButton = (training: AssignedTraining) => {
    if (training.status === 'completed') {
      return (
        <SecondaryButton
          text="View Results"
          size="medium"
          onClick={() =>
            router.push(`/people/training/${training.id}/take-quiz`)
          }
        />
      );
    }

    if (training.status === 'In Progress') {
      return (
        <PrimaryButton
          text="Continue"
          icon={<Play className="w-4 h-4" />}
          size="medium"
          onClick={() => handleStartTraining(training)}
        />
      );
    }

    return (
      <PrimaryButton
        text="Start Training"
        icon={<Play className="w-4 h-4" />}
        size="medium"
        isLoading={isPosting}
        onClick={() => handleStartTrainingAction(training.id)}
      />
    );
  };

  useEffect(() => {
    if (postResponse) {
      toast.success('Training updated successfully');
      router.push(`/people/training/${postResponse.training.id}/take-quiz`);
    }
    if (postError) {
      toast.error('Oops! Something went wrong');
    }
  }, [postResponse, postError]);

  if (isLoading) {
    return <Loader />;
  }

  return (
    <Layout>
      <div className="flex flex-col flex-1">
        <div className="my-5">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            My Trainings
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="mb-2 mt-2">
          <Tabs
            tabsData={tabsData}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            tabGroupName="my-trainings"
          />
        </div>

        {/* Training Table */}
        <div className="">
          <AdvancedTable
            data={filteredTrainings}
            columns={trainingColumns}
            loading={isLoading}
            pagination={true}
            defaultPageSize={10}
          />
        </div>
      </div>
    </Layout>
  );
};

export default MyTrainings;
