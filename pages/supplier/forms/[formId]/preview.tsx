import { useRouter } from 'next/router';
import React from 'react';

import Layout from '@/components/common/sidebar/layout';
import FormPublicRender from '@/components/supplier/forms/FormPublicRender';
import { useFormStore } from '@/components/supplier/forms/store';

const PreviewPage = () => {
  const router = useRouter();
  const { currentForm } = useFormStore();

  return (
    <Layout>
      <div className="p-6">
        <FormPublicRender
          form={currentForm}
          onSubmit={(payload) => {
            console.log('Preview submit payload (mock):', payload);
            router.back();
          }}
        />
      </div>
    </Layout>
  );
};

export default PreviewPage;
