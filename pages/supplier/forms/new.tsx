import dynamic from 'next/dynamic';
import React, { useEffect } from 'react';

import { useFormStore } from '@/components/supplier/forms/store';

const Builder = dynamic(
  () => import('@/components/supplier/forms/FormBuilderPage'),
  { ssr: false },
);

const NewFormPage = () => {
  const { newForm } = useFormStore();
  useEffect(() => {
    newForm();
  }, []);
  return <Builder />;
};

export default NewFormPage;
