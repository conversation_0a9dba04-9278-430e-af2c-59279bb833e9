import { useRouter } from "next/router";
import { useEffect } from "react";
import { useAuthStore } from "@/globalProvider/authStore";
import Loader from "@/components/common/loader";

const withAuth = (Component: React.FC) => {
  const AuthenticatedComponent: React.FC = (props) => {
    const { accessToken, isLoading, user } = useAuthStore((state) => state);

    const router = useRouter();

    useEffect(() => {
      if (!user?.id || !accessToken) {
        router.push("/login");
      }
    }, [user, accessToken]);

    if (isLoading) {
      return <Loader className="h-40" />;
    }

    return user?.id && accessToken ? <Component {...props} /> : null;
  };

  return AuthenticatedComponent;
};

export default withAuth;
