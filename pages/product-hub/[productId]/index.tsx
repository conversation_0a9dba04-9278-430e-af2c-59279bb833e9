// import { useParams } from "react-router-dom";

import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import Loader from '@/components/common/loader';
import Layout from '@/components/common/sidebar/layout';
import Status from '@/components/common/status';
import Tabs from '@/components/common/tabs';
import InfoTabContent, {
  TProductInfo,
} from '@/components/masterProduct/infoTab';
import LogTabs from '@/components/masterProduct/logsTab';
import ProcessTab from '@/components/masterProduct/processTab/processTab';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePut } from '@/hooks/usePut';
import { hasAccess } from '@/utils/roleAccessConfig';

export const MasterProductStatus = {
  Draft: 'Draft',
  PendingReview: 'Pending review',
  Approved: 'Approved',
};

const ProductView = () => {
  const router = useRouter();
  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);
  const [approvalLoading, setApprovalLoading] = useState(false);

  const {
    data,
    isLoading: productLoading,
    error,
    reFetch,
  } = useFetch<{
    record: TProductInfo;
  }>(
    accessToken,
    router.query.productId ? `products/${router.query.productId}` : undefined,
  );

  const [activeTab, setActiveTab] = useState<number>(0);

  const breadcrumbData = [
    {
      name: 'Product Hub',
      link: '/product-hub',
    },
    {
      name: 'Product View',
      link: '#',
    },
  ];

  const tabsData = [
    { name: 'Info', textColor: 'text-dark-100' },
    { name: 'Process', textColor: 'text-dark-100' },
    { name: 'Logs', textColor: 'text-dark-100' },
  ];

  const { putData } = usePut();

  const SendForReview = async () => {
    try {
      if (data && accessToken) {
        setApprovalLoading(true);
        await putData(accessToken, `products/${data?.record?.id}`, {
          product_info: { status: MasterProductStatus.PendingReview },
        });
        setApprovalLoading(false);
        reFetch();
      }
    } catch (error) {
      setApprovalLoading(false);
      console.error('error', error);
    }
  };

  const HandleApprove = async () => {
    try {
      if (data && accessToken) {
        setApprovalLoading(true);
        await putData(accessToken, `products/${data?.record?.id}`, {
          product_info: { status: MasterProductStatus.Approved },
        });
        setApprovalLoading(false);
        reFetch();
      }
    } catch (error) {
      setApprovalLoading(false);
      console.error('error', error);
    }
  };

  useEffect(() => {
    if (router.query.tab) {
      if (router.query.tab === 'info') {
        setActiveTab(0);
      } else if (router.query.tab === 'process') {
        setActiveTab(1);
      } else if (router.query.tab === 'logs') {
        setActiveTab(2);
      } else {
        setActiveTab(0);
      }
    }
  }, [router.query.tab]);

  return (
    <>
      <Layout>
        <div className="flex flex-col flex-1">
          <div className=" my-5">
            <div className="relative">
              <Breadcrumb data={breadcrumbData} />
              <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5 relative">
                {data?.record.name}
                {data?.record.status && (
                  <Status type={data?.record.status.toLowerCase()} />
                )}
              </div>
            </div>
            {productLoading ? (
              ''
            ) : (
              <div className="mt-4  flex justify-between items-center">
                <Tabs
                  tabsData={tabsData}
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                />

                <div>
                  {/* {hasAccess(
                    AccessActions.CanEditSpecificProduct,
                    user,
                    data?.record.assignees?.some(
                      (assignee) => assignee.id === user?.id,
                    )
                      ? data?.record.assignees?.some(
                          (assignee) => assignee.id === user?.id,
                        )
                      : false,
                  ) &&
                    data?.record?.status === MasterProductStatus.Draft && (
                      <div className="y-2 text-sm font-medium">
                        <PrimaryButton
                          onClick={SendForReview}
                          text="Send for review"
                          size="medium"
                          isLoading={approvalLoading}
                        />
                      </div>
                    )} */}
                  {/* {hasAccess(
                    AccessActions.CanEditSpecificProduct,
                    user,
                    hasAccess(
                      AccessActions.CanEditSpecificProduct,
                      user,
                      data?.record.approvers?.some(
                        (approver) => approver.id === user?.id,
                      )
                        ? data?.record.approvers?.some(
                            (approver) => approver.id === user?.id,
                          )
                        : false,
                    ),
                  ) &&
                    data?.record?.status ===
                      MasterProductStatus.PendingReview && (
                      <div className="y-2 text-sm font-medium">
                        <PrimaryButton
                          onClick={HandleApprove}
                          text="Approve"
                          size="medium"
                          isLoading={approvalLoading}
                        />
                      </div>
                    )} */}
                </div>
              </div>
            )}
          </div>
          {productLoading ? (
            <Loader className="h-[400px]" />
          ) : (
            <div className=" flex-1">
              {activeTab === 0 && data?.record ? (
                <InfoTabContent data={data?.record} reFetch={reFetch} />
              ) : activeTab === 1 ? (
                <>
                  <ProcessTab
                    data={data?.record}
                    reFetchProductData={reFetch}
                  />
                </>
              ) : activeTab === 2 ? (
                <>
                  <LogTabs />
                </>
              ) : (
                ''
              )}
            </div>
          )}
        </div>
      </Layout>
    </>
  );
};

export default ProductView;
