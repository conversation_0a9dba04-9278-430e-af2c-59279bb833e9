import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { DialogContent, DialogTrigger } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import CreateProductModal from '@/components/masterProduct/modals/createProductModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TProductData } from '@/interfaces/masterProduct';
import { hasAccess } from '@/utils/roleAccessConfig';
import { getValueOrDefault } from '@/utils/table';
import { formatDate } from '@/utils/time';
import { Dialog } from '@radix-ui/react-dialog';

const MasterProduct = () => {
  const { accessToken, user } = useAuthStore();

  const { data, isLoading, error, reFetch } = useFetch(accessToken, 'products');
  const [createProduct, setCreateProduct] = useState(false);
  const [editProduct, setEditProduct] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<
    undefined | TProductData
  >(undefined);
  const router = useRouter();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const { deleteData, response: onDelete } = useDelete();

  useEffect(() => {
    if (onDelete) {
      setShowDeleteModal(false);
      reFetch();
    }
  }, [onDelete]);

  const handleEdit = (rowData: TProductData) => {
    setSelectedProduct(rowData);
    setEditProduct(true);
  };

  const handleDeleteModal = (rowData: TProductData) => {
    setSelectedProduct(rowData);
    setShowDeleteModal(true);
  };

  const handleDelete = () => {
    async function fetch() {
      await deleteData(accessToken, `products/${selectedProduct?.id}`);
    }
    fetch();
  };

  const getProductColumns = useCallback(() => {
    const productColumns: any = [
      {
        headerName: 'ID',
        field: 'product_id',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: any) => {
          return params.value;
        },
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'product_id'),
        filter: false,
      },
      {
        headerName: 'Product name',
        field: 'name',
        resizable: true,
        valueFormatter: (params: any) => getValueOrDefault(params.data, 'name'),
        filter: false,
      },
      {
        headerName: 'Assignees',
        field: 'assignee.full_name',
        filter: 'agMultiColumnFilter',
        sortable: false,
        filterValueGetter: (params: any) => {
          return params?.data?.assignees
            ? params?.data?.assignees?.map((assignee: any, index: number) => {
                return `${assignee.full_name}`;
              })
            : '-';
        },
        getQuickFilterText: (params: any) => {
          return params?.data?.assignees?.map((process: any) => {
            return `${process.full_name}`;
          });
        },
        valueFormatter: (params: any) => {
          return params?.data?.assignees
            ? params?.data?.assignees?.map((process: any) => {
                return `${process.full_name}`;
              })
            : '-';
        },
      },
      {
        headerName: 'Approvers',
        field: 'approver.full_name',
        filter: 'agMultiColumnFilter',
        sortable: false,
        filterValueGetter: (params: any) => {
          return params?.data?.approvers
            ? params?.data?.approvers?.map((approver: any, index: number) => {
                return `${approver.full_name}`;
              })
            : '-';
        },
        getQuickFilterText: (params: any) => {
          return params?.data?.approvers?.map((approver: any) => {
            return `${approver.full_name}`;
          });
        },
        valueFormatter: (params: any) => {
          return params?.data?.approvers
            ? params?.data?.approvers?.map((approver: any) => {
                return `${approver.full_name}`;
              })
            : '-';
        },
      },
      {
        headerName: 'Status',
        field: 'status',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'status'),
        filter: false,
      },
      {
        headerName: 'Version',
        field: 'version',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'version'),
        filter: false,
      },
      {
        headerName: 'Launch Date',
        field: 'launch_date',
        resizable: true,
        valueFormatter: (params: any) =>
          formatDate(getValueOrDefault(params.data, 'launch_date'), false),
        filter: false,
      },
    ];

    if (hasAccess(AccessActions.CanAddOrEditProducts, user)) {
      productColumns.push({
        headerName: 'Manage',
        field: 'manage',
        cellRenderer: (params: any) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={handleEdit}
            handleDelete={handleDeleteModal}
            hideDelete={
              !hasAccess(AccessActions.CanDeleteSpecificProduct, user)
            }
          />
        ),
        width: 100,
        pinned: 'right',
        filter: false,
      });
    }

    return productColumns;
  }, [user]);

  const breadcrumbData = [
    {
      name: 'Product Hub',
      link: '/product-hub',
    },
  ];

  return (
    <Layout>
      {editProduct && selectedProduct && (
        <Dialog
          open={editProduct}
          onOpenChange={() => {
            setEditProduct(false);
          }}
        >
          <CreateProductModal
            edit={true}
            productData={selectedProduct}
            reFetch={reFetch}
            setOpenEdit={setEditProduct}
          />
        </Dialog>
      )}
      {showDeleteModal && (
        <Dialog
          open={showDeleteModal}
          onOpenChange={() => setShowDeleteModal(false)}
        >
          <DeleteModal
            title={`Delete Product`}
            infoText={`Are you sure you want to delete this product?`}
            btnText={'Delete'}
            onClick={handleDelete}
          />
        </Dialog>
      )}

      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Product Hub
          </div>
        </div>
      </div>
      <div className="mb-5">
        <CommonTable
          data={data}
          columnDefs={getProductColumns()}
          handleRowClick={(e) => {
            router.push(`product-hub/${e.data?.id}`);
          }}
          isLoading={isLoading}
          searchRightSideElement={
            hasAccess(AccessActions.CanAddOrEditProducts, user) ? (
              <div className="flex gap-4">
                <Dialog open={createProduct} onOpenChange={setCreateProduct}>
                  <DialogTrigger asChild>
                    <PrimaryButton
                      text="Add Product"
                      buttonClasses="!px-5 !py-2"
                      icon={<PlusIcon color="white" />}
                    />
                  </DialogTrigger>
                  <DialogContent>
                    {' '}
                    <CreateProductModal
                      edit={false}
                      reFetch={reFetch}
                      setOpenEdit={setCreateProduct}
                    />
                  </DialogContent>
                </Dialog>
              </div>
            ) : (
              <></>
            )
          }
        />
      </div>
    </Layout>
  );
};

export default MasterProduct;
