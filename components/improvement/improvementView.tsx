import axios from 'axios';
import _ from 'lodash';
import moment from 'moment';
import { useParams } from 'next/navigation';
import React, { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { set, z } from 'zod';

import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
import { ICapaInfo } from '@/interfaces/improvement';
import { IAttachment } from '@/interfaces/misc';
import { IUser } from '@/interfaces/user';

import Breadcrumb from '../common/breadcrumb';
import PrimaryButton from '../common/button/primaryButton';
import TertiaryButton from '../common/button/tertiaryButton';
import Calendar from '../common/calendar';
import { Checkbox } from '../common/checkbox';
import { Dialog, DialogTrigger } from '../common/dialog';
import { FileCard } from '../common/fileCard';
import { Input } from '../common/input';
import Loader from '../common/loader';
import { IOption, ReactSelectMulti } from '../common/multiSelectInput';
import Layout from '../common/sidebar/layout';
import Status from '../common/status';
import { Textarea } from '../common/textarea';
import FeedbackModal from './modal/feedbackModal';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';

export const createDocumentSchema = {
  corrective_action_description: z
    .string()
    .nonempty('Corrective action is required'),
};

interface IFormData {
  description: string;
  corrective_action_description: string;
  corrective_action_due_date: string;
  corrective_action_verification_date: string;
  preventive_action_description: string;
  preventive_action_verification_date: string;
  preventive_action_due_date: string;
  corrective_action_users: IOption[];
  corrective_action_verification_users: IOption[];
  preventive_action_users: IOption[];
  preventive_action_verification_users: IOption[];
  root_cause: string;
}

const ImprovementView = () => {
  const { accessToken, user } = useAuthStore();
  const params = useParams();
  const [correctiveActionFile, setCorrectiveActionFile] =
    useState<IAttachment[]>();
  const [preventiveActionFile, setPreventiveActionFile] =
    useState<IAttachment[]>();
  const [preventiveFileLoading, setPreventiveFileLoading] = useState(false);
  const [correctiveFileLoading, setCorrectiveFileLoading] = useState(false);
  const [openFeedbackModal, setOpenFeedbackModal] = useState(false);
  const { data, isLoading, error, reFetch } = useFetch<{ record: ICapaInfo }>(
    accessToken as string,
    `capas/${params?.capaId}`,
    {},
  );
  const { data: users } = useFetch<{ records: IUser[] }, { can_edit: boolean }>(
    accessToken as string,
    `users`,
    {
      can_edit: true,
    },
  );
  const {
    putData,
    response: updateResponse,
    isLoading: updateLoading,
    error: updateError,
  } = usePut();

  const {
    postData,
    response: postDataResponse,
    isLoading: postDataLoading,
    error: postDataError,
  } = usePost();

  const userData = users?.records?.map((e) => ({
    label: e.full_name,
    value: e.id,
  })) as IOption[];
  const IsAuditee = data?.record.audit_info.auditees.find(
    (e) => e.id === user?.id,
  );

  const IsAuditor = data?.record.audit_info.auditors.find(
    (e) => e.id === user?.id,
  );
  const [formData, setFormData] = useState<IFormData>({
    description: '',
    corrective_action_description: '',
    corrective_action_due_date: '',
    corrective_action_verification_date: '',
    preventive_action_description: '',
    preventive_action_verification_date: '',
    preventive_action_due_date: '',
    corrective_action_users: [],
    corrective_action_verification_users: [],
    preventive_action_users: [],
    preventive_action_verification_users: [],
    root_cause: '',
  });
  const [isChecked, setIsChecked] = useState(false);
  const { startValidation } = useValidators({
    schemas: createDocumentSchema,
    values: formData,
  });

  const onDropCorrective = useCallback(
    (acceptedFiles: File[]) => {
      handleFilesUpload(
        acceptedFiles,
        setCorrectiveActionFile,
        setCorrectiveFileLoading,
      );
    },
    [data, accessToken],
  );
  const onDropPreventive = useCallback(
    (acceptedFiles: File[]) => {
      handleFilesUpload(
        acceptedFiles,
        setPreventiveActionFile,
        setPreventiveFileLoading,
      );
    },
    [data, accessToken],
  );

  const breadcrumbData = [
    {
      name: 'Improvement Hub',
      link: '/improvement',
    },
    {
      name: data?.record.audit_info.audit.name || '',
      link: '#',
    },
  ];

  const {
    getRootProps: getRootPropsCorrective,
    getInputProps: getInputPropsCorrective,
  } = useDropzone({
    onDrop: onDropCorrective,
    disabled: correctiveFileLoading || !IsAuditee,
    multiple: true,
  });

  const {
    getRootProps: getRootPropsPreventive,
    getInputProps: getInputPropsPreventive,
  } = useDropzone({
    onDrop: onDropPreventive,
    disabled: preventiveFileLoading || !IsAuditee,
    multiple: true,
  });

  const handleFilesUpload = useCallback(
    (
      acceptedFiles: File[],
      setAddedFiles: React.Dispatch<
        React.SetStateAction<IAttachment[] | undefined>
      >,
      setUploading: React.Dispatch<React.SetStateAction<boolean>>,
    ) => {
      const formData = new FormData();
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const auditId = data?.record?.audit_info?.audit.id;
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=audit_hub&sub_path=/${auditId}/capa`;

      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
        onUploadProgress: () => {
          setUploading(true);
        },
      };
      acceptedFiles.forEach((acceptedFile) => {
        formData.append('file', acceptedFile as unknown as Blob);
        axios.post(url, formData, config).then((response) => {
          setAddedFiles((prev) => [
            ...(prev ?? []),
            {
              file_path: response.data.file_path,
              file_extension: response.data.file_ext,
            },
          ]);
          setUploading(false);
        });
      });

      setUploading(false);
    },
    [data?.record.audit_info, accessToken],
  );

  const handleDelete = (
    filepath: string,
    addedFiles: IAttachment[] | undefined,
    setAddedFiles: React.Dispatch<
      React.SetStateAction<IAttachment[] | undefined>
    >,
  ) => {
    let updatedFiles = [...(addedFiles ?? [])];
    updatedFiles = updatedFiles.filter((file) => file.file_path !== filepath);
    setAddedFiles(updatedFiles);
  };

  const handleSubmit = async () => {
    const { hasValidationErrors } = await startValidation();

    if (!hasValidationErrors) {
      const body = {
        capa_info: {
          status: 'Open',
          root_cause: formData.root_cause || null,
          corrective_action_description: formData.corrective_action_description,

          corrective_action_due_date: formData.corrective_action_due_date
            ? moment(formData.corrective_action_due_date).format('YYYY-MM-DD')
            : null,
          corrective_action_verification_date:
            formData.corrective_action_verification_date
              ? moment(formData.corrective_action_verification_date).format(
                  'YYYY-MM-DD',
                )
              : null,

          preventive_action_description:
            formData.preventive_action_description || null,
          preventive_action_due_date: formData.preventive_action_due_date
            ? moment(formData.preventive_action_due_date).format('YYYY-MM-DD')
            : null,
          preventive_action_verification_date:
            formData.preventive_action_verification_date
              ? moment(formData.preventive_action_verification_date).format(
                  'YYYY-MM-DD',
                )
              : null,
        },

        corrective_action_users: formData.corrective_action_users
          ? formData.corrective_action_users.map((option) => option.value)
          : null,

        corrective_action_verification_users:
          formData.corrective_action_verification_users
            ? formData.corrective_action_verification_users.map(
                (option) => option.value,
              )
            : null,

        corrective_action_documents: correctiveActionFile
          ? correctiveActionFile.flat()
          : null,

        preventive_action_users: formData.preventive_action_users
          ? formData.preventive_action_users.map((option) => option.value)
          : null,

        preventive_action_verification_users:
          formData.preventive_action_verification_users
            ? formData.preventive_action_verification_users.map(
                (option) => option.value,
              )
            : null,

        preventive_action_documents: preventiveActionFile
          ? preventiveActionFile.flat()
          : null,
      };

      async function fetch() {
        await putData(accessToken as string, `capas/${params?.capaId}`, body);
      }
      fetch();

      if (isChecked) {
        notifyAuditor();
      }
    }
  };

  const notifyAuditor = async () => {
    async function fetch() {
      await postData(
        accessToken as string,
        `capas/${params?.capaId}/notify-auditors/`,
        {},
      );
    }
    fetch();
  };

  useEffect(() => {
    if (data) {
      setFormData({
        description: data?.record.description || '',
        corrective_action_description:
          data?.record.corrective_action_description || '',
        corrective_action_due_date:
          data?.record.corrective_action_due_date || '',
        corrective_action_verification_date:
          data?.record.corrective_action_verification_date || '',
        preventive_action_description:
          data?.record.preventive_action_description || '',
        preventive_action_verification_date:
          data?.record.preventive_action_verification_date || '',
        preventive_action_due_date:
          data?.record.preventive_action_due_date || '',
        corrective_action_users:
          (data?.record.corrective_action_users?.map((e) => ({
            label: e.full_name,
            value: e.id,
          })) as IOption[]) || [],
        corrective_action_verification_users:
          (data?.record.corrective_action_verification_users?.map((e) => ({
            label: e.full_name,
            value: e.id,
          })) as IOption[]) || [],
        preventive_action_users:
          (data?.record.preventive_action_users?.map((e) => ({
            label: e.full_name,
            value: e.id,
          })) as IOption[]) || [],
        preventive_action_verification_users:
          (data?.record.preventive_action_verification_users?.map((e) => ({
            label: e.full_name,
            value: e.id,
          })) as IOption[]) || [],
        root_cause: data?.record.root_cause || '',
      });

      setCorrectiveActionFile(data?.record?.corrective_action_documents);
      setPreventiveActionFile(data?.record?.preventive_action_documents);
    }
  }, [data]);

  useEffect(() => {
    if (updateResponse || postDataResponse) {
      reFetch();
    }
  }, [updateResponse, postDataResponse]);

  console.log(!_.isEqual(data?.record, formData));

  return (
    <Layout>
      {isLoading ? (
        <Loader />
      ) : (
        <div className="flex items-start justify-between my-5">
          <div className="flex flex-col w-full">
            <Breadcrumb data={breadcrumbData} />
            <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5 mb-4">
              {data?.record.audit_info.audit.name || ''}
              <Status
                type={
                  data?.record.status.toLowerCase().replace(' ', '_') as string
                }
              />
            </div>
            <div className="flex items-center py-2 px-3 text-base font-medium leading-6 text-dark-300 gap-2 w-full rounded-lg bg-white-150 mb-6">
              <span className="text-grey-300">CAPA ID:</span>
              {data?.record.capa_id || ''}
            </div>

            <div className="mb-4">
              <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                Minor Non-Conformity
              </div>
              <Textarea
                placeholder="Minor Non-Conformity"
                value={formData.description}
                onChange={(e) =>
                  setFormData({ ...formData, description: e.target.value })
                }
                disabled={true}
              />
            </div>

            <div className="mb-4">
              <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                Root Cause
              </div>
              <Textarea
                placeholder="Root Cause"
                value={formData.root_cause}
                onChange={(e) =>
                  setFormData({ ...formData, root_cause: e.target.value })
                }
                disabled={!IsAuditee}
              />
            </div>

            <div className="p-5 rounded-lg bg-white-150 mb-4">
              <div className="mb-4">
                <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                  Corrective Action<span className="text-[#F55D5D]">*</span>
                </div>
                <Textarea
                  placeholder="Corrective Action"
                  value={formData.corrective_action_description}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      corrective_action_description: e.target.value,
                    })
                  }
                  disabled={!IsAuditee}
                />
              </div>

              <div className="mb-4">
                <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                  Corrective action to be taken by
                </div>
                <ReactSelectMulti
                  value={formData.corrective_action_users}
                  options={userData}
                  placeholder="Select employees"
                  onChange={(value) => {
                    setFormData((pre) => ({
                      ...pre,
                      corrective_action_users: value as IOption[],
                    }));
                  }}
                  isDisabled={!IsAuditee}
                />
              </div>

              <div className="mb-4">
                <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                  To be verified by
                </div>
                <ReactSelectMulti
                  value={formData.corrective_action_verification_users}
                  options={userData}
                  placeholder="Select employees"
                  onChange={(value) => {
                    setFormData((pre) => ({
                      ...pre,
                      corrective_action_verification_users: value as IOption[],
                    }));
                  }}
                  isDisabled={!IsAuditee}
                />
              </div>

              <div className="w-full flex gap-4">
                <div className="mb-4 w-1/2">
                  <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                    Corrective action due date
                  </div>
                  <Calendar
                    selectedDate={formData?.corrective_action_due_date}
                    onDateChange={(date) => {
                      if (date) {
                        setFormData((prev) => ({
                          ...prev,
                          corrective_action_due_date: moment(
                            date as string,
                          ).format('YYYY-MM-DD'),
                        }));
                      } else {
                        setFormData((prev) => ({
                          ...prev,
                          corrective_action_due_date: '',
                        }));
                      }
                    }}
                    disabled={!IsAuditee}
                  />
                </div>
                <div className="mb-4 w-1/2">
                  <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                    Verification due date
                  </div>
                  <Calendar
                    selectedDate={formData?.corrective_action_verification_date}
                    onDateChange={(date) => {
                      if (date) {
                        setFormData((prev) => ({
                          ...prev,
                          corrective_action_verification_date: moment(
                            date as string,
                          ).format('YYYY-MM-DD'),
                        }));
                      } else {
                        setFormData((prev) => ({
                          ...prev,
                          corrective_action_verification_date: '',
                        }));
                      }
                    }}
                    disabled={!IsAuditee}
                  />
                </div>
              </div>

              <div>
                <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                  Attach document
                </div>
                <div>
                  <div
                    className=" min-h-28 bg-white-100 border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2"
                    {...getRootPropsCorrective()}
                  >
                    {!(
                      correctiveActionFile?.length &&
                      correctiveActionFile?.length > 0
                    ) && (
                      <div className="text-sm font-medium leading-5 text-[#49474E]">
                        Upload or Drag and drop to upload your file
                      </div>
                    )}

                    <input {...getInputPropsCorrective()} />
                    <div className="flex justify-center items-center flex-wrap gap-2">
                      {correctiveActionFile?.map((file, index) => (
                        <FileCard
                          key={index}
                          filepath={file.file_path}
                          file_extension={file.file_extension}
                          handleDelete={(filepath) =>
                            handleDelete(
                              filepath,
                              correctiveActionFile,
                              setCorrectiveActionFile,
                            )
                          }
                        />
                      ))}
                    </div>
                    <TertiaryButton
                      text={'Upload file'}
                      size="small"
                      isLoading={correctiveFileLoading}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="p-5 rounded-lg bg-white-150 mb-4">
              <div className="mb-4">
                <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                  Preventive Action
                </div>
                <Textarea
                  placeholder="Preventive Action"
                  value={formData.preventive_action_description}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      preventive_action_description: e.target.value,
                    })
                  }
                  disabled={!IsAuditee}
                />
              </div>

              <div className="mb-4">
                <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                  Preventive action taken by
                </div>
                <ReactSelectMulti
                  value={formData.preventive_action_users}
                  options={userData}
                  placeholder="Select employees"
                  onChange={(value) => {
                    setFormData((pre) => ({
                      ...pre,
                      preventive_action_users: value as IOption[],
                    }));
                  }}
                  isDisabled={!IsAuditee}
                />
              </div>

              <div className="mb-4">
                <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                  To be verified by
                </div>
                <ReactSelectMulti
                  value={formData.preventive_action_verification_users}
                  options={userData}
                  placeholder="Select employees"
                  onChange={(value) => {
                    setFormData((pre) => ({
                      ...pre,
                      preventive_action_verification_users: value as IOption[],
                    }));
                  }}
                  isDisabled={!IsAuditee}
                />
              </div>

              <div className="w-full flex gap-4">
                <div className="mb-4 w-1/2">
                  <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                    Preventive action due date
                  </div>

                  <Calendar
                    selectedDate={formData?.preventive_action_due_date}
                    onDateChange={(date) => {
                      if (date) {
                        setFormData((prev) => ({
                          ...prev,
                          preventive_action_due_date: moment(
                            date as string,
                          ).format('YYYY-MM-DD'),
                        }));
                      } else {
                        setFormData((prev) => ({
                          ...prev,
                          preventive_action_due_date: '',
                        }));
                      }
                    }}
                    disabled={!IsAuditee}
                  />
                </div>
                <div className="mb-4 w-1/2">
                  <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                    Verification due date
                  </div>

                  <Calendar
                    selectedDate={formData?.preventive_action_verification_date}
                    onDateChange={(date) => {
                      if (date) {
                        setFormData((prev) => ({
                          ...prev,
                          preventive_action_verification_date: moment(
                            date as string,
                          ).format('YYYY-MM-DD'),
                        }));
                      } else {
                        setFormData((prev) => ({
                          ...prev,
                          preventive_action_verification_date: '',
                        }));
                      }
                    }}
                    disabled={!IsAuditee}
                  />
                </div>
              </div>

              <div>
                <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                  Attach document
                </div>
                <div>
                  <div
                    className=" min-h-28 bg-white-100 border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2"
                    {...getRootPropsPreventive()}
                  >
                    {!(
                      preventiveActionFile?.length &&
                      preventiveActionFile?.length > 0
                    ) && (
                      <div className="text-sm font-medium leading-5 text-[#49474E]">
                        Upload or Drag and drop to upload your file
                      </div>
                    )}

                    <input {...getInputPropsPreventive()} />
                    <div className="flex justify-center items-center flex-wrap gap-2">
                      {preventiveActionFile?.map((file, index) => (
                        <FileCard
                          key={index}
                          filepath={file.file_path}
                          file_extension={file.file_extension}
                          handleDelete={(filepath) =>
                            handleDelete(
                              filepath,
                              preventiveActionFile,
                              setPreventiveActionFile,
                            )
                          }
                        />
                      ))}
                    </div>
                    <TertiaryButton
                      text={'Upload file'}
                      size="small"
                      isLoading={preventiveFileLoading}
                    />
                  </div>
                </div>
              </div>
            </div>

            {data?.record.feedback && (
              <div>
                <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                  Feedback by Auditor
                </div>
                <div className="px-3 py-2 bg-white-150 rounded-lg min-h-24">
                  <div className="text-base font-medium leading-6 text-dark-300 ">
                    {data?.record.feedback}
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-center justify-end gap-8 mt-6">
              {IsAuditee && (
                <>
                  <div className="flex items-center  gap-4">
                    <Checkbox
                      id="notify"
                      checked={isChecked}
                      onCheckedChange={(checked) =>
                        setIsChecked(checked as boolean)
                      }
                    />
                    <label
                      htmlFor="notify"
                      className="text-base font-medium leading-6 text-dark-300"
                    >
                      Notify auditor{' '}
                    </label>
                  </div>
                  <div>
                    <PrimaryButton
                      text="Save"
                      size="large"
                      onClick={() => handleSubmit()}
                      disabled={_.isEqual(
                        {
                          description: data?.record.description || '',
                          corrective_action_description:
                            data?.record.corrective_action_description || '',
                          corrective_action_due_date:
                            data?.record.corrective_action_due_date || '',
                          corrective_action_verification_date:
                            data?.record.corrective_action_verification_date ||
                            '',
                          preventive_action_description:
                            data?.record.preventive_action_description || '',
                          preventive_action_verification_date:
                            data?.record.preventive_action_verification_date ||
                            '',
                          preventive_action_due_date:
                            data?.record.preventive_action_due_date || '',
                          corrective_action_users:
                            (data?.record.corrective_action_users?.map((e) => ({
                              label: e.full_name,
                              value: e.id,
                            })) as IOption[]) || [],
                          corrective_action_verification_users:
                            (data?.record.corrective_action_verification_users?.map(
                              (e) => ({
                                label: e.full_name,
                                value: e.id,
                              }),
                            ) as IOption[]) || [],
                          preventive_action_users:
                            (data?.record.preventive_action_users?.map((e) => ({
                              label: e.full_name,
                              value: e.id,
                            })) as IOption[]) || [],
                          preventive_action_verification_users:
                            (data?.record.preventive_action_verification_users?.map(
                              (e) => ({
                                label: e.full_name,
                                value: e.id,
                              }),
                            ) as IOption[]) || [],
                          root_cause: data?.record.root_cause || '',
                        },
                        formData,
                      )}
                      isLoading={updateLoading}
                    />
                  </div>
                </>
              )}

              {IsAuditor && (
                <>
                  <div>
                    <Dialog
                      open={openFeedbackModal}
                      onOpenChange={setOpenFeedbackModal}
                    >
                      <DialogTrigger>
                        <PrimaryButton
                          text={
                            data?.record.feedback
                              ? 'Edit Feedback'
                              : 'Provide Feedback'
                          }
                        />
                      </DialogTrigger>
                      <FeedbackModal
                        setOpenFeedbackModal={setOpenFeedbackModal}
                        refetch={reFetch}
                        feedback={data?.record?.feedback || null}
                      />
                    </Dialog>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default ImprovementView;
