import { useParams } from "next/navigation";
import React, { useEffect } from "react";

import { Textarea } from "@/components/common/textarea";
import { useAuthStore } from "@/globalProvider/authStore";
import { usePut } from "@/hooks/usePut";

import PrimaryButton from "../../common/button/primaryButton";
import { DialogContent, DialogHeader, DialogTitle } from "../../common/dialog";

interface IProps {
  setOpenFeedbackModal: React.Dispatch<React.SetStateAction<boolean>>;
  refetch: () => void;
  feedback: string | null;
}
const FeedbackModal = ({
  setOpenFeedbackModal,
  refetch,
  feedback: initialFeedback,
}: IProps) => {
  const [feedback, setFeedback] = React.useState("");
  const { putData, response, isLoading } = usePut();

  const param = useParams();
  const { accessToken } = useAuthStore();

  const handleCommentSubmit = () => {
    const body = {
      capa_info: {
        feedback: feedback,
      },
    };

    async function fetch() {
      await putData(accessToken as string, `capas/${param?.capaId}`, body);
    }
    fetch();
  };

  useEffect(() => {
    if (response) {
      setFeedback("");
      setOpenFeedbackModal(false);
      setTimeout(() => {
        refetch();
      }, 2000);
    }
  }, [response]);

  useEffect(() => {
    if (initialFeedback) setFeedback(initialFeedback);
  }, [initialFeedback]);
  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>
          {initialFeedback ? "Edit" : "Provide"} feedback{" "}
        </DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Feedback <span className="text-[#F55D5D]">*</span>
        </div>
        <Textarea
          placeholder="Enter feedback here"
          value={feedback}
          onChange={(e) => setFeedback(e.target.value)}
        />
        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text="Save"
            isLoading={isLoading}
            onClick={handleCommentSubmit}
            disabled={feedback === ""}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default FeedbackModal;
