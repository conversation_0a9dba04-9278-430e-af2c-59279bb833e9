import { useRouter } from 'next/router';

import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { ICapa } from '@/interfaces/improvement';
import { getValueOrDefault } from '@/utils/table';
import { formatDate } from '@/utils/time';

import Breadcrumb from '../common/breadcrumb';
import Layout from '../common/sidebar/layout';
import CommonTable from '../common/table';

interface ColumnParams {
  value: any;
}

interface ValueFormatterParams {
  data: ICapa;
}

interface ColumnDef {
  headerName: string;
  field: string;
  sortable?: boolean;
  resizable?: boolean;
  getQuickFilterText?: (params: ColumnParams) => string;
  valueFormatter?: (params: ValueFormatterParams) => string;
}
const Improvement = () => {
  const { accessToken } = useAuthStore();
  const router = useRouter();

  const breadcrumbData = [
    {
      name: 'Improvement Hub',
      link: '#',
    },
  ];

  const { data, isLoading, error, reFetch } = useFetch<{ records: ICapa }>(
    accessToken as string,
    `capas`,
    {},
  );

  const columnDefs: ColumnDef[] = [
    {
      headerName: 'ID',
      field: 'capa_id',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: ColumnParams) => {
        return params.value;
      },
      valueFormatter: (params: ValueFormatterParams) =>
        getValueOrDefault(params.data, 'capa_id'),
    },
    {
      headerName: 'Description',
      field: 'description',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: ColumnParams) => {
        return params.value;
      },
      valueFormatter: (params: ValueFormatterParams) =>
        getValueOrDefault(params.data, 'description'),
    },
    {
      headerName: 'Status',
      field: 'status',
      sortable: true,
      resizable: true,
    },
    {
      headerName: 'Source',
      field: 'source',
      sortable: true,
      resizable: true,
      valueFormatter: (params: ValueFormatterParams) =>
        String(getValueOrDefault(params.data, 'source'))
          .charAt(0)
          .toUpperCase() +
        String(getValueOrDefault(params.data, 'source')).slice(1),
    },
    {
      headerName: 'Created',
      field: 'created_on',
      resizable: true,
      valueFormatter: (params: ValueFormatterParams) =>
        formatDate(getValueOrDefault(params.data, 'created_on'), false),
    },
  ];

  const handleClick = (id: string) => {
    router.push(`improvement/${id}`);
  };
  return (
    <Layout>
      <div className="flex items-start justify-between my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Improvement Hub
          </div>
        </div>
      </div>
      <div className="mt-5 mb-5">
        <CommonTable
          data={data || []}
          // @ts-expect-error dssd
          columnDefs={columnDefs}
          isLoading={isLoading}
          handleRowClick={(e) => handleClick(e?.data?.id)}
        />
      </div>
    </Layout>
  );
};

export default Improvement;
