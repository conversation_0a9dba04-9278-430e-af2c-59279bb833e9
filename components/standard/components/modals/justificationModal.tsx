import React, { useEffect } from 'react';

import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';

import PrimaryButton from '../../../common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../common/dialog';
import { usePut } from '@/hooks/usePut';
import { toast } from 'react-toastify';

interface IProps {
  clauseId: string;
  clauseType: string;
  setOpenJustificationModal: React.Dispatch<React.SetStateAction<boolean>>;
  refetch: () => void;
}

interface PutResponse {
  message: string;
  [key: string]: any;
}

const JustificationModal = ({
  clauseId,
  clauseType,
  setOpenJustificationModal,
  refetch,
}: IProps) => {
  const [justification, setJustification] = React.useState('');

  const { putData, response, isLoading, error } = usePut<PutResponse>();

  const { accessToken } = useAuthStore();

  const handleSubmit = () => {
    const key = clauseType === 'parent' ? 'clause_id' : 'sub_clause_id';
    const body = {
      [key]: clauseId,
      is_active: false,
      justification,
    };
    async function fetch() {
      await putData(accessToken as string, `clauses/toggle-scope/`, body);
    }
    fetch();
  };

  useEffect(() => {
    if (response) {
      setJustification('');
      setOpenJustificationModal(false);
      toast.success(response?.message);
      refetch();
    }
  }, [response]);

  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>Justification for Out of Scope</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Justification <span className="text-[#F55D5D]">*</span>
        </div>
        <Textarea
          placeholder="Start Typing..."
          value={justification}
          onChange={(e) => setJustification(e.target.value)}
        />
        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text="Submit"
            isLoading={isLoading}
            onClick={handleSubmit}
            disabled={justification === ''}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default JustificationModal;
