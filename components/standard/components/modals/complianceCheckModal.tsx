import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuthStore } from '@/globalProvider/authStore';
import { useRouter } from 'next/router';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/common/dialog';

import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/common/tooltip';

import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Brain,
  FileSearch,
  SpellCheck,
  ClipboardCheck,
  Loader2,
  Upload,
  FileX,
  ChevronDown,
  ChevronRight,
} from 'lucide-react';

interface ProcessingStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  status: 'pending' | 'processing' | 'completed';
  duration?: number;
}

interface DocumentStatus {
  id: string;
  title: string;
  isUploaded: boolean;
  filePath?: string;
  reason?: string;
}

interface ComplianceCheckModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  document_ids: string[]; // Changed to array of document IDs
  standard?: string; // Add standard prop with default being optional
  clause?: any; // Add clause data for API request
}

export const ComplianceCheckModal = ({
  isOpen,
  onOpenChange,
  document_ids,
  standard = 'ISO 9001', // Default to ISO 9001 if not provided
  clause,
}: ComplianceCheckModalProps) => {
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [hasApiError, setHasApiError] = useState<boolean>(false);
  const [downloadProgress, setDownloadProgress] = useState(0);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [steps, setSteps] = useState<ProcessingStep[]>([]);
  const [checkResults, setCheckResults] = useState<any>(null);
  const [documentsData, setDocumentsData] = useState<any[]>([]);
  const [documentStatuses, setDocumentStatuses] = useState<DocumentStatus[]>(
    [],
  );
  const [showMissingDocuments, setShowMissingDocuments] = useState(false);
  const [expandedDocuments, setExpandedDocuments] = useState<Set<number>>(
    new Set(),
  );
  const [expandedDocumentGaps, setExpandedDocumentGaps] = useState<Set<number>>(
    new Set(),
  );
  const { accessToken } = useAuthStore();
  const router = useRouter();

  // Helper functions for compliance UI
  const mapStandardToApiFormat = (standard: string) => {
    const mapping: { [key: string]: string } = {
      'ISO 9001': 'ISO_9001_2015',
      'ISO 14001': 'ISO_14001_2015',
      'ISO 45001': 'ISO_45001_2018',
    };
    return mapping[standard] || 'ISO_9001_2015';
  };

  const getComplianceIcon = (compliance: string) => {
    switch (compliance) {
      case 'compliant':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'non-compliant':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
    }
  };

  const getComplianceColor = (compliance: string) => {
    switch (compliance) {
      case 'compliant':
        return 'border-green-200 text-green-800';
      case 'non-compliant':
        return 'border-red-200 text-red-800';
      default:
        return 'border-yellow-200 text-yellow-800';
    }
  };

  // Handle modal close
  const onClose = () => {
    onOpenChange(false);
  };

  // Handle retry functionality
  const handleRetry = () => {
    setHasApiError(false);
    setError('');
    setCheckResults(null);
    setLoading(false);
    if (documentsData && documentsData.length > 0) {
      setTimeout(() => performComplianceCheck(documentsData), 500);
    }
  };

  // Toggle document accordion
  const toggleDocument = (index: number) => {
    const newExpanded = new Set(expandedDocuments);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedDocuments(newExpanded);
  };

  // Toggle document gaps accordion
  const toggleDocumentGaps = (index: number) => {
    const newExpanded = new Set(expandedDocumentGaps);
    if (newExpanded.has(index)) {
      newExpanded.delete(index);
    } else {
      newExpanded.add(index);
    }
    setExpandedDocumentGaps(newExpanded);
  };

  const processingSteps: ProcessingStep[] = [
    {
      id: 'init',
      title: 'Initializing Octo AI',
      description: 'Starting compliance analysis engine...',
      icon: <Brain className="h-5 w-5" />,
      status: 'pending',
      duration: 1000,
    },
    {
      id: 'fetch-document',
      title: 'Fetching Document',
      description: 'Retrieving and parsing document content...',
      icon: <FileSearch className="h-5 w-5" />,
      status: 'pending',
      duration: 1500,
    },
    {
      id: 'iso-analysis',
      title: 'ISO 9001 Clause Analysis',
      description: 'Analyzing document against ISO 9001:2015 requirements...',
      icon: <ClipboardCheck className="h-5 w-5" />,
      status: 'pending',
      duration: 2500,
    },
    {
      id: 'spell-check',
      title: 'Spell Check & Grammar',
      description: 'Performing linguistic analysis and corrections...',
      icon: <SpellCheck className="h-5 w-5" />,
      status: 'pending',
      duration: 1000,
    },
    {
      id: 'compliance-rating',
      title: 'Compliance Rating',
      description: 'Generating compliance score and recommendations...',
      icon: <CheckCircle className="h-5 w-5" />,
      status: 'pending',
      duration: 1500,
    },
  ];

  // Mock compliance results for UI display
  const mockComplianceResults = {
    overallCompliance: 'partially-compliant',
    score: 75,
    gaps: [
      {
        section: 'Section 3.3',
        issue: 'Missing specific timeline for quarterly reviews',
        severity: 'medium',
        remediation:
          'Add specific dates and calendar schedule for quarterly reviews',
      },
      {
        section: 'Section 4',
        issue: 'No mention of customer requirements consideration',
        severity: 'high',
        remediation:
          'Include requirement to consider customer needs when setting objectives',
      },
    ],
    spellCheckIssues: [
      {
        word: 'quaterly',
        suggestion: 'quarterly',
        location: 'Section 3.3',
      },
    ],
    recommendations: [
      'Add measurable criteria for each objective',
      'Include process for escalating non-conforming objectives',
      'Define roles for objective communication',
    ],
  };

  useEffect(() => {
    if (isOpen && document_ids && document_ids.length > 0) {
      console.log(document_ids, accessToken);
      fetchDocumentsData(document_ids);
    }
  }, [isOpen, document_ids]);

  // Function to get multiple documents data using new API
  const fetchDocumentsData = async (documentIds: string[]) => {
    if (!documentIds || documentIds.length === 0) return;

    try {
      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      const config = {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };

      const response = await axios.post(
        `${baseUrl}/${productVersion}/documents/by-ids`,
        { document_ids: documentIds },
        config,
      );

      if (response.data && response.data.records) {
        setDocumentsData(response.data.records);

        // Analyze document upload status
        const statuses: DocumentStatus[] = response.data.records.map(
          (doc: any) => ({
            id: doc?.id || '',
            title: doc?.title || 'Unknown Document',
            isUploaded: !!doc?.document_version?.file_path,
            filePath: doc?.document_version?.file_path,
            reason: !doc?.document_version?.file_path
              ? 'Document not uploaded'
              : undefined,
          }),
        );

        setDocumentStatuses(statuses);

        // Check if any documents are missing
        const hasMissingDocs = statuses.some((status) => !status.isUploaded);
        setShowMissingDocuments(hasMissingDocs);
      } else {
        setError('Failed to load documents data. Please try again.');
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
      setError('Failed to load documents data. Please try again.');
    }
  };

  useEffect(() => {
    if (isOpen && documentsData && documentsData.length > 0) {
      setSteps(
        processingSteps.map((step) => ({
          ...step,
          status: 'pending' as const,
        })),
      );
      setCurrentStepIndex(0);
      setCheckResults(null);
      setLoading(false);

      // Only start compliance check if no missing documents
      if (!showMissingDocuments) {
        setTimeout(() => performComplianceCheck(documentsData), 500);
      }
    } else {
      // Reset state when modal closes
      setResult('');
      setError('');
      setCheckResults(null);
      setShowMissingDocuments(false);
      setDocumentStatuses([]);
      setHasApiError(false);
    }
  }, [isOpen, documentsData, showMissingDocuments]);

  const performComplianceCheck = async (docsData: any[]) => {
    setLoading(true);
    setResult('');
    setDownloadProgress(0);
    setCheckResults(null);

    try {
      // Check if all required documents are uploaded - this should already be handled by fetchDocumentsData
      const uploadedDocuments = docsData.filter(
        (docData) => !!docData?.document_version?.file_path,
      );

      // STEP 1: Initializing AI
      setSteps((prev) =>
        prev.map((step, index) => ({
          ...step,
          status: index === 0 ? 'processing' : 'pending',
        })),
      );
      setCurrentStepIndex(0);
      await new Promise((resolve) =>
        setTimeout(resolve, processingSteps[0].duration),
      );

      // STEP 2: Fetching Document
      setSteps((prev) =>
        prev.map((step, index) => ({
          ...step,
          status:
            index === 0 ? 'completed' : index === 1 ? 'processing' : 'pending',
        })),
      );
      setCurrentStepIndex(1);

      // Process only uploaded documents
      console.log('Uploaded Documents Data:', uploadedDocuments);
      const documentUrls = [];

      for (const docData of uploadedDocuments) {
        let filePath = docData?.document_version?.file_path;
        const fileExtension =
          docData?.document_version?.file_extension?.toLowerCase() || 'docx';

        // Apply the same logic as in documentViewModalWithSidebar.tsx
        if (fileExtension === 'docx') {
          filePath = filePath.replace('.docx', '.pdf');
        }

        if (fileExtension === 'doc') {
          filePath = filePath.replace('.doc', '.pdf');
        }

        const encodedFilePath = encodeURIComponent(filePath);
        const expiration = '800';

        const orgId =
          typeof window !== 'undefined'
            ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
            : null;

        try {
          // Get presigned URL for each document
          const baseUrl = process.env.NEXT_PUBLIC_URL;
          const productVersion = process.env.NEXT_PUBLIC_VERSION;

          const presignedUrlResponse = await axios.get(
            `${baseUrl}/${productVersion}/file/presigned-url?file_path=${encodedFilePath}&expiration=${expiration}`,
            {
              headers: {
                Authorization: `Bearer ${accessToken}`,
                'Content-Type': 'application/json',
                ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
              },
            },
          );

          if (presignedUrlResponse?.data?.url) {
            documentUrls.push({
              url: presignedUrlResponse.data.url,
              title: docData?.title || 'Unknown Document',
              id: docData?.id,
            });
          }
        } catch (error) {
          console.error(
            `Error getting presigned URL for ${docData?.title}:`,
            error,
          );
        }
      }

      if (documentUrls.length === 0) {
        setError('No valid document URLs found. Please try again.');
        return;
      }

      // Wait for the step duration to complete
      await new Promise((resolve) =>
        setTimeout(
          resolve,
          Math.max(0, (processingSteps[1].duration ?? 1000) - 500),
        ),
      );

      // STEP 3: ISO Analysis
      setSteps((prev) =>
        prev.map((step, index) => ({
          ...step,
          status:
            index <= 1 ? 'completed' : index === 2 ? 'processing' : 'pending',
        })),
      );
      setCurrentStepIndex(2);

      // Wait for the step duration to complete
      await new Promise((resolve) =>
        setTimeout(resolve, processingSteps[2].duration),
      );

      setResult('# Processing\n\nAnalyzing documents for compliance...');

      // Get organization ID
      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      // Make API request to the backend compliance check endpoint
      const baseUrl = process.env.NEXT_PUBLIC_AI_URL;

      const response = await axios.post(
        `${baseUrl}/compliance/check`,
        {
          document_urls: documentUrls.map((doc) => doc.url),
          clause_number: clause?.clause_no || '',
          clause_name: clause?.title || '',
          standard_name: standard,
          organization_id: orgId || '',
          store_document: false,
        },
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
          },
        },
      );

      console.log('Compliance check API response:', response.data);

      // Process response and update UI
      if (response.data) {
        // Update steps to mark the ISO analysis as completed
        setSteps((prev) =>
          prev.map((step, index) => ({
            ...step,
            status:
              index <= 2 ? 'completed' : index === 3 ? 'processing' : 'pending',
          })),
        );

        // Move to next step
        setCurrentStepIndex(3);

        // Complete the final step after a short delay
        await new Promise((resolve) =>
          setTimeout(resolve, processingSteps[3].duration),
        );

        // Mark all steps as completed
        setSteps((prev) =>
          prev.map((step) => ({
            ...step,
            status: 'completed',
          })),
        );

        // Store the structured results from the new backend API response
        setCheckResults({
          documents: docsData.map((doc) => doc?.title || 'Unknown Document'),
          documentIds: docsData.map((doc) => doc?.id || ''),
          result: response.data,
          timestamp: new Date().toISOString(),
          overallCompliance: response.data.overallCompliance || 'unknown',
          score: response.data.aggregatedScore || 0,
          gaps: response.data.aggregatedGaps || [],
          recommendations: response.data.aggregatedRecommendations || [],
          documentResults: response.data.documentResults || [],
          totalDocuments: response.data.totalDocuments || 0,
          clauseNumber: response.data.clause_number || '',
          clauseName: response.data.clause_name || '',
          standardName: response.data.standard_name || '',
        });

        setLoading(false);
      } else {
        throw new Error('Invalid response format from compliance check API');
      }
    } catch (error: any) {
      let errorMessage = 'Unknown error';

      if (error?.response?.data?.error) {
        errorMessage = `${
          error.response.data.error.message || error.response.data.error
        }`;
      } else if (error?.response?.status) {
        errorMessage = `Request failed with status code ${
          error.response.status
        }: ${error.response.statusText || ''}`;
      }

      setResult(`# Error

Failed to perform compliance check: ${errorMessage}

Please try again or contact support if the problem persists.`);
      setHasApiError(true);
      setLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ClipboardCheck className="h-5 w-5" />
            Compliance Check:
            <span className="text-primary-500">
              {documentsData.length > 0
                ? documentsData.length === 1
                  ? documentsData[0]?.title || 'Loading document...'
                  : `${documentsData.length} documents`
                : 'Loading documents...'}
            </span>
          </DialogTitle>
          <DialogDescription>
            Analyzing compliance against {standard} requirements
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Error State */}
          {hasApiError && (
            <div className="flex flex-col items-center justify-center py-12 px-6">
              <div className="text-center space-y-4">
                <div className="w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center">
                  <XCircle className="h-8 w-8 text-red-600" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    Compliance Check Failed
                  </h3>
                  <p className="text-gray-600 mb-4 max-w-md">
                    We encountered an error while analyzing your documents. This
                    could be due to a temporary network issue or server problem.
                    If the issue persists, please contact the admin.
                  </p>
                  {error && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4 text-left">
                      <p className="text-sm text-red-700 font-medium mb-1">
                        Error Details:
                      </p>
                      <p className="text-sm text-red-600">{error}</p>
                    </div>
                  )}
                </div>
                <div className="flex w-full align-middle justify-center">
                  <PrimaryButton
                    onClick={handleRetry}
                    text="Try Again"
                    icon={<Brain className="h-4 w-4" />}
                    buttonClasses="flex items-center gap-2"
                  />
                </div>
              </div>
            </div>
          )}

          {/* Missing Documents Warning */}
          {!hasApiError && showMissingDocuments && (
            <div className="border border-gray-300 rounded-lg bg-gray-50 p-4">
              <div className="flex items-center gap-2 mb-3">
                <FileX className="h-5 w-5 text-gray-600" />
                <h3 className="font-semibold text-gray-800">
                  Missing Required Documents
                </h3>
              </div>

              <p className="text-gray-700 mb-4 text-sm">
                All documents must be uploaded before compliance check can be
                performed.
              </p>

              <div className="space-y-2">
                <h4 className="font-medium text-gray-800 text-sm">
                  Document Status:
                </h4>
                <div className="space-y-2">
                  {documentStatuses.map((docStatus) => (
                    <div
                      key={docStatus.id}
                      className={`flex items-center justify-between p-3 rounded-lg border ${
                        docStatus.isUploaded
                          ? 'border-green-200 bg-green-50'
                          : 'border-red-200 bg-red-100'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        {docStatus.isUploaded ? (
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        ) : (
                          <XCircle className="h-4 w-4 text-red-600" />
                        )}
                        <div>
                          <p
                            className={`font-medium text-sm ${
                              docStatus.isUploaded
                                ? 'text-green-800'
                                : 'text-red-800'
                            }`}
                          >
                            {docStatus.title}
                          </p>
                          {!docStatus.isUploaded && docStatus.reason && (
                            <p className="text-xs text-red-600">
                              {docStatus.reason}
                            </p>
                          )}
                        </div>
                      </div>
                      <div
                        className={`px-2 py-1 text-xs font-semibold rounded-md ${
                          docStatus.isUploaded
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {docStatus.isUploaded ? 'Uploaded' : 'Missing'}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center gap-2">
                  <Upload className="h-4 w-4 text-yellow-600" />
                  <p className="text-sm text-yellow-800 font-medium">
                    Please upload all missing documents to proceed with
                    compliance check.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Processing Steps */}
          {!checkResults && !showMissingDocuments && !hasApiError && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-blue-600" />
                <h3 className="font-semibold">Octo AI Analysis in Progress</h3>
              </div>

              <div className="space-y-3">
                {steps.map((step, index) => (
                  <div
                    key={step.id}
                    className={`flex items-center gap-3 p-3 rounded-lg border transition-all ${
                      step.status === 'processing'
                        ? 'border-blue-200 bg-blue-50'
                        : step.status === 'completed'
                        ? 'border-green-200 bg-green-50'
                        : 'border-gray-200 bg-gray-50'
                    }`}
                  >
                    <div
                      className={`${
                        step.status === 'processing'
                          ? 'text-blue-600'
                          : step.status === 'completed'
                          ? 'text-green-600'
                          : 'text-gray-400'
                      }`}
                    >
                      {step.status === 'processing' ? (
                        <Loader2 className="h-5 w-5 animate-spin" />
                      ) : step.status === 'completed' ? (
                        <CheckCircle className="h-5 w-5" />
                      ) : (
                        step.icon
                      )}
                    </div>
                    <div className="flex-1">
                      <p
                        className={`font-medium ${
                          step.status === 'completed'
                            ? 'text-green-800'
                            : step.status === 'processing'
                            ? 'text-blue-800'
                            : 'text-gray-600'
                        }`}
                      >
                        {step.title}
                      </p>
                      <p
                        className={`text-sm ${
                          step.status === 'completed'
                            ? 'text-green-600'
                            : step.status === 'processing'
                            ? 'text-blue-600'
                            : 'text-gray-500'
                        }`}
                      >
                        {step.description}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Compliance Results */}
          {checkResults && (
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                {getComplianceIcon(checkResults.overallCompliance)}
                <h3 className="font-semibold">Compliance Results</h3>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <p className="font-medium">Overall Compliance</p>
                    <p className="text-sm text-gray-600">
                      Score: {checkResults.score}% •{' '}
                      {checkResults.totalDocuments ||
                        checkResults.documents.length}{' '}
                      document
                      {(checkResults.totalDocuments ||
                        checkResults.documents.length) > 1
                        ? 's'
                        : ''}{' '}
                      analyzed
                    </p>
                    {checkResults.clauseNumber && (
                      <p className="text-xs text-gray-500 mt-1">
                        Clause {checkResults.clauseNumber}:{' '}
                        {checkResults.clauseName}
                      </p>
                    )}
                  </div>
                  <div
                    className={`px-2 py-1 text-xs font-semibold rounded-md border ${getComplianceColor(
                      checkResults.overallCompliance || 'unknown',
                    )}`}
                  >
                    {(checkResults.overallCompliance || 'unknown')
                      .replace('-', ' ')
                      .replace('_', ' ')
                      .toUpperCase()}
                  </div>
                </div>

                <div className="h-px w-full bg-gray-200 my-4" />

                <div>
                  <h4 className="font-medium mb-2 flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4" />
                    Compliance Gaps (
                    {checkResults.gaps.length +
                      (checkResults.documentResults?.reduce(
                        (total: number, doc: any) =>
                          total + (doc.gaps?.length || 0),
                        0,
                      ) || 0)}
                    )
                  </h4>
                  <div className="space-y-2">
                    {/* Aggregated Gaps */}
                    {checkResults.gaps && checkResults.gaps.length > 0 && (
                      <div className="space-y-2">
                        {checkResults.gaps.map((gap: any, index: number) => (
                          <div
                            key={index}
                            className="p-3 border rounded-lg bg-yellow-50"
                          >
                            <div className="flex items-center justify-between mb-1">
                              <span className="font-medium text-sm">
                                {gap.clause ||
                                  (gap.section && gap.section.includes('%')
                                    ? decodeURIComponent(gap.section)
                                        .replace(/%20/g, ' ')
                                        .replace(/\.pdf$/, '')
                                        .replace(/_v\d+.*$/, '')
                                    : gap.section) ||
                                  `Gap ${index + 1}`}
                              </span>
                              <div
                                className={`px-2 py-0.5 text-xs font-semibold rounded-md border ${
                                  gap.severity === 'high' ||
                                  gap.severity === 'High'
                                    ? 'border-red-200 text-red-800'
                                    : gap.severity === 'medium' ||
                                      gap.severity === 'Medium'
                                    ? 'border-yellow-200 text-yellow-800'
                                    : 'border-blue-200 text-blue-800'
                                }`}
                              >
                                {gap.severity || 'Medium'}
                              </div>
                            </div>
                            <p className="text-sm text-gray-700 mb-2">
                              {gap.description || gap.issue || gap.gap}
                            </p>
                            {gap.document_name && (
                              <p className="text-xs text-gray-500 mb-1">
                                Document:{' '}
                                {decodeURIComponent(gap.document_name)
                                  .replace(/%20/g, ' ')
                                  .replace(/\.pdf$/, '')}
                              </p>
                            )}
                            {gap.recommendation && (
                              <p className="text-sm text-blue-700 bg-blue-50 p-2 rounded">
                                <strong>Recommendation:</strong>{' '}
                                {gap.recommendation}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Document-Level Gaps */}
                    {checkResults.documentResults &&
                      checkResults.documentResults.length > 0 &&
                      checkResults.documentResults.some(
                        (doc: any) => doc.gaps && doc.gaps.length > 0,
                      ) && (
                        <div className="space-y-2">
                          <div className="border rounded-lg bg-blue-50 border-blue-200">
                            <div
                              className="p-3 cursor-pointer hover:bg-blue-100 transition-colors"
                              onClick={() => toggleDocumentGaps(0)}
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  {expandedDocumentGaps.has(0) ? (
                                    <ChevronDown className="h-4 w-4 text-blue-600" />
                                  ) : (
                                    <ChevronRight className="h-4 w-4 text-blue-600" />
                                  )}
                                  <FileSearch className="h-4 w-4 text-blue-600" />
                                  <span className="font-medium text-sm text-blue-800">
                                    Document-Level Gaps
                                  </span>
                                  <span className="text-xs bg-blue-200 text-blue-700 px-2 py-1 rounded">
                                    {checkResults.documentResults.reduce(
                                      (total: number, doc: any) =>
                                        total + (doc.gaps?.length || 0),
                                      0,
                                    )}{' '}
                                    total
                                  </span>
                                </div>
                                <span className="text-xs text-blue-600">
                                  {expandedDocumentGaps.has(0)
                                    ? 'Hide'
                                    : 'Show'}{' '}
                                  details
                                </span>
                              </div>
                            </div>

                            {expandedDocumentGaps.has(0) && (
                              <div className="border-t border-blue-200 bg-white">
                                <div className="p-3 space-y-3">
                                  {checkResults.documentResults.map(
                                    (docResult: any, docIndex: number) => {
                                      if (
                                        !docResult.gaps ||
                                        docResult.gaps.length === 0
                                      )
                                        return null;

                                      return (
                                        <div
                                          key={docIndex}
                                          className="border rounded-lg bg-gray-50"
                                        >
                                          <div className="p-3 bg-gray-100 border-b">
                                            <div className="flex items-center gap-2">
                                              <FileSearch className="h-4 w-4 text-gray-600" />
                                              <span className="font-medium text-sm text-gray-800">
                                                {decodeURIComponent(
                                                  docResult.document_name ||
                                                    `Document ${docIndex + 1}`,
                                                )}
                                              </span>
                                              <span className="text-xs bg-gray-200 text-gray-700 px-2 py-1 rounded">
                                                {docResult.gaps.length} gap
                                                {docResult.gaps.length > 1
                                                  ? 's'
                                                  : ''}
                                              </span>
                                            </div>
                                          </div>
                                          <div className="p-3 space-y-2">
                                            {docResult.gaps.map(
                                              (gap: any, gapIndex: number) => (
                                                <div
                                                  key={gapIndex}
                                                  className="p-2 border rounded bg-red-50 border-red-200 ml-4"
                                                >
                                                  <div className="flex items-center justify-between mb-1">
                                                    <span className="font-medium text-xs text-red-800">
                                                      {gap.clause ||
                                                        gap.section ||
                                                        `Gap ${gapIndex + 1}`}
                                                    </span>
                                                    <span
                                                      className={`px-1 py-0.5 text-xs font-semibold rounded ${
                                                        gap.severity === 'high'
                                                          ? 'bg-red-200 text-red-800'
                                                          : gap.severity ===
                                                            'medium'
                                                          ? 'bg-yellow-200 text-yellow-800'
                                                          : 'bg-blue-200 text-blue-800'
                                                      }`}
                                                    >
                                                      {gap.severity || 'Medium'}
                                                    </span>
                                                  </div>
                                                  <p className="text-xs text-red-700">
                                                    {gap.description ||
                                                      gap.issue ||
                                                      gap.gap}
                                                  </p>
                                                  {gap.recommendation && (
                                                    <p className="text-xs text-blue-700 bg-blue-50 p-1 rounded mt-1">
                                                      <strong>
                                                        Recommendation:
                                                      </strong>{' '}
                                                      {gap.recommendation}
                                                    </p>
                                                  )}
                                                </div>
                                              ),
                                            )}
                                          </div>
                                        </div>
                                      );
                                    },
                                  )}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                    {/* No Gaps Found */}
                    {(!checkResults.gaps || checkResults.gaps.length === 0) &&
                      (!checkResults.documentResults ||
                        checkResults.documentResults.every(
                          (doc: any) => !doc.gaps || doc.gaps.length === 0,
                        )) && (
                        <div className="p-3 border rounded-lg bg-green-50 text-green-700">
                          <p className="flex items-center gap-2">
                            <CheckCircle className="h-5 w-5" />
                            No compliance gaps found. Great job!
                          </p>
                        </div>
                      )}
                  </div>
                </div>

                <div className="h-px w-full bg-gray-200 my-4" />

                {/* Document Analysis Results (Spelling & Grammar Only) */}
                {checkResults.documentResults &&
                  checkResults.documentResults.length > 0 &&
                  checkResults.documentResults.some(
                    (doc: any) =>
                      doc.spellCheckIssues && doc.spellCheckIssues.length > 0,
                  ) && (
                    <>
                      <div className="h-px w-full bg-gray-200 my-4" />
                      <div>
                        <h4 className="font-medium mb-2 flex items-center gap-2">
                          <SpellCheck className="h-4 w-4" />
                          Spelling & Grammar Issues
                        </h4>
                        <div className="space-y-3">
                          {checkResults.documentResults.map(
                            (docResult: any, index: number) => {
                              const spellIssueCount =
                                docResult.spellCheckIssues?.length || 0;
                              if (spellIssueCount === 0) return null;

                              const isExpanded = expandedDocuments.has(index);

                              return (
                                <div
                                  key={index}
                                  className="border rounded-lg bg-gray-50"
                                >
                                  {/* Accordion Header */}
                                  <div
                                    className="p-3 cursor-pointer hover:bg-gray-100 transition-colors"
                                    onClick={() => toggleDocument(index)}
                                  >
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-3">
                                        {isExpanded ? (
                                          <ChevronDown className="h-4 w-4 text-gray-600" />
                                        ) : (
                                          <ChevronRight className="h-4 w-4 text-gray-600" />
                                        )}
                                        <div>
                                          <span className="font-medium text-sm">
                                            {decodeURIComponent(
                                              docResult.document_name || '',
                                            )}
                                          </span>
                                          <div className="flex items-center gap-2 mt-1">
                                            <span className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded">
                                              {spellIssueCount} spelling issue
                                              {spellIssueCount > 1 ? 's' : ''}
                                            </span>
                                            {docResult.using_cached_document && (
                                              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                                                Cached
                                              </span>
                                            )}
                                          </div>
                                        </div>
                                      </div>
                                      <div
                                        className={`px-2 py-1 text-xs font-semibold rounded-md border ${getComplianceColor(
                                          docResult.overallCompliance ||
                                            'unknown',
                                        )}`}
                                      >
                                        {docResult.score}%
                                      </div>
                                    </div>
                                  </div>

                                  {/* Accordion Content */}
                                  {isExpanded && (
                                    <div className="px-3 pb-3 border-t bg-white">
                                      {/* Spelling Issues Section */}
                                      <div className="mt-3">
                                        <h5 className="font-medium text-sm text-gray-800 mb-2">
                                          Spelling & Grammar Issues (
                                          {docResult.spellCheckIssues.length})
                                        </h5>
                                        <div className="space-y-1">
                                          {docResult.spellCheckIssues.map(
                                            (
                                              issue: any,
                                              issueIndex: number,
                                            ) => (
                                              <div
                                                key={issueIndex}
                                                className="p-2 border rounded bg-orange-50 border-orange-200"
                                              >
                                                <p className="text-xs text-orange-800">
                                                  <strong>{issue.word}</strong>{' '}
                                                  → {issue.suggestion}
                                                  {issue.location && (
                                                    <span className="text-orange-600">
                                                      {' '}
                                                      ({issue.location})
                                                    </span>
                                                  )}
                                                </p>
                                              </div>
                                            ),
                                          )}
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              );
                            },
                          )}
                        </div>
                      </div>
                    </>
                  )}

                <div>
                  <h4 className="font-medium mb-2">Recommendations</h4>
                  <ul className="space-y-1">
                    {checkResults.recommendations &&
                    checkResults.recommendations.length > 0 ? (
                      checkResults.recommendations.map(
                        (rec: string, index: number) => (
                          <li
                            key={index}
                            className="text-sm text-gray-700 flex items-start gap-2"
                          >
                            <span className="text-blue-600 mt-1">•</span>
                            {rec}
                          </li>
                        ),
                      )
                    ) : (
                      <li className="text-sm text-gray-700">
                        No specific recommendations at this time.
                      </li>
                    )}
                  </ul>
                </div>

                {/* <div className="mt-8 pt-4 border-t border-gray-200">
                  <PrimaryButton
                    onClick={() => onOpenChange(false)}
                    width="100%"
                    buttonClasses="flex items-center justify-center"
                    text="I Acknowledge These Compliance Results"
                    icon={<CheckCircle className="h-4 w-4" />}
                  />
                </div> */}
              </div>
            </div>
          )}
        </div>

        {(checkResults || showMissingDocuments || hasApiError) && (
          <div className="flex justify-end gap-3 pt-4 border-t">
            {showMissingDocuments || hasApiError ? (
              <SecondaryButton onClick={onClose} text="Close" />
            ) : (
              <TooltipProvider>
                <div className="flex gap-3">
                  <SecondaryButton onClick={onClose} text="Close" />
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <div>
                        <PrimaryButton
                          onClick={() => {}}
                          text="Apply Recommendations"
                          disabled={true}
                          buttonClasses="opacity-50 cursor-not-allowed"
                        />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Feature in development - Coming soon!</p>
                    </TooltipContent>
                  </Tooltip>
                </div>
              </TooltipProvider>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ComplianceCheckModal;
