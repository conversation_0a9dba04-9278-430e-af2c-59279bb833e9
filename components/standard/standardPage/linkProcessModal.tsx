import Image from 'next/image';
import React, { useEffect, useState } from 'react';

import LinkDocument from '@/assets/linkDocument.svg';
import UnlinkIcon from '@/assets/outline/unlink';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Checkbox } from '@/components/common/checkbox';
import {
    Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle, DialogTrigger
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import Loader from '@/components/common/loader';
import DeleteModal from '@/components/common/modals/deleteModal';
import {
    Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from '@/components/common/select';
import OtpModal from '@/components/document/components/modals/otpModal';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { usePut } from '@/hooks/usePut';
import { IProcess } from '@/interfaces/process';
import { fetchData } from '@/utils/api';

interface IDocumentData {
  id: string;
  created_on: string;
  last_modified_on: string;
  title: string;
  origin: string;
}

interface IProps {
  subclauseId: string;
  edit?: boolean;
  selectedProcessId?: string | null;
  selectedDocumentId?: string[];
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  showModal: boolean;
  refetch: () => void;
  alreadyLinkedProcessId?: string[];
  processes: {
    records: IProcess[];
  } | null;
}

interface Response {
  id: string;
  step: string;
}

const LinkProcessModal = ({
  subclauseId,
  edit = false,
  alreadyLinkedProcessId,
  selectedProcessId,
  selectedDocumentId,
  setShowModal,
  showModal,
  refetch,
  processes,
}: IProps) => {
  const [processId, setProcessId] = useState<string | undefined>(undefined);
  const [documentData, setDocumentData] = useState<IDocumentData[]>([]);
  const [documentDataLoading, setDocumentDataLoading] = useState(false);
  const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);
  const [openUnlinkModal, setOpenUnlinkModal] = useState(false);
  const { accessToken } = useAuthStore();
  const cfr_enabled = useAuthStore(
    (state) => state.user?.company.is_cfr11_required,
  );
  const [searchTerm, setSearchTerm] = useState('');
  const [otpModal, setOtpModal] = useState(false);
  const [mfaSessionId, setMfaSessionId] = useState('');

  const filteredDocuments = documentData?.filter((doc) =>
    doc.title.toLowerCase().includes(searchTerm.toLowerCase()),
  );

  const filterProcesses = edit
    ? processes?.records
    : processes?.records?.filter((process) =>
        alreadyLinkedProcessId?.includes(process.id) ? false : true,
      );

  const { putData, response: updatedResponse, isLoading } = usePut<Response>();
  const {
    deleteData,
    isLoading: deleteLoading,
    response: deleteResponse,
    error: deleteError,
  } = useDelete<Response>();

  useEffect(() => {
    if (processId) {
      (async () => {
        setDocumentDataLoading(true);
        await fetchData(accessToken as string, `documents/process/${processId}`)
          .then((res) => {
            setDocumentData(res?.data?.records);
            setDocumentDataLoading(false);
          })
          .catch((err) => {
            console.log(err);
            setDocumentDataLoading(false);
          });
      })();
    }
  }, [accessToken, processId]);

  const handleUpdateProcess = async () => {
    await putData(
      accessToken as string,
      `clauses/${subclauseId}/link-process`,
      {
        process_id: processId,
        document_ids: selectedDocumentIds,
      },
    );
  };

  const unlinkProcess = async () => {
    await deleteData(
      accessToken as string,
      `clauses/${subclauseId}/unlink-process/${processId}`,
    );
  };

  useEffect(() => {
    if (selectedProcessId) setProcessId(selectedProcessId);
    if (selectedDocumentId?.length && selectedDocumentId?.length > 0)
      setSelectedDocumentIds(selectedDocumentId);
  }, [selectedProcessId, selectedDocumentId]);

  useEffect(() => {
    if (updatedResponse) {
      if (cfr_enabled && updatedResponse.step === 'mfa') {
        setShowModal(false);
        setProcessId(undefined);
        setSelectedDocumentIds([]);
        setMfaSessionId(updatedResponse.id);
        setOtpModal(true);
      } else {
        setTimeout(() => {
          refetch();
        }, 800);
        setShowModal(false);
        setSelectedDocumentIds([]);
        setProcessId(undefined);
      }
    }
  }, [updatedResponse]);

  useEffect(() => {
    if (!showModal) {
      setSearchTerm('');
      setSelectedDocumentIds([]);
      setProcessId(undefined);
      setDocumentData([]);
    }
  }, [showModal]);

  useEffect(() => {
    if (deleteResponse) {
      if (cfr_enabled && deleteResponse.step === 'mfa') {
        setShowModal(false);
        setOpenUnlinkModal(false);
        setMfaSessionId(deleteResponse.id);
        setOtpModal(true);
      } else {
        setOpenUnlinkModal(false);
        refetch();
        setShowModal(false);
      }
    }

    if (deleteError) {
      setOpenUnlinkModal(false);
      refetch();
      setShowModal(false);
    }
  }, [deleteResponse, deleteError]);

  return (
    <>
      <Dialog open={otpModal} onOpenChange={setOtpModal}>
        {otpModal && (
          <OtpModal
            setOtpModal={setOtpModal}
            sessionId={mfaSessionId}
            refetchDocumentData={refetch}
          />
        )}
      </Dialog>

      <DialogContent
        className="min-w-[43.75rem] overflow-hidden  "
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
        }}
      >
        <DialogHeader>
          <DialogTitle>{edit ? 'Edit process' : 'Link a process'} </DialogTitle>
        </DialogHeader>
        <div className="mt-2 overflow-hidden w-full">
          <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
            Process<span className="text-[#F55D5D]">*</span>
          </div>
          <Select
            value={processId}
            onValueChange={(value) => {
              setProcessId(value);
              setSelectedDocumentIds([]);
            }}
            disabled={edit}
          >
            <SelectTrigger id="origin">
              <SelectValue placeholder="Choose Process" />
            </SelectTrigger>
            <SelectContent>
              {filterProcesses?.map((e, i) => (
                <SelectItem value={e.id} key={i}>
                  {e.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="p-4 border border-white-300 rounded-lg mt-5 ">
            <div className="text-grey-300 text-base font-medium leading-6 mb-3">
              Link documents
            </div>
            {documentDataLoading ? (
              <Loader className="h-[calc(29vh+3.25rem)]" />
            ) : documentData.length > 0 ? (
              <>
                <Input
                  id="filter-text-box"
                  placeholder={` Search ...`}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <div className="flex flex-col gap-1 h-[29vh] overflow-x-hidden overflow-y-auto mt-2">
                  {filteredDocuments.length > 0 ? (
                    filteredDocuments.map((e, i) => (
                      <div
                        className="flex items-center gap-4 px-3 py-2.5"
                        key={i}
                      >
                        <Checkbox
                          id={e.id}
                          checked={selectedDocumentIds.includes(e.id)}
                          onCheckedChange={(checked) => {
                            return checked
                              ? setSelectedDocumentIds((pre) => [
                                  ...(pre ?? []),
                                  e.id,
                                ])
                              : setSelectedDocumentIds((pre) =>
                                  pre?.filter((value) => value !== e.id),
                                );
                          }}
                        />
                        <label
                          htmlFor={e.id}
                          className="text-base font-medium leading-6 text-dark-300"
                          onClick={(e) => {
                            e.stopPropagation();
                          }}
                        >
                          {e.title}
                        </label>
                      </div>
                    ))
                  ) : (
                    <div className="w-full h-[29vh] flex items-center justify-center flex-col gap-3">
                      <div>
                        <Image src={LinkDocument} alt="" />
                      </div>
                      <div className="text-grey-300 text-sm font-medium leading-5">
                        No result found
                      </div>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <div className="w-full h-[calc(29vh+3.25rem)] flex items-center justify-center flex-col gap-3">
                <div>
                  <Image src={LinkDocument} alt="" />
                </div>
                <div className="text-grey-300 text-sm font-medium leading-5">
                  Add a process to update linked document list
                </div>
              </div>
            )}
            {/*  */}
          </div>
        </div>
        <DialogFooter className="mt-2 gap-2">
          {edit ? (
            <Dialog open={openUnlinkModal} onOpenChange={setOpenUnlinkModal}>
              <DialogTrigger>
                <SecondaryButton
                  text="Unlink Process"
                  size="medium"
                  buttonClasses="!bg-red-100/10 hover:!bg-red-100 !text-red-300"
                  icon={<UnlinkIcon className="h-5 w-5" color="#E05252" />}
                />
              </DialogTrigger>
              <DeleteModal
                title={'Unlink process'}
                infoText={'Are you sure you want to unlink this process? '}
                btnText={'Unlink'}
                onClick={() => unlinkProcess()}
                btnLoading={deleteLoading}
              />
            </Dialog>
          ) : (
            ''
          )}

          <PrimaryButton
            size="medium"
            text={edit ? 'Update' : 'Submit'}
            disabled={!processId}
            isLoading={isLoading}
            onClick={() => handleUpdateProcess()}
          />
        </DialogFooter>
      </DialogContent>
    </>
  );
};

export default LinkProcessModal;
