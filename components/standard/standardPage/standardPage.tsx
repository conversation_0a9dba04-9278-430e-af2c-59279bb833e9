import { Minus, Search } from 'lucide-react';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';
import scopeIcon from '@/assets/standard/scope.svg';
import unScopeIcon from '@/assets/standard/unscope.svg';

import CheckIcon from '@/assets/outline/check';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/common/accordion';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import Layout from '@/components/common/sidebar/layout';
import Tabs from '@/components/common/tabs';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { IProcess } from '@/interfaces/process';
import { IStandardRecord, IStandardView } from '@/interfaces/standard';
import { hasAccess } from '@/utils/roleAccessConfig';
import { cn } from '@/utils/styleUtils';

import ProcessDocument from '../processDocument';
import LinkProcessModal from './linkProcessModal';
import { filterClauses, transformClause } from '@/utils/transformClause';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import JustificationModal from '../components/modals/justificationModal';
import ConfirmModal from '@/components/common/modals/confirmModal';
import { usePut } from '@/hooks/usePut';
import { toast } from 'react-toastify';
import ToggleSwitch from '@/components/common/toogleSwitch';
import InfoCircle from '@/assets/outline/infoCircle';
import { Bot } from 'lucide-react';
import ComplianceCheckModal from '../components/modals/complianceCheckModal';

const aiEnabledCompanies = [
  // '6bc3bab7-bbb3-4697-b586-ce8d0d52158d', //Plumage
  // '6d88182b-0581-4667-93a5-3c71a8c6f49f', //Plumage
  'af04b4ab-8174-40a9-9589-7250437e7ea5', //prod BPRHub
  '780a27d6-3e62-4ac7-886f-f94ff38cb1c5', //dev BPRHub
  'af04b4ab-8174-40a9-9589-7250437e7ea5', //qa BPRHub
  'c67f2ab3-a837-47ec-8935-6a94ffccf371',
];

interface PutResponse {
  message: string;
  [key: string]: any;
}

const StandardHubInnerPage = () => {
  const [activeTab, setActiveTab] = useState<number>(0);
  const [activeTab2, setActiveTab2] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredData, setFilteredData] = useState<IStandardRecord[] | []>([]);
  const [transformedClauses, setTransformedClauses] = useState<any[]>([]);
  const [showOnlyInScope, setShowOnlyInScope] = useState(true);
  const [activeClauseCount, setActiveClauseCount] = useState(0);
  const [complianceCheckModal, setComplianceCheckModal] = useState(false);
  const [selectedClauseForCompliance, setSelectedClauseForCompliance] =
    useState<any>(null);

  const { accessToken, user } = useAuthStore();
  const param = useParams();
  const standardId = param?.standardId;
  const [filter, setFilter] = useState({});

  const { data, isLoading, error, reFetch } = useFetch<IStandardView>(
    accessToken,
    `clauses/${standardId}`,
    filter,
  );
  const { data: processes } = useFetch<{ records: IProcess[] }>(
    accessToken,
    `processes`,
    {},
  );
  const breadcrumbData = [
    {
      name: 'Standard Hub',
      link: '/standard',
    },
    {
      name: data?.standard?.title || '',
      link: '#',
    },
  ];

  const tabsData = [
    {
      name: `Requirements (${data?.summary.total_document_count || '0'})`,
      textColor: 'text-dark-100',
      onClick: () => setFilter({}),
    },
    {
      name: `Compliant (${data?.summary.compliant_document_count || '0'})`,
      textColor: 'text-dark-100',
      onClick: () => setFilter({ is_compliant: true }),
    },
    {
      name: `Non Compliant (${
        data?.summary.non_compliant_document_count || '0'
      })`,
      textColor: 'text-dark-100',
      onClick: () => setFilter({ is_compliant: false }),
    },
  ];

  const tabsData2 = [{ name: 'Linked Processes', textColor: 'text-dark-100' }];

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!transformedClauses) return;

    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    const searchItems = filterClauses(transformedClauses, term);
    setFilteredData(searchItems || []);
  };

  useEffect(() => {
    reFetch();
  }, [filter]);

  useEffect(() => {
    if (!data?.records?.length) return;

    const transformItems = data.records.map((record) =>
      transformClause(record),
    );

    const count = countActiveClauses(transformItems);
    setActiveClauseCount(count);

    const filteredItems = showOnlyInScope
      ? transformItems.map((item) => filterActiveClauses(item)).filter(Boolean)
      : transformItems;

    setTransformedClauses(filteredItems);
  }, [data, showOnlyInScope]);

  const countActiveClauses = (nodes: any[]): number => {
    let count = 0;

    for (const node of nodes) {
      if (!node.is_active) count++;
      if (node.children) {
        count += countActiveClauses(node.children);
      }
    }
    return count;
  };

  const handleSubclauseComplianceCheck = (clause: any) => {
    setSelectedClauseForCompliance(clause);
    setComplianceCheckModal(true);
  };

  const getDocumentIdsFromClause = (clause: any): string[] => {
    const documentIds: string[] = [];

    if (clause.processes && clause.processes.length > 0) {
      clause.processes.forEach((process: any) => {
        if (process.documents && process.documents.length > 0) {
          process.documents.forEach((doc: any) => {
            if (doc.id && !documentIds.includes(doc.id)) {
              documentIds.push(doc.id);
            }
          });
        }
      });
    }

    return documentIds;
  };

  const filterActiveClauses = (node: any): any | null => {
    const filteredChildren = node.children
      ? node.children
          .map((child: any) => filterActiveClauses(child))
          .filter(Boolean)
      : [];

    const isActiveOrHasActiveChild =
      node.is_active || filteredChildren.length > 0;

    if (!isActiveOrHasActiveChild) return null;

    return {
      ...node,
      children: filteredChildren.length > 0 ? filteredChildren : undefined,
    };
  };

  return (
    <Layout>
      <div className=" my-5">
        <div className="flex flex-col">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10">
            {data?.standard?.title}
          </div>
        </div>
        <div className="mt-5">
          <Tabs
            tabsData={tabsData}
            setActiveTab={setActiveTab}
            activeTab={activeTab}
            tabRightSideElement={
              <>
                {activeClauseCount > 0 && (
                  <div className="flex gap-4 items-center">
                    <div className="flex items-center">
                      <Tooltip>
                        <TooltipTrigger>
                          <div className="w-10 h-10 flex items-center justify-center rounded-full cursor-pointer">
                            <InfoCircle />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-sm text-dark-300">
                            Include clauses marked as out of scope in the view
                          </div>
                        </TooltipContent>
                      </Tooltip>
                      <p className="text-dark-300">
                        Only show clauses in scope
                      </p>
                    </div>
                    <ToggleSwitch
                      initialState={showOnlyInScope}
                      onChange={(state) => {
                        setShowOnlyInScope(state);
                      }}
                    />
                  </div>
                )}
              </>
            }
          />
          {data?.records.length === 0 ? (
            <div className="flex flex-col items-center justify-center my-20">
              <div>
                <Image
                  src={'/zeroStateTable.svg'}
                  alt=""
                  width={100}
                  height={100}
                />
              </div>
              <div className="text-base font-medium leading-6 text-grey-200 mt-2">
                {activeTab === 2
                  ? 'All clauses are compliant'
                  : activeTab === 1
                  ? 'No clauses are compliant'
                  : 'No data to show'}
              </div>
            </div>
          ) : (
            <>
              <div className="py-2.5 px-3 bg-white flex items-center rounded-lg gap-1 border border-grey-100 max-w-[40rem] mt-6 focus-within:border-primary-100">
                <Search className="h-5 w-5 " color="#B9B9B9" />
                <input
                  type="text"
                  placeholder="Search by clause or number"
                  className="flex-1 focus-visible:outline-none"
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </div>
              <div className="mt-4">
                {(
                  (filteredData?.length > 0 && filteredData) ||
                  transformedClauses
                )?.map((clause, index) => (
                  <RecursiveAccordion
                    key={clause.id || index}
                    clause={clause}
                    processes={processes}
                    reFetch={reFetch}
                    tabsData2={tabsData2}
                    activeTab2={activeTab2}
                    setActiveTab2={setActiveTab2}
                    user={user}
                    handleSubclauseComplianceCheck={
                      handleSubclauseComplianceCheck
                    }
                    getDocumentIdsFromClause={getDocumentIdsFromClause}
                    processLoading={isLoading}
                  />
                ))}
              </div>
            </>
          )}
        </div>
      </div>

      {/* Compliance Check Modal */}
      <ComplianceCheckModal
        isOpen={complianceCheckModal}
        onOpenChange={setComplianceCheckModal}
        document_ids={
          selectedClauseForCompliance
            ? getDocumentIdsFromClause(selectedClauseForCompliance)
            : []
        }
        standard={data?.standard?.title || 'ISO 9001'}
        clause={selectedClauseForCompliance}
      />
    </Layout>
  );
};

const RecursiveAccordion = ({
  clause,
  processes,
  reFetch,
  tabsData2,
  activeTab2,
  setActiveTab2,
  user,
  handleSubclauseComplianceCheck,
  getDocumentIdsFromClause,
  isChild,
  processLoading,
}: {
  clause: any;
  processes: any;
  reFetch: any;
  tabsData2: any;
  activeTab2: any;
  setActiveTab2: any;
  user: any;

  handleSubclauseComplianceCheck?: (clause: any) => void;
  getDocumentIdsFromClause?: (clause: any) => string[];
  isChild?: boolean;
  processLoading?: boolean;
}) => {
  const [markScopeConfirmModal, setMarkScopeConfirmModal] = useState(false);
  const [openLinkProcessModal, setOpenLinkProcessModal] = useState(false);
  const { putData, response, isLoading, error } = usePut<PutResponse>();
  const { accessToken } = useAuthStore();

  const [openJustificationModal, setOpenJustifcationModal] = useState(false);
  const [selectedClause, setSelectedClause] = useState<{
    id: string;
    type: 'parent' | 'child';
  } | null>(null);

  const handleSubmit = () => {
    const key =
      selectedClause?.type === 'parent' ? 'clause_id' : 'sub_clause_id';
    const body = {
      [key]: selectedClause?.id,
      is_active: true,
    };

    async function fetch() {
      await putData(accessToken as string, `clauses/toggle-scope/`, body);
    }
    fetch();
  };

  useEffect(() => {
    if (response) {
      setSelectedClause(null);
      setMarkScopeConfirmModal(false);
      toast.success(response?.message);
      reFetch();
    }
  }, [response]);
  const param = useParams();
  const standardId = param?.standardId;

  return (
    <Accordion type="single" collapsible className="w-full mt-2">
      <AccordionItem value={`item-${clause.id}`}>
        <AccordionTrigger className="">
          <div className="flex flex-1 items-center justify-between mr-2">
            <div className="flex items-center gap-2">
              {standardId !== '7c196a7e-7903-4550-a12f-3b0aec6cd0e2' && (
                <span>{clause.clause_no}</span>
              )}

              <div className="text-dark-300 text-base leading-6 font-medium">
                {clause.title}
              </div>
            </div>

            <div className="flex items-center gap-3">
              <div
                className={cn(
                  'rounded-full text-xs font-bold px-3 py-1',
                  !clause.is_active
                    ? 'bg-gray-50 text-gray-400'
                    : clause.is_compliant
                    ? 'bg-green-50 text-primary-400'
                    : 'bg-red-50 text-red-400',
                )}
              >
                {!clause.is_active ? (
                  <span>Out of scope</span>
                ) : clause.is_compliant ? (
                  <span>Compliant</span>
                ) : (
                  <span>Non-Compliant</span>
                )}
              </div>
              {hasAccess(AccessActions.CanEditScope, user) && (
                <div
                  className="flex justify-center items-center"
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedClause({
                      id: clause.id,
                      type: isChild ? 'child' : 'parent',
                    });
                  }}
                >
                  {clause.is_active ? (
                    <Dialog
                      open={openJustificationModal}
                      onOpenChange={setOpenJustifcationModal}
                    >
                      <DialogTrigger className="flex">
                        <Tooltip>
                          <TooltipTrigger>
                            <div>
                              <Image src={scopeIcon} alt="scope icon" />
                            </div>
                          </TooltipTrigger>
                          <TooltipContent>
                            <div className="text-sm text-dark-300">
                              Mark as out of scope
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      </DialogTrigger>
                      <JustificationModal
                        setOpenJustificationModal={setOpenJustifcationModal}
                        clauseId={selectedClause?.id ?? ''}
                        clauseType={selectedClause?.type ?? ''}
                        refetch={() => {
                          setSelectedClause(null);
                          reFetch();
                        }}
                      />
                    </Dialog>
                  ) : (
                    <>
                      <Dialog
                        open={markScopeConfirmModal}
                        onOpenChange={setMarkScopeConfirmModal}
                      >
                        <DialogTrigger className="flex">
                          <Tooltip>
                            <TooltipTrigger>
                              <div>
                                <Image src={unScopeIcon} alt="scope icon" />
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <div className=" text-sm text-dark-300">
                                Mark to scope
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        </DialogTrigger>
                        <ConfirmModal
                          title={'Mark as In Scope'}
                          infoText={
                            'Are you sure you want to include this clause in the scope?'
                          }
                          btnText={'Confirm'}
                          onClick={() => handleSubmit()}
                          btnLoading={isLoading}
                        />
                      </Dialog>
                    </>
                  )}
                </div>
              )}
            </div>
          </div>
        </AccordionTrigger>

        <AccordionContent className="py-2 px-5 flex flex-col">
          {/* Render child clauses or sub-clauses */}
          {clause.children?.map((child: any, index: number) => {
            // Recursively render child elements (could be sub-clause or nested clauses)
            return (
              <RecursiveAccordion
                key={child.id || index}
                clause={child}
                processes={processes}
                reFetch={reFetch}
                tabsData2={tabsData2}
                activeTab2={activeTab2}
                setActiveTab2={setActiveTab2}
                user={user}
                handleSubclauseComplianceCheck={handleSubclauseComplianceCheck}
                getDocumentIdsFromClause={getDocumentIdsFromClause}
                processLoading={processLoading}
                isChild={
                  !!(
                    child.description ||
                    child.question ||
                    (child.processes && child.processes.length > 0)
                  )
                }
              />
            );
          })}

          {clause.justification ? (
            <div className="mt-4">
              <div className="text-dark-300 text-lg font-semibold leading-7 mb-2">
                Out of scope Justification:
              </div>
              <div className="text-dark-100 text-base leading-6 font-medium">
                <TextWithLineBreaks text={clause.justification} />
              </div>
            </div>
          ) : (
            ''
          )}

          {clause.description && (
            <div className="mt-4">
              <div
                className={`text-lg font-semibold leading-7 mb-2 ${
                  clause.is_active ? 'text-dark-300' : 'text-gray-300'
                }`}
              >
                Clause Description:
              </div>
              <div
                className={`text-base leading-6 font-medium ${
                  clause.is_active ? 'text-dark-100' : 'text-gray-300'
                }`}
              >
                <TextWithLineBreaks text={clause.description} />
              </div>
            </div>
          )}

          {/* clause question */}
          {clause.question && (
            <div className="mt-4">
              <div
                className={`text-lg font-semibold leading-7 mb-2 ${
                  clause.is_active ? 'text-dark-300' : 'text-gray-300'
                }`}
              >
                Clause Question:
              </div>
              <div
                className={`text-base leading-6 font-medium ${
                  clause.is_active ? 'text-dark-100' : 'text-gray-300'
                }`}
              >
                <TextWithLineBreaks text={clause.question} />
              </div>
            </div>
          )}

          {clause?.question && (
            <div className="mt-4">
              <div className="flex items-center justify-between">
                <Tabs
                  tabsData={tabsData2}
                  activeTab={activeTab2}
                  setActiveTab={setActiveTab2}
                />
                <div className="flex items-center gap-2">
                  {/* AI Compliance Check Button */}
                  {handleSubclauseComplianceCheck &&
                    getDocumentIdsFromClause &&
                    clause.processes &&
                    clause.processes.length > 0 &&
                    getDocumentIdsFromClause(clause).length > 0 &&
                    user?.company?.id &&
                    aiEnabledCompanies.includes(user?.company?.id) && (
                      <Tooltip>
                        <TooltipTrigger>
                          <button
                            className="flex items-center justify-center px-5 py-2 text-base leading-6 font-medium rounded-lg bg-gradient-to-r from-primary-400 to-primary-600 hover:bg-opacity-90 hover:brightness-95 text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            onClick={() =>
                              clause.is_active &&
                              handleSubclauseComplianceCheck(clause)
                            }
                            disabled={!clause.is_active}
                          >
                            <Bot className="h-4 w-4 mr-2" />
                            AI Check
                          </button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-sm text-dark-300">
                            Run AI compliance check on all documents in this
                            subclause ({getDocumentIdsFromClause(clause).length}{' '}
                            documents)
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    )}

                  {hasAccess(AccessActions.CanLinkDocument, user) && (
                    <Dialog
                      open={openLinkProcessModal}
                      onOpenChange={setOpenLinkProcessModal}
                    >
                      <DialogTrigger>
                        <PrimaryButton
                          text="Link Process"
                          size="medium"
                          disabled={!clause.is_active}
                        />
                      </DialogTrigger>
                      <LinkProcessModal
                        subclauseId={clause.id}
                        setShowModal={setOpenLinkProcessModal}
                        showModal={openLinkProcessModal}
                        refetch={reFetch}
                        alreadyLinkedProcessId={clause.processes.flatMap(
                          (process: any) => process.id,
                        )}
                        processes={processes}
                      />
                    </Dialog>
                  )}
                </div>
              </div>
              <div>
                {clause?.processes?.length > 0 &&
                  clause?.processes?.map((process: any, y: number) => (
                    <ProcessDocument
                      key={`${process.id}-${y}`}
                      process={process}
                      subClause={clause}
                      reFetch={reFetch}
                      processes={processes}
                      index={y}
                      canEdit={clause.is_active}
                      isLoading={processLoading}
                    />
                  ))}
              </div>
            </div>
          )}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

const TextWithLineBreaks = ({ text }: { text: string }) => {
  return (
    <div>
      {text.split('\n').map((line, index) => (
        <React.Fragment key={index}>
          {line}
          <br />
        </React.Fragment>
      ))}
    </div>
  );
};

export default StandardHubInnerPage;
