import { useRouter } from 'next/router';
import React from 'react';

import { IStandardData } from '@/interfaces/standard';
import { Tooltip, TooltipContent, TooltipTrigger } from '../common/tooltip';

const Card = ({ data }: { data: IStandardData }) => {
  const router = useRouter();
  return (
    <>
      <Tooltip>
        <TooltipTrigger>
          <div
            className="text-left p-5 rounded-lg border border-gray-100 bg-white-100 hover:border-grey-200 hover:shadow-shadow-1 cursor-pointer transition-all"
            onClick={() => router.push(`/standard/${data.id}`)}
          >
            <div>
              <div className="text-gray-300 text-base leading-6 font-medium mb-2">
                {data.title}
              </div>
              <div className="text-dark-300 font-semibold leading-7 text-xl mb-5 line-clamp-1">
                {data.description}
              </div>
              <div className="text-dark-100 text-base leading-6 font-medium mb-3">
                Compliant score: {data.percent_complete}%
              </div>
              <div className="w-full bg-primary-200 h-3 rounded-full relative overflow-hidden">
                <div
                  className={`absolute top-0 left-0 bg-primary-400 h-full rounded-full`}
                  style={{ width: `${data.percent_complete}%` }}
                ></div>
              </div>
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="text-sm text-dark-300">{data.description}</div>
        </TooltipContent>
      </Tooltip>
    </>
  );
};

export default Card;
