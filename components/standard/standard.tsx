import React from 'react';

import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { IStandardData } from '@/interfaces/standard';

import Breadcrumb from '../common/breadcrumb';
import Loader from '../common/loader';
import Layout from '../common/sidebar/layout';
import Card from './card';

const Standard = () => {
  const { accessToken } = useAuthStore();
  const { data, isLoading, error, reFetch } = useFetch<{
    records: IStandardData[];
  }>(accessToken as string, 'standards', {});

  const breadcrumbData = [
    {
      name: 'Standard Hub',
      link: '#',
    },
  ];
  return (
    <div>
      <Layout>
        <div className=" my-5">
          <div className="flex flex-col">
            <Breadcrumb data={breadcrumbData} />
            <div className="text-dark-300 font-semibold text-3xl leading-10">
              Standard Hub
            </div>

            {error && (
              <div className="rounded-md border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark mt-4">
                {/* @ts-expect-error Server Component */}
                <p className="p-4">{error.response?.data?.error}</p>
              </div>
            )}
          </div>
          {isLoading ? (
            <Loader className="h-[50vh]" />
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5 mt-6">
              {data?.records?.map((e, i) => (
                <Card key={i} data={e} />
              ))}
            </div>
          )}
        </div>
      </Layout>
    </div>
  );
};

export default Standard;
