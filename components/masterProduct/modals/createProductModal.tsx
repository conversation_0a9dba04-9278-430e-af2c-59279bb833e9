import moment from 'moment';
import { version } from 'os';
import React, { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  IOption,
  ReactSelectMulti,
} from '@/components/common/multiSelectInput';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
// import { validateForm } from "@/hooks/useValidator";
import { TProductData } from '@/interfaces/masterProduct';
import { IUser } from '@/interfaces/user';
import { transformList } from '@/utils/general';

const optionSchema = z.object({
  label: z.string().optional(),
  value: z.string(),
});

export const createProductSchema = {
  product_id: z.string().nonempty('Product ID is required'),
  name: z.string().nonempty('Name is required'),
  assignees: z.array(optionSchema).min(1, 'At least one assignee is required'),
  approvers: z.array(optionSchema).min(1, 'At least one approver is required'),
  launch_date: z.string().nonempty('Estimated launch date is required'),
  version: z
    .number({ message: 'Version is required' })
    .nonnegative('Version is required'),
};

interface IData extends Record<string, unknown> {
  product_id: string;
  name: string;
  assignees: IOption[];
  approvers: IOption[];
  version: number | undefined;
  launch_date: string;
}

const CreateProductModal = ({
  edit,
  productData,
  reFetch,
  setOpenEdit,
}: {
  edit: boolean;
  productData?: TProductData;
  reFetch: any;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { data: users } = useFetch<{ records: IUser[] }>(accessToken, `users`);
  const { putData, isLoading: updateLoading } = usePut();
  const {
    postData,
    isLoading: createIsloading,
    response,
    error: postError,
  } = usePost();
  const [sameUserError, setSameUserError] = useState(false);
  const [data, setData] = useState<IData>({
    product_id: '',
    name: '',
    assignees: [],
    approvers: [],
    version: undefined,
    launch_date: '',
  });
  const [error, setError] = useState<Record<string, string> | undefined>();

  const userData = users?.records?.map((e) => ({
    label: e.full_name,
    value: e.id,
  })) as IOption[];

  const { validationErrors, startValidation, reset } = useValidators({
    schemas: createProductSchema,
    values: data,
  });
  useEffect(() => {
    if (edit && productData) {
      setData({
        product_id: productData?.product_id,
        name: productData?.name,
        assignees: transformList(productData.assignees),
        approvers: transformList(productData.approvers),
        version: productData.version,
        launch_date: productData.launch_date,
      });
    }
  }, [productData, edit]);

  const handleSubmit = async () => {
    setError(undefined);
    const { hasValidationErrors } = await startValidation();

    if (!hasValidationErrors) {
      if (edit && productData) {
        const payload = {
          product_info: {
            id: productData?.id,
            product_id: data.product_id,
            name: data.name,
            version: data.version,
            launch_date: moment(data.launch_date as string).format(
              'YYYY-MM-DD',
            ),
          },
          assignees: data.assignees.map((option) => option.value),
          approvers: data.approvers.map((option) => option.value),
        };
        try {
          accessToken &&
            (await putData(
              accessToken,
              `products/${productData?.id}`,
              payload,
            ));
          reFetch();
        } catch (error) {
          console.error('error', error);
        }
      } else {
        const payload = {
          product_id: data.product_id,
          name: data.name,
          assignees: data.assignees.map((option) => option.value),
          approvers: data.approvers.map((option) => option.value),
          version: data.version,
          launch_date: moment(data.launch_date as string).format('YYYY-MM-DD'),
        };
        try {
          if (accessToken) await postData(accessToken, `products`, payload);
        } catch (error) {
          console.error('error', error);
        }
      }
      if (edit) {
        if (setOpenEdit) setOpenEdit(false);
        if (reFetch) reFetch();
      }
    }
  };

  useEffect(() => {
    if (response) {
      if (setOpenEdit) setOpenEdit(false);
      if (reFetch) reFetch();
    }
  }, [response]);

  useEffect(() => {
    if (
      postError &&
      postError.status === 400 &&
      postError.response?.data &&
      typeof postError.response.data === 'object' &&
      'error' in postError.response.data &&
      postError.response.data.error ===
        'assignees and approvers can not be same'
    ) {
      setSameUserError(true);
    }
  }, [postError]);

  useEffect(() => {
    if (data.approvers && data.assignees) {
      setSameUserError(false);
    }
  }, [data.approvers, data.assignees]);

  return (
    <DialogContent className="min-w-[65vw] max-h-[100vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle>{edit ? 'Edit' : 'Create'} Product</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="productId"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Product ID<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Enter Product ID"
              id="productId"
              type="text"
              name="product_id"
              value={data?.product_id}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, product_id: e.target.value }))
              }
              errorMsg={validationErrors?.product_id[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="title"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Product name<span className="text-red-200">*</span>
            </Label>
            <Input
              type="text"
              name="name"
              value={data?.name}
              placeholder="Enter Product name"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, name: e.target.value }))
              }
              errorMsg={validationErrors?.name[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="assignee"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Assignees<span className="text-red-200">*</span>
            </Label>
            <ReactSelectMulti
              options={userData}
              value={data.assignees}
              placeholder="Select assignees"
              onChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  assignees: value as IOption[],
                }));
              }}
              hasError={
                Boolean(validationErrors?.assignees[0]) || sameUserError
              }
            />
            {validationErrors?.assignees[0] || sameUserError ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.assignees[0] ||
                  (sameUserError && "Assignee and Approver can't be same")}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="approvers"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Approvers<span className="text-red-200">*</span>
            </Label>
            <ReactSelectMulti
              options={userData}
              placeholder="Select approvers"
              value={data?.approvers}
              onChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  approvers: value as IOption[],
                }));
              }}
              hasError={
                Boolean(validationErrors?.approvers[0]) || sameUserError
              }
            />
            {validationErrors?.approvers[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.approvers[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col  flex-1">
            <Label
              htmlFor="launch_date"
              className="text-base font-medium leading-6 text-dark-100 mb-2.5"
            >
              Estimated launch date<span className="text-red-200">*</span>
            </Label>
            <Calendar
              selectedDate={data?.launch_date}
              onDateChange={(date) => {
                if (date) {
                  setData((prev) => ({
                    ...prev,
                    launch_date: moment(date as string).format('YYYY-MM-DD'),
                  }));
                } else {
                  setData((prev) => ({
                    ...prev,
                    launch_date: '',
                  }));
                }
              }}
              className={
                validationErrors?.launch_date[0] ? 'border !border-red-200' : ''
              }
            />
            {validationErrors?.launch_date[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200 mt-2.5">
                {validationErrors?.launch_date[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="version"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Version<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Enter version"
              id="version"
              type="number"
              name="version"
              value={data?.version}
              onChange={(e) =>
                setData((pre) => ({ ...pre, version: Number(e.target.value) }))
              }
              errorMsg={validationErrors?.version[0]}
              required
            />
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <PrimaryButton
            size="medium"
            text={edit ? 'Update' : 'Submit'}
            onClick={handleSubmit}
            isLoading={createIsloading || updateLoading}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default CreateProductModal;
