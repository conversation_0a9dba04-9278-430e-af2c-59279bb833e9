import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { useParams } from 'next/navigation';
import Loader from '../common/loader';
import LogsCard from '../document/components/logsCard';
// import { useParams } from "react-router-dom";

// import useFetch from "../../hooks/useFetch";
// import LogList from "../List/LogList";

const LogTabs = () => {
  const param = useParams();
  const { accessToken, user } = useAuthStore();
  const {
    data: logs,
    isLoading,
    error,
    reFetch,
  } = useFetch<{
    records: any[];
  }>(accessToken, `products/${param?.productId}/logs`);

  return (
    <>
      {isLoading ? (
        <Loader className="h-[700px]" />
      ) : logs?.records?.length && logs?.records?.length > 0 ? (
        logs?.records.map((e, i) => <LogsCard logs={e} key={i} />)
      ) : (
        <>
          <div className="w-full h-24 bg-white-100 border border-white-300 rounded-lg p-4">
            <p className="text-base text-dark-300 leading-6 font-medium">
              No logs have been added yet
            </p>
          </div>
        </>
      )}
    </>
  );
};

export default LogTabs;
