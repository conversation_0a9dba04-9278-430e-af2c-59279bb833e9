import { Info } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';

interface AddStepModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: () => void;
  isTerminalStartUsed: boolean;
  isTerminalStopUsed: boolean;
}

export interface StepFormData {
  name: string;
  type: string;
  description: string;
}

const AddStepModal: React.FC<AddStepModalProps> = ({
  open,
  onOpenChange,
  onSave,
  isTerminalStartUsed,
  isTerminalStopUsed,
}) => {
  const router = useRouter();
  const { productId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);

  // Initialize the POST hook
  const { postData, isLoading, response, error } = usePost();

  const [formData, setFormData] = useState<StepFormData>({
    name: '',
    type: '',
    description: '',
  });

  const [errors, setErrors] = useState<{
    name?: string;
    type?: string;
    description?: string;
  }>({});

  // Map the UI step type to API step type
  const mapStepTypeToAPI = (uiType: string): string => {
    const typeMap: Record<string, string> = {
      'Terminal start': 'terminal_start',
      Step: 'step',
      Decision: 'decision',
      'Terminal stop': 'terminal_stop',
    };
    return typeMap[uiType] || '';
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when user types
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  const handleTypeChange = (value: string) => {
    setFormData((prev) => ({ ...prev, type: value }));

    // Clear error when user selects a type
    if (errors.type) {
      setErrors((prev) => ({ ...prev, type: undefined }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: {
      name?: string;
      type?: string;
      description?: string;
    } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Step name is required';
    }

    if (!formData.type) {
      newErrors.type = 'Step type is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle API response
  useEffect(() => {
    if (response) {
      // Call the onSave callback to update the UI
      onSave();

      // Reset form after save
      setFormData({
        name: '',
        type: '',
        description: '',
      });

      // Close the modal
      onOpenChange(false);
    }
  }, [response]);

  // Handle API error
  useEffect(() => {
    if (error) {
      console.error('Error creating step:', error);
      // You could set specific error messages here based on the error response
      setErrors((prev) => ({
        ...prev,
        name: error.message || 'Failed to create step. Please try again.',
      }));
    }
  }, [error]);

  // Get the current steps count from the processTab component
  const getStepsCount = () => {
    try {
      // Try to get the steps count from the parent component
      const stepsCount = document.querySelectorAll(
        '[data-step-item="true"]',
      ).length;
      return stepsCount + 1; // New step will be the next one
    } catch (error) {
      console.error('Error getting steps count:', error);
      return 1; // Default to 1 if we can't determine
    }
  };

  const handleSave = () => {
    if (validateForm()) {
      // Get the current number of steps to determine the sequence number
      const sequenceNumber = getStepsCount();

      // Prepare the API request body
      const apiBody = {
        step_name: formData.name,
        step_type: mapStepTypeToAPI(formData.type),
        description: formData.description,
        sequence_no: sequenceNumber,
      };

      // Make the API call
      postData(
        accessToken as string,
        `products/${productId}/process_step/details`,
        apiBody,
      );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="min-w-[47.875rem] p-8">
        <DialogHeader>
          <DialogTitle>Add new step</DialogTitle>
        </DialogHeader>

        <div className="mt-6 space-y-6">
          <div className="grid grid-cols-2 gap-6">
            <div>
              <Label
                htmlFor="stepName"
                className="text-base font-medium text-dark-300 mb-2 block"
              >
                Step name
              </Label>
              <Input
                id="stepName"
                name="name"
                placeholder="Enter step name"
                value={formData.name}
                onChange={handleChange}
                errorMsg={errors.name}
              />
            </div>

            <div>
              <Label
                htmlFor="stepType"
                className="text-base font-medium text-dark-300 mb-2 block"
              >
                Step type
              </Label>
              <Select value={formData.type} onValueChange={handleTypeChange}>
                <SelectTrigger
                  id="stepType"
                  className={errors.type ? 'border-red-200' : ''}
                >
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  {!isTerminalStartUsed && (
                    <SelectItem value="Terminal start">
                      Terminal start
                    </SelectItem>
                  )}
                  <SelectItem value="Step">Step</SelectItem>
                  <SelectItem value="Decision">Decision</SelectItem>
                  {!isTerminalStopUsed && (
                    <SelectItem value="Terminal stop">Terminal stop</SelectItem>
                  )}
                </SelectContent>
              </Select>
              {errors.type && (
                <div className="text-xs font-semibold leading-5 text-left text-red-200 mt-1">
                  {errors.type}
                </div>
              )}
            </div>
          </div>

          <div>
            <Label
              htmlFor="description"
              className="text-base font-medium text-dark-300 mb-2 block"
            >
              Description
            </Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description"
              value={formData.description}
              onChange={handleChange}
              errorMsg={errors.description}
              className="min-h-[120px]"
            />
          </div>

          <div className="flex items-center gap-3 py-3 px-4 bg-white-150 rounded-lg">
            <div className="bg-[#91909A29] h-9 w-9 rounded-full flex justify-center items-center flex-shrink-0">
              <Info className="w-5 h-5 text-gray-500" />
            </div>
            <p className="text-sm text-dark-300 font-medium">
              Add conditional branching to the relevant work instruction by
              setting it as a validation rule.
            </p>
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <PrimaryButton
            text={isLoading ? 'Saving...' : 'Save'}
            size="medium"
            onClick={handleSave}
            disabled={isLoading}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddStepModal;
