import { ChevronDown } from 'lucide-react';
import React, { useState } from 'react';

import { ReactFlowProvider } from '@xyflow/react';

interface FlowchartAccordionProps {
  data: Record<string, any>;
  title?: string;
  placeholder?: string;
  children?: React.ReactNode;
}

const FlowchartAccordion: React.FC<FlowchartAccordionProps> = ({
  data,
  title = 'Process flowchart',
  placeholder = 'Create process flowchart',
  children,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  // Process data from the parent component
  const processData = data?.flowchart;

  // Check if we have flowchart data
  const hasFlowchartData = processData?.nodes?.length > 0;

  return (
    <div className="w-full mb-6">
      {/* Header section with title if provided */}
      {title && (
        <div className="text-lg font-medium text-dark-300 mb-4 ">{title}</div>
      )}

      {/* Accordion trigger that looks like a select box */}
      <div className="w-full ">
        <div
          className={`flex h-11 w-full items-center justify-between rounded-lg border border-grey-100 bg-transparent px-4 py-2 text-base leading-6 text-dark-300  cursor-pointer ${
            isOpen ? 'border-grey-100 border-b-0 rounded-b-none' : ''
          }`}
          onClick={() => setIsOpen(!isOpen)}
          role="button"
          aria-expanded={isOpen}
        >
          <span>
            {hasFlowchartData ? 'View process flowchart' : placeholder}
          </span>
          <ChevronDown
            size={20}
            className={`transition-transform duration-300 ${
              isOpen ? 'rotate-180' : ''
            }`}
          />
        </div>
      </div>

      {/* Accordion content */}
      {isOpen && (
        <div
          className={`w-full border border-grey-100 rounded-lg p-4 mb-6 transition-all duration-300 ease-in-out ${
            isOpen ? 'rounded-t-none' : ''
          }`}
        >
          <div className="w-full h-[35vh] relative">
            {children ? (
              children
            ) : (
              <ReactFlowProvider>
                {/* You can replace this with your PreviewFlow component */}
                <div className="w-full h-full flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    {hasFlowchartData
                      ? 'Loading flowchart...'
                      : 'No flowchart data available'}
                  </div>
                </div>
              </ReactFlowProvider>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FlowchartAccordion;
