import DeleteIcon from '@/assets/outline/delete';
import { NodeToolbar, Position } from '@xyflow/react';

type NodeToolbarProps = {
  onDelete: () => void;
};

function CustomNodeToolbar({ onDelete }: NodeToolbarProps) {
  return (
    <NodeToolbar className="nodrag" offset={32} position={Position.Right}>
      <div onClick={onDelete} className="cursor-pointer bg-red-100">
        <DeleteIcon color="#E05252" />
      </div>
    </NodeToolbar>
  );
}

export default CustomNodeToolbar;
