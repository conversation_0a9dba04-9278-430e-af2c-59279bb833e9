import type { XYPosition } from '@xyflow/react';


import type { ControlPointData } from '../ControlPoint';

export const isControlPoint = (
  point: ControlPointData | XYPosition
): point is ControlPointData => 'id' in point;

export const getAdjustedLabelPosition = (
  labelX: number,
  labelY: number,
  sourceX: number,
  sourceY: number,
  targetX: number,
  targetY: number,
  zoom: number, // Pass current zoom level
  baseOffset = 15, // Base perpendicular offset
  baseExtraHorizontalOffset = 10 // Extra horizontal push for vertical edges
) => {
  const dx = targetX - sourceX;
  const dy = targetY - sourceY;
  const length = Math.sqrt(dx * dx + dy * dy) || 1; // Avoid division by zero

  // Scale offsets based on zoom level
  const offsetDistance = baseOffset / zoom;
  const extraHorizontalOffset = baseExtraHorizontalOffset / zoom;

  // Get the perpendicular unit vector
  const perpX = (dy / length) * offsetDistance;
  const perpY = (-dx / length) * offsetDistance;

  // Determine the edge angle in degrees
  const angle = Math.atan2(dy, dx) * (180 / Math.PI);
  const isVertical = Math.abs(dy) > Math.abs(dx);

  // Apply perpendicular offset
  let adjustedLabelX = labelX + perpX;
  let adjustedLabelY = labelY + perpY;

  // If the edge is more vertical, add extra horizontal offset
  if (isVertical) {
    adjustedLabelX += dx > 0 ? extraHorizontalOffset : -extraHorizontalOffset;
  }

  // If the edge is at an angle between 30° and 60°, push the label further away
  if (Math.abs(angle) > 30 && Math.abs(angle) < 60) {
    adjustedLabelX += perpX * 1.5; // Increase offset in diagonal cases
    adjustedLabelY += perpY * 1.5;
  }

  return { x: adjustedLabelX, y: adjustedLabelY };
};
