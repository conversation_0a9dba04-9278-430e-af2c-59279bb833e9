import { useCallback, useRef } from "react";

import {
  BaseEdge,
  BuiltInNode,
  Edge,
  EdgeLabelRenderer,
  EdgeProps,
  getStraightPath,
  useReactFlow,
  useStore,
  XYPosition,
} from "@xyflow/react";


import { COLORS, Label } from './constants';
import { ControlPoint, ControlPointData } from './ControlPoint';
import { getLinearControlPoints, getLinearPath } from './path/linear';
import { getAdjustedLabelPosition } from './path/utils';

const useIdsForInactiveControlPoints = (points: ControlPointData[]) => {
  const ids = useRef<string[]>([]);

  if (ids.current.length === points.length) {
    return points.map((point, i) =>
      point.id ? point : { ...point, id: ids.current[i] },
    );
  } else {
    ids.current = [];

    return points.map((point, i) => {
      if (!point.id) {
        const id = window.crypto.randomUUID();
        ids.current[i] = id;
        return { ...point, id: id };
      } else {
        ids.current[i] = point.id;
        return point;
      }
    });
  }
};

export type EditableEdge = Edge<{
  algorithm?: Algorithm;
  points: ControlPointData[];
  label?: string;
}>;

export function EditableEdgeComponent({
  id,
  selected,
  source,
  sourceX,
  sourceY,
  sourcePosition,
  target,
  targetX,
  targetY,
  targetPosition,
  markerEnd,
  markerStart,
  style,
  data = { points: [] },
  ...delegated
}: EdgeProps<EditableEdge>) {
  const sourceOrigin = { x: sourceX, y: sourceY } as XYPosition;
  const targetOrigin = { x: targetX, y: targetY } as XYPosition;

  const { setEdges, getZoom } = useReactFlow<BuiltInNode, EditableEdge>();
  const shouldShowPoints = useStore((store) => {
    const sourceNode = store.nodeLookup.get(source)!;
    const targetNode = store.nodeLookup.get(target)!;

    return selected || sourceNode.selected || targetNode.selected;
  });

  const setControlPoints = useCallback(
    (update: (points: ControlPointData[]) => ControlPointData[]) => {
      setEdges((edges) =>
        edges.map((e) => {
          if (e.id !== id) return e;
          if (!isEditableEdge(e)) return e;

          const points = e.data?.points ?? [];
          const data = { ...e.data, points: update(points) };

          return { ...e, data };
        }),
      );
    },
    [id, setEdges],
  );

  const pathPoints = [sourceOrigin, ...data.points, targetOrigin];
  const controlPoints = getLinearControlPoints(pathPoints);
  const path = getLinearPath(pathPoints);
  const zoom = getZoom(); // Get the current zoom level

  const [edgePath, labelX, labelY] = getStraightPath({
    sourceX,
    sourceY,
    targetX,
    targetY,
  });

  // Calculate the vector from source to center
  const dx = labelX - sourceX;
  const dy = labelY - sourceY;
  const length = Math.sqrt(dx * dx + dy * dy) || 1; // Avoid division by zero

  // Adjust label position to the start of the edge (source)
  const adjustedLabelX = labelX - dx;
  const adjustedLabelY = labelY - dy;

  // Add padding to avoid sticking to the source node
  const padding = 20; // Adjust padding as needed

  // If the edge is nearly horizontal (flat), adjust the padding horizontally

  // Apply padding in the direction of the edge
  let paddedLabelX = adjustedLabelX + (dx / length) * padding;
  const paddedLabelY = adjustedLabelY + (dy / length) * padding;

  if (Math.abs(dy) < Math.abs(dx)) {
    paddedLabelX = adjustedLabelX + (dx > 0 ? padding : -padding); // Move label right or left for horizontal edges
  }

  const controlPointsWithIds = useIdsForInactiveControlPoints(controlPoints);

  return (
    <>
      <BaseEdge
        id={id}
        path={path}
        {...delegated}
        markerStart={markerStart}
        markerEnd={markerEnd}
        style={{
          ...style,
          strokeWidth: 2,
          stroke: COLORS[data.label ? data.label : Label.Regular],
        }}
      />
      <EdgeLabelRenderer>
        {data!.label !== Label.Regular && (
          <button
            style={{
              position: 'absolute',
              transform: `translate(-50%, -50%) translate(${paddedLabelX}px,${paddedLabelY}px)`,
              pointerEvents: 'all',
              fontSize: '10px',
            }}
          >
            {data!.label}
          </button>
        )}
      </EdgeLabelRenderer>
      {shouldShowPoints &&
        controlPointsWithIds.map((point, index) => (
          <ControlPoint
            key={point.id}
            index={index}
            setControlPoints={setControlPoints}
            color={COLORS[data.label ? data.label : Label.Regular]}
            {...point}
          />
        ))}
    </>
  );
}

const isEditableEdge = (edge: Edge): edge is EditableEdge =>
  edge.type === "editable-edge";
