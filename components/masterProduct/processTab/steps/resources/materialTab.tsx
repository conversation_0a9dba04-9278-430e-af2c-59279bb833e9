import { Plus } from 'lucide-react';
import React, { useCallback, useMemo, useState } from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import CreatableSingleSelect, {
  IOption,
} from '@/components/common/creatableSelect';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import { ValueFormatterParams } from '@ag-grid-community/core';

export interface Material {
  id: string;
  name: string;
  quantity: string;
  unit: string;
  material_id?: string; // For API compatibility
}

interface MaterialTabProps {
  materials: Material[];
  onMaterialsChange: (materials: Material[]) => void;
}

// Material tab content
const MaterialTab: React.FC<MaterialTabProps> = ({
  materials,
  onMaterialsChange,
}) => {
  const [selectedMaterial, setSelectedMaterial] = useState<IOption | undefined>(
    undefined,
  );
  const [quantity, setQuantity] = useState('');
  // Dynamically show the selected material's unit (from materials API)
  const [unit, setUnit] = useState<string>('');
  const [editingMaterial, setEditingMaterial] = useState<Material | null>(null);

  const handleMaterialChange = (option: IOption) => {
    setSelectedMaterial(option);
    // If meta has units, store it for display
    const maybeUnits = (option as any)?.meta?.units as string | undefined;
    if (maybeUnits) setUnit(maybeUnits);
    else setUnit('');
  };

  const handleAddMaterial = () => {
    if (selectedMaterial && quantity.trim()) {
      if (editingMaterial) {
        // Update existing material
        const updatedMaterials = materials.map((material) =>
          material.id === editingMaterial.id
            ? {
                ...material,
                id: selectedMaterial.value,
                name: selectedMaterial.label,
                quantity: quantity || '0',
                material_id: selectedMaterial.value, // For API compatibility
              }
            : material,
        );
        onMaterialsChange(updatedMaterials);
        setEditingMaterial(null);
      } else {
        // Add new material
        const newMaterial = {
          id: selectedMaterial.value || `material-${Date.now()}`,
          name: selectedMaterial.label,
          quantity: quantity || '0',
          unit: unit || '—',
          material_id: selectedMaterial.value, // For API compatibility
        };
        onMaterialsChange([...materials, newMaterial]);
      }
      setSelectedMaterial(undefined);
      setQuantity('');
    }
  };

  const handleEditMaterial = useCallback((material: Material) => {
    setSelectedMaterial({
      value: material.id,
      label: material.name,
    });
    setQuantity(material.quantity);
    setUnit(material.unit || '');
    setEditingMaterial(material);
  }, []);

  const handleDeleteMaterial = useCallback(
    (material: Material) => {
      const updatedMaterials = materials.filter((m) => m.id !== material.id);
      onMaterialsChange(updatedMaterials);

      if (editingMaterial?.id === material.id) {
        setEditingMaterial(null);
        setSelectedMaterial(undefined);
        setQuantity('');
      }
    },
    [editingMaterial, materials, onMaterialsChange],
  );

  const columnDefs = useMemo(() => {
    return [
      {
        headerName: 'Materials',
        field: 'name',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: Record<string, unknown>) =>
          params.value as string,
        valueFormatter: (params: ValueFormatterParams) =>
          params.value as string,
        filter: true,
        flex: 2,
      },
      {
        headerName: 'Quantity',
        field: 'quantity',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: Record<string, unknown>) =>
          params.value as string,
        valueFormatter: (params: ValueFormatterParams) =>
          `${params.data.quantity} ${params.data.unit}`,
        filter: true,
        flex: 1,
      },
      {
        headerName: 'Manage',
        field: 'manage',
        sortable: false,
        resizable: false,
        getQuickFilterText: () => '',
        valueFormatter: () => '',
        filter: false,
        cellRenderer: (params: Record<string, unknown>) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={handleEditMaterial}
            handleDelete={handleDeleteMaterial}
          />
        ),
        width: 120,
      },
    ];
  }, [handleEditMaterial, handleDeleteMaterial]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between">
        <div className="flex-1">
          <Label
            htmlFor="add-material"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Add material:
          </Label>
          <div className="flex items-center gap-4 mt-2">
            <CreatableSingleSelect
              placeholder="Search material"
              selectedOption={selectedMaterial}
              onChange={handleMaterialChange}
              endpoint="materials"
              isCreatable={false}
            />
          </div>
        </div>

        <div className="ml-4">
          <Label
            htmlFor="quantity"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Quantity
          </Label>
          <div className="flex mt-2">
            <Input
              id="quantity"
              placeholder="Enter here"
              value={quantity}
              onChange={(e) => setQuantity(e.target.value)}
              className="w-32 rounded-r-none"
            />
            <div className="flex items-center justify-center h-11 rounded-r-lg border bg-white-100 border-grey-100 border-l-0  px-3 py-2 text-base text-gray-200">
              {unit || '—'}
            </div>
          </div>
        </div>

        <div className="flex items-end ml-4">
          <SecondaryButton
            size="medium"
            text="Add"
            icon={<Plus size={20} />}
            iconPosition="left"
            onClick={handleAddMaterial}
            buttonClasses="h-11"
          />
        </div>
      </div>

      {materials.length > 0 && (
        <div className="mt-6">
          <CommonTable
            data={{ records: materials }}
            columnDefs={columnDefs}
            searchBox={false}
            paginate={false}
            height={300}
          />
        </div>
      )}
    </div>
  );
};

export default MaterialTab;
