import { Plus, X } from 'lucide-react';
import { KeyboardEvent, useCallback, useEffect, useState } from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import CreatableSingleSelect, { IOption } from '@/components/common/creatableSelect';
import { Label } from '@/components/common/label';

export interface Person {
  id: string;
  role: string;
}

interface PeopleSectionProps {
  title: string;
  placeholder: string;
  users: Person[];
  onAddUser: (role: string, selectedOption?: IOption) => void;
  onRemoveUser: (id: string) => void;
}

interface PeopleTabProps {
  people: Person[] | string[];
  onPeopleChange: (people: Person[]) => void;
  title?: string;
  placeholder?: string;
}

const PeopleSection = ({
  title,
  placeholder,
  users,
  onAddUser,
  onRemoveUser,
}: PeopleSectionProps) => {
  const [selectedUser, setSelectedUser] = useState<IOption | undefined>(
    undefined,
  );

  const handleUserChange = useCallback((option: IOption) => {
    setSelectedUser(option);
  }, []);

  const handleAddUser = useCallback(() => {
    if (selectedUser?.label) {
      onAddUser(selectedUser.label, selectedUser);
      setSelectedUser(undefined);
    }
  }, [selectedUser, onAddUser]);

  const handleKeyDown = useCallback(
    (e: KeyboardEvent) => {
      if (e.key === 'Enter' && selectedUser?.label) {
        e.preventDefault();
        onAddUser(selectedUser.label, selectedUser);
        setSelectedUser(undefined);
      }
    },
    [selectedUser, onAddUser],
  );

  return (
    <div className="border border-white-300 rounded-lg p-5 mb-5">
      <Label className="text-base font-medium text-dark-100 mb-2 block">
        {title}
      </Label>

      <div className="flex items-center gap-4 mb-2">
        <div className="flex-1">
          <div onKeyDown={handleKeyDown}>
            <CreatableSingleSelect
              placeholder={placeholder}
              selectedOption={selectedUser}
              onChange={handleUserChange}
              endpoint="users"
              isCreatable={false} // Disable creation of new options
            />
          </div>
        </div>
        <PrimaryButton
          size="medium"
          text="Add user"
          icon={<Plus size={20} />}
          iconPosition="left"
          onClick={handleAddUser}
        />
      </div>

      {users.length === 0 && (
        <div className="text-base text-grey-200 font-medium mt-5">
          Search to add people
        </div>
      )}

      {users.length > 0 && (
        <div className="flex flex-wrap gap-2.5 mt-5">
          {users.map((user) => (
            <div
              key={user.id}
              className="flex items-center bg-white-200 rounded-md px-2 py-1 gap-2"
            >
              <span className="text-base text-dark-300">{user.role}</span>
              <button onClick={() => onRemoveUser(user.id)}>
                <X size={16} />
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

// People tab content
const PeopleTab: React.FC<PeopleTabProps> = ({
  people,
  onPeopleChange,
  title = 'Add People',
  placeholder = 'Search users',
}) => {
  // Convert people array to Person objects for UI
  const [users, setUsers] = useState<Person[]>(() => {
    // Check if people is already an array of Person objects
    if (people.length > 0 && typeof people[0] === 'object') {
      return people as Person[];
    }

    // Otherwise, convert string array to Person objects
    return (people as string[]).map((personId) => ({
      id: personId,
      role: personId,
    }));
  });

  // Update parent component when users change
  useEffect(() => {
    onPeopleChange(users);
  }, [users]);

  // Function to generate a UUID v4
  const generateUUID = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(
      /[xy]/g,
      function (c) {
        const r = (Math.random() * 16) | 0,
          v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      },
    );
  };

  const handleAddUser = useCallback(
    (role: string, selectedOption?: IOption) => {
      // Check if user already exists
      const userExists = users.some((user) => user.role === role);

      if (!userExists) {
        const newUser = {
          // Use the value from CreatableSingleSelect if available (which should be a UUID)
          // Otherwise use the role as ID (for email addresses)
          id:
            selectedOption?.value ||
            (role.includes('@') ? role : generateUUID()),
          role,
        };
        setUsers((prev) => [...prev, newUser]);
      }
    },
    [users],
  );

  const handleRemoveUser = useCallback((userId: string) => {
    setUsers((prev) => prev.filter((user) => user.id !== userId));
  }, []);

  return (
    <div className="space-y-6">
      <PeopleSection
        title={title}
        placeholder={placeholder}
        users={users}
        onAddUser={handleAddUser}
        onRemoveUser={handleRemoveUser}
      />
    </div>
  );
};

export default PeopleTab;
