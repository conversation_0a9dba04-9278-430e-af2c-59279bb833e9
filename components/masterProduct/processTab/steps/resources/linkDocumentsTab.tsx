import axios from 'axios';
import { Trash2 } from 'lucide-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import TertiaryButton from '@/components/common/button/tertiaryButton';
import FileCard from '@/components/common/modals/uploadModal/fileCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import CommonTable from '@/components/common/table';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { ValueFormatterParams } from '@ag-grid-community/core';

export interface Document {
  id: string;
  name: string;
  type: string;
}

interface IDocumentDetails {
  id: string;
  title: string;
}

interface TableItem {
  id: string;
  name: string;
  type: string;
  isDocument?: boolean;
  isAttachment?: boolean;
}

interface Attachment {
  id?: string; // Optional since it's no longer supported
  file_path: string;
  file_extension: string;
  name?: string;
  type: 'independent';
}

interface LinkDocumentsTabProps {
  documents: Document[];
  attachments: Attachment[];
  onDocumentsChange: (documents: Document[]) => void;
  onAttachmentsChange: (attachments: Attachment[]) => void;
}

// Accept file types for the dropzone
const acceptFileTypes = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
    '.doc',
  ],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
  'application/vnd.ms-excel': ['.xls', '.csv'],
};

// Link Documents tab content
const LinkDocumentsTab: React.FC<LinkDocumentsTabProps> = ({
  documents: initialDocuments,
  attachments: initialAttachments,
  onDocumentsChange,
  onAttachmentsChange,
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>('');
  const [documents, setDocuments] = useState<Document[]>([]);

  // Initialize documents from props
  useEffect(() => {
    if (initialDocuments && initialDocuments.length > 0) {
      setDocuments(initialDocuments);
    }
  }, [initialDocuments]);

  const [addedFile, setAddedFile] = useState<File[] | null>(null);
  const [documentType, setDocumentType] = useState<string>('doc_hub');

  // Fetch published documents
  const { data: publishedDocuments } = useFetch<
    { records: IDocumentDetails[] },
    { status: string }
  >(accessToken, 'documents', {
    status: 'Published',
  });

  // Handle document selection from document hub
  const handleDocumentChange = useCallback((value: string) => {
    setSelectedDocumentId(value);
    setDocumentType('doc_hub');
    setAddedFile(null); // Clear any uploaded file when selecting a document
  }, []);

  // Handle adding document from document hub
  const handleAddDocument = useCallback(() => {
    if (selectedDocumentId && publishedDocuments?.records) {
      // Check if document already exists
      const documentExists = documents.some(
        (doc) => doc.id === selectedDocumentId,
      );

      if (!documentExists) {
        const selectedDoc = publishedDocuments.records.find(
          (doc) => doc.id === selectedDocumentId,
        );

        if (selectedDoc) {
          const newDocuments = [
            ...documents,
            {
              id: selectedDoc.id,
              name: selectedDoc.title,
              type: 'doc_hub',
            },
          ];

          setDocuments(newDocuments);

          // Call the parent callback with the updated document IDs
          onDocumentsChange(newDocuments.map((doc) => doc));
        }
      }

      setSelectedDocumentId('');
    }
  }, [selectedDocumentId, documents, publishedDocuments]);

  // Handle file drop
  const onDrop = useCallback((acceptedFiles: File[]) => {
    setAddedFile(acceptedFiles);
    setDocumentType('independent');
    setSelectedDocumentId(''); // Clear selected document when uploading a file
  }, []);

  // Setup dropzone
  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: false,
    accept: acceptFileTypes,
  });

  // Track loading state for file upload
  const [isUploading, setIsUploading] = useState(false);

  // File upload handler
  const handleFileUpload = useCallback(
    async (
      file: File,
    ): Promise<{ file_path: string; file_extension: string } | null> => {
      try {
        const formData = new FormData();
        formData.append('file', file);

        const baseUrl = process.env.NEXT_PUBLIC_URL;
        const productVersion = process.env.NEXT_PUBLIC_VERSION;

        // Adjust the sub_path as needed for your application
        const url = `${baseUrl}/${productVersion}/file/upload?document_for=product_hub&sub_path=/documents`;
        const orgId =
          typeof window !== 'undefined'
            ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
            : null;
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: `Bearer ${accessToken}`,
            ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
          },
        };

        const response = await axios.post(url, formData, config);

        if (response.status === 200) {
          return {
            file_path: response.data.file_path,
            file_extension: response.data.file_ext,
          };
        } else {
          console.error('Error uploading file:', file.name);
          toast.error(`Failed to upload file: ${file.name}`);
          return null;
        }
      } catch (error) {
        console.error('Error uploading file:', error);
        toast.error(`Failed to upload file: ${file.name}`);
        return null;
      }
    },
    [accessToken],
  );

  // Handle adding uploaded file
  const handleAddUploadedFile = useCallback(async () => {
    if (addedFile && addedFile.length > 0) {
      // Upload the file
      setIsUploading(true);
      try {
        const uploadResult = await handleFileUpload(addedFile[0]);
        if (!uploadResult) {
          toast.error('File upload failed');
          return;
        }

        // Create a new attachment with the response data
        const newAttachment: Attachment = {
          // id is no longer needed
          name: addedFile[0].name,
          type: 'independent',
          file_path: uploadResult.file_path,
          file_extension: uploadResult.file_extension,
        };

        // Get current attachments - make sure to include all required fields
        const currentAttachments = (initialAttachments || []).map(
          (attachment) => ({
            file_path: attachment.file_path,
            file_extension: attachment.file_extension,
            name: attachment.name,
            type: attachment.type,
          }),
        );

        // Create updated attachments array
        const updatedAttachments = [...currentAttachments, newAttachment];

        // Call the parent callbacks - only update attachments
        onAttachmentsChange(updatedAttachments as Attachment[]);

        // Clear the file input
        setAddedFile(null);

        // Show success message
        toast.success('File uploaded successfully');
      } catch (error) {
        console.error('Error uploading file:', error);
        toast.error('Failed to upload file');
      } finally {
        setIsUploading(false);
      }
    }
  }, [addedFile, initialAttachments, handleFileUpload]);

  // Handle document removal
  const handleRemoveDocument = useCallback(
    (
      itemId: string,
      data?: { isDocument?: boolean; isAttachment?: boolean },
    ) => {
      // Check if it's a document from document hub or an attachment based on the data passed
      const isDocument = data?.isDocument;
      const isAttachment = data?.isAttachment;

      if (isDocument) {
        // Remove from documents
        const newDocuments = documents.filter((doc) => doc.id !== itemId);
        setDocuments(newDocuments);

        // Call the parent callback with the updated document IDs
        onDocumentsChange(newDocuments.map((doc) => doc));
      } else if (isAttachment) {
        // It's an attachment - filter by file_path since we're using that as the ID
        const newAttachments = initialAttachments
          .filter((attachment) => attachment.file_path !== itemId)
          .map((attachment) => ({
            file_path: attachment.file_path,
            file_extension: attachment.file_extension,
            name: attachment.name,
            type: attachment.type,
          }));

        // Call the parent callback with the updated attachments
        onAttachmentsChange(newAttachments as Attachment[]);
      }
    },
    [documents, initialAttachments],
  );

  // Combine documents and attachments for display in the table
  const tableData = useMemo(() => {
    // Filter documents to only include those from document hub (not uploaded files)
    const docHubItems = documents
      .filter((doc) => doc.type === 'doc_hub')
      .map((doc) => ({
        id: doc.id,
        name: doc.name,
        type: doc.type,
        isDocument: true,
      }));

    // For attachments, we only want to show the successfully uploaded files
    const attachmentItems = initialAttachments.map((attachment, index) => ({
      // Use file_path as a unique identifier since id is no longer supported
      id: attachment.file_path,
      name: attachment.name || `File ${index + 1}`,
      type: 'independent',
      isAttachment: true,
      // Keep the original data for reference
      originalData: attachment,
    }));

    // Return the combined list - only doc hub documents and uploaded attachments
    return [...docHubItems, ...attachmentItems];
  }, [documents, initialAttachments]);

  // Table column definitions
  const columnDefs = useMemo(() => {
    return [
      {
        headerName: 'Document',
        field: 'name',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: Record<string, unknown>) =>
          params.value as string,
        valueFormatter: (params: ValueFormatterParams) =>
          params.value as string,
        filter: true,
        flex: 2,
      },
      {
        headerName: 'Type',
        field: 'type',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: Record<string, unknown>) =>
          params.value as string,
        valueFormatter: (params: ValueFormatterParams) => {
          const type = params.value as string;
          return type === 'doc_hub' ? 'Document Hub' : 'Uploaded File';
        },
        filter: true,
        flex: 1,
      },
      {
        headerName: 'Manage',
        field: 'manage',
        sortable: false,
        resizable: false,
        getQuickFilterText: () => '',
        valueFormatter: () => '',
        filter: false,
        cellRenderer: (params: Record<string, unknown>) => (
          <div className="flex justify-center h-full items-center">
            <button
              onClick={(e) => {
                e.stopPropagation();
                // Pass both the ID and the full data object
                const data = params.data as {
                  id: string;
                  isDocument?: boolean;
                  isAttachment?: boolean;
                };
                handleRemoveDocument(data.id, data);
              }}
              className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
              title="Delete"
            >
              <Trash2 height={16} />
            </button>
          </div>
        ),
        width: 120,
      },
    ];
  }, [handleRemoveDocument]);

  return (
    <div className="space-y-6 mb-6">
      <div>
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Link Document from document hub
        </div>
        <div className="flex items-center gap-4 mb-2">
          <div className="flex-1">
            <Select
              value={selectedDocumentId}
              onValueChange={handleDocumentChange}
            >
              <SelectTrigger id="document-select">
                <SelectValue placeholder="Select documents" />
              </SelectTrigger>
              <SelectContent>
                {publishedDocuments?.records?.map((doc, i) => (
                  <SelectItem value={doc.id} key={i}>
                    {doc.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <PrimaryButton
            size="medium"
            text="Add Document"
            onClick={handleAddDocument}
            disabled={!selectedDocumentId}
          />
        </div>
      </div>

      <div className="w-full h-0 border-b border-white-300 relative my-4">
        <div className="absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 bg-white-100 px-4 text-sm font-medium text-grey-300">
          Or
        </div>
      </div>

      <div>
        <div className="text-base leading-6 font-medium text-dark-100 mb-2.5">
          Attach document
        </div>

        <div>
          <div
            className={`min-h-28 border border-dashed rounded-xl flex items-center justify-center flex-col gap-2 p-6 ${
              documentType === 'independent' && addedFile?.length
                ? 'bg-white-100 border-teal-600'
                : 'bg-white-100 border-[#C7C7CC] hover:bg-[#F8F8F8]'
            }`}
            {...getRootProps()}
          >
            {!(addedFile?.length && addedFile?.length > 0) && (
              <div className="text-sm font-medium leading-5 text-[#49474E]">
                Upload or Drag and drop to upload your file
              </div>
            )}

            <input {...getInputProps()} />
            <div className="flex justify-center items-center flex-wrap gap-2">
              {addedFile?.map((file, index) => (
                <FileCard key={index} file={file} setAddedFile={setAddedFile} />
              ))}
            </div>
            <TertiaryButton
              text={
                addedFile?.length && addedFile?.length > 0
                  ? 'Replace'
                  : 'Select file'
              }
              size="small"
            />
          </div>
        </div>

        {addedFile && addedFile.length > 0 && (
          <div className="flex justify-end mt-4">
            <PrimaryButton
              size="medium"
              text={isUploading ? 'Uploading...' : 'Add File'}
              onClick={handleAddUploadedFile}
              disabled={isUploading}
            />
          </div>
        )}
      </div>

      {tableData.length > 0 && (
        <div className="my-6">
          <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
            Linked Documents
          </div>
          <CommonTable
            data={{ records: tableData }}
            columnDefs={columnDefs}
            searchBox={false}
            paginate={false}
            height={300}
          />
        </div>
      )}
    </div>
  );
};

export default LinkDocumentsTab;
