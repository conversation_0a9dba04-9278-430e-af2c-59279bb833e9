import { Plus } from 'lucide-react';
import Image from 'next/image';
import React from 'react';

import NotesIcon from '@/assets/outline/note';
import PrimaryButton from '@/components/common/button/primaryButton';
import { cn } from '@/utils/styleUtils';

interface WorkInstructionZerostateProps {
  onAddWorkInstruction?: () => void;
  className?: string;
}

const WorkInstructionZerostate: React.FC<WorkInstructionZerostateProps> = ({
  onAddWorkInstruction,
  className,
}) => {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center py-8 px-6 bg-white-100 border border-dashed border-grey-200 rounded-lg h-72',
        className,
      )}
    >
      <div className="mb-4">
        <div className="w-14 h-14 flex items-center justify-center">
          <NotesIcon />
        </div>
      </div>

      <h3 className="text-lg font-medium text-dark-300 mb-2">
        No work instructions added
      </h3>

      <p className="text-sm text-grey-300 mb-6 text-center">
        Add instructions to guide operators through this process step.
      </p>

      <PrimaryButton
        size="medium"
        text="Add first instruction"
        icon={<Plus size={16} />}
        iconPosition="left"
        onClick={onAddWorkInstruction}
        buttonClasses="bg-teal-600 hover:bg-teal-700"
      />
    </div>
  );
};

export default WorkInstructionZerostate;
