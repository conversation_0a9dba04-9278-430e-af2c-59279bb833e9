import { GripVertical, Plus, Trash2 } from 'lucide-react';
import React, { useState } from 'react';

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/common/accordion';
import PrimaryButton from '@/components/common/button/primaryButton';
import { Checkbox } from '@/components/common/checkbox';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import { cn } from '@/utils/styleUtils';

import ValidationRules from './ValidationRules';

export interface ProcessFlowOption {
  value: string;
  nextStep: string;
}

export interface ValidationRule {
  id: string;
  ruleName: string;
  type: string;
  value: string;
  errorMessage: string;
  options?: string[];
  processFlowOptions?: ProcessFlowOption[];
  originalId?: string; // Store the original rule ID from the API
  isConditional?: boolean; // New field to track if this rule is conditional
}

export interface WorkInstructionData {
  id: string;
  title: string;
  description: string;
  requiresEvidence: boolean;
  validationRules: ValidationRule[];
}

interface WorkInstructionAccordionProps {
  instructions: WorkInstructionData[];
  onAddInstruction: () => void;
  onUpdateInstruction: (instruction: WorkInstructionData) => void;
  onDeleteInstruction: (id: string) => void;
  onAddRule: (instructionId: string) => void;
  onDeleteRule: (instructionId: string, ruleId: string) => void;
  handleRuleChange: (
    instructionId: string,
    ruleId: string,
    field: keyof ValidationRule,
    value: string | string[] | ProcessFlowOption[] | boolean,
  ) => void;
  allSteps?: {
    id: string;
    step_name?: string;
  }[];
  currentStepId?: string;
  className?: string;
  // Drag and drop props
  onDragStart?: (index: number) => void;
  onDragOver?: (e: React.DragEvent<HTMLDivElement>, index: number) => void;
  onDragEnd?: () => void;
  draggingIndex?: number | null;
  isHovering?: number | null;
  setIsHovering?: (index: number | null) => void;
  onValidationChange?: (instructionId: string, hasErrors: boolean) => void;
}

const WorkInstructionAccordion: React.FC<WorkInstructionAccordionProps> = ({
  instructions,
  onAddInstruction,
  onUpdateInstruction,
  onDeleteInstruction,
  onAddRule,
  onDeleteRule,
  handleRuleChange,
  allSteps = [],
  currentStepId,
  className,
  // Drag and drop props
  onDragStart,
  onDragOver,
  onDragEnd,
  draggingIndex,
  isHovering,
  setIsHovering,
  onValidationChange,
}) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const handleValueChange = (
    instructionId: string,
    field: keyof WorkInstructionData,
    value: string | boolean | ValidationRule[],
  ) => {
    const instruction = instructions.find((item) => item.id === instructionId);
    if (instruction) {
      onUpdateInstruction({
        ...instruction,
        [field]: value,
      });
    }
  };

  // The handleRuleChange function is now passed as a prop

  const handleAccordionChange = (value: string[]) => {
    setExpandedItems(value);
  };

  return (
    <div className={cn('space-y-6', className)}>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-base font-medium text-dark-300">
            Work instructions
          </h2>
          <div className="text-sm text-grey-300 mt-1">
            Add instructions to guide operators through this process step.
          </div>
        </div>
        <PrimaryButton
          size="medium"
          text="Add instruction"
          icon={<Plus size={20} />}
          iconPosition="left"
          onClick={onAddInstruction}
        />
      </div>

      <Accordion
        type="multiple"
        value={expandedItems}
        onValueChange={handleAccordionChange}
        className="w-full"
      >
        {instructions.map((instruction, idx) => (
          <AccordionItem
            key={instruction.id}
            value={instruction.id}
            className="mb-3 last:mb-0"
            draggable={true}
            data-instruction-item="true"
            onDragStart={(e) => {
              onDragStart?.(idx);
            }}
            onDragOver={(e) => {
              onDragOver?.(e, idx);
            }}
            onDragEnd={(e) => {
              onDragEnd?.();
            }}
          >
            <AccordionTrigger className="">
              <div className="flex items-center w-full">
                <div
                  className="inline-flex items-center justify-center w-8 h-8 rounded-md bg-white-150 text-dark-300 font-semibold text-sm mr-3 cursor-grab"
                  onMouseEnter={() => setIsHovering?.(idx)}
                  onMouseLeave={() => setIsHovering?.(null)}
                >
                  {isHovering === idx ? (
                    <GripVertical className="h-5 w-5 text-gray-400" />
                  ) : (
                    String(idx + 1).padStart(2, '0')
                  )}
                </div>
                <span className="font-medium text-dark-300 text-base text-left flex-1">
                  {instruction.title}
                </span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    onDeleteInstruction(instruction.id);
                  }}
                  className="text-grey-300 hover:text-red-500 transition-colors mr-2"
                  aria-label="Delete instruction"
                >
                  <Trash2 size={18} />
                </button>
              </div>
            </AccordionTrigger>
            <AccordionContent className="bg-white p-5 overflow-hidden">
              <div className="space-y-5 ">
                {/* Instruction name */}
                <div>
                  <Label
                    htmlFor={`instruction-name-${instruction.id}`}
                    className="block mb-2.5 text-dark-100"
                  >
                    Instruction name
                  </Label>
                  <Input
                    id={`instruction-name-${instruction.id}`}
                    value={instruction.title}
                    onChange={(e) =>
                      handleValueChange(instruction.id, 'title', e.target.value)
                    }
                    className="w-full"
                  />
                </div>

                {/* Description */}
                <div>
                  <Label
                    htmlFor={`description-${instruction.id}`}
                    className="block mb-2.5 text-dark-100"
                  >
                    Description
                  </Label>
                  <Textarea
                    id={`description-${instruction.id}`}
                    value={instruction.description}
                    onChange={(e) =>
                      handleValueChange(
                        instruction.id,
                        'description',
                        e.target.value,
                      )
                    }
                    className="w-full min-h-[120px]"
                  />
                </div>
                <div className="flex items-center gap-6">
                  {/* Requires evidence */}
                  <div className="flex items-center space-x-2 py-1.5">
                    <Checkbox
                      id={`requires-evidence-${instruction.id}`}
                      checked={instruction.requiresEvidence}
                      onCheckedChange={(checked) =>
                        handleValueChange(
                          instruction.id,
                          'requiresEvidence',
                          !!checked,
                        )
                      }
                      className="text-teal-600 border-teal-600 data-[state=checked]:bg-teal-600 data-[state=checked]:border-teal-600"
                    />
                    <Label
                      htmlFor={`requires-evidence-${instruction.id}`}
                      className="text-base font-medium text-dark-300"
                    >
                      Requires evidence
                    </Label>
                  </div>
                </div>

                {/* Validation Rules */}
                <ValidationRules
                  instructionId={instruction.id}
                  validationRules={instruction.validationRules}
                  onAddRule={onAddRule}
                  onDeleteRule={onDeleteRule}
                  handleRuleChange={handleRuleChange}
                  allSteps={allSteps}
                  currentStepId={currentStepId}
                  allInstructionsInStep={instructions}
                  onValidationChange={onValidationChange}
                />
              </div>
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default WorkInstructionAccordion;
