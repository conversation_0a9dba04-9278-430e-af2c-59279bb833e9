import { Plus, Trash2 } from 'lucide-react';
import React, { useEffect, useState } from 'react';

import LinkButton from '@/components/common/button/linkButton';
import TertiaryButton from '@/components/common/button/tertiaryButton';
import { Checkbox } from '@/components/common/checkbox';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/common/tooltip';
import { validateRangeFormat } from '@/utils/validation';

import { ProcessFlowOption, ValidationRule } from './workInstructionAccordion';

interface ValidationRulesProps {
  instructionId: string;
  validationRules: ValidationRule[];
  onAddRule: (instructionId: string) => void;
  onDeleteRule: (instructionId: string, ruleId: string) => void;
  handleRuleChange: (
    instructionId: string,
    ruleId: string,
    field: keyof ValidationRule,
    value: string | string[] | ProcessFlowOption[] | boolean,
  ) => void;
  allSteps?: {
    id: string;
    step_name?: string;
  }[];
  currentStepId?: string;
  allInstructionsInStep?: any[]; // All work instructions in the current step
  onValidationChange?: (instructionId: string, hasErrors: boolean) => void; // New prop to report validation state
}

const ValidationRules: React.FC<ValidationRulesProps> = ({
  instructionId,
  validationRules,
  onAddRule,
  onDeleteRule,
  handleRuleChange,
  allSteps = [],
  currentStepId,
  allInstructionsInStep = [],
  onValidationChange,
}) => {
  // Check if any rule is already conditional across ALL work instructions in the step
  const hasConditionalRuleInStep = allInstructionsInStep.some((instruction) =>
    instruction.validationRules?.some(
      (rule: ValidationRule) => rule.isConditional,
    ),
  );

  // State for validation errors
  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});

  // Handle conditional checkbox change
  const handleConditionalChange = (ruleId: string, isChecked: boolean) => {
    handleRuleChange(instructionId, ruleId, 'isConditional', isChecked);
  };

  // Handle range value change with validation
  const handleRangeValueChange = (ruleId: string, value: string) => {
    // Update the value
    handleRuleChange(instructionId, ruleId, 'value', value);

    // Validate the range format
    const errorKey = `${instructionId}-${ruleId}-value`;
    if (value.trim() === '') {
      // Clear error if empty (will be handled by required validation)
      setValidationErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[errorKey];
        return newErrors;
      });
    } else {
      const validationError = validateRangeFormat(value);
      if (validationError) {
        setValidationErrors((prev) => ({
          ...prev,
          [errorKey]: validationError,
        }));
      } else {
        setValidationErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[errorKey];
          return newErrors;
        });
      }
    }
  };

  // Handle process flow condition change with validation
  const handleProcessFlowConditionChange = (
    ruleId: string,
    optionIdx: number,
    value: string,
    rule: ValidationRule,
  ) => {
    // Update the value
    const newOptions = [...(rule.processFlowOptions || [])];
    newOptions[optionIdx].value = value;
    handleRuleChange(instructionId, ruleId, 'processFlowOptions', newOptions);

    // Validate the condition format if it's a range type rule
    if (rule.type === 'range') {
      const errorKey = `${instructionId}-${ruleId}-condition-${optionIdx}`;
      if (value.trim() === '') {
        // Clear error if empty
        setValidationErrors((prev) => {
          const newErrors = { ...prev };
          delete newErrors[errorKey];
          return newErrors;
        });
      } else {
        const validationError = validateRangeFormat(value);
        if (validationError) {
          setValidationErrors((prev) => ({
            ...prev,
            [errorKey]: validationError,
          }));
        } else {
          setValidationErrors((prev) => {
            const newErrors = { ...prev };
            delete newErrors[errorKey];
            return newErrors;
          });
        }
      }
    }
  };

  // Notify parent component when validation state changes
  useEffect(() => {
    if (onValidationChange) {
      const hasFormatErrors = Object.keys(validationErrors).length > 0;

      // Check for blank required fields
      const hasBlankFields = validationRules.some((rule) => {
        // Check if rule name is blank
        if (!rule.ruleName || rule.ruleName.trim() === '') {
          return true;
        }

        // Check if validation type is not selected
        if (!rule.type) {
          return true;
        }

        // For non-conditional rules, check if expected value is blank
        if (!rule.isConditional) {
          if (!rule.value || rule.value.trim() === '') {
            return true;
          }
        }

        // For conditional rules, check if process flow conditions are properly filled
        if (rule.isConditional && rule.processFlowOptions) {
          return rule.processFlowOptions.some((option) => {
            // Check if condition value is blank
            if (!option.value || option.value.trim() === '') {
              return true;
            }
            // Check if next step is not selected
            if (!option.nextStep || option.nextStep.trim() === '') {
              return true;
            }
            return false;
          });
        }

        return false;
      });

      const hasAnyErrors = hasFormatErrors || hasBlankFields;
      onValidationChange(instructionId, hasAnyErrors);
    }
  }, [validationErrors, validationRules, instructionId, onValidationChange]);
  return (
    <div className="mt-6">
      <div className="flex items-center justify-between mb-5">
        <div>
          <h3 className="text-base font-medium text-dark-100">
            Validation Rules
          </h3>
          <p className="text-sm text-grey-300 mt-1">
            Set the required rules for this work instruction
          </p>
        </div>
        <TertiaryButton
          size="medium"
          text="Add Rule"
          icon={<Plus size={16} />}
          iconPosition="left"
          onClick={() => onAddRule(instructionId)}
        />
      </div>

      {/* Rules list */}
      {validationRules.map((rule, ruleIdx) => (
        <div key={rule.id}>
          <div className="mb-5 rounded-lg p-5 bg-white-150">
            <div className="flex items-center justify-between mb-5">
              <div className="font-medium text-dark-100 text-sm">
                Rule {ruleIdx + 1}
              </div>
              <button
                onClick={() => onDeleteRule(instructionId, rule.id)}
                className="text-grey-300 hover:text-red-500 transition-colors"
                aria-label="Delete rule"
              >
                <Trash2 size={18} />
              </button>
            </div>

            <div className="grid grid-cols-1  ">
              <Label
                htmlFor={`rule-name-${rule.id}`}
                className="block text-base font-medium text-dark-100 mb-2"
              >
                Rule Name
              </Label>
              <Input
                id={`rule-name-${rule.id}`}
                value={rule.ruleName}
                onChange={(e) =>
                  handleRuleChange(
                    instructionId,
                    rule.id,
                    'ruleName',
                    e.target.value,
                  )
                }
                className="w-full"
                placeholder="Enter rule name"
              />
            </div>
            <div className="grid grid-cols-2 gap-6 mt-4">
              <div>
                <Label
                  htmlFor={`validation-type-${rule.id}`}
                  className="block text-base font-medium text-dark-100 mb-2"
                >
                  Validation Type
                </Label>
                <Select
                  value={rule.type}
                  onValueChange={(value) =>
                    handleRuleChange(instructionId, rule.id, 'type', value)
                  }
                >
                  <SelectTrigger
                    id={`validation-type-${rule.id}`}
                    className="w-full bg-white"
                  >
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="equals">Equals (ValueMatch)</SelectItem>
                    <SelectItem value="range">Range (ValueRange)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label
                  htmlFor={`error-message-${rule.id}`}
                  className="block text-base font-medium text-dark-100 mb-2"
                >
                  Error Message
                </Label>
                <Input
                  id={`error-message-${rule.id}`}
                  value={rule.errorMessage}
                  onChange={(e) =>
                    handleRuleChange(
                      instructionId,
                      rule.id,
                      'errorMessage',
                      e.target.value,
                    )
                  }
                  className="w-full"
                  placeholder="Invalid input"
                />
              </div>
            </div>

            {/* Conditional Rule Checkbox */}
            <div className="mt-4">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id={`conditional-${rule.id}`}
                        checked={rule.isConditional || false}
                        disabled={
                          hasConditionalRuleInStep && !rule.isConditional
                        }
                        onCheckedChange={(checked) =>
                          handleConditionalChange(rule.id, checked as boolean)
                        }
                      />
                      <Label
                        htmlFor={`conditional-${rule.id}`}
                        className={`text-base font-medium ${
                          hasConditionalRuleInStep && !rule.isConditional
                            ? 'text-grey-300'
                            : 'text-dark-100'
                        }`}
                      >
                        Conditional rule
                      </Label>
                    </div>
                  </TooltipTrigger>
                  {hasConditionalRuleInStep && !rule.isConditional && (
                    <TooltipContent>
                      <p>Only one conditional rule is allowed per step</p>
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            </div>

            <div className="grid grid-cols-1 gap-6 mt-4">
              {/* Show expected value input only if not conditional */}
              {!rule.isConditional && rule.type === 'equals' && (
                <div className="col-span-2">
                  <Label
                    htmlFor={`expected-value-${rule.id}`}
                    className="block text-base font-medium text-dark-100 mb-2"
                  >
                    Expected Value:
                  </Label>
                  <Input
                    id={`expected-value-${rule.id}`}
                    value={rule.value}
                    onChange={(e) =>
                      handleRuleChange(
                        instructionId,
                        rule.id,
                        'value',
                        e.target.value,
                      )
                    }
                    className="w-full"
                  />
                </div>
              )}

              {!rule.isConditional && rule.type === 'range' && (
                <div className="col-span-2">
                  <Label
                    htmlFor={`expected-value-${rule.id}`}
                    className="block text-base font-medium text-dark-100 mb-2"
                  >
                    Expected Value:
                  </Label>
                  <Input
                    id={`expected-value-${rule.id}`}
                    value={rule.value}
                    onChange={(e) =>
                      handleRangeValueChange(rule.id, e.target.value)
                    }
                    className={`w-full ${
                      validationErrors[`${instructionId}-${rule.id}-value`]
                        ? 'border-red-500'
                        : ''
                    }`}
                    placeholder="0-100"
                  />
                  {validationErrors[`${instructionId}-${rule.id}-value`] && (
                    <p className="text-xs text-red-500 mt-1">
                      {validationErrors[`${instructionId}-${rule.id}-value`]}
                    </p>
                  )}
                  <p className="text-xs text-grey-300 mt-1">
                    For range validation, use format: min-max (e.g., 0-100)
                  </p>
                </div>
              )}

              {/* Show process flow conditions only if conditional */}
              {rule.isConditional && (
                <div className="col-span-2 space-y-4 mt-2">
                  <div>
                    <Label className="block text-base font-medium text-dark-100 mb-2">
                      Create Process Flow:
                    </Label>

                    <div className="mt-4">
                      <div className="grid grid-cols-2 gap-6 items-center mb-3">
                        <div>
                          <Label className="block text-sm text-grey-300 mb-2">
                            Conditions - If value is
                          </Label>
                        </div>
                        <div className="flex items-center justify-between">
                          <Label className="block text-sm text-grey-300 mb-2">
                            Then go to:
                          </Label>
                          <div className="text-xs text-grey-300">
                            {(rule.processFlowOptions || []).length}/5
                            conditions
                          </div>
                        </div>
                      </div>

                      {(rule.processFlowOptions || []).map(
                        (option, optionIdx) => (
                          <div
                            key={optionIdx}
                            className="grid grid-cols-2 gap-6 items-center mb-3"
                          >
                            <div className="relative">
                              <Input
                                value={option.value}
                                onChange={(e) => {
                                  handleProcessFlowConditionChange(
                                    rule.id,
                                    optionIdx,
                                    e.target.value,
                                    rule,
                                  );
                                }}
                                className={`w-full pr-10 ${
                                  validationErrors[
                                    `${instructionId}-${rule.id}-condition-${optionIdx}`
                                  ]
                                    ? 'border-red-500'
                                    : ''
                                }`}
                                placeholder={
                                  rule.type === 'range' ? '0-100' : '<500'
                                }
                              />
                              {validationErrors[
                                `${instructionId}-${rule.id}-condition-${optionIdx}`
                              ] && (
                                <p className="text-xs text-red-500 mt-1">
                                  {
                                    validationErrors[
                                      `${instructionId}-${rule.id}-condition-${optionIdx}`
                                    ]
                                  }
                                </p>
                              )}
                            </div>

                            <div className="flex items-center space-x-2">
                              <Select
                                value={
                                  rule.processFlowOptions?.[optionIdx]
                                    ?.nextStep || ''
                                }
                                onValueChange={(value) => {
                                  const newProcessFlowOptions = [
                                    ...(rule.processFlowOptions || []),
                                  ];

                                  newProcessFlowOptions[optionIdx] = {
                                    value: option.value,
                                    nextStep: value,
                                  };
                                  handleRuleChange(
                                    instructionId,
                                    rule.id,
                                    'processFlowOptions',
                                    newProcessFlowOptions,
                                  );
                                }}
                              >
                                <SelectTrigger className="w-full bg-white">
                                  <SelectValue placeholder="Select next step" />
                                </SelectTrigger>
                                <SelectContent>
                                  {allSteps
                                    .filter((step) => step.id !== currentStepId) // Filter out current step
                                    .map((step) => (
                                      <SelectItem key={step.id} value={step.id}>
                                        {step.step_name || `Step ${step.id}`}
                                      </SelectItem>
                                    ))}
                                  {allSteps.length <= 1 && (
                                    <SelectItem value="no-steps" disabled>
                                      No other steps available
                                    </SelectItem>
                                  )}
                                </SelectContent>
                              </Select>

                              <button
                                onClick={() => {
                                  const newProcessFlowOptions = [
                                    ...(rule.processFlowOptions || []),
                                  ];
                                  newProcessFlowOptions.splice(optionIdx, 1);

                                  handleRuleChange(
                                    instructionId,
                                    rule.id,
                                    'processFlowOptions',
                                    newProcessFlowOptions,
                                  );
                                }}
                                className="text-grey-300 hover:text-red-500 flex-shrink-0"
                                aria-label="Remove option"
                              >
                                <Trash2 size={16} />
                              </button>
                            </div>
                          </div>
                        ),
                      )}

                      {/* Add conditions button with maximum limit */}
                      {(rule.processFlowOptions || []).length < 5 ? (
                        <LinkButton
                          onClick={() => {
                            const newOptions = [
                              ...(rule.processFlowOptions || []),
                              { value: '', nextStep: '' },
                            ];
                            handleRuleChange(
                              instructionId,
                              rule.id,
                              'processFlowOptions',
                              newOptions,
                            );
                          }}
                          type="button"
                          icon={<Plus size={16} className="mr-1" />}
                          text="Add conditions"
                          size="medium"
                        ></LinkButton>
                      ) : (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <div className="inline-block">
                                <LinkButton
                                  onClick={() => {}} // No action when disabled
                                  type="button"
                                  icon={<Plus size={16} className="mr-1" />}
                                  text="Add conditions"
                                  size="medium"
                                  disabled={true}
                                ></LinkButton>
                              </div>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Maximum 5 conditions allowed</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ValidationRules;
