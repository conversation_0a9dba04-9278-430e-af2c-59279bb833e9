import { X } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';

interface AddWorkInstructionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  stepId: string;
  onSuccess?: () => void;
}

interface WorkInstructionFormData {
  name: string;
  description: string;
}

const AddWorkInstructionModal: React.FC<AddWorkInstructionModalProps> = ({
  open,
  onOpenChange,
  stepId,
  onSuccess,
}) => {
  const router = useRouter();
  const { productId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);

  // Initialize the POST hook
  const { postData, isLoading, response, error } = usePost();

  // Form state
  const [formData, setFormData] = useState<WorkInstructionFormData>({
    name: '',
    description: '',
  });

  // Validation errors
  const [errors, setErrors] = useState<{
    name?: string;
    description?: string;
  }>({});

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));

    // Clear error when user types
    if (errors[name as keyof typeof errors]) {
      setErrors((prev) => ({ ...prev, [name]: undefined }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: {
      name?: string;
      description?: string;
    } = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Instruction name is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle API response
  useEffect(() => {
    if (response) {
      toast.success('Work instruction added successfully');

      // Reset form
      setFormData({
        name: '',
        description: '',
      });

      // Close modal
      onOpenChange(false);

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    }
  }, [response]);

  // Handle API error
  useEffect(() => {
    if (error) {
      toast.error('Failed to add work instruction');
      console.error('Error adding work instruction:', error);
    }
  }, [error]);

  // Handle save
  const handleSave = () => {
    if (validateForm()) {
      // Prepare API request body
      const apiBody = {
        instruction_name: formData.name,
        description: formData.description,
      };

      // Make API call
      postData(
        accessToken as string,
        `products/${productId}/process_step/${stepId}/work_instruction`,
        apiBody,
      );
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-[600px] p-8">
        <DialogHeader className="flex relative -top-1">
          <DialogTitle>Add Work Instructions</DialogTitle>
        </DialogHeader>

        <div className="mt-4 space-y-6">
          <div>
            <Label
              htmlFor="name"
              className="text-base font-medium text-dark-300 mb-2 block"
            >
              Instruction name
            </Label>
            <Input
              id="name"
              name="name"
              placeholder="Enter instruction name"
              value={formData.name}
              onChange={handleChange}
              errorMsg={errors.name}
            />
          </div>

          <div>
            <Label
              htmlFor="description"
              className="text-base font-medium text-dark-300 mb-2 block"
            >
              Description
            </Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Enter description"
              value={formData.description}
              onChange={handleChange}
              errorMsg={errors.description}
              className="min-h-[120px]"
            />
          </div>

          <div className="flex justify-end mt-6">
            <PrimaryButton
              text={isLoading ? 'Saving...' : 'Save'}
              size="medium"
              onClick={handleSave}
              disabled={isLoading}
            />
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddWorkInstructionModal;
