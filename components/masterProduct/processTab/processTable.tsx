import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';

import { Dialog } from '@/components/common/dialog';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import TableAttachment from '@/components/common/tableAttachment';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePut } from '@/hooks/usePut';
import { getValueOrDefault } from '@/utils/general';
import { RowDragEndEvent } from '@ag-grid-community/core';

import MasterProductStepModal from './masterProductStepModal';

// import { useNavigate, useParams } from "react-router-dom";

// import MasterProductStepModal from "../Modals/masterProductStepModal";

interface TProcessData {
  id: string;
  steps: string;
  material?: string[] | [];
  people?: string[] | [];
  asset?: string[] | [];
  sequence_no: number;
}

const flowKey = 'Master-Product-Flow';

const MasterProductTable = ({
  editFlow,
  data,
  reFetch,
  edit,
}: {
  editFlow: boolean;
  data: any;
  reFetch: () => void;
  edit?: boolean;
}) => {
  const { accessToken } = useAuthStore();
  const [showStepModal, setShowStepModal] = useState<boolean>(false); // [showStepModal, settShowModal]
  const [processData, setProcessData] = useState<TProcessData[]>([]);
  const [selectedData, setSelectedData] = useState<any | undefined>(undefined);
  const router = useRouter();
  const { putData, isLoading } = usePut();

  const getMasterProductColumns = useCallback(() => {
    const masterProductColumns: any = [
      // {
      //   // headerName: 'S.No.',
      //   // field: 'sequence_no',
      //   // valueFormatter: (params: any) =>
      //   //   getValueOrDefault(params.data, 'sequence_no'),
      //   filter: false,
      //   maxWidth: 50,
      //   rowDrag: true,
      // },
      {
        headerName: 'S.No.',
        field: 'sequence_no',
        resizable: false,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'sequence_no'),
        filter: false,
        maxWidth: 70,
        rowDrag: edit,
      },
      {
        headerName: 'Step',
        field: 'steps',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: any) => {
          return params.value;
        },
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'steps'),
        filter: false,
        onCellClicked: (params: any) => {
          router.push(
            `${router?.query?.productId}/process-step/${params.data?.id}`,
          );
        },
        cellStyle: () => {
          return {
            cursor: 'pointer',
            color: '#016366',
          };
        },
      },
      {
        headerName: 'Material',
        field: 'materials',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'materials'),
        filter: false,
      },
      {
        headerName: 'People',
        field: 'employees',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'employees'),
        filter: false,
      },
      {
        headerName: 'Asset',
        field: 'assets',
        resizable: true,
        valueFormatter: (params: any) =>
          getValueOrDefault(params.data, 'assets'),
        filter: false,
      },
      {
        headerName: 'Linked documents',
        field: 'attachments',
        resizable: true,
        minWidth: 100,
        cellRenderer: (params: any) => {
          return (
            <TableAttachment obj={params.data} backendKey={'attachments'} />
          );
        },
        filter: false,
        cellStyle: () => {
          return {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
          };
        },
      },
    ];
    edit &&
      masterProductColumns.push({
        headerName: 'Action',
        field: 'action',
        cellRenderer: (params: any) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={(rowData) => {
              setShowStepModal(true);
              setSelectedData(rowData);
            }}
            hideDelete
          />
        ),
        width: 100,
        pinned: 'right',
        filter: false,
      });

    return masterProductColumns;
  }, [edit]);

  useEffect(() => {
    const steps = data?.record?.steps;
    steps?.sort((a: any, b: any) => a.sequence_no - b.sequence_no);

    setProcessData(
      steps?.map((step: any) => ({
        ...step,
        steps: step.name,
        materials: step?.materials?.map((e: any) => e.name).join(', '),
        employees: step?.employees?.map((e: any) => e.name).join(', '),
        assets: step?.assets?.map((e: any) => e.name).join(', '),
        attachments: step?.attachments,
      })),
    );
  }, [data]);

  const reorderArrayAndUpdateSequence = async (
    e: RowDragEndEvent<any, any>,
  ) => {
    const newData = [...processData];

    // Find the index of the dragged item
    const fromIndex = newData.findIndex((item) => item.id === e.node.data.id);
    if (fromIndex === -1 || e.overIndex < 0 || e.overIndex >= newData.length)
      return newData; // Edge case handling

    // Remove the dragged item from its original position
    const [movedItem] = newData.splice(fromIndex, 1);

    // Insert the dragged item at the new position
    newData.splice(e.overIndex, 0, movedItem);

    // Update sequence_no based on new order
    newData.forEach((item, index) => (item.sequence_no = index + 1));
    setProcessData(newData);

    const payload = newData?.map((step: any) => ({
      step_id: step.id,
      sequence_no: step.sequence_no,
    }));

    await putData(
      accessToken as string,
      `products/${router?.query?.productId}/process-steps/rank/`,
      payload as any,
    );
  };

  return (
    <div className="w-full mt-8">
      <div className="mb-2 text-base leading-6 font-semibold text-dark-300">
        Table view:
      </div>
      <div className="w-full">
        <CommonTable
          data={{ records: processData }}
          columnDefs={getMasterProductColumns()}
          rowDrag
          paginate={false}
          handleDrop={(e: RowDragEndEvent<any, any>) => {
            edit && reorderArrayAndUpdateSequence(e);
          }}
        />
      </div>

      <Dialog
        open={showStepModal}
        onOpenChange={() => {
          setShowStepModal(false);
          setSelectedData(undefined);
        }}
      >
        <MasterProductStepModal
          open={showStepModal}
          selectedData={selectedData}
          closeModal={() => {
            setShowStepModal(false);
            setSelectedData(undefined);
            reFetch();
          }}
        />
      </Dialog>
    </div>
  );
};

export default MasterProductTable;
