import { ChevronRight, GripVertical } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useRef, useState } from 'react';
import { toast } from 'react-toastify';

import EditIcon from '@/assets/outline/edit';
import InfoCircle from '@/assets/outline/infoCircle';
import PlusIcon from '@/assets/outline/plus';
import ProcessIcon from '@/assets/outline/process';
import TickIcon from '@/assets/outline/tick';
import WarningIcon from '@/assets/outline/warning';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/common/accordion';
import LinkButton from '@/components/common/button/linkButton';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import TertiaryButton from '@/components/common/button/tertiaryButton';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from '@/components/common/dialog';
import Loader from '@/components/common/loader';
import DeleteModal from '@/components/common/modals/deleteModal';
import Status from '@/components/common/status';
import ToggleSwitch from '@/components/common/toogleSwitch';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import AddApprovers from '@/components/document/addApprovers';
import ApprovalTypeSelector, {
  ApprovalType,
} from '@/components/document/approvalTypeSelector';
import EditApproversModal from '@/components/document/components/modals/editApprovers';
import JustificationModal from '@/components/document/components/modals/justificationModal';
import OtpModal from '@/components/document/components/modals/otpModal';
import Flow from '@/components/masterProduct_deprecated/processTab/Flowchart/flow';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';
import { ReactFlowProvider } from '@xyflow/react';

import { useDelete } from '../../../hooks/useDelete';
import useFetch from '../../../hooks/useFetch';
import { usePost } from '../../../hooks/usePost';
import { usePut } from '../../../hooks/usePut';
import { TProductInfo } from '../infoTab';
import AddStepModal from './addStepModal';
import FlowchartAccordion from './Flowchart/FlowchartAccordion';
import PreviewFlow from './Flowchart/preview';
import BasicDetail from './steps/basicDetail';
import Resources from './steps/resources';
import WorkInstruction from './steps/workInstruction';

interface ApproverData {
  id: string;
  name: string;
  email: string;
  user_id?: string;
  record_id?: string;
}

// Interface for approval data from API
interface ApprovalData {
  id: string;
  status: string;
  flow: string;
  approvers: Array<{
    id: string;
    user: {
      id: string;
      name: string;
      full_name: string;
    };
    status: string;
    sequence_number: number;
  }>;
  created_at: string;
  updated_at: string;
}

interface ProductApprovalResponse {
  record: ApprovalData | null;
}

// Interface for approval action response
interface ApprovalActionResponse {
  id: string;
  step: string;
}

// Interface to match the backend step data structure
interface Step {
  id: string;
  step_name: string;
  step_type: string;
  description: string;
  sequence_no: number;
  product?: string;
  company?: string;
}

// Interface for component mappings
interface ComponentMapping {
  [stepId: string]: {
    basicDetails: string;
    workInstructions: string;
    resources: string;
  };
}

interface ProcessTabProps {
  data: TProductInfo | undefined;
  reFetchProductData: () => void;
}

const ProcessTab: React.FC<ProcessTabProps> = ({
  data,
  reFetchProductData,
}) => {
  // State for approval section
  const [requireApproval, setRequireApproval] = useState<boolean>(false);
  const [approvalType, setApprovalType] = useState<ApprovalType>('Sequential');
  const [approversData, setApproversData] = useState<ApproverData[]>([]);
  const [approverError, setApproverError] = useState<boolean>(false);

  // State for steps (from backend)
  const [steps, setSteps] = useState<Step[]>([]);

  // State for component mappings (managed locally)
  const [componentMappings, setComponentMappings] = useState<ComponentMapping>(
    {},
  );

  const [activeStep, setActiveStep] = useState<string>(''); // Will be set when data is loaded
  const [activeComponent, setActiveComponent] = useState<string>(''); // Will be set when data is loaded

  // Component types
  const componentTypes = {
    BASIC_DETAILS: 'basic-details',
    WORK_INSTRUCTIONS: 'work-instructions',
    RESOURCES: 'resources',
  };
  const [draggingIndex, setDraggingIndex] = useState<number | null>(null);
  const [isHovering, setIsHovering] = useState<number | null>(null);
  const [isAddStepModalOpen, setIsAddStepModalOpen] = useState(false);

  const accessToken = useAuthStore((state) => state.accessToken);
  const user = useAuthStore((state) => state.user);

  const [editFlow, setEditFlow] = useState<boolean>(false);
  const [editApproversModal, setEditApproversModal] = useState<boolean>(false);

  // Approval action states
  const [openJustificationModal, setOpenJustificationModal] =
    useState<boolean>(false);
  const [otpModal, setOtpModal] = useState<boolean>(false);
  const [mfaSessionId, setMfaSessionId] = useState<string>('');

  // Initialize usePost hook for approval API
  const {
    postData,
    response: approvalResponse,
    isLoading: isSubmittingApproval,
    error: approvalError,
  } = usePost();

  // Initialize usePost hook for publish API
  const {
    postData: publishProduct,
    response: publishResponse,
    isLoading: isPublishing,
    error: publishError,
  } = usePost();

  // Initialize useDelete hook for step deletion
  const {
    deleteData: deleteStep,
    response: deleteResponse,
    isLoading: isDeleting,
    error: deleteError,
  } = useDelete();

  // Step deletion state
  const [stepToDelete, setStepToDelete] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Initialize usePut hook for reorder API
  const {
    putData: reorderStep,
    response: reorderResponse,
    isLoading: isReordering,
    error: reorderError,
  } = usePut();

  // Initialize usePost hook for approval actions (approve/reject)
  const {
    postData: postApprovalAction,
    response: approvalActionResponse,
    isLoading: approvalActionLoading,
    error: approvalActionError,
  } = usePost<ApprovalActionResponse>();

  // Get CFR enabled status
  const cfr_enabled = useAuthStore(
    (state) => state.user?.company.is_cfr11_required,
  );
  const router = useRouter();
  const {
    data: processData,
    isLoading: processLoading,
    reFetch,
  } = useFetch(accessToken, `products/${router.query.productId}/flowcharts`);

  interface ProcessStepResponse {
    steps: Step[];
  }

  const {
    data: processStep,
    isLoading: processStepLoading,
    reFetch: reFetchProcessSteps,
  } = useFetch<ProcessStepResponse>(
    accessToken,
    `products/${router.query.productId}/process_steps`,
  );

  // Fetch approval data
  const {
    data: approvalData,
    isLoading: approvalLoading,
    reFetch: reFetchApprovalData,
  } = useFetch<ProductApprovalResponse>(
    accessToken,
    `product/${router.query.productId}/approvals`,
  );

  // Track if this is the first render
  const isFirstRender = useRef(true);
  // Track the previous steps to detect new steps
  const prevSteps = useRef<Step[]>([]);

  // Initialize component mappings when processStep data is loaded
  useEffect(() => {
    if (processStep?.steps && processStep.steps.length > 0) {
      // Set steps from backend data
      setSteps(processStep.steps);

      // Initialize component mappings
      const newComponentMappings: ComponentMapping = {};

      processStep.steps.forEach((step: Step) => {
        newComponentMappings[step.id] = {
          basicDetails: `${step.id}-basic-details`,
          workInstructions: `${step.id}-work-instructions`,
          resources: `${step.id}-resources`,
        };
      });

      setComponentMappings(newComponentMappings);

      // Check if this is the first render with data
      if (isFirstRender.current) {
        // On first render, select the first step's basic details
        const firstStepId = processStep.steps[0].id;
        setActiveStep(firstStepId);
        setActiveComponent(
          newComponentMappings[firstStepId]?.basicDetails || '',
        );
        isFirstRender.current = false;
      }
      // Check if a new step was added by comparing current steps with previous steps
      else if (prevSteps.current.length < processStep.steps.length) {
        // Find the new step (the one that wasn't in the previous steps array)
        const newStep = processStep.steps.find(
          (step) =>
            !prevSteps.current.some((prevStep) => prevStep.id === step.id),
        );

        if (newStep) {
          // Select the new step's basic details
          setActiveStep(newStep.id);
          setActiveComponent(
            newComponentMappings[newStep.id]?.basicDetails || '',
          );
        }
      }

      // Update the previous steps reference
      prevSteps.current = [...processStep.steps];
    }
  }, [processStep]);

  // Handle approval API response
  useEffect(() => {
    if (approvalResponse) {
      toast.success('Approval request sent successfully');
      // Reset approval state after successful submission
      setRequireApproval(false);
      setApproversData([]);
      // Refresh approval data to show the new approval
      reFetchApprovalData();
    }
    if (approvalError) {
      console.error('Error sending approval request:', approvalError);
      toast.error('Failed to send approval request');
    }
  }, [approvalResponse, approvalError]);

  // Handle reorder API response
  useEffect(() => {
    if (reorderResponse) {
      toast.success('Step order updated successfully');
    }
    if (reorderError) {
      console.error('Error reordering step:', reorderError);
      toast.error('Failed to update step order');
      // Revert the local state by refetching
      reFetchProcessSteps();
    }
  }, [reorderResponse, reorderError]);

  // Handle approval action API response
  useEffect(() => {
    if (approvalActionResponse) {
      if (cfr_enabled && approvalActionResponse.step === 'mfa') {
        setMfaSessionId(approvalActionResponse.id);
        setOtpModal(true);
      } else {
        reFetchApprovalData();
        toast.success('Approval action completed successfully');
      }
    }
    if (approvalActionError) {
      console.error('Error with approval action:', approvalActionError);
      toast.error('Failed to process approval action');
    }
  }, [approvalActionResponse, approvalActionError, cfr_enabled]);

  // Handle publish API response
  useEffect(() => {
    if (publishResponse) {
      toast.success('Product published successfully');
      // Refresh approval data to update the UI
      reFetchApprovalData();
      reFetchProductData();
    }
    if (publishError) {
      console.error('Error publishing product:', publishError);
      toast.error('Failed to publish product');
    }
  }, [publishResponse, publishError]);

  // Handle delete API response
  useEffect(() => {
    if (deleteResponse) {
      toast.success('Step deleted successfully');

      // If the deleted step was the currently active step, reset to first step
      if (stepToDelete === activeStep) {
        // Find the first remaining step after deletion
        const remainingSteps = steps.filter((step) => step.id !== stepToDelete);
        if (remainingSteps.length > 0) {
          const firstStep = remainingSteps[0];
          setActiveStep(firstStep.id);
          setActiveComponent(
            componentMappings[firstStep.id]?.basicDetails || '',
          );
        } else {
          // No steps remaining, clear active selections
          setActiveStep('');
          setActiveComponent('');
        }
      }

      setShowDeleteModal(false);
      setStepToDelete(null);
      // Refresh process steps data
      reFetchProcessSteps();
    }
    if (deleteError) {
      console.error('Error deleting step:', deleteError);
      toast.error('Failed to delete step');
      setShowDeleteModal(false);
      setStepToDelete(null);
    }
  }, [deleteResponse, deleteError]);

  // Step deletion functions
  const handleDeleteStep = (stepId: string) => {
    // Validation: Don't allow deleting if it's the only step
    if (steps.length <= 1) {
      toast.error('Cannot delete the last remaining step');
      return;
    }

    // Find the step to be deleted
    // const stepToDelete = steps.find((step) => step.id === stepId);

    // // Additional validation for terminal steps
    // if (stepToDelete?.step_type === 'terminal_start') {
    //   // Check if there are other terminal_start steps
    //   const terminalStartSteps = steps.filter(
    //     (step) => step.step_type === 'terminal_start',
    //   );
    //   if (terminalStartSteps.length <= 1) {
    //     toast.error('Cannot delete the only terminal start step');
    //     return;
    //   }
    // }

    // if (stepToDelete?.step_type === 'terminal_stop') {
    //   // Check if there are other terminal_stop steps
    //   const terminalStopSteps = steps.filter(
    //     (step) => step.step_type === 'terminal_stop',
    //   );
    //   if (terminalStopSteps.length <= 1) {
    //     toast.error('Cannot delete the only terminal stop step');
    //     return;
    //   }
    // }

    setStepToDelete(stepId);
    setShowDeleteModal(true);
  };

  const confirmDeleteStep = async () => {
    if (stepToDelete && router.query.productId) {
      try {
        await deleteStep(
          accessToken as string,
          `products/${router.query.productId}/process_step/${stepToDelete}`,
        );
      } catch (error) {
        console.error('Error deleting step:', error);
      }
    }
  };

  // Approval action functions
  const handleApprove = () => {
    const body = {
      action: 'Approved',
    };

    async function fetch() {
      await postApprovalAction(
        accessToken as string,
        `approvals/${approvalData?.record?.id}/action`,
        body,
      );
    }
    fetch();
  };

  // Publish function
  const handlePublish = async () => {
    try {
      await publishProduct(
        accessToken as string,
        `products/${router.query.productId}/publish`,
        {},
      );
    } catch (error) {
      console.error('Error publishing product:', error);
    }
  };

  const handleRejectMfa = (data: any) => {
    if (data && cfr_enabled) {
      setMfaSessionId(data.id);
      setOtpModal(true);
    } else {
      reFetchApprovalData();
    }
  };

  // Helper functions to determine publish button state
  const isProductPublished = () => {
    // Check if product status indicates it's published
    return data?.status?.toLowerCase() === 'published';
  };

  const isApprovalCompleted = () => {
    // Check if approval exists and all approvers have approved
    if (!approvalData?.record) return false;

    const allApproved = approvalData.record.approvers.every(
      (approver) => approver.status === 'Approved',
    );

    return allApproved && approvalData.record.status === 'Approved';
  };

  const getPublishButtonState = () => {
    if (isProductPublished()) {
      return {
        text: 'Published',
        disabled: true,
        variant: 'secondary' as const,
      };
    }

    if (isApprovalCompleted()) {
      return {
        text: 'Publish',
        disabled: false,
        variant: 'primary' as const,
      };
    }

    return {
      text: 'Publish',
      disabled: true,
      variant: 'primary' as const,
    };
  };

  // Handle drag start
  const handleDragStart = (index: number) => {
    setDraggingIndex(index);
  };

  // Handle drag over
  const handleDragOver = (
    e: React.DragEvent<HTMLDivElement>,
    index: number,
  ) => {
    e.preventDefault();

    if (draggingIndex === null || draggingIndex === index) {
      return;
    }

    const newSteps = [...steps];
    const draggedItem = newSteps[draggingIndex];

    // Remove the dragged item
    newSteps.splice(draggingIndex, 1);
    // Insert it at the new position
    newSteps.splice(index, 0, draggedItem);

    // Update the sequence numbers
    newSteps.forEach((step, i) => {
      step.sequence_no = i + 1;
    });

    setSteps(newSteps);
    setDraggingIndex(index);
  };

  // Handle drag end - call reorder API
  const handleDragEnd = async () => {
    if (draggingIndex !== null) {
      const draggedStep = steps[draggingIndex];

      // Find the current position of the dragged step in the updated steps array
      const currentIndex = steps.findIndex((s) => s.id === draggedStep.id);
      const newSequenceNo = currentIndex + 1; // sequence_no is 1-based

      // Only call API if the position actually changed
      const originalStep = processStep?.steps?.find(
        (s) => s.id === draggedStep.id,
      );

      if (originalStep && originalStep.sequence_no + 1 !== newSequenceNo) {
        try {
          await reorderStep(
            accessToken as string,
            `products/${router.query.productId}/reorder_process_step`,
            {
              step_id: draggedStep.id,
              new_sequence_no: newSequenceNo,
            },
          );
        } catch (error) {
          console.error('❌ Error reordering step:', error);
          // The error handling is done in the useEffect
        }
      }
    }
    setDraggingIndex(null);
  };

  // Handle save from the modal
  const handleSaveNewStep = () => {
    // The API call is handled in the AddStepModal component
    // After successful API call, we'll refresh the process steps data
    reFetchProcessSteps();
    setIsAddStepModalOpen(false);

    // Note: The new step selection is now handled in the useEffect that watches processStep
    // It detects when a new step is added and automatically selects it
  };
  // Submit approval request function
  const handleSendForApproval = async () => {
    if (!requireApproval) {
      return;
    }

    if (!approversData || approversData.length === 0) {
      setApproverError(true);
      return;
    }

    setApproverError(false);

    const body = {
      product_name: 'string',
      product_description: 'string',
      product_category: 'string',
      approval_request: {
        flow: approvalType === 'Sequential' ? 'Sequential' : 'Parallel',
        approvers: approversData.map((user: ApproverData, index: number) => ({
          sequence_number: index + 1,
          user_id: user?.user_id || user?.id,
        })),
      },
    };

    try {
      await postData(
        accessToken as string,
        `products/${router.query.productId}/send-for-approval`,
        body,
      );
    } catch (error) {
      console.error('Error sending for approval:', error);
    }
  };

  // Check if terminal start/stop are already used
  const isTerminalStartUsedForNew = steps.some(
    (s) => s.step_type === 'terminal_start',
  );

  const isTerminalStopUsedForNew = steps.some(
    (s) => s.step_type === 'terminal_stop',
  );

  return (
    <div>
      {/* Add Step Modal */}
      <AddStepModal
        open={isAddStepModalOpen}
        onOpenChange={setIsAddStepModalOpen}
        onSave={handleSaveNewStep}
        isTerminalStartUsed={isTerminalStartUsedForNew}
        isTerminalStopUsed={isTerminalStopUsedForNew}
      />

      {/* <FlowChartModal
        editFlow={editFlow}
        setEditFlow={setEditFlow}
        handleConfirm={undefined}
        data={processData}
        reFetch={reFetch}
      /> */}
      {processLoading ? (
        <Loader className="h-[700px]" />
      ) : (
        <div className="w-300 h-90 gap-5 mb-10">
          <FlowchartAccordion
            data={processData as any}
            title="Process flowchart"
            placeholder="Create process flowchart"
          >
            {/* This is the content that will be shown when the accordion is open */}
            <ReactFlowProvider>
              <PreviewFlow data={processData} />
              {hasAccess(
                AccessActions.CanEditSpecificProduct,
                user,
                data?.assignees?.some(
                  (assignee: any) => assignee.id === user?.id,
                ),
              ) && (
                <Dialog open={editFlow} onOpenChange={setEditFlow}>
                  <DialogTrigger>
                    <div className=" absolute top-2 right-2  ">
                      <PrimaryButton
                        onClick={() => setEditFlow(true)}
                        text="Edit"
                        size="medium"
                        icon={<EditIcon color="#fff" width="18" height="18" />}
                      />
                    </div>
                  </DialogTrigger>
                  <DialogContent
                    className="!max-h-[90vh] h-[90vh] !max-w-[90vw] "
                    showClose={false}
                  >
                    <ReactFlowProvider>
                      <Flow
                        setEditFlow={setEditFlow}
                        data={processData}
                        reFetch={reFetch}
                      />
                    </ReactFlowProvider>
                  </DialogContent>
                </Dialog>
              )}
            </ReactFlowProvider>
          </FlowchartAccordion>

          <div className="flex gap-4 text-lg text-medium text-dark">
            <div className=" min-w-12 w-12 h-12 rounded bg-white-150 flex items-center justify-center">
              <ProcessIcon />
            </div>
            <div>
              <div className="text-lg font-medium text-dark-300 mb-1">
                Process steps
              </div>
              <div className="text-sm text-grey-300 font-medium">
                Open the drop down below to create or view the flowchart
              </div>
            </div>
          </div>
          <div className="flex mt-5 gap-3">
            <div className="border border-white-300 rounded-lg p-3 w-2/6 flex flex-col">
              <div className="flex justify-between items-center text-sm font-medium text-grey-300 mb-2.5 pb-2.5 border-b border-white-200">
                <span>STEPS</span>
              </div>
              <div className="flex-1">
                <Accordion
                  type="single"
                  collapsible
                  className="w-full"
                  value={activeStep}
                  onValueChange={setActiveStep}
                >
                  {steps?.map((step: Step, index: number) => (
                    <AccordionItem
                      key={step.id}
                      value={step.id}
                      className="border-none"
                      draggable={true}
                      data-step-item="true"
                      onDragStart={(e) => {
                        handleDragStart(index);
                      }}
                      onDragOver={(e) => {
                        handleDragOver(e, index);
                      }}
                      onDragEnd={(e) => {
                        handleDragEnd();
                      }}
                    >
                      <AccordionTrigger
                        className="hover:bg-white-150 hover:shadow-none !border-none"
                        triggerClassname="!data-[state=open]:border-none"
                      >
                        <div
                          className="w-full flex items-center gap-2"
                          onMouseEnter={() => setIsHovering(index)}
                          onMouseLeave={() => setIsHovering(null)}
                        >
                          <div className="h-8 w-8 flex items-center justify-center text-sm font-medium text-dark-300 bg-white-150 cursor-grab">
                            {isHovering === index ? (
                              <GripVertical className="h-5 w-5 text-gray-400" />
                            ) : (
                              String(step.sequence_no).padStart(2, '0')
                            )}
                          </div>
                          <div>{step.step_name}</div>
                        </div>
                      </AccordionTrigger>

                      <AccordionContent className="p-2 mt-1">
                        <div className="relative">
                          <div className="h-full w-0 border-r border-[#D0D5DD] absolute top-0 left-0"></div>
                          <div className="pl-3 flex flex-col gap-2">
                            <div
                              className={`text-base font-medium leading-6 text-dark-300 px-2.5 py-2 rounded-lg hover:bg-white-150 cursor-pointer flex items-center justify-between flex-1 ${
                                activeComponent ===
                                componentMappings[step.id]?.basicDetails
                                  ? 'bg-primary-100 hover:bg-primary-100'
                                  : ''
                              }`}
                              onClick={() =>
                                setActiveComponent(
                                  componentMappings[step.id]?.basicDetails ||
                                    '',
                                )
                              }
                            >
                              Basic Details
                              {activeComponent ===
                              componentMappings[step.id]?.basicDetails ? (
                                <ChevronRight size={20} />
                              ) : (
                                ''
                              )}
                            </div>
                            <div
                              className={`text-base font-medium leading-6 text-dark-300 px-2.5 py-2 rounded-lg hover:bg-white-150 cursor-pointer flex items-center justify-between flex-1 ${
                                activeComponent ===
                                componentMappings[step.id]?.workInstructions
                                  ? 'bg-primary-100 hover:bg-primary-100'
                                  : ''
                              }`}
                              onClick={() =>
                                setActiveComponent(
                                  componentMappings[step.id]
                                    ?.workInstructions || '',
                                )
                              }
                            >
                              Work Instructions
                              {activeComponent ===
                              componentMappings[step.id]?.workInstructions ? (
                                <ChevronRight size={20} />
                              ) : (
                                ''
                              )}
                            </div>
                            <div
                              className={`text-base font-medium leading-6 text-dark-300 px-2.5 py-2 rounded-lg hover:bg-white-150 cursor-pointer flex items-center justify-between flex-1 ${
                                activeComponent ===
                                componentMappings[step.id]?.resources
                                  ? 'bg-primary-100 hover:bg-primary-100'
                                  : ''
                              }`}
                              onClick={() =>
                                setActiveComponent(
                                  componentMappings[step.id]?.resources || '',
                                )
                              }
                            >
                              Resources
                              {activeComponent ===
                              componentMappings[step.id]?.resources ? (
                                <ChevronRight size={20} />
                              ) : (
                                ''
                              )}
                            </div>
                          </div>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
              <div className="p-5">
                <LinkButton
                  text={'Add new step'}
                  icon={<PlusIcon />}
                  buttonClasses="w-full"
                  onClick={() => setIsAddStepModalOpen(true)}
                />
              </div>
            </div>
            <div className="w-4/6">
              {steps.map((step) => (
                <React.Fragment key={`components-${step.id}`}>
                  {/* Basic Details */}
                  {activeComponent ===
                    componentMappings[step.id]?.basicDetails && (
                    <BasicDetail
                      stepId={`${step.id}-basic-details`}
                      step={step}
                      allSteps={steps}
                      onDelete={() => handleDeleteStep(step.id)}
                      onSave={(data) => {
                        // Store the current active component
                        const currentActiveComponent = activeComponent;

                        // Refresh the steps data after saving
                        reFetchProcessSteps();

                        // Restore the active component after a short delay to ensure the refresh is complete
                        setTimeout(() => {
                          setActiveComponent(currentActiveComponent);
                        }, 100);
                      }}
                    />
                  )}

                  {/* Work Instructions */}
                  {activeComponent ===
                    componentMappings[step.id]?.workInstructions && (
                    <WorkInstruction
                      stepId={`${step.id}-work-instructions`}
                      step={step}
                      allSteps={steps}
                      onDelete={() => handleDeleteStep(step.id)}
                      onSave={(data) => {
                        // Store the current active component
                        const currentActiveComponent = activeComponent;

                        // Refresh the steps data after saving
                        reFetchProcessSteps();

                        // Restore the active component after a short delay to ensure the refresh is complete
                        setTimeout(() => {
                          setActiveComponent(currentActiveComponent);
                        }, 100);
                      }}
                    />
                  )}

                  {/* Resources */}
                  {activeComponent ===
                    componentMappings[step.id]?.resources && (
                    <Resources
                      stepId={`${step.id}-resources`}
                      step={step}
                      onDelete={() => handleDeleteStep(step.id)}
                      onSave={(data) => {
                        // Store the current active component
                        const currentActiveComponent = activeComponent;

                        // Refresh the steps data after saving
                        reFetchProcessSteps();

                        // Restore the active component after a short delay to ensure the refresh is complete
                        setTimeout(() => {
                          setActiveComponent(currentActiveComponent);
                        }, 100);
                      }}
                    />
                  )}
                </React.Fragment>
              ))}
            </div>
          </div>

          {/* Pending Approval Section */}
          {approvalData?.record ? (
            <div className="mt-8">
              <div className="flex gap-4 text-lg text-medium text-dark my-6">
                <div className=" min-w-12 w-12 h-12 rounded bg-white-150 flex items-center justify-center">
                  <ProcessIcon />
                </div>
                <div>
                  <div className="text-lg font-medium text-dark-300 mb-1">
                    Product Approval
                  </div>
                  <div className="text-sm text-grey-300 font-medium">
                    Manage the product approval workflow
                  </div>
                </div>
              </div>

              <div className="p-2 bg-white-100 border border-white-300 rounded-lg mt-3">
                <div className="flex justify-between p-2 pb-4 pt-2 mb-2 border-b border-white-300">
                  <div className="flex gap-4">
                    Pending approvals{' '}
                    <Status
                      type={approvalData.record.status.toLowerCase() as string}
                    />
                  </div>
                  {hasAccess(AccessActions.CanEditSpecificProduct, user) &&
                    approvalData.record.status === 'Pending' && (
                      <Dialog
                        open={editApproversModal}
                        onOpenChange={setEditApproversModal}
                      >
                        <DialogTrigger asChild>
                          <LinkButton
                            size="medium"
                            icon={
                              <EditIcon color="#016366" className="h-5 w-5" />
                            }
                            text="Edit Approvers"
                          />
                        </DialogTrigger>
                        <EditApproversModal
                          setEditApproversModal={setEditApproversModal}
                          approvalId={approvalData.record.id}
                          existingApprovers={approvalData.record.approvers}
                          existingApprovalType={approvalData.record.flow}
                          refetch={reFetchApprovalData}
                          assignees={data?.assignees}
                          masterProduct
                        />
                      </Dialog>
                    )}
                </div>

                <div className="px-2">
                  {approvalData.record.approvers?.map((approver, index) => {
                    return (
                      <div
                        className="border-b border-white-300 last:border-none"
                        key={index}
                      >
                        <div className="py-4 flex items-center justify-between gap-2 text-base text-dark-300 leading-6 font-medium">
                          <div className="flex items-center gap-3">
                            <p>
                              {index + 1}.{' '}
                              {approver?.user.name || approver?.user.full_name}
                            </p>
                            {approver?.status === 'Approved' ? (
                              <>
                                <div className="h-8 w-8 flex items-center justify-center bg-green-50 rounded-full">
                                  <TickIcon
                                    height="16"
                                    width="16"
                                    color="#309665"
                                  />
                                </div>
                                <p className="text-green-200 text-sm">
                                  {approver?.status}
                                </p>
                              </>
                            ) : approver?.status === 'Pending' ? (
                              <>
                                <div className="h-8 w-8 flex items-center justify-center bg-yellow-50 rounded-full">
                                  <WarningIcon
                                    height="16"
                                    width="16"
                                    color="#F19413"
                                  />
                                </div>
                                <p className="text-yellow-200 text-sm">
                                  {approver?.status}
                                </p>
                              </>
                            ) : (
                              <>
                                <div className="h-8 w-8 flex items-center justify-center bg-red-50 rounded-full">
                                  <WarningIcon
                                    height="16"
                                    width="16"
                                    color="#F19413"
                                  />
                                </div>
                                <p className="text-red-200 text-sm">
                                  {approver?.status}
                                </p>
                                <br />
                              </>
                            )}
                          </div>

                          {/* Approve/Reject Buttons - Show only for current user if they are an approver with pending status */}
                          {user?.id === approver.user.id &&
                            approver?.status === 'Pending' &&
                            approvalData.record?.status === 'Pending' &&
                            (() => {
                              // For sequential approval, check if all prior approvers have approved
                              if (approvalData.record?.flow === 'Sequential') {
                                const priorApprovers =
                                  approvalData.record?.approvers.filter(
                                    (a) =>
                                      a.sequence_number <
                                      approver.sequence_number,
                                  ) || [];
                                const allPriorApproved = priorApprovers.every(
                                  (a) => a.status === 'Approved',
                                );
                                return allPriorApproved;
                              }
                              // For parallel approval, user can approve anytime
                              return true;
                            })() && (
                              <div className="flex gap-2">
                                <PrimaryButton
                                  text="Approve"
                                  size="small"
                                  onClick={handleApprove}
                                  isLoading={approvalActionLoading}
                                />
                                <Dialog
                                  open={openJustificationModal}
                                  onOpenChange={setOpenJustificationModal}
                                >
                                  <DialogTrigger asChild>
                                    <TertiaryButton
                                      text="Reject"
                                      size="small"
                                      disabled={approvalActionLoading}
                                    />
                                  </DialogTrigger>
                                  <JustificationModal
                                    setOpenJustificationModal={
                                      setOpenJustificationModal
                                    }
                                    approvalId={approvalData.record?.id || ''}
                                    handleRejectMfa={handleRejectMfa}
                                    refetch={reFetchApprovalData}
                                  />
                                </Dialog>
                              </div>
                            )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
              <div className="mt-4 flex justify-end">
                {(() => {
                  const buttonState = getPublishButtonState();

                  if (buttonState.variant === 'secondary') {
                    return (
                      <SecondaryButton
                        size="medium"
                        text={buttonState.text}
                        disabled={buttonState.disabled}
                      />
                    );
                  }

                  return (
                    <PrimaryButton
                      size="medium"
                      text={buttonState.text}
                      disabled={buttonState.disabled}
                      isLoading={isPublishing}
                      onClick={handlePublish}
                    />
                  );
                })()}
              </div>
            </div>
          ) : (
            <>
              <div className="flex gap-4 text-lg text-medium text-dark my-6 mt-9">
                <div className=" min-w-12 w-12 h-12 rounded bg-white-150 flex items-center justify-center">
                  <ProcessIcon />
                </div>
                <div>
                  <div className="text-lg font-medium text-dark-300 mb-1">
                    Approval Flow
                  </div>
                  <div className="text-sm text-grey-300 font-medium">
                    Open the drop down below to create or view the flowchart
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-16 justify-between mb-5 bg-gray-50 px-6 py-5 rounded-xl">
                  <div>
                    <div className="flex items-center gap-2 flex-nowrap">
                      <h3 className="text-base font-medium text-approval-text-primary">
                        Requires approval
                      </h3>

                      <Tooltip>
                        <TooltipTrigger>
                          <div className="w-10 h-10 flex items-center justify-center rounded-full cursor-pointer">
                            <InfoCircle />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-sm text-dark-300">Test</div>
                        </TooltipContent>
                      </Tooltip>
                    </div>
                    <p className="text-grey-300">
                      Enable if approval is required
                    </p>
                  </div>
                  <div>
                    <ToggleSwitch
                      initialState={requireApproval}
                      onChange={(state) => {
                        setRequireApproval(state);
                      }}
                    />
                  </div>
                </div>
              </div>
              {requireApproval && (
                <>
                  <div className="mb-5">
                    <div className="w-full">
                      <ApprovalTypeSelector
                        value={approvalType}
                        onChange={setApprovalType}
                      />
                    </div>
                  </div>

                  <AddApprovers
                    approvalType={approvalType}
                    approversData={(approvers: ApproverData[]) =>
                      setApproversData(approvers)
                    }
                    assignees={data?.assignees}
                  />
                  {approverError ? (
                    <div className="text-xs font-semibold leading-5 text-left text-red-200">
                      At least one approver is required
                    </div>
                  ) : (
                    <></>
                  )}

                  <div className="mt-5">
                    <PrimaryButton
                      text="Send for Approval"
                      size="medium"
                      onClick={handleSendForApproval}
                      isLoading={isSubmittingApproval}
                      disabled={!approversData || approversData.length === 0}
                    />
                  </div>
                </>
              )}
            </>
          )}
        </div>
      )}

      {/* OTP Modal for CFR compliance */}
      <Dialog open={otpModal} onOpenChange={setOtpModal}>
        {otpModal && (
          <OtpModal
            setOtpModal={setOtpModal}
            sessionId={mfaSessionId}
            refetchDocumentData={reFetchApprovalData}
          />
        )}
      </Dialog>

      {/* Step Delete Confirmation Modal */}
      <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
        <DeleteModal
          title="Delete Step"
          infoText="Are you sure you want to delete this step? This action cannot be undone and will remove all associated work instructions and resources."
          btnText="Delete"
          btnLoading={isDeleting}
          onClick={confirmDeleteStep}
        />
      </Dialog>
    </div>
  );
};

export default ProcessTab;
