import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Plus, Trash2, Check, Upload } from 'lucide-react';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePut } from '@/hooks/usePut';
import { toast } from 'react-toastify';
import useFetch from '@/hooks/useFetch';
import Tabs from '@/components/common/tabs';
import { useDropzone } from 'react-dropzone';
import FileCard from '@/components/common/modals/uploadModal/fileCard';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import ToggleSwitch from '../common/toogleSwitch';
import SingleSelect, { IOption } from '../common/creatableSingleSelect';
import { IDocumentDetails } from '@/interfaces/document';

interface Question {
  id: string;
  description: string;
  type: 'MultipleChoice' | 'Boolean' | 'FillInBlank';
  options: string[];
  correctAnswer: string;
  correctAnswers: number[];
  sampleAnswer: string;
  explanation: string;
  points: number;
  blanks: string[];
}

interface ManageQuizModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  trainingId: string;
  trainingTitle: string;
  onSuccess?: () => void;
  trainingVersion: string;
  passing_score_in_percent?: number;
  includeQuiz?: boolean;
  trainingContent?: any;
}

interface IExtendedDocumentDetails extends IDocumentDetails {
  path?: string;
  file_path?: string;
  name?: string;
}

const ManageQuizModal: React.FC<ManageQuizModalProps> = ({
  open,
  onOpenChange,
  trainingId,
  trainingTitle,
  onSuccess,
  trainingVersion,
  passing_score_in_percent,
  includeQuiz,
  trainingContent,
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { putData, isLoading } = usePut();
  const [questions, setQuestions] = useState<Question[]>([]);
  const [activeTab, setActiveTab] = useState(0);
  const [contentFiles, setContentFiles] = useState<File[]>([]);
  const [uploadingFile, setUploadingFile] = useState(false);
  const [passingScore, setPassingScore] = useState<string>('');
  const [isQuizIncluded, setIsQuizIncluded] = useState(false);
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>('');
  const [documentType, setDocumentType] = useState<string>('');
  const [hasPassingScoreChanged, setHasPassingScoreChanged] = useState(false);
  const [shouldClearExistingContent, setShouldClearExistingContent] =
    useState(false);

  // Fetch documents for document hub
  const { data: publishedDocuments } = useFetch<{
    records: IExtendedDocumentDetails[];
    total: number;
  }>(accessToken, `documents?status=Published`);

  // File upload configuration
  const acceptFileTypes = {
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
      '.docx',
    ],
    'application/msword': ['.doc'],
    'video/mp4': ['.mp4'],
    'video/avi': ['.avi'],
    'video/mov': ['.mov'],
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    // Clear document selection when uploading files
    setSelectedDocumentId('');
    setDocumentType('');
    setShouldClearExistingContent(true);
    // Only take the first file
    setContentFiles([acceptedFiles[0]]);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: acceptFileTypes,
    multiple: false, // Changed to false
  });

  const removeFile = (index: number) => {
    setContentFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const tabsData = [
    { name: 'Quiz', textColor: 'text-dark-100' },
    { name: 'Content', textColor: 'text-dark-100' },
  ];

  // Fetch questions using useFetch
  const {
    data: questionsData,
    isLoading: questionsLoading,
    error: questionsError,
    reFetch: refetchQuestions,
  } = useFetch<{ records: any[] }>(
    accessToken as string,
    open ? `trainings/${trainingId}/questions` : undefined,
    {},
  );

  useEffect(() => {
    if (open && questionsData?.records) {
      console.log('Raw questions data:', questionsData.records); // Debug log

      // Convert API questions to local format
      const formattedQuestions = questionsData.records.map((q: any) => {
        console.log('Processing question:', q.question_type); // Debug log

        return {
          id: q.id || Date.now().toString(),
          description: q.question_text || '',
          type: mapApiTypeToLocal(q.question_type),
          options:
            q.options ||
            (mapApiTypeToLocal(q.question_type) === 'Boolean'
              ? ['True', 'False']
              : ['']),
          correctAnswer:
            Array.isArray(q.correct_answers) && q.correct_answers.length > 0
              ? q.options?.[q.correct_answers[0]] || ''
              : q.correct_answer || '',
          correctAnswers: q.correct_answers || [],
          sampleAnswer: q.sample_answer || '',
          explanation: q.explanation || '',
          points: q.points || 1,
          blanks: q.blanks || [],
        };
      });

      console.log('Formatted questions:', formattedQuestions); // Debug log
      setQuestions(formattedQuestions);
    } else if (open && !questionsLoading && !questionsData?.records?.length) {
      setQuestions([]);
    }
  }, [open, questionsData, questionsLoading]);

  useEffect(() => {
    if (open) {
      const initialPassingScore = String(passing_score_in_percent ?? 0);
      setPassingScore(initialPassingScore);
      setHasPassingScoreChanged(passingScore !== initialPassingScore);
      setIsQuizIncluded(!!includeQuiz);
      setShouldClearExistingContent(false);
    }
  }, [open, passing_score_in_percent, includeQuiz]);

  useEffect(() => {
    if (open && trainingContent?.document?.id) {
      setSelectedDocumentId(trainingContent.document.id);
      setDocumentType('doc_hub');
    } else if (open && !trainingContent?.document?.id) {
      setSelectedDocumentId('');
      setDocumentType('');
    }
  }, [open, trainingContent]);

  // Format documents as options for CreatableSingleSelect
  const documentOptions: IOption[] = React.useMemo(() => {
    if (!publishedDocuments?.records?.length) return [];

    return publishedDocuments.records.map((doc) => ({
      value: doc.id || '',
      label: doc.title || 'Untitled Document',
    }));
  }, [publishedDocuments?.records]);

  // Selected document option for CreatableSingleSelect
  const selectedDocumentOption = React.useMemo(() => {
    if (!selectedDocumentId || !documentOptions.length) return undefined;

    return (
      documentOptions.find((option) => option.value === selectedDocumentId) || {
        value: selectedDocumentId,
        label: 'Selected Document',
      }
    );
  }, [selectedDocumentId, documentOptions]);

  // Map API question types to local types
  const mapApiTypeToLocal = (
    apiType: string,
  ): 'MultipleChoice' | 'Boolean' | 'FillInBlank' => {
    switch (apiType) {
      case 'multiple_choice':
      case 'MultipleChoice':
        return 'MultipleChoice';
      case 'true_false':
      case 'Boolean':
        return 'Boolean';
      case 'fill_in_the_blanks':
      case 'fill_in_blanks':
      case 'FillInBlank':
        return 'FillInBlank';
      default:
        console.log('Unknown question type:', apiType);
        return 'MultipleChoice';
    }
  };

  // Map local question types to API types
  const mapLocalTypeToApi = (localType: string): string => {
    switch (localType) {
      case 'MultipleChoice':
        return 'multiple_choice';
      case 'Boolean':
        return 'true_false';
      case 'FillInBlank':
        return 'fill_in_the_blanks';
      default:
        return 'multiple_choice';
    }
  };

  const addQuestion = () => {
    const newQuestion: Question = {
      id: `new_${Date.now()}`,
      description: '',
      type: 'MultipleChoice',
      options: [''],
      correctAnswer: '',
      correctAnswers: [],
      sampleAnswer: '',
      explanation: '',
      points: 1,
      blanks: [],
    };
    setQuestions((prev) => [...prev, newQuestion]);
  };

  const removeQuestion = (questionId: string) => {
    setQuestions((prev) => prev.filter((q) => q.id !== questionId));
  };

  const updateQuestion = (questionId: string, updates: Partial<Question>) => {
    setQuestions((prev) =>
      prev.map((q) => (q.id === questionId ? { ...q, ...updates } : q)),
    );
  };

  const updateOption = (
    questionId: string,
    optionIndex: number,
    value: string,
  ) => {
    const question = questions.find((q) => q.id === questionId);
    if (!question) return;

    const newOptions = [...question.options];
    newOptions[optionIndex] = value;
    updateQuestion(questionId, { options: newOptions });
  };

  const addBlank = (questionId: string) => {
    const question = questions.find((q) => q.id === questionId);
    if (!question) return;

    const newBlanks = [...(question.blanks || []), ''];
    updateQuestion(questionId, { blanks: newBlanks });
  };

  const updateBlank = (
    questionId: string,
    blankIndex: number,
    value: string,
  ) => {
    const question = questions.find((q) => q.id === questionId);
    if (!question) return;

    const newBlanks = [...(question.blanks || [])];
    newBlanks[blankIndex] = value;
    updateQuestion(questionId, { blanks: newBlanks });
  };

  const removeBlank = (questionId: string, blankIndex: number) => {
    const question = questions.find((q) => q.id === questionId);
    if (!question) return;

    const newBlanks = (question.blanks || []).filter(
      (_, index) => index !== blankIndex,
    );
    updateQuestion(questionId, { blanks: newBlanks });
  };

  const toggleCorrectAnswer = (questionId: string, optionIndex: number) => {
    const question = questions.find((q) => q.id === questionId)!;
    const isCorrect = question.correctAnswers.includes(optionIndex);
    const newCorrectAnswers = isCorrect
      ? question.correctAnswers.filter((index) => index !== optionIndex)
      : [...question.correctAnswers, optionIndex];
    updateQuestion(questionId, { correctAnswers: newCorrectAnswers });
  };

  const duplicateQuestion = (questionId: string) => {
    const questionToDuplicate = questions.find((q) => q.id === questionId);
    if (!questionToDuplicate) return;

    const duplicatedQuestion: Question = {
      ...questionToDuplicate,
      id: `new_${Date.now()}`,
      description: `${questionToDuplicate.description} (Copy)`,
    };

    setQuestions((prev) => [...prev, duplicatedQuestion]);
  };

  const moveQuestion = (questionId: string, direction: 'up' | 'down') => {
    const currentIndex = questions.findIndex((q) => q.id === questionId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= questions.length) return;

    const newQuestions = [...questions];
    [newQuestions[currentIndex], newQuestions[newIndex]] = [
      newQuestions[newIndex],
      newQuestions[currentIndex],
    ];
    setQuestions(newQuestions);
  };

  const handleClearSelection = () => {
    setContentFiles([]);
    setSelectedDocumentId('');
    setDocumentType('');
    setShouldClearExistingContent(true);
  };

  const handleSave = async () => {
    if (activeTab === 0) {
      // Quiz tab validation and save
      const invalidQuestions = questions.filter(
        (q) =>
          !q.description.trim() ||
          !q.points ||
          q.points < 1 ||
          (q.type === 'MultipleChoice' && !q.correctAnswer.trim()) ||
          (q.type === 'Boolean' && !q.correctAnswer.trim()) ||
          (q.type === 'FillInBlank' &&
            (!q.blanks ||
              q.blanks.length === 0 ||
              q.blanks.some((b) => !b.trim()))),
      );

      if (invalidQuestions.length > 0) {
        toast.error('Please complete all questions with required fields');
        return;
      }

      // Format questions for API
      const formattedQuestions = questions.map((q, index) => ({
        question_text: q.description,
        question_type: q.type,
        points: q.points,
        options:
          q.type === 'MultipleChoice' || q.type === 'Boolean'
            ? q.options
            : undefined,
        correct_answer:
          q.type === 'FillInBlank'
            ? q.blanks && q.blanks.length > 0
              ? q.blanks.join('|')
              : q.correctAnswer
            : q.correctAnswer,
        blanks: q.type === 'FillInBlank' ? q.blanks : undefined,
        sequence_number: index + 1,
      }));

      const payload = {
        passing_score_in_percent: passingScore ?? 0,
        questions: formattedQuestions,
      };

      try {
        await putData(
          accessToken as string,
          `trainings/${trainingId}/content`,
          payload,
        );
        toast.success('Quiz questions updated successfully');

        await refetchQuestions();
        if (onSuccess) {
          onSuccess();
        }

        onOpenChange(false);
      } catch (error) {
        toast.error('Failed to update quiz questions');
      }
    } else if (activeTab === 1) {
      try {
        let uploadedFilePath = '';
        let fileExtension = '';

        if (contentFiles.length > 0) {
          const file = contentFiles[0]; // Take first file
          const uploadResult = await handleFileUpload(file);
          if (!uploadResult) {
            toast.error('File upload failed');
            return;
          }
          uploadedFilePath = uploadResult.file_path;
          fileExtension = uploadResult.file_extension;
        }

        const payload: any = {};

        if (shouldClearExistingContent) {
          payload.file_path = null;
          payload.file_extension = null;
          payload.document_id = null;
        }

        if (contentFiles.length > 0) {
          payload.file_path = uploadedFilePath;
          payload.file_extension = fileExtension;
          payload.document_id = null; // Clear document when uploading file
        } else if (documentType === 'doc_hub' && selectedDocumentId) {
          payload.document_id = selectedDocumentId;
          payload.file_path = null; // Clear file when selecting document
          payload.file_extension = null;
        }

        await putData(
          accessToken as string,
          `trainings/${trainingId}/content`,
          payload,
        );

        toast.success('Training content updated successfully');
        setContentFiles([]);
        setShouldClearExistingContent(false);

        if (onSuccess) {
          onSuccess();
        }

        onOpenChange(false);
      } catch (error) {
        toast.error('Failed to update training content');
      }
    }
  };

  const handleFileUpload = async (
    file: File,
  ): Promise<{ file_path: string; file_extension: string } | null> => {
    try {
      setUploadingFile(true);
      const formData = new FormData();
      const id = uuidv4();
      formData.append('file', file);

      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=people_hub&sub_path=/${`people/training/${trainingId}/${trainingVersion}`}`;
      const response = await axios.post(url, formData, config);

      setUploadingFile(false);
      return {
        file_path: response.data.file_path,
        file_extension: response.data.file_ext,
      };
    } catch (error) {
      setUploadingFile(false);
      console.error('Error uploading file:', error);
      toast.error('File upload failed');
      return null;
    }
  };

  const renderQuizTab = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-base font-medium">Quiz Configuration</h3>
        <div className="flex items-center space-x-3">
          <SecondaryButton
            text="+ Multiple Choice"
            size="medium"
            onClick={() => {
              const newQuestion: Question = {
                id: `new_${Date.now()}`,
                description: '',
                type: 'MultipleChoice',
                options: [''],
                correctAnswers: [],
                correctAnswer: '',
                sampleAnswer: '',
                explanation: '',
                points: 1,
                blanks: [],
              };
              setQuestions((prev) => [...prev, newQuestion]);
            }}
          />
          <SecondaryButton
            text="+ True/False"
            size="medium"
            onClick={() => {
              const newQuestion: Question = {
                id: `new_${Date.now()}`,
                description: '',
                type: 'Boolean',
                options: ['True', 'False'],
                correctAnswers: [],
                correctAnswer: '',
                sampleAnswer: '',
                explanation: '',
                points: 1,
                blanks: [],
              };
              setQuestions((prev) => [...prev, newQuestion]);
            }}
          />
          <SecondaryButton
            text="+ Fill in Blanks"
            size="medium"
            onClick={() => {
              const newQuestion: Question = {
                id: `new_${Date.now()}`,
                description: '',
                type: 'FillInBlank',
                options: [''],
                correctAnswers: [],
                correctAnswer: '',
                sampleAnswer: '',
                explanation: '',
                points: 1,
                blanks: [''],
              };
              setQuestions((prev) => [...prev, newQuestion]);
            }}
          />
        </div>
      </div>

      {questions.length === 0 && (
        <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
          <div className="text-gray-500">
            <h4 className="text-lg font-medium mb-2">No Quiz Questions</h4>
            <p className="text-sm mb-4">
              Start building your quiz by adding questions using the dropdown
              above.
            </p>
          </div>
        </div>
      )}

      {/* Question rendering logic */}
      {questions.map((question, questionIndex) => (
        <div key={question.id} className="border rounded-lg p-6 space-y-4">
          <div className="flex justify-between items-start">
            <div className="flex items-center space-x-2">
              <h4 className="text-lg font-medium">
                Question {questionIndex + 1}
              </h4>
              {question.id.startsWith('new_') && (
                <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">
                  New
                </span>
              )}
            </div>
            <button
              onClick={() => removeQuestion(question.id)}
              className="text-red-500 hover:text-red-700"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>

          <div>
            <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
              Question Description<span className="text-red-200">*</span>
            </Label>
            <Input
              value={question.description}
              onChange={(e) =>
                updateQuestion(question.id, {
                  description: e.target.value,
                })
              }
              placeholder="Enter question"
            />
          </div>

          <div>
            <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
              Question Type
            </Label>
            <Select
              value={question.type}
              onValueChange={(
                value: 'MultipleChoice' | 'Boolean' | 'FillInBlank',
              ) =>
                updateQuestion(question.id, {
                  type: value,
                  options: value === 'Boolean' ? ['True', 'False'] : [''],
                  correctAnswers: [],
                  correctAnswer: '',
                  sampleAnswer: '',
                  explanation: '',
                  blanks: value === 'FillInBlank' ? [''] : [],
                })
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="MultipleChoice">Multiple Choice</SelectItem>
                <SelectItem value="Boolean">True/False</SelectItem>
                <SelectItem value="FillInBlank">Fill in Blanks</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Multiple Choice Options */}
          {question.type === 'MultipleChoice' && (
            <div>
              <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
                Options<span className="text-red-200">*</span>
              </Label>
              <div className="space-y-2">
                {question.options.map((option, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Input
                      value={option}
                      onChange={(e) =>
                        updateOption(question.id, index, e.target.value)
                      }
                      placeholder={`Option ${index + 1}`}
                      className="flex-1"
                    />
                    <input
                      type="radio"
                      name={`correct-${question.id}`}
                      checked={question.correctAnswer === option}
                      onChange={() =>
                        updateQuestion(question.id, {
                          correctAnswer: option,
                        })
                      }
                      className="w-4 h-4 text-primary-600 border-gray-300 focus:ring-primary-500"
                    />
                    <label className="text-sm text-gray-600">Correct</label>
                    {question.options.length > 2 && (
                      <button
                        onClick={() => {
                          const newOptions = question.options.filter(
                            (_, i) => i !== index,
                          );
                          updateQuestion(question.id, {
                            options: newOptions,
                            correctAnswer:
                              question.correctAnswer === option
                                ? ''
                                : question.correctAnswer,
                          });
                        }}
                        className="text-red-500 hover:text-red-700 p-1"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
                <button
                  onClick={() => {
                    const newOptions = [...question.options, ''];
                    updateQuestion(question.id, { options: newOptions });
                  }}
                  className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
                >
                  <Plus className="w-3 h-3" />
                  Add Option
                </button>
              </div>
            </div>
          )}

          {/* Boolean (True/False) */}
          {question.type === 'Boolean' && (
            <div>
              <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
                Correct Answer
              </Label>
              <div className="grid grid-cols-2 gap-4">
                {['True', 'False'].map((answer) => (
                  <div
                    key={answer}
                    className={`p-4 border rounded-lg cursor-pointer ${
                      question.correctAnswer === answer
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() =>
                      updateQuestion(question.id, {
                        correctAnswer: answer,
                      })
                    }
                  >
                    <div className="text-center font-medium">{answer}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Fill in Blanks */}
          {question.type === 'FillInBlank' && (
            <div>
              <div>
                <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
                  Instructions
                </Label>
                <p className="text-sm text-gray-600 mb-3">
                  Use [...] in your question text to indicate where blanks
                  should appear. Add multiple correct answers below for each
                  blank.
                </p>
              </div>
              <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
                Correct Answers for Blanks
                <span className="text-red-200">*</span>
              </Label>
              <div className="space-y-2">
                {(question.blanks || []).map((blank, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <span className="text-sm text-gray-600 w-16">
                      Blank {index + 1}:
                    </span>
                    <Input
                      value={blank}
                      onChange={(e) =>
                        updateBlank(question.id, index, e.target.value)
                      }
                      placeholder="Enter correct answer"
                      className="flex-1"
                    />
                    {(question.blanks || []).length > 1 && (
                      <button
                        onClick={() => removeBlank(question.id, index)}
                        className="text-red-500 hover:text-red-700 p-1"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    )}
                  </div>
                ))}
                <button
                  onClick={() => addBlank(question.id)}
                  className="text-blue-600 hover:text-blue-800 text-sm flex items-center gap-1"
                >
                  <Plus className="w-3 h-3" />
                  Add Blank
                </button>
              </div>
            </div>
          )}

          <div>
            <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
              Explanation (optional)
            </Label>
            <Textarea
              value={question.explanation || ''}
              onChange={(e) =>
                updateQuestion(question.id, {
                  explanation: e.target.value,
                })
              }
              placeholder="Provide additional explanation"
              rows={2}
            />
          </div>

          <div>
            <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
              Points<span className="text-red-200">*</span>
            </Label>
            <Input
              type="number"
              value={question.points}
              onChange={(e) => {
                const newPoints = parseInt(e.target.value, 10);
                if (!isNaN(newPoints) && newPoints > 0) {
                  updateQuestion(question.id, { points: newPoints });
                }
              }}
              placeholder="Enter points"
              className="!w-32"
            />
          </div>
        </div>
      ))}
    </div>
  );

  const renderContentTab = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Training Content</h3>
      </div>

      {/* Show existing file from trainingContent if it exists and not being cleared */}
      {trainingContent?.file_path && !shouldClearExistingContent && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Current File:</h4>
          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <Upload className="w-5 h-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {trainingContent?.file_path.split('/').pop() ||
                      'Training File'}
                  </p>
                  <p className="text-xs text-gray-500">
                    Existing training content
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Show existing document from trainingContent if it exists and not being cleared */}
      {trainingContent?.document?.id && !shouldClearExistingContent && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-700">
            Current Document:
          </h4>
          <div className="border rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                  <Upload className="w-5 h-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {trainingContent?.document?.title || 'Linked Document'}
                  </p>
                  <p className="text-xs text-gray-500">From Document Hub</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Clear existing content message */}
      {shouldClearExistingContent &&
        (trainingContent?.file_path || trainingContent?.document?.id) && (
          <div className="border rounded-lg p-4 bg-yellow-50 border-yellow-200">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <Trash2 className="w-4 h-4 text-yellow-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  Existing content will be removed
                </p>
                <p className="text-xs text-yellow-600">
                  The current training content will be cleared when you save
                </p>
              </div>
            </div>
          </div>
        )}

      {/* File Upload Section */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <Label className="text-base font-medium leading-6 text-dark-100">
            Upload New Training Content File
          </Label>
          {selectedDocumentId && (
            <span className="text-xs text-gray-500">
              (Disabled - Document from hub selected)
            </span>
          )}
        </div>

        <div
          {...getRootProps()}
          className={`min-h-32 border-2 border-dashed rounded-lg flex items-center justify-center flex-col gap-2 p-6 transition-colors ${
            selectedDocumentId
              ? 'bg-gray-100 border-gray-200 cursor-not-allowed opacity-50'
              : contentFiles.length > 0
              ? 'bg-blue-50 border-blue-300 cursor-pointer'
              : 'bg-gray-50 border-gray-300 hover:bg-gray-100 cursor-pointer'
          }`}
        >
          <input {...getInputProps()} disabled={!!selectedDocumentId} />
          <Upload className="w-8 h-8 text-gray-400" />
          <div className="text-center">
            <p className="text-sm font-medium text-gray-700">
              {contentFiles.length > 0
                ? `${contentFiles[0].name}`
                : selectedDocumentId
                ? 'File upload disabled'
                : trainingContent?.file_path && !shouldClearExistingContent
                ? 'Upload new training content file (replaces current)'
                : 'Upload training content file'}
            </p>
            <p className="text-xs text-gray-500">
              {selectedDocumentId
                ? 'Clear document selection to upload files'
                : 'Drag and drop a file here, or click to select'}
            </p>
            {!selectedDocumentId && (
              <p className="text-xs text-gray-400 mt-1">
                Supports PDF, DOC, DOCX, MP4, AVI, MOV
              </p>
            )}
          </div>
        </div>

        {/* New File List */}
        {contentFiles.length > 0 && (
          <div className="space-y-2 mt-4">
            <h4 className="text-sm font-medium text-gray-700">
              New File to Upload:
            </h4>
            <div className="space-y-2">
              <FileCard file={contentFiles[0]} />
            </div>
          </div>
        )}
      </div>

      {/* Divider */}
      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-gray-300" />
        </div>
        <div className="relative flex justify-center text-sm">
          <span className="px-2 bg-white text-gray-500">OR</span>
        </div>
      </div>

      {/* Document Hub Section */}
      <div>
        <div className="flex items-center justify-between mb-3">
          <Label className="text-base font-medium leading-6 text-dark-100">
            Link Document from Document Hub
          </Label>
          {contentFiles.length > 0 && (
            <span className="text-xs text-gray-500">
              (Disabled - File uploaded)
            </span>
          )}
        </div>

        <SingleSelect
          selectedOption={
            selectedDocumentOption?.value.trim() ? selectedDocumentOption : null
          }
          options={documentOptions}
          onChange={(selected) => {
            if (selected?.value) {
              // Clear uploaded files when selecting document
              setContentFiles([]);
              setSelectedDocumentId(selected.value);
              setDocumentType('doc_hub');
              setShouldClearExistingContent(true);
            } else {
              // Clear document selection
              setSelectedDocumentId('');
              setDocumentType('');
            }
          }}
          placeholder="Select documents"
          isDisabled={contentFiles.length > 0}
          hasError={false}
        />

        {contentFiles.length > 0 && (
          <p className="text-xs text-gray-500 mt-1">
            Remove uploaded file to select document from hub
          </p>
        )}
      </div>

      {/* Clear Selection Button - Enhanced */}
      {(contentFiles.length > 0 ||
        selectedDocumentId ||
        shouldClearExistingContent) && (
        <div className="flex justify-end space-x-2">
          <SecondaryButton
            text="Clear All Content"
            size="small"
            onClick={handleClearSelection}
          />
        </div>
      )}

      {/* Information about what will happen */}
      {(contentFiles.length > 0 ||
        selectedDocumentId ||
        shouldClearExistingContent) && (
        <div className="text-xs text-gray-500 p-3 bg-gray-50 rounded-lg">
          <p className="font-medium mb-1">What happens when you save:</p>
          <ul className="space-y-1">
            {contentFiles.length > 0 && (
              <li>• New file will be uploaded and set as training content</li>
            )}
            {selectedDocumentId && !contentFiles.length && (
              <li>• Selected document will be linked as training content</li>
            )}
            {shouldClearExistingContent &&
              !contentFiles.length &&
              !selectedDocumentId && (
                <li>• All existing training content will be removed</li>
              )}
            {(contentFiles.length > 0 || selectedDocumentId) &&
              (trainingContent?.file_path || trainingContent?.document?.id) && (
                <li>• Previous training content will be replaced</li>
              )}
          </ul>
        </div>
      )}
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Training</DialogTitle>
          <p className="text-sm text-gray-600">{trainingTitle}</p>
        </DialogHeader>

        <div className="mt-4">
          <div className="border-b border-white-300 mb-6">
            <Tabs
              tabsData={tabsData}
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              tabGroupName="manage-training"
            />
          </div>

          <div className="min-h-[400px]">
            {questionsLoading ? (
              <div className="flex justify-center items-center py-12">
                <div className="text-gray-500">Loading...</div>
              </div>
            ) : questionsError ? (
              <div className="flex justify-center items-center py-12">
                <div className="text-red-500">
                  Failed to load data. Please try again.
                </div>
              </div>
            ) : (
              <>
                {activeTab === 0 && renderQuizTab()}
                {activeTab === 1 && renderContentTab()}
              </>
            )}
          </div>

          <div className="my-4">
            {activeTab === 0 && (
              <div className="flex items-center justify-end space-x-2">
                <Label htmlFor="passingScore">Passing Score (%)</Label>
                <Input
                  id="passingScore"
                  type="number"
                  min="0"
                  max="100"
                  value={passingScore}
                  onChange={(e) => {
                    const val = e.target.value;
                    // Allow empty string, otherwise keep numeric
                    setPassingScore(
                      val === ''
                        ? ''
                        : String(Math.min(100, Math.max(0, Number(val)))),
                    );
                  }}
                  className="w-20"
                  placeholder="70"
                />
              </div>
            )}
          </div>

          {/* Actions */}
          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-gray-600">
              {activeTab === 0
                ? `${questions.length} question${
                    questions.length !== 1 ? 's' : ''
                  } total`
                : contentFiles.length > 0
                ? `1 file selected`
                : selectedDocumentId
                ? `1 document selected`
                : shouldClearExistingContent
                ? `Content will be cleared`
                : `No changes`}
            </div>
            <div className="flex space-x-3">
              <SecondaryButton
                size="medium"
                text="Cancel"
                onClick={() => onOpenChange(false)}
              />
              <PrimaryButton
                text={activeTab === 0 ? 'Save Questions' : 'Update Content'}
                size="medium"
                onClick={handleSave}
                disabled={
                  (activeTab === 0 &&
                    questions.length === 0 &&
                    !hasPassingScoreChanged) ||
                  (activeTab === 1 &&
                    contentFiles.length === 0 &&
                    !selectedDocumentId &&
                    !shouldClearExistingContent) ||
                  isLoading ||
                  uploadingFile
                }
                isLoading={isLoading || uploadingFile}
              />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ManageQuizModal;
