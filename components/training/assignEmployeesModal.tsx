import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { toast } from 'react-toastify';
import useFetch from '@/hooks/useFetch';
import moment from 'moment';
import Calendar from '../common/calendar';
import { Checkbox } from '../common/checkbox';

interface User {
  id: string;
  name: string;
  email: string;
  departments: { id: string; name: string }[];
  job_title: { id: string; name: string };
}

interface Assignment {
  id: string;
  user: {
    id: string;
    employee_id: string;
    name: string;
  };
  training: {
    id: string;
    title: string;
    description: string;
  };
  assigned_on: string;
  due_date: string;
  status: string;
}

interface AssignEmployeesModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  trainingId: string;
  trainingTitle: string;
  refetch: () => void;
}

const AssignEmployeesModal: React.FC<AssignEmployeesModalProps> = ({
  open,
  onOpenChange,
  trainingId,
  trainingTitle,
  refetch,
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { postData, isLoading } = usePost();
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [dueDate, setDueDate] = useState<string | null>(null);
  const [notes, setNotes] = useState('');

  // Fetch users
  const { data: usersData } = useFetch<{ records: User[] }>(
    accessToken,
    'employees',
  );

  const { data: assignmentsData } = useFetch<{ records: Assignment[] }>(
    accessToken,
    'trainings/assignments/',
  );

  const users = usersData?.records || [];
  const assignments = assignmentsData?.records || [];

  const assignedUserIds = assignments
    .filter((assignment) => assignment.training.id === trainingId)
    .map((assignment) => assignment.user.id);

  const availableUsers = users.filter(
    (user) => !assignedUserIds.includes(user.id),
  );

  const filteredUsers = availableUsers.filter((user) => {
    const name = user?.name?.toLowerCase() || '';
    const email = user?.email?.toLowerCase() || '';
    return (
      name.includes(searchTerm.toLowerCase()) ||
      email.includes(searchTerm.toLowerCase())
    );
  });

  const handleUserToggle = (userId: string) => {
    setSelectedUsers((prev) =>
      prev.includes(userId)
        ? prev.filter((id) => id !== userId)
        : [...prev, userId],
    );
  };

  const handleSelectAll = () => {
    if (selectedUsers.length === filteredUsers.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(filteredUsers.map((user) => user.id));
    }
  };

  const handleAssign = async () => {
    if (selectedUsers.length === 0) {
      toast.error('Please select at least one employee');
      return;
    }

    if (!dueDate) {
      toast.error('Please select a due date');
      return;
    }

    const payload = {
      due_date: dueDate,
      assignment_type: 'user',
      entity_ids: selectedUsers,
      notes: notes || '',
    };

    try {
      await postData(
        accessToken as string,
        `trainings/${trainingId}/assign`,
        payload,
      );
      toast.success(
        `Successfully assigned training to ${selectedUsers.length} employees`,
      );
      handleClose();
      refetch();
    } catch (error) {
      toast.error('Failed to assign training');
    }
  };

  const handleClose = () => {
    setSelectedUsers([]);
    setSearchTerm('');
    setDueDate('');
    setNotes('');
    onOpenChange(false);
  };

  useEffect(() => {
    if (open) {
      setSelectedUsers([]);
    }
  }, [open]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Assign Training: {trainingTitle}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 overflow-hidden flex flex-col space-y-6">
          {/* Due Date and Notes */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label
                htmlFor="start_date"
                className="text-base font-medium leading-6 text-dark-100"
              >
                Due Date<span className="text-red-200">*</span>
              </Label>
              <Calendar
                selectedDate={dueDate || ''}
                onDateChange={(date) =>
                  setDueDate(moment(date as string).format('YYYY-MM-DD'))
                }
                allowPastDates={false}
                className="mt-2"
              />
            </div>
          </div>

          {/* Search */}
          <div>
            <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
              Search Employees
            </Label>
            <Input
              type="text"
              placeholder="Search by name or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full"
            />
          </div>

          {/* Select All */}
          <div className="flex items-center justify-between">
            <button
              onClick={handleSelectAll}
              className="text-sm text-primary-500 hover:text-primary-600"
              disabled={filteredUsers.length === 0}
            >
              {selectedUsers.length === filteredUsers.length
                ? 'Deselect All'
                : 'Select All'}
            </button>
            <span className="text-sm text-gray-600">
              {selectedUsers.length} of {filteredUsers.length} selected
            </span>
          </div>

          {/* Users List */}
          <div className="flex-1 overflow-y-auto border border-gray-200 rounded-lg">
            <div className="space-y-2 p-2">
              {filteredUsers.map((user) => (
                <div
                  key={user.id}
                  className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                    selectedUsers.includes(user.id)
                      ? 'border-primary-500 bg-primary-50'
                      : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleUserToggle(user.id)}
                >
                  <div className="flex items-center space-x-3">
                    <div onClick={(e) => e.stopPropagation()}>
                      <Checkbox
                        checked={selectedUsers.includes(user.id)}
                        onCheckedChange={() => handleUserToggle(user.id)}
                      />
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">
                        {user.name}
                      </p>
                      <p className="text-xs text-gray-500">{user.email}</p>
                    </div>
                    <div className="">
                      <p className="text-sm capitalize text-gray-900">
                        {user?.job_title?.name}
                      </p>
                    </div>
                  </div>
                </div>
              ))}

              {availableUsers.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  All employees are already assigned to this training
                </div>
              )}

              {availableUsers.length > 0 && filteredUsers.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No employees found matching your search criteria
                </div>
              )}
            </div>
          </div>
        </div>

        <div>
          <Label className="text-base font-medium leading-6 text-dark-100 mb-2 block">
            Assignment Notes
          </Label>
          <Textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Optional notes for this assignment..."
            rows={3}
            className="w-full"
          />
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <SecondaryButton size="medium" text="Cancel" onClick={handleClose} />
          <PrimaryButton
            text={`Assign to ${selectedUsers.length} Employee${
              selectedUsers.length !== 1 ? 's' : ''
            }`}
            size="medium"
            onClick={handleAssign}
            disabled={
              selectedUsers.length === 0 ||
              !dueDate ||
              isLoading ||
              filteredUsers.length === 0
            }
            isLoading={isLoading}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AssignEmployeesModal;
