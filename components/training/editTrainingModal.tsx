import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePut } from '@/hooks/usePut';
import { toast } from 'react-toastify';
import { ReactSelectMulti } from '../common/multiSelectInput';
import ToggleSwitch from '../common/toogleSwitch';
import useFetch from '@/hooks/useFetch';

interface IOption {
  label: string;
  value: string;
}

interface TrainingData {
  id: string;
  title: string;
  description: string;
  category: any;
  type: string;
  status: string;
  version: string;
  includeQuiz?: boolean;
  passing_score_in_percent?: number;
  training_version: {
    passing_score: number;
  };
}

interface EditTrainingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  trainingId: string;
  trainingData: TrainingData;
  refetch: () => void;
}

const EditTrainingModal: React.FC<EditTrainingModalProps> = ({
  open,
  onOpenChange,
  trainingId,
  trainingData,
  refetch,
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { putData, isLoading } = usePut();

  interface Category {
    id: string;
    name: string;
  }

  interface CategoriesResponse {
    records: Category[];
  }

  const {
    data: categories,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useFetch<CategoriesResponse>(accessToken, 'training/categories', {});

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    training_type: 'document',
    category: '',
    passing_score_in_percent: 0,
    includeQuiz: false,
  });

  const [validationErrors, setValidationErrors] = useState<
    Record<string, string>
  >({});
  const [showValidationErrors, setShowValidationErrors] = useState(false);
  const contentTypes = [{ label: 'PDF', value: 'document' }];

  useEffect(() => {
    if (open && trainingData) {
      setFormData({
        title: trainingData.title || '',
        description: trainingData.description || '',
        training_type: trainingData.type || 'document',
        category: trainingData.category?.id,
        passing_score_in_percent:
          trainingData.training_version.passing_score || 0,
        includeQuiz: (trainingData.training_version.passing_score ?? 0) > 0,
      });
      setValidationErrors({});
      setShowValidationErrors(false);
    }
  }, [open, trainingData]);

  const validateForm = () => {
    const errors: Record<string, string> = {};

    if (!formData.title.trim()) {
      errors.title = 'Title is required';
    }
    if (!formData.description.trim()) {
      errors.description = 'Description is required';
    }
    if (!formData.category) {
      errors.category = 'Category is required';
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSave = async () => {
    setShowValidationErrors(true);

    if (!validateForm()) {
      toast.error('Please fix the validation errors');
      return;
    }

    const payload = {
      title: formData.title,
      description: formData.description,
      category: formData.category,
      training_type: 'document',
      passingScore: formData.passing_score_in_percent,
    };

    try {
      await putData(accessToken as string, `trainings/${trainingId}`, payload);
      toast.success('Training updated successfully');
      onOpenChange(false);
      refetch();
      // Optionally trigger a refresh of the parent component
    } catch (error) {
      toast.error('Failed to update training');
    }
  };

  const handleClose = () => {
    setValidationErrors({});
    setShowValidationErrors(false);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Edit Training</DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="md:col-span-2">
              <Label
                htmlFor="title"
                className="text-base font-medium leading-6 text-dark-100 mb-2 block"
              >
                Training Title<span className="text-red-200">*</span>
              </Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, title: e.target.value }))
                }
                placeholder="Enter training title"
                className={
                  showValidationErrors && validationErrors.title
                    ? 'border-red-500'
                    : ''
                }
              />
              {showValidationErrors && validationErrors.title && (
                <p className="text-red-500 text-sm mt-1">
                  {validationErrors.title}
                </p>
              )}
            </div>

            <div className="md:col-span-2">
              <Label
                htmlFor="description"
                className="text-base font-medium leading-6 text-dark-100 mb-2 block"
              >
                Description<span className="text-red-200">*</span>
              </Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                placeholder="Enter training description"
                rows={3}
                className={
                  showValidationErrors && validationErrors.description
                    ? 'border-red-500'
                    : ''
                }
              />
              {showValidationErrors && validationErrors.description && (
                <p className="text-red-500 text-sm mt-1">
                  {validationErrors.description}
                </p>
              )}
            </div>

            <div>
              <Label
                htmlFor="training_type"
                className="text-base font-medium leading-6 text-dark-100 mb-2 block"
              >
                Training Type<span className="text-red-200">*</span>
              </Label>
              <Select
                value={'document'}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, training_type: value }))
                }
              >
                <SelectTrigger
                  className={
                    showValidationErrors && validationErrors.training_type
                      ? 'border-red-500'
                      : ''
                  }
                >
                  <SelectValue placeholder="Select training type" />
                </SelectTrigger>
                <SelectContent>
                  {contentTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showValidationErrors && validationErrors.training_type && (
                <p className="text-red-500 text-sm mt-1">
                  {validationErrors.training_type}
                </p>
              )}
            </div>

            <div>
              <Label
                htmlFor="category"
                className="text-base font-medium leading-6 text-dark-100 mb-2 block"
              >
                Category<span className="text-red-200">*</span>
              </Label>
              <Select
                value={formData.category}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, category: value }))
                }
              >
                <SelectTrigger
                  className={
                    showValidationErrors && validationErrors.category
                      ? 'border-red-500'
                      : ''
                  }
                >
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories?.records?.map((cat: any) => (
                    <SelectItem key={cat.id} value={cat.id}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {showValidationErrors && validationErrors.category && (
                <p className="text-red-500 text-sm mt-1">
                  {validationErrors.category}
                </p>
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t">
          <SecondaryButton text="Cancel" size="medium" onClick={handleClose} />
          <PrimaryButton
            text="Save Changes"
            size="medium"
            onClick={handleSave}
            isLoading={isLoading}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EditTrainingModal;
