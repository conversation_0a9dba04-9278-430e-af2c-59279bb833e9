import axios from 'axios';
import moment from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import {
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';

import TertiaryButton from '../../common/button/tertiaryButton';
import FileCard from '../../common/modals/uploadModal/fileCard';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';

// Document interface is not needed anymore as we're only using independent documents
const acceptFileTypes = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
    '.doc',
  ],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
  'application/vnd.ms-excel': ['.xls', '.csv'],
};
interface IProps {
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  showModal: boolean;
  edit?: boolean;
  vendorId: string;
  documentData?: {
    id?: string;
    document_type?: string;
    file_path?: string;
    file_name?: string;
    document_id?: string;
    expiry_date?: string;
    document_path?: string;
  };
  onSuccess?: () => void;
}

export const VendorDocumentModal = ({
  setShowModal,
  showModal,
  edit = false,
  vendorId,
  documentData,
  onSuccess,
}: IProps) => {
  const { accessToken } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  // Track loading state
  const [uploadingFile, setUploadingFile] = useState(false);
  const [addedFile, setAddedFile] = React.useState<File[] | null>(null);
  const [expiryDate, setExpiryDate] = useState<string>('');

  // Track if there are changes in edit mode
  const [hasChanges, setHasChanges] = useState(false);
  // Initialize component with document data if in edit mode
  useEffect(() => {
    if (showModal) {
      if (edit && documentData) {
        // Reset changes flag when modal opens
        setHasChanges(false);
      }
      if (documentData?.expiry_date) {
        setExpiryDate(documentData.expiry_date);
      } else {
        setExpiryDate('');
      }
    }
  }, [edit, documentData, showModal]);

  // Track changes in edit mode
  useEffect(() => {
    if (edit && documentData) {
      const fileChanged = addedFile && addedFile.length > 0;
      const expiryChanged = expiryDate !== (documentData.expiry_date || '');

      setHasChanges(fileChanged || expiryChanged);
    }
  }, [edit, documentData, addedFile, expiryDate]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setAddedFile(acceptedFiles);
    // Document type is already 'independent'
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: false,
    accept: acceptFileTypes,
  });

  const handleFileUpload = async (
    file: File,
  ): Promise<{ file_path: string; file_extension: string } | null> => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      // Adjust the sub_path as needed for your application
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=vendor_hub&sub_path=/${vendorId}/documents`;
      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };

      const response = await axios.post(url, formData, config);

      if (response.status === 200) {
        return {
          file_path: response.data.file_path,
          file_extension: response.data.file_ext,
        };
      } else {
        console.error('Error uploading file:', file.name);
        return null;
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error(`Failed to upload file: ${file.name}`);
      return null;
    }
  };

  // Initialize the POST and PUT hooks
  const { postData, isLoading: isPosting, response: postResponse } = usePost();
  const { putData, isLoading: isPutting, response: putResponse } = usePut();

  // Set loading state based on both hooks
  useEffect(() => {
    setIsLoading(isPosting || isPutting || uploadingFile);
  }, [isPosting, isPutting, uploadingFile]);

  // Handle successful responses
  useEffect(() => {
    if (postResponse || putResponse) {
      if (onSuccess) {
        onSuccess();
      }
      setShowModal(false);
      toast.success(
        edit ? 'Document updated successfully' : 'Document added successfully',
      );
    }
  }, [postResponse, putResponse, onSuccess, setShowModal, edit]);

  const handleSubmit = async () => {
    try {
      // Handle differently based on whether we're in edit mode or create mode
      if (edit && documentData?.id) {
        // EDIT MODE

        const payload: Record<string, string | undefined> = {
          document_type: 'independent',
        };

        if (addedFile && addedFile.length > 0) {
          setUploadingFile(true);
          try {
            const uploadResult = await handleFileUpload(addedFile[0]);
            if (!uploadResult) {
              toast.error('File upload failed');
              return;
            }

            payload.file_path = uploadResult.file_path;
            payload.file_name = addedFile[0].name;
          } finally {
            setUploadingFile(false);
          }
        } else if (documentData?.file_path) {
          if (!documentData.file_path.includes(vendorId)) {
            payload.file_path = documentData.file_path;
            payload.file_name = documentData.file_name;
          }
        }

        const originalExpiryDate = documentData.expiry_date || '';
        if (expiryDate !== originalExpiryDate) {
          payload.expiry_date = expiryDate || undefined;
        }

        if (Object.keys(payload).length <= 1) {
          toast.info('No changes to update');
          return;
        }

        const path = `vendors/${vendorId}/documents/${documentData.id}`;
        await putData(accessToken as string, path, payload);
      } else {
        // CREATE MODE

        if (!addedFile || addedFile.length === 0) {
          toast.error('Please select a file');
          return;
        }

        setUploadingFile(true);
        let uploadedFilePath = '';
        let uploadedFileName = '';

        try {
          const uploadResult = await handleFileUpload(addedFile[0]);
          if (!uploadResult) {
            toast.error('File upload failed');
            return;
          }
          uploadedFilePath = uploadResult.file_path;
          uploadedFileName = addedFile[0].name;
        } finally {
          setUploadingFile(false);
        }

        const payload: Record<string, string | undefined> = {
          document_type: 'independent',
          file_path: uploadedFilePath,
          file_name: uploadedFileName,
        };

        if (expiryDate) {
          payload.expiry_date = expiryDate;
        }

        const path = `vendors/${vendorId}/documents`;
        await postData(accessToken as string, path, payload);
      }
    } catch (error) {
      console.error('Error processing document:', error);
      toast.error('Failed to process document');
    }
  };

  useEffect(() => {
    if (!showModal) {
      setAddedFile(null);
      setExpiryDate('');
      setHasChanges(false);
    }
  }, [showModal]);

  console.log(documentData, expiryDate, 'expiryDate');
  return (
    <DialogContent
      className="min-w-[43.75rem] p-6"
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      <DialogHeader>
        <DialogTitle>{edit ? 'Edit document' : 'Add document'} </DialogTitle>
      </DialogHeader>
      {/* <div className="mt-2 overflow-hidden w-full">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Link Document from document hub
          <span className="text-[#F55D5D]">*</span>
        </div>
        <Select
          value={selectedDocumentId}
          onValueChange={(value) => {
            setSelectedDocumentId(value);
            setDocumentType('doc_hub');
            // Clear any uploaded file when selecting a document
            setAddedFile(null);
          }}
          // disabled={edit}
        >
          <SelectTrigger id="origin">
            <SelectValue placeholder="Select documents" />
          </SelectTrigger>
          <SelectContent>
            {publishedDocuments?.records.map((e, i) => (
              <SelectItem value={e.id} key={i}>
                {e.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="w-full h-0 border-b border-white-300 relative my-4">
        <div className="absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 bg-white-100 px-4 text-sm font-medium text-grey-300">
          Or
        </div>
      </div> */}

      <div className="mt-2">
        <div className="text-base leading-6 font-medium text-dark-100 mb-2.5">
          Attach document
        </div>

        <div>
          <div
            className=" min-h-28 bg-white-100 border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2"
            {...getRootProps()}
          >
            {!(addedFile?.length && addedFile?.length > 0) ||
              (documentData?.document_path?.length === 0 && (
                <div className="text-sm font-medium leading-5 text-[#49474E]">
                  Upload or Drag and drop to upload your file
                </div>
              ))}

            <input {...getInputProps()} />
            <div className="flex justify-center items-center flex-wrap gap-2">
              {addedFile?.map((file, index) => (
                <FileCard key={index} file={file} setAddedFile={setAddedFile} />
              ))}
              {!addedFile && documentData?.document_path && (
                <FileCard
                  key={documentData.document_path}
                  prefillFileName={documentData.file_name}
                />
              )}
            </div>
            <TertiaryButton
              text={
                (addedFile?.length && addedFile?.length > 0) ||
                (documentData?.document_path?.length &&
                  documentData?.document_path?.length > 0)
                  ? 'Replace'
                  : 'Select file'
              }
              size="small"
            />
          </div>
        </div>
      </div>
      <div className="flex flex-col flex-1">
        <Label
          htmlFor="expiry_date"
          className="text-base font-medium leading-6 text-dark-100 mb-2.5"
        >
          Expiry date
        </Label>

        <Calendar
          selectedDate={expiryDate}
          onDateChange={(date) => {
            if (date) {
              setExpiryDate(moment(date as string).format('YYYY-MM-DD'));
            } else {
              setExpiryDate('');
            }
          }}
          allowPastDates={false}
          disableFutureDate={false}
        />
      </div>

      <DialogFooter className="mt-2 gap-2">
        <PrimaryButton
          size="medium"
          text={edit ? 'Update' : 'Submit'}
          disabled={edit ? !hasChanges : !addedFile || addedFile.length === 0}
          isLoading={isLoading}
          onClick={handleSubmit}
        />
      </DialogFooter>
    </DialogContent>
  );
};
