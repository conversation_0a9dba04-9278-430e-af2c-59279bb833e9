import axios from 'axios';
import { <PERSON><PERSON>he<PERSON> } from 'lucide-react';
import moment from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'react-toastify';

import CheckIcon from '@/assets/outline/check';
import InfoCircle from '@/assets/outline/infoCircle';
import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import {
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import ToggleSwitch from '@/components/common/toogleSwitch';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import { IDocumentDetails } from '@/interfaces/document';
import { cn } from '@/utils/styleUtils';

import TertiaryButton from '../../common/button/tertiaryButton';
import FileCard from '../../common/modals/uploadModal/fileCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../common/select';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';

interface IExtendedDocumentDetails extends IDocumentDetails {
  path?: string;
  file_path?: string;
}
const acceptFileTypes = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
    '.doc',
  ],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
  'application/vnd.ms-excel': ['.xls', '.csv'],
};
interface IProps {
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  showModal: boolean;
  onSubmit?: (files: File[]) => void;
  edit?: boolean;
  vendorId: string;
  evaluationId?: string;
  onSuccess?: () => void;
  // Evaluation data for edit mode
  evaluationData?: {
    id?: string;
    document_type?: string;
    file_path?: string;
    file_name?: string;
    document_id?: string;
    evaluation_result?: string;
    document_path?: string;
    evaluation_date?: string;
  };
  evaluationListLength: number;
  reviewPeriod?: string;
}

export const EvaluationModal = ({
  setShowModal,
  showModal,
  onSubmit,
  edit = false,
  vendorId,
  evaluationId,
  onSuccess,
  evaluationData,
  evaluationListLength,
  reviewPeriod,
}: IProps) => {
  const { accessToken } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [uploadingFile, setUploadingFile] = useState(false);
  const [addedFile, setAddedFile] = React.useState<File[] | null>(null);
  const [evaluationResult, setEvaluationResult] = useState<string>(
    evaluationData?.evaluation_result || 'pass',
  );
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>(
    evaluationData?.document_id || '',
  );
  const [documentType, setDocumentType] = useState<string>(
    evaluationData?.document_type || 'independent',
  );
  const [fileName, setFileName] = useState<string>(
    evaluationData?.file_name || '',
  );
  const [filePath, setFilePath] = useState<string>(
    evaluationData?.file_path || '',
  );

  const [publishDate, setPublishDate] = useState<string | undefined>(
    evaluationData?.evaluation_date,
  );

  // Track if there are changes in edit mode
  const [hasChanges, setHasChanges] = useState(false);

  // Store original values for comparison
  const [originalValues, setOriginalValues] = useState({
    evaluationResult: evaluationData?.evaluation_result || 'pass',
    documentType: evaluationData?.document_type || 'independent',
    documentId: evaluationData?.document_id || '',
    filePath: evaluationData?.file_path || '',
    fileName: evaluationData?.file_name || '',
    nextEvaluationDate: evaluationData?.evaluation_date || '',
  });
  useEffect(() => {
    if (edit && evaluationData) {
      // Initialize form values
      setDocumentType(evaluationData.document_type || 'independent');
      setSelectedDocumentId(evaluationData.document_id || '');
      setFileName(evaluationData.file_name || '');
      setFilePath(evaluationData.file_path || '');
      setEvaluationResult(evaluationData.evaluation_result || 'pass');
      setPublishDate(
        evaluationData.evaluation_date || moment().format('YYYY-MM-DD'),
      );

      // Store original values for comparison
      setOriginalValues({
        evaluationResult: evaluationData.evaluation_result || 'pass',
        documentType: evaluationData.document_type || 'independent',
        documentId: evaluationData.document_id || '',
        filePath: evaluationData.file_path || '',
        fileName: evaluationData.file_name || '',
        nextEvaluationDate: evaluationData.evaluation_date || '',
      });

      // Reset changes flag when modal opens with data
      setHasChanges(false);
    }
  }, [edit, evaluationData]);

  useEffect(() => {
    if (edit && evaluationData) {
      const resultChanged =
        evaluationResult !== originalValues.evaluationResult;
      const typeChanged = documentType !== originalValues.documentType;
      const docIdChanged = selectedDocumentId !== originalValues.documentId;
      const fileChanged = addedFile && addedFile.length > 0;

      const nextEvaluationDateChanged =
        publishDate !== originalValues.nextEvaluationDate;

      // Set hasChanges based on any field being changed
      setHasChanges(
        Boolean(
          resultChanged ||
            typeChanged ||
            docIdChanged ||
            fileChanged ||
            nextEvaluationDateChanged,
        ),
      );
    }
  }, [
    edit,
    evaluationData,
    evaluationResult,
    documentType,
    selectedDocumentId,
    addedFile,
    publishDate,
    originalValues,
  ]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setAddedFile(acceptedFiles);
    setSelectedDocumentId('');
    setDocumentType('independent');
  }, []);
  const { data: publishedDocuments } = useFetch<
    { records: IExtendedDocumentDetails[] },
    { status: string }
  >(accessToken, 'documents', {
    status: 'Published',
  });

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: false,
    accept: acceptFileTypes,
  });

  const handleFileUpload = async (
    file: File,
  ): Promise<{ file_path: string; file_extension: string } | null> => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      const url = `${baseUrl}/${productVersion}/file/upload?document_for=vendor_hub&sub_path=/${vendorId}/evaluations`;

      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };

      const response = await axios.post(url, formData, config);

      if (response.status === 200) {
        return {
          file_path: response.data.file_path,
          file_extension: response.data.file_ext,
        };
      } else {
        console.error('Error uploading file:', file.name);
        return null;
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error(`Failed to upload file: ${file.name}`);
      return null;
    }
  };

  const { postData, isLoading: isPosting, response: postResponse } = usePost();
  const { putData, isLoading: isPutting, response: putResponse } = usePut();

  useEffect(() => {
    setIsLoading(isPosting || isPutting || uploadingFile);
  }, [isPosting, isPutting, uploadingFile]);

  useEffect(() => {
    if (postResponse || putResponse) {
      if (onSuccess) {
        onSuccess();
      }
      setShowModal(false);
      toast.success(
        edit
          ? 'Evaluation updated successfully'
          : 'Evaluation added successfully',
      );
    }
  }, [postResponse, putResponse, onSuccess, setShowModal, edit]);

  const handleSubmit = async () => {
    try {
      // Check if publish date is provided
      if (!publishDate) {
        toast.error('Evaluation date is required');
        return;
      }

      if (edit && (evaluationId || evaluationData?.id)) {
        // EDIT MODE
        const payload: Record<string, string | undefined> = {};

        if (evaluationResult !== originalValues.evaluationResult) {
          payload.evaluation_result = evaluationResult;
        } else {
          payload.evaluation_result = evaluationResult;
        }

        if (documentType !== originalValues.documentType) {
          payload.document_type = documentType;
        } else {
          payload.document_type = documentType;
        }

        if (documentType === 'doc_hub') {
          if (
            selectedDocumentId !== originalValues.documentId ||
            documentType !== originalValues.documentType
          ) {
            if (!selectedDocumentId) {
              toast.error('Please select a document from the document hub');
              return;
            }
            payload.document_id = selectedDocumentId;
            payload.file_name = publishedDocuments?.records.find(
              (doc) => doc.id === selectedDocumentId,
            )?.title;

            if (originalValues.documentType === 'independent') {
              payload.file_path = undefined;
            }
          }
        } else {
          if (addedFile && addedFile.length > 0) {
            setUploadingFile(true);
            try {
              const uploadResult = await handleFileUpload(addedFile[0]);
              if (!uploadResult) {
                toast.error('File upload failed');
                return;
              }
              payload.file_path = uploadResult.file_path;
              payload.file_name = addedFile[0].name;

              if (originalValues.documentType === 'doc_hub') {
                payload.document_id = undefined;
              }
            } finally {
              setUploadingFile(false);
            }
          } else if (documentType !== originalValues.documentType) {
            toast.error('Please select a file');
            return;
          } else if (originalValues.documentType === 'independent') {
            payload.file_path = originalValues.filePath;
            payload.file_name = originalValues.fileName;
          }
        }

        // Add evaluation_date to payload if toggle is on

        payload.evaluation_date = publishDate;

        if (
          Object.keys(payload).length <= 2 &&
          payload.evaluation_result === originalValues.evaluationResult &&
          payload.document_type === originalValues.documentType
        ) {
          toast.info('No changes to update');
          return;
        }

        const path = `vendors/${vendorId}/evaluations/${
          evaluationId || evaluationData?.id
        }`;
        await putData(accessToken as string, path, payload);
      } else {
        // CREATE MODE
        let uploadedFilePath = '';
        let uploadedFileName = '';

        if (documentType === 'independent') {
          if (!addedFile || addedFile.length === 0) {
            toast.error('Please select a file');
            return;
          }

          setUploadingFile(true);
          try {
            const uploadResult = await handleFileUpload(addedFile[0]);
            if (!uploadResult) {
              toast.error('File upload failed');
              return;
            }
            uploadedFilePath = uploadResult.file_path;
            uploadedFileName = addedFile[0].name;
          } finally {
            setUploadingFile(false);
          }
        } else if (documentType === 'doc_hub') {
          if (!selectedDocumentId) {
            toast.error('Please select a document from the document hub');
            return;
          }
        }

        const payload: Record<string, string | undefined> = {
          document_type: documentType,
          evaluation_result: evaluationResult,
        };

        if (documentType === 'doc_hub') {
          payload.document_id = selectedDocumentId;
          payload.file_name = publishedDocuments?.records.find(
            (doc) => doc.id === selectedDocumentId,
          )?.title;
        } else {
          payload.file_path = uploadedFilePath;
          payload.file_name = uploadedFileName;
        }

        // Add evaluation_date to payload if toggle is on

        payload.evaluation_date = publishDate;

        const path = `vendors/${vendorId}/evaluations`;
        await postData(accessToken as string, path, payload);
      }
    } catch (error) {
      console.error('Error processing evaluation:', error);
      toast.error('Failed to process evaluation');
    }
  };

  useEffect(() => {
    if (!showModal) {
      // Reset all form values when modal closes
      setAddedFile(null);
      setSelectedDocumentId('');
      setDocumentType('independent');
      setFileName('');
      setFilePath('');
      setEvaluationResult('pass');
      setPublishDate(undefined);
      setHasChanges(false);
    }
  }, [showModal]);

  console.log(evaluationListLength);
  return (
    <DialogContent
      className="min-w-[52rem] max-h-[95vh] overflow-auto p-6"
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      <DialogHeader>
        <DialogTitle>
          {edit ? 'Edit evaluation report' : 'Add evaluation report'}{' '}
        </DialogTitle>
      </DialogHeader>
      <div className="mt-2 overflow-hidden w-full">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Link Document from document hub
          <span className="text-[#F55D5D]">*</span>
        </div>
        <Select
          value={selectedDocumentId}
          onValueChange={(value) => {
            setSelectedDocumentId(value);
            setDocumentType('doc_hub');
            // Clear any uploaded file when selecting a document
            setAddedFile(null);
          }}
        >
          <SelectTrigger id="origin">
            <SelectValue placeholder="Select documents" />
          </SelectTrigger>
          <SelectContent>
            {publishedDocuments?.records.map((e, i) => (
              <SelectItem value={e.id} key={i}>
                {e.title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="w-full h-0 border-b border-white-300 relative my-4">
        <div className="absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 bg-white-100 px-4 text-sm font-medium text-grey-300">
          Or
        </div>
      </div>

      <div className="">
        <div className="text-base leading-6 font-medium text-dark-100 mb-2.5">
          Attach document
        </div>

        <div>
          <div
            className=" min-h-28 bg-white-100 border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2"
            {...getRootProps()}
          >
            {!(addedFile?.length && addedFile?.length > 0) ||
              (evaluationData?.document_path?.length === 0 && (
                <div className="text-sm font-medium leading-5 text-[#49474E]">
                  Upload or Drag and drop to upload your file
                </div>
              ))}

            <input {...getInputProps()} />
            <div className="flex justify-center items-center flex-wrap gap-2">
              {addedFile?.map((file, index) => (
                <FileCard key={index} file={file} setAddedFile={setAddedFile} />
              ))}
              {!addedFile && evaluationData?.document_path && (
                <FileCard
                  key={evaluationData.document_path}
                  prefillFileName={evaluationData.file_name}
                />
              )}
            </div>
            <TertiaryButton
              text={
                (addedFile?.length && addedFile?.length > 0) ||
                (evaluationData?.document_path?.length &&
                  evaluationData?.document_path?.length > 0)
                  ? 'Replace'
                  : 'Select file'
              }
              size="small"
            />
          </div>
        </div>
      </div>

      <div className="flex gap-4 items-center mt-2">
        <div className=" overflow-hidden flex flex-col flex-1  items-start">
          <div className="text-base font-medium leading-6 text-dark-100 mb-2.5 text-nowrap ">
            Evaluation result
          </div>
          <Select
            value={evaluationResult}
            onValueChange={(value) => {
              setEvaluationResult(value);
            }}
          >
            <SelectTrigger id="evaluation-result">
              <SelectValue placeholder="Select result" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={'pass'}>Pass</SelectItem>
              <SelectItem value={'fail'}>Fail</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex flex-1  flex-col items-start text-dark-300 leading-6 font-medium">
          <p className="text-dark-100 font-medium text-base leading-6 whitespace-nowrap mb-2.5">
            Evaluation date
            <span className="text-[#F55D5D]">*</span>
          </p>
          <Calendar
            className="w-[9rem] bg-slate-50"
            selectedDate={publishDate as string}
            onDateChange={(date) => {
              if (date) {
                setPublishDate(moment(date as string).format('YYYY-MM-DD'));
              } else {
                setPublishDate(undefined);
              }
            }}
            disableFutureDate
            allowPastDates
          />
        </div>
      </div>

      {publishDate && (
        <div className="">
          <div className="flex-1 bg-green-50 rounded-md">
            <div className="flex gap-2 px-3 py-2 items-center text-base text-dark-300 leading-6 font-medium">
              <div
                className={cn(
                  'h-9 w-9 flex items-center justify-center bg-[#37FF6329] rounded-full',
                )}
              >
                <CircleCheck height="16" width="16" color="#309665" />
              </div>

              <p className="text-sm font-medium text-[#309665]">
                Next review scheduled for:{' '}
                {moment(publishDate)
                  .add(Number(reviewPeriod) || 0, 'months')
                  .format('MMM D YYYY')}
              </p>
            </div>
          </div>
        </div>
      )}

      <DialogFooter className="mt-2 gap-2">
        <PrimaryButton
          size="medium"
          text={edit ? 'Update' : 'Submit'}
          disabled={
            edit
              ? !hasChanges || !publishDate // In edit mode, require publishDate and changes
              : (documentType === 'independent' &&
                  (!addedFile || addedFile.length === 0)) ||
                (documentType === 'doc_hub' && !selectedDocumentId) ||
                !publishDate // Also require publishDate in create mode
          }
          isLoading={isLoading}
          onClick={handleSubmit}
        />
      </DialogFooter>
    </DialogContent>
  );
};
