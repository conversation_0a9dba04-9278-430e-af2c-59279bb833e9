import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import { IOption } from '@/components/common/creatableSelect';
import { Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
    Select, SelectContent, SelectItem, SelectTrigger, SelectValue
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
// import { validateForm } from "@/hooks/useValidator";
import { IUser } from '@/interfaces/user';
import { ReviewPeriod, TVendorD<PERSON>, VendorStatus } from '@/interfaces/vendor';
import { nullEmptyFields, removeEmptyFields } from '@/utils/removeEmptyFields';
import { getMonthsFromPeriod, getPeriodFromMonth } from '@/utils/time';

const createVendorSchema = {
  name: z.string().nonempty('Vendor Name is required'),
  service: z.string().nonempty('Services is required'),
  contact_date: z.string().optional(),
  contact_name: z.string().optional(),
  contact_email: z.union([z.string().email(), z.literal('')]).optional(),
  contact_phone: z.string().optional(),
  address: z.string().optional(),
  assignee: z.string().optional(),
  status: z.string().optional(),
  review_period: z.string().optional(),
  description: z.string().optional(),
};

interface IData extends Record<string, unknown> {
  name?: string;
  service?: string;
  contact_date: string;
  contact_name: string;
  contact_email: string;
  contact_phone: string | number;
  address: string;
  assignee: string; // UUID
  status: VendorStatus;
  review_period: ReviewPeriod;
  description: string;
}

const CreateVendorModal = ({
  edit,
  vendorData,
  setOpenEdit,
  reFetch,
}: {
  edit?: boolean;
  vendorData?: TVendorData;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  reFetch?: () => void;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { data: users } = useFetch<
    { records: IUser[] },
    { asset_admin: boolean }
  >(accessToken, `users`, {
    asset_admin: true,
  });
  const {
    putData,
    isLoading: isPutLoading,
    response: putResponse,
    error: putError,
  } = usePut();
  const {
    postData,
    isLoading: isPostLoading,
    response: postResponse,
    error: postError,
  } = usePost();

  const [data, setData] = useState<IData>({
    name: '',
    service: '',
    contact_date: '',
    contact_name: '',
    contact_email: '',
    contact_phone: '',
    address: '',
    assignee: '',
    status: VendorStatus.UnderEvaluation,
    review_period: ReviewPeriod.Quarterly,
    description: '',
  });
  const [error, setError] = useState<Record<string, string> | undefined>();
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const { validationErrors, startValidation } = useValidators({
    schemas: createVendorSchema,
    values: data,
  });

  const userData = users?.records?.map((e) => ({
    label: e.full_name,
    value: e.id,
  })) as IOption[];

  // Monitor API responses
  useEffect(() => {
    if (putResponse || postResponse) {
      setIsSubmitting(false);
      if (setOpenEdit) setOpenEdit(false);
      if (reFetch) reFetch();
    }
  }, [putResponse, postResponse, setOpenEdit, reFetch]);

  // Monitor API errors
  useEffect(() => {
    if (putError || postError) {
      setIsSubmitting(false);
      const errorMessage =
        putError?.message || postError?.message || 'Failed to save vendor data';
      setError({ general: errorMessage });
      console.error('API Error:', putError || postError);
    }
  }, [putError, postError]);

  const handleSubmit = async () => {
    setError(undefined);
    const { hasValidationErrors } = await startValidation();

    if (!hasValidationErrors) {
      setIsSubmitting(true);
      const payload: Record<string, unknown> = { ...data };

      payload.review_period = getMonthsFromPeriod(data.review_period);
      // Remove any empty fields before sending
      if (edit && vendorData?.id) {
        putData(
          accessToken as string,
          `vendors/${vendorData.id}`,
          nullEmptyFields({ ...payload, id: vendorData.id }),
        );
      } else if (accessToken) {
        postData(accessToken, 'vendors', removeEmptyFields(payload));
      } else {
        setIsSubmitting(false);
        setError({ general: 'Authentication token is missing' });
      }
    }
  };

  useEffect(() => {
    if (edit && vendorData) {
      setData({
        name: vendorData?.name,
        service: vendorData?.service,
        contact_date: vendorData?.contact_date,
        contact_name: vendorData?.contact_name,
        contact_email: vendorData?.contact_email,
        contact_phone: vendorData?.contact_phone
          ? String(vendorData?.contact_phone)
          : vendorData?.contact_phone,
        address: vendorData?.address,
        assignee: vendorData?.assignee,
        status: vendorData?.status,
        review_period:
          typeof vendorData?.review_period === 'number'
            ? (getPeriodFromMonth(vendorData?.review_period) as ReviewPeriod)
            : (vendorData?.review_period as ReviewPeriod),
        description: vendorData?.description,
      });
    }
  }, [vendorData, edit]);

  return (
    <DialogContent className="min-w-[65vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle>{edit ? 'Edit' : 'Create'} vendor details</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="name"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Vendor Name<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Enter vendor name"
              id="name"
              type="text"
              name="name"
              value={data?.name}
              onChange={(e) =>
                setData((pre) => ({ ...pre, name: e.target.value }))
              }
              errorMsg={validationErrors?.name?.[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="service"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Product / Services<span className="text-red-200">*</span>
            </Label>
            <Input
              type="text"
              name="service"
              id="service"
              value={data?.service}
              placeholder="Enter product or services"
              onChange={(e) =>
                setData((pre) => ({ ...pre, service: e.target.value }))
              }
              errorMsg={validationErrors?.service?.[0]}
            />
          </div>
          <div className="flex flex-col flex-1">
            <Label
              htmlFor="contact_date"
              className="text-base font-medium leading-6 text-dark-100 mb-2.5"
            >
              Date of contact
            </Label>

            <Calendar
              selectedDate={data?.contact_date || ''}
              onDateChange={(date) => {
                if (date) {
                  setData((prev) => ({
                    ...prev,
                    contact_date: moment(date as string).format('YYYY-MM-DD'),
                  }));
                } else {
                  setData((prev) => ({
                    ...prev,
                    contact_date: '',
                  }));
                }
              }}
              allowPastDates
              disableFutureDate
              className={
                validationErrors?.contact_date?.[0]
                  ? 'border !border-red-200'
                  : ''
              }
            />
            {validationErrors?.contact_date?.[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.contact_date?.[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="contact_name"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Contact Name
            </Label>
            <Input
              placeholder="Enter contact name"
              id="contact_name"
              type="text"
              name="contact_name"
              value={data?.contact_name}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, contact_name: e.target.value }))
              }
              errorMsg={validationErrors?.contact_name?.[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="contact_email"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Contact Email
            </Label>
            <Input
              placeholder="Enter contact email"
              id="contact_email"
              type="email"
              name="contact_email"
              value={data?.contact_email}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, contact_email: e.target.value }))
              }
              errorMsg={validationErrors?.contact_email?.[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="contact_phone"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Contact Phone
            </Label>
            <Input
              placeholder="Enter contact phone"
              id="contact_phone"
              type="number"
              name="contact_phone"
              value={data?.contact_phone}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, contact_phone: e.target.value }))
              }
              errorMsg={validationErrors?.contact_phone?.[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="address"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Address
            </Label>
            <Input
              placeholder="Enter address"
              id="address"
              type="text"
              name="address"
              value={data?.address}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, address: e.target.value }))
              }
              errorMsg={validationErrors?.address?.[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="description"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Description
            </Label>
            <Input
              placeholder="Enter description"
              id="description"
              type="text"
              name="description"
              value={data?.description}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, description: e.target.value }))
              }
              errorMsg={validationErrors?.description?.[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="assignee"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Assignee
            </Label>
            <Select
              value={data.assignee}
              onValueChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  assignee: value,
                }));
              }}
            >
              <SelectTrigger
                className={
                  validationErrors?.assignee?.[0] ? 'border-red-200' : ''
                }
                id="assignee"
              >
                <SelectValue placeholder="Select assignee" />
              </SelectTrigger>
              <SelectContent>
                {userData?.map((e, i) => (
                  <SelectItem value={e.value} key={i}>
                    {e.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {validationErrors?.assignee?.[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.assignee?.[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="status"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Status
            </Label>
            <Select
              value={data.status}
              onValueChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  status: value as VendorStatus,
                }));
              }}
            >
              <SelectTrigger
                className={
                  validationErrors?.status?.[0] ? 'border-red-200' : ''
                }
                id="status"
              >
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={VendorStatus.Approved}>
                  {VendorStatus.Approved}
                </SelectItem>
                <SelectItem value={VendorStatus.Rejected}>
                  {VendorStatus.Rejected}
                </SelectItem>
                <SelectItem value={VendorStatus.UnderEvaluation}>
                  {VendorStatus.UnderEvaluation}
                </SelectItem>
              </SelectContent>
            </Select>
            {validationErrors?.status?.[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.status?.[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="review_period"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Review Period
            </Label>
            <Select
              value={data.review_period}
              onValueChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  review_period: value as ReviewPeriod,
                }));
              }}
            >
              <SelectTrigger
                className={
                  validationErrors?.review_period?.[0] ? 'border-red-200' : ''
                }
                id="review_period"
              >
                <SelectValue placeholder="Select review period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={ReviewPeriod.Monthly}>
                  {ReviewPeriod.Monthly}
                </SelectItem>
                <SelectItem value={ReviewPeriod.Quarterly}>
                  {ReviewPeriod.Quarterly}
                </SelectItem>
                <SelectItem value={ReviewPeriod.HalfYearly}>
                  {ReviewPeriod.HalfYearly}
                </SelectItem>
                <SelectItem value={ReviewPeriod.Yearly}>
                  {ReviewPeriod.Yearly}
                </SelectItem>
              </SelectContent>
            </Select>
            {validationErrors?.review_period?.[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.review_period?.[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>

        {error?.general && (
          <div className="text-red-200 text-sm font-medium mb-4">
            {error.general}
          </div>
        )}
        <div className="flex justify-end mt-6">
          <PrimaryButton
            size="medium"
            text={isSubmitting ? 'Saving...' : 'Submit'}
            onClick={handleSubmit}
            disabled={isSubmitting || isPutLoading || isPostLoading}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default CreateVendorModal;
