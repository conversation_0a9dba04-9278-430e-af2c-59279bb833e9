import axios from 'axios';
import { ArrowLeft, Download, Plus } from 'lucide-react';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import DeleteIcon from '@/assets/outline/delete';
import EditIcon from '@/assets/outline/edit';
// Using Lucide Plus icon instead of PlusIcon
import Delete<PERSON>utton from '@/components/common/button/deleteButton';
import LinkButton from '@/components/common/button/linkButton';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/common/dialog';
import { DetailsTextNew } from '@/components/common/infoDetail';
import Loader from '@/components/common/loader';
import DeleteModal from '@/components/common/modals/deleteModal';
import DocumentViewModal from '@/components/common/modals/documentViewModal';
import SidebarWrapper from '@/components/common/sidebar/layout';
import CommonTable from '@/components/common/table';
import CreateVendorModal from '@/components/vendor/modal/createVendorModal';
import { EvaluationModal } from '@/components/vendor/modal/evaluationModal';
import { VendorDocumentModal } from '@/components/vendor/modal/vendorDocumentModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { TVendorData } from '@/interfaces/vendor';
import { getFileNameFromPath } from '@/utils/helper';
import { hasAccess } from '@/utils/roleAccessConfig';
import { getValueOrDefault } from '@/utils/table';
import { formatDate, getPeriodFromMonth } from '@/utils/time';

import { Tooltip, TooltipContent, TooltipTrigger } from '../common/tooltip';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';

const VendorDetailPage = () => {
  const [openEditModal, setOpenEditModal] = useState<boolean>(false);
  const [selectedDocument, setSelectedDocument] = useState<Record<
    string,
    unknown
  > | null>(null);
  const [selectedEvaluation, setSelectedEvaluation] = useState<Record<
    string,
    unknown
  > | null>(null);
  const [openDocumentModal, setOpenDocumentModal] = useState<boolean>(false);
  const [openEvaluationModal, setOpenEvaluationModal] =
    useState<boolean>(false);
  const [editMode, setEditMode] = useState<boolean>(false);
  const [deleteConfirmationModal, setDeleteConfirmationModal] =
    useState<boolean>(false);
  const [itemToDelete, setItemToDelete] = useState<{
    id: string;
    type: 'document' | 'evaluation';
  } | null>(null);
  // State for edit mode and modals

  // const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false);
  const { user, accessToken } = useAuthStore();

  const router = useRouter();
  const { vendorId } = router.query;

  const {
    data,
    isLoading: loadingData,
    reFetch: refetchVendorData,
  } = useFetch<TVendorData>(
    accessToken,
    vendorId ? `vendors/${vendorId}` : undefined,
  );

  const {
    deleteData,
    isLoading: deleteLoading,
    response: deleteResponse,
  } = useDelete();

  const {
    deleteData: deleteDocumentData,
    isLoading: deleteDocumentLoading,
    response: deleteDocumentResponse,
  } = useDelete();

  const {
    deleteData: deleteEvaluationData,
    isLoading: deleteEvaluationLoading,
    response: deleteEvaluationResponse,
  } = useDelete();

  useEffect(() => {
    if (deleteResponse) {
      router.push('/vendor');
    }
  }, [deleteResponse, router]);

  // Handle document deletion response
  useEffect(() => {
    if (deleteDocumentResponse) {
      toast.success('Document deleted successfully');
      refetchVendorData();
      setDeleteConfirmationModal(false);
      setItemToDelete(null);
    }
  }, [deleteDocumentResponse]);

  // Handle evaluation deletion response
  useEffect(() => {
    if (deleteEvaluationResponse) {
      toast.success('Evaluation deleted successfully');
      refetchVendorData();
      setDeleteConfirmationModal(false);
      setItemToDelete(null);
    }
  }, [deleteEvaluationResponse]);

  const handleDelete = () => {
    if (accessToken && vendorId) {
      deleteData(accessToken, `vendors/${vendorId}`);
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirmation = useCallback(() => {
    if (!accessToken || !vendorId || !itemToDelete) return;

    if (itemToDelete.type === 'document') {
      // Delete document
      deleteDocumentData(
        accessToken,
        `vendors/${vendorId}/documents/${itemToDelete.id}`,
      );
    } else {
      // Delete evaluation
      deleteEvaluationData(
        accessToken,
        `vendors/${vendorId}/evaluations/${itemToDelete.id}`,
      );
    }
  }, [
    accessToken,
    vendorId,
    itemToDelete,
    deleteDocumentData,
    deleteEvaluationData,
  ]);

  const handleDownloadDoc = (path: string, documentTitle: string) => {
    const baseUrl = process.env.NEXT_PUBLIC_URL;
    const productVersion = process.env.NEXT_PUBLIC_VERSION;
    const orgId =
      typeof window !== 'undefined'
        ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
        : null;
    axios
      .get(
        `${baseUrl}/${productVersion}/file/presigned-url?file_path=${path}&expiration=600`,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'application/json',
            ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
          },
        },
      )
      .then((res) => {
        fetch(res.data.url)
          .then((res) => res.blob())
          .then((res) => {
            const link = document.createElement('a');
            link.href = window.URL.createObjectURL(res);
            link.download = getFileNameFromPath(path, documentTitle, 0);
            link.click();
          });
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const handleEditDocument = useCallback((rowData: Record<string, unknown>) => {
    setSelectedDocument(rowData);
    setEditMode(true);
    setOpenDocumentModal(true);
  }, []);

  const handleEditEvaluation = useCallback(
    (rowData: Record<string, unknown>) => {
      setSelectedEvaluation(rowData);
      setEditMode(true);
      setOpenEvaluationModal(true);
    },
    [],
  );
  const handleDeleteDocument = useCallback(
    (rowData: Record<string, unknown>) => {
      // Set the document to delete and show confirmation modal
      setItemToDelete({
        id: rowData.id as string,
        type: 'document',
      });
      setDeleteConfirmationModal(true);
    },
    [],
  );

  const handleDeleteEvaluation = useCallback(
    (rowData: Record<string, unknown>) => {
      // Set the evaluation to delete and show confirmation modal
      setItemToDelete({
        id: rowData.id as string,
        type: 'evaluation',
      });
      setDeleteConfirmationModal(true);
    },
    [],
  );

  const isViewableDoc = (data: any) => {
    if (data.document_type === 'independent') {
      return ['pdf', 'docx', 'doc', 'jpg', 'png', 'jpeg'].includes(
        (data?.file_name as string)?.split('.').pop() || '',
      );
    }
    if (data.document_type === 'doc_hub') {
      return ['pdf', 'docx', 'doc', 'jpg', 'png', 'jpeg'].includes(
        (data?.document_version_data?.file_extension as string) || '',
      );
    }

    return false;
  };

  const getFilePath = (data: any) => {
    if (data.document_type === 'independent') {
      return data.document_path;
    }
    if (data.document_type === 'doc_hub') {
      return data.document_version_data.file_path;
    }
    return '';
  };

  const getEvaluationHistoryColumns = useCallback(() => {
    // Using any type for column definitions to match CommonTable requirements
    const evaluationHistoryColumns: any = [
      {
        headerName: 'File name',
        field: 'file_name',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: Record<string, unknown>) => {
          return params.value;
        },
        valueFormatter: (params: Record<string, unknown>) =>
          getValueOrDefault(params.data, 'file_name'),
        filter: false,
        cellRenderer: (params: {
          data: { file_name: string; [key: string]: any };
        }) => {
          return isViewableDoc(params.data) ? (
            <Dialog>
              <DialogTrigger asChild>
                <div className="text-primary-400 font-medium cursor-pointer">
                  <span>{params.data.file_name}</span>
                </div>
              </DialogTrigger>
              <DocumentViewModal
                title={params.data.file_name}
                filePath={getFilePath(params.data)}
                extension={
                  getFilePath(params.data)?.split('.').pop() as
                    | 'html'
                    | 'pdf'
                    | 'png'
                    | 'jpeg'
                    | 'jpg'
                }
                dialogClass="min-w-[95%]"
              />
            </Dialog>
          ) : (
            <span>{params.data.file_name}</span>
          );
        },
      },
      // {
      //   headerName: 'Document type',
      //   field: 'document_type',
      //   sortable: true,
      //   resizable: true,
      //   valueFormatter: (params: Record<string, unknown>) =>
      //     getValueOrDefault(params.data, 'document_type') === 'doc_hub'
      //       ? 'Document'
      //       : 'Independent',
      //   filter: false,
      // },
      {
        headerName: 'Evaluation date',
        field: 'evaluation_result',
        sortable: true,
        resizable: true,
        valueFormatter: (params: Record<string, unknown>) =>
          getValueOrDefault(params.data, 'evaluation_result') === 'pass'
            ? 'Pass'
            : 'Fail',
        filter: false,
      },
      {
        headerName: 'Evaluation date',
        field: 'evaluation_date',
        sortable: true,
        resizable: true,
        valueFormatter: (params: Record<string, unknown>) =>
          formatDate(getValueOrDefault(params.data, 'evaluation_date'), false),
        filter: false,
      },
      {
        headerName: 'Upload date',
        field: 'created_on',
        sortable: true,
        resizable: true,
        valueFormatter: (params: Record<string, unknown>) =>
          formatDate(getValueOrDefault(params.data, 'created_on'), false),
        filter: false,
      },
    ];
    if (
      (hasAccess(AccessActions.VendorEditor, user) &&
        data?.assignee === user?.id) ||
      hasAccess(AccessActions.VendorAdmin, user)
    ) {
      evaluationHistoryColumns.push({
        headerName: 'Manage',
        field: 'manage',
        sortable: false,
        resizable: true,
        cellRenderer: (params: any) => {
          const rowIndex = params.node.rowIndex;
          const totalRows = params.api.getDisplayedRowCount();

          return (
            <VendorManageCellRenderer
              rowData={params.data as Record<string, unknown>}
              handleEdit={handleEditEvaluation}
              handleDelete={handleDeleteEvaluation}
              hideDelete={true}
              handleDownloadDoc={handleDownloadDoc}
              showEdit={rowIndex === 0}
            />
          );
        },
        pinned: 'right',
        filter: false,
      });
    }
    return evaluationHistoryColumns;
  }, [user, handleEditEvaluation, handleDeleteEvaluation]);

  const getComplianceDocumentColumns = useCallback(() => {
    // Using any type for column definitions to match CommonTable requirements
    const complianceDocumentColumns: any = [
      {
        headerName: 'File name',
        field: 'file_name',
        sortable: true,
        resizable: true,
        getQuickFilterText: (params: Record<string, unknown>) => {
          return params.value;
        },
        valueFormatter: (params: Record<string, unknown>) =>
          getValueOrDefault(params.data, 'file_name'),
        filter: false,
        cellRenderer: (params: {
          data: { file_name: string; [key: string]: any };
        }) => {
          return isViewableDoc(params.data) ? (
            <Dialog>
              <DialogTrigger asChild>
                <div className="text-primary-400 font-medium cursor-pointer">
                  <span>{params.data.file_name}</span>
                </div>
              </DialogTrigger>
              <DocumentViewModal
                title={params.data.file_name}
                filePath={getFilePath(params.data)}
                extension={
                  getFilePath(params.data)?.split('.').pop() as
                    | 'html'
                    | 'pdf'
                    | 'png'
                    | 'jpeg'
                    | 'jpg'
                }
                dialogClass="min-w-[95%]"
              />
            </Dialog>
          ) : (
            <span>{params.data.file_name}</span>
          );
        },
      },

      {
        headerName: 'Date added',
        field: 'created_on',
        sortable: true,
        resizable: true,
        valueFormatter: (params: Record<string, unknown>) =>
          formatDate(getValueOrDefault(params.data, 'created_on'), false),
        filter: false,
      },
      {
        headerName: 'Expiry Date',
        field: 'expiry_date',
        sortable: true,
        resizable: true,
        valueFormatter: (params: Record<string, unknown>) =>
          formatDate(getValueOrDefault(params.data, 'expiry_date'), false),
        filter: false,
      },
    ];
    if (
      (hasAccess(AccessActions.VendorEditor, user) &&
        data?.assignee === user?.id) ||
      hasAccess(AccessActions.VendorAdmin, user)
    ) {
      complianceDocumentColumns.push({
        headerName: 'Manage',
        field: 'manage',
        sortable: false,
        resizable: true,
        cellRenderer: (params: Record<string, unknown>) => {
          return (
            <VendorManageCellRenderer
              rowData={params.data as Record<string, unknown>}
              handleEdit={handleEditDocument}
              handleDelete={handleDeleteDocument}
              hideDelete={!hasAccess(AccessActions.VendorAdmin, user)}
              handleDownloadDoc={handleDownloadDoc}
            />
          );
        },

        pinned: 'right',
        filter: false,
      });
    }
    return complianceDocumentColumns;
  }, [user, handleEditDocument, handleDeleteDocument]);

  return (
    <SidebarWrapper>
      {' '}
      <div className="flex flex-col flex-1">
        <div className=" my-5">
          <div className="flex items-center gap-4">
            <SecondaryButton
              icon={<ArrowLeft />}
              onClick={() => router.push('/vendor')}
              text=""
              size="medium"
              buttonClasses="!px-2.5"
            />
            <div>
              <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5">
                {data?.name}
              </div>
            </div>
          </div>

          <div className="mt-6 flex items-center justify-between"></div>
          {!loadingData ? (
            <div className=" flex-1">
              <>
                <div className="border border-grey-100 bg-white p-2 rounded-lg">
                  <div className="flex items-start justify-between ">
                    <div className="p-2 flex flex-col gap-3">
                      <DetailsTextNew
                        label="Date of contact"
                        value={
                          data?.contact_date
                            ? formatDate(data?.contact_date)
                            : '--'
                        }
                      />
                      <DetailsTextNew
                        label="Product/Service"
                        value={data?.service || '--'}
                      />
                      <DetailsTextNew
                        label="Contact name"
                        value={data?.contact_name || '--'}
                      />
                      <DetailsTextNew
                        label="Assignee"
                        value={data?.assignee_details?.full_name || '--'}
                      />
                    </div>
                    <div className="p-2 flex flex-col gap-3">
                      <DetailsTextNew
                        label="Contact email"
                        value={data?.contact_email || '--'}
                      />
                      <DetailsTextNew
                        label="Contact phone"
                        value={data?.contact_phone?.toString() || '--'}
                      />
                      <DetailsTextNew
                        label="Address"
                        value={data?.address || '--'}
                      />
                    </div>
                    <div className="p-2 flex flex-col gap-3">
                      <DetailsTextNew
                        label="Review period"
                        value={
                          typeof data?.review_period === 'number'
                            ? getPeriodFromMonth(data?.review_period)
                            : data?.review_period?.toString() || '--'
                        }
                      />
                      <DetailsTextNew
                        label="Next evaluation date"
                        value={
                          data?.next_evaluation_date
                            ? formatDate(data?.next_evaluation_date)
                            : '--'
                        }
                      />
                      <DetailsTextNew
                        label="Status"
                        value={data?.status || '--'}
                      />
                    </div>

                    {hasAccess(AccessActions.VendorAdmin, user) && (
                      <div className="flex items-center gap-3">
                        <Dialog
                          open={openEditModal}
                          onOpenChange={setOpenEditModal}
                        >
                          <DialogTrigger asChild>
                            <SecondaryButton
                              size="medium"
                              icon={
                                <EditIcon color="#016366" className="h-5 w-5" />
                              }
                              text="Edit"
                            />
                          </DialogTrigger>
                          <CreateVendorModal
                            edit
                            vendorData={data || undefined}
                            setOpenEdit={setOpenEditModal}
                            reFetch={refetchVendorData}
                          />
                        </Dialog>

                        <Dialog>
                          <DialogTrigger asChild>
                            <DeleteButton />
                          </DialogTrigger>
                          <DeleteModal
                            title="Delete"
                            infoText="Are you sure?"
                            btnText="Delete"
                            onClick={handleDelete}
                            btnLoading={deleteLoading}
                            dialogContentClass="min-w-[28.5rem]"
                          >
                            <div className="p-2 border flex flex-col gap-4 border-white-300 bg-white-100 px-2.5 py-2 rounded-lg">
                              <div className="flex justify-between items-center">
                                <div className="text-sm font-medium leading-5 text-grey-300">
                                  Vendor Name
                                </div>
                                <div className="text-base font-medium leading-6 text-dark-300">
                                  {data?.name || '--'}
                                </div>
                              </div>
                              <div className="flex justify-between items-center">
                                <div className="text-sm font-medium leading-5 text-grey-300">
                                  Contact name
                                </div>
                                <div className="text-base font-medium leading-6 text-dark-300">
                                  {data?.contact_name || '--'}
                                </div>
                              </div>
                            </div>
                          </DeleteModal>
                        </Dialog>
                      </div>
                    )}
                    {!hasAccess(AccessActions.VendorAdmin, user) &&
                      hasAccess(AccessActions.VendorEditor, user) && (
                        <div className="flex items-center gap-3">
                          <Dialog
                            open={openEditModal}
                            onOpenChange={setOpenEditModal}
                          >
                            <DialogTrigger asChild>
                              <SecondaryButton
                                size="medium"
                                icon={
                                  <EditIcon
                                    color="#016366"
                                    className="h-5 w-5"
                                  />
                                }
                                text="Edit"
                              />
                            </DialogTrigger>
                            <CreateVendorModal
                              edit
                              vendorData={data || undefined}
                              setOpenEdit={setOpenEditModal}
                              reFetch={refetchVendorData}
                            />
                          </Dialog>
                        </div>
                      )}
                  </div>
                  <div className="pl-2 pb-2">
                    <DetailsTextNew
                      label="Description"
                      value={data?.description || '--'}
                    />
                  </div>
                </div>
                <div className=" mt-8 mb-5">
                  <div className="text-lg leading-7 font-medium text-dark-300 mb-0.5 ">
                    Evaluation
                  </div>
                  <div className="text-sm leading-5 font-medium text-grey-300 ">
                    Manage the compliance documents and evaluation reports for
                    this vendor
                  </div>
                  <div className="px-7 py-6 border border-white-300 rounded-lg mt-5 mb-6">
                    <div className="flex items-center justify-between mb-5 ">
                      <div className="text-base font-medium leading-6 text-dark-300 ">
                        Evaluation history:
                      </div>
                      <div className="flex items-center gap-3">
                        {(hasAccess(AccessActions.VendorEditor, user) &&
                          data?.assignee === user?.id) ||
                        hasAccess(AccessActions.VendorAdmin, user) ? (
                          <Dialog
                            open={openEvaluationModal}
                            onOpenChange={setOpenEvaluationModal}
                          >
                            <Tooltip>
                              <TooltipTrigger
                                asChild
                                onClick={(e) => {
                                  e.stopPropagation();
                                }}
                              >
                                <DialogTrigger asChild>
                                  <LinkButton
                                    size="medium"
                                    icon={<Plus className="h-5 w-5" />}
                                    text="Add evaluation report"
                                    disabled={data?.review_period === undefined}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (data?.review_period !== undefined) {
                                        setEditMode(false);
                                        setSelectedEvaluation(null);
                                      }
                                    }}
                                  />
                                </DialogTrigger>
                              </TooltipTrigger>
                              <TooltipContent
                                side="right"
                                sideOffset={4}
                                className="bg-white-100 rounded-lg p-1 shadow-shadow-2"
                              >
                                {data?.review_period ? (
                                  ''
                                ) : (
                                  <div>
                                    Please set the review period for this vendor
                                    to add evaluation reports.
                                  </div>
                                )}
                              </TooltipContent>
                            </Tooltip>
                            <EvaluationModal
                              setShowModal={setOpenEvaluationModal}
                              showModal={openEvaluationModal}
                              edit={editMode}
                              vendorId={vendorId as string}
                              evaluationId={
                                selectedEvaluation?.id as string | undefined
                              }
                              evaluationData={selectedEvaluation as any}
                              onSuccess={refetchVendorData}
                              evaluationListLength={
                                data?.evaluations?.length || 0
                              }
                              reviewPeriod={data?.review_period as string}
                            />
                          </Dialog>
                        ) : (
                          ''
                        )}
                      </div>
                    </div>
                    <div>
                      <div className="mb-5">
                        <CommonTable
                          data={{
                            records: data?.evaluations?.toReversed() || [],
                          }}
                          columnDefs={getEvaluationHistoryColumns()}
                          searchPlaceholder="Search by ID, name, description, location, owner or status"
                          isLoading={false}
                          searchBox={false}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="px-7 py-6 border border-white-300 rounded-lg mt-5 ">
                    <div className="flex items-center justify-between mb-5 ">
                      <div className="text-base font-medium leading-6 text-dark-300 ">
                        Compliance Documents:
                      </div>
                      <div className="flex items-center gap-3">
                        {(hasAccess(AccessActions.VendorEditor, user) &&
                          data?.assignee === user?.id) ||
                        hasAccess(AccessActions.VendorAdmin, user) ? (
                          <Dialog
                            open={openDocumentModal}
                            onOpenChange={setOpenDocumentModal}
                          >
                            <DialogTrigger asChild>
                              <LinkButton
                                size="medium"
                                icon={<Plus className="h-5 w-5" />}
                                text="Add Documents"
                                onClick={() => {
                                  setEditMode(false);
                                  setSelectedDocument(null);
                                }}
                              />
                            </DialogTrigger>
                            <VendorDocumentModal
                              setShowModal={setOpenDocumentModal}
                              showModal={openDocumentModal}
                              edit={editMode}
                              vendorId={vendorId as string}
                              documentData={selectedDocument as any}
                              onSuccess={refetchVendorData}
                            />
                          </Dialog>
                        ) : (
                          ''
                        )}
                      </div>
                    </div>
                    <div>
                      <div className="mb-5">
                        <CommonTable
                          data={{
                            records: data?.documents?.toReversed() || [],
                          }}
                          columnDefs={getComplianceDocumentColumns()}
                          searchPlaceholder="Search by ID, name, description, location, owner or status"
                          isLoading={false}
                          searchBox={false}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </>
            </div>
          ) : (
            <Loader className="h-[400px]" />
          )}
        </div>
      </div>
      {/* Delete Confirmation Modal */}
      <Dialog
        open={deleteConfirmationModal}
        onOpenChange={setDeleteConfirmationModal}
      >
        <DeleteModal
          title={
            itemToDelete?.type === 'document'
              ? 'Delete Compliance Document'
              : 'Delete Evaluation Report'
          }
          infoText={`Are you sure you want to delete this ${
            itemToDelete?.type === 'document'
              ? 'compliance document'
              : 'evaluation report'
          }?`}
          btnText={'Delete'}
          btnLoading={
            itemToDelete?.type === 'document'
              ? deleteDocumentLoading
              : deleteEvaluationLoading
          }
          onClick={handleDeleteConfirmation}
        />
      </Dialog>
    </SidebarWrapper>
  );
};

export default VendorDetailPage;

export const VendorManageCellRenderer = ({
  rowData,
  handleEdit,
  handleDelete,
  hideDelete,
  handleDownloadDoc,
  showEdit = true,
}: {
  rowData: Record<string, unknown>;
  handleEdit: (rowData: Record<string, unknown>) => void;
  handleDelete?: (rowData: Record<string, unknown>) => void;
  hideDelete?: boolean;
  handleDownloadDoc: (path: string, documentTitle: string) => void;
  showEdit?: boolean;
}) => {
  return (
    <div className="flex justify-start w-32 items-center gap-2 h-[100%]">
      {showEdit && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            handleEdit(rowData);
          }}
          className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
          title="Edit"
        >
          <EditIcon height={'20'} width="20" />
        </button>
      )}

      <button
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
          if (rowData.document_type === 'independent') {
            handleDownloadDoc(
              rowData?.document_path as string,
              rowData?.file_name as string,
            );
          } else {
            handleDownloadDoc(
              (rowData?.document_version_data as { file_path: string })
                ?.file_path,
              (
                rowData?.document_version_data as { file_path: string }
              )?.file_path
                .split('/')
                .pop() as string,
            );
          }
        }}
        className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
        title="Edit"
      >
        <Download height={'20'} width="20" />
      </button>
      {!hideDelete && (
        <button
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            if (handleDelete) handleDelete(rowData);
          }}
          className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300 transition"
          title="Delete"
        >
          <DeleteIcon height={'20'} width="20" />
        </button>
      )}
    </div>
  );
};
