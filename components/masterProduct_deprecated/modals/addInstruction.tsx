import { useRouter } from 'next/router';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';

const AddInstructionModal = ({
  edit,
  closeModal,
  instruction,
  sequenceNumber,
  reFetch,
}: {
  edit?: boolean;
  closeModal: any;
  instruction?: any;
  sequenceNumber: number;
  reFetch: () => void;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const router = useRouter();

  const {
    postData,
    response: responsePost,
    isLoading: isLoadingPost,
    error: errorPost,
  } = usePost();

  const {
    putData,
    response: responsePut,
    isLoading: isLoadingPut,
    error: errorPut,
  } = usePut();

  const handleSubmit = async (event: any) => {
    try {
      event.preventDefault();
      if (!instruction) {
        const formData = new FormData(event.target);

        const body = {
          description: formData.get('instruction'),
          sequence_no: sequenceNumber,
        };

        accessToken &&
          (await postData(
            accessToken,
            `products/${router.query.productId}/process-steps/${router.query.stepId}/checklist-item`,
            body,
          ));
      } else {
        const formData = new FormData(event.target);

        const body = {
          description: formData.get('instruction'),
        };

        accessToken &&
          (await putData(
            accessToken,
            `products/${router.query.productId}/process-steps/${router.query.stepId}/checklist-item/${instruction.id}`,
            body,
          ));
      }

      event.target.reset();
      reFetch();
      closeModal();
    } catch (error) {
      console.error('Error during operation:', error);
    }
  };

  return (
    <>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{edit ? 'Edit' : 'Add an'} instruction</DialogTitle>
        </DialogHeader>

        <div className="w-full flex justify-center">
          <form onSubmit={handleSubmit} className="w-full">
            <div className="flex flex-col gap-9">
              <div className=" bg-white rounded-sm">
                <div className="p-6.5">
                  <div className="mb-4.5 mt-2">
                    <label className="mb-2.5 text-base leading-6 block text-dark-100 ">
                      Instruction <span className="text-red-500">*</span>
                    </label>
                    <Textarea
                      rows={5}
                      name="instruction"
                      required
                      placeholder="Add here..."
                      // className="w-full rounded border-[1.5px] border-stroke bg-transparent py-3 px-5 text-black outline-none transition focus:border-primary active:border-primary disabled:cursor-default disabled:bg-whiter dark:border-form-strokedark dark:bg-form-input  dark:focus:border-primary"
                      defaultValue={instruction?.description}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-0 bg-white mx-0 pt-6">
              <PrimaryButton
                size="medium"
                text="Submit"
                isLoading={isLoadingPost || isLoadingPut}
              />
            </div>
          </form>
        </div>
      </DialogContent>
    </>
  );
};

export default AddInstructionModal;
