import React from 'react';

const FlowInstructions = () => {
  return (
    <div className="flex flex-col gap-1">
      <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-md max-w-[500px]">
        <h3 className="font-semibold text-gray-800 mb-2">How to use:</h3>
        <p className="mb-2 text-sm text-gray-700 whitespace-normal">
          <span className="font-semibold">Moving a Container:</span> Click and
          drag anywhere within the container to reposition it.
        </p>
        <p className="mb-2 text-sm text-gray-700 whitespace-normal">
          <span className="font-semibold">Resizing a Container:</span> Hover
          over the container&apos;s edge until a double-sided arrow appears,
          then click and drag to adjust the size.
        </p>
        <p className="mb-2 text-sm text-gray-700 whitespace-normal">
          <span className="font-semibold">Connecting Containers:</span> Click
          and drag from any of the dots along the container&apos;s edges to
          establish a connection.
        </p>
        <p className="mb-2 text-sm text-gray-700 whitespace-normal">
          <span className="font-semibold">Deleting a Container:</span> Select
          the container and press &quot;Backspace&quot; on Windows or
          &quot;Delete&quot; on macOS.
        </p>
        <p className="mb-2 text-sm text-gray-700 whitespace-normal">
          <span className="font-semibold">Recommended Browser:</span> For the
          best experience, use Google Chrome.
        </p>
      </div>
    </div>
  );
};

export default FlowInstructions;