import '@xyflow/react/dist/style.css';

import { useRouter } from 'next/router';
import { useCallback, useEffect, useRef, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';

import DecisionNodeIcon from '@/assets/masterProduct/decisionNodeIcon';
// import Notification from "../../../notifications/Notification";
import ProcessNodeIcon from '@/assets/masterProduct/ProcessNodeIcon';
import InfoCircle from '@/assets/outline/infoCircle';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import TertiaryButton from '@/components/common/button/tertiaryButton';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import {
  Background,
  Controls,
  Edge,
  MarkerType,
  OnEdgesDelete,
  OnNodeDrag,
  OnNodesDelete,
  Panel,
  ReactFlow,
  SelectionDragHandler,
  useEdgesState,
  useNodesState,
  useReactFlow,
} from '@xyflow/react';

import { nodeTypes } from './constant';
import {
  ControlPointData,
  edgeTypes,
  EditableEdge,
} from './edges/EditableEdge';
import { COLORS, Label } from './edges/EditableEdge/constants';
import FlowInstructions from './flowInstructions';
import { useAppStore } from './utils/store';
import { Toolbar } from './utils/toolbar';
import useUndoRedo from './utils/undoRedo';

const Flow = ({
  setEditFlow,
  data,
  reFetch,
}: {
  setEditFlow?: any;
  data?: any;
  reFetch: () => void;
}) => {
  const { takeSnapshot } = useUndoRedo();
  const accessToken = useAuthStore((state) => state.accessToken);

  const [nodes, setNodes, onNodesChange] = useNodesState<any>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<any>([]);
  const [newNodeLabel, setNewNodeLabel] = useState('');
  const [rfInstance, setRfInstance] = useState<any>(null);
  const { setViewport } = useReactFlow();
  const yPos = useRef(0);
  const [showNotification, setShowNotification] = useState(false);
  const { putData, response, isLoading, error } = usePut();
  const router = useRouter();

  const onChange = (event: any) => {
    setNodes((nds) =>
      nds.map((node) => {
        const label = event.target.value;

        return {
          ...node,
          data: {
            ...node.data,
            label,
          },
        };
      }),
    );
  };

  useEffect(() => {
    onRestore();
  }, [data]);

  const onSave = useCallback(() => {
    if (rfInstance) {
      const flow = rfInstance.toObject();

      const steps = flow.nodes?.map((n: any, index: number) => ({
        name: n.data?.label,
        id: n.id,
        sequence_no: index + 1,
      }));

      const body = {
        diagram_json: JSON.stringify(flow),
      };
      async function fetch() {
        accessToken &&
          (await putData(
            accessToken,
            `products/${router.query?.productId}/flowcharts`,
            body,
          ));
        reFetch();
      }
      fetch();
    }
  }, [rfInstance]);

  useEffect(() => {
    if (error || response) {
      setShowNotification(true);
      setTimeout(() => {
        setShowNotification(false);
      }, 2000);
    }
  }, [response, error]);

  const onRestore = useCallback(() => {
    const restoreFlow = async () => {
      const flow = data.flowchart;

      if (flow) {
        const { x = 0, y = 0, zoom = 1 } = flow.viewport;
        setNodes(flow.nodes || []);
        setEdges(flow.edges || []);
        setViewport({ x, y, zoom });
      }
    };

    restoreFlow();
  }, [setNodes, setViewport, data]);

  const addProcessNode = useCallback(() => {
    yPos.current = (yPos.current ? yPos.current : 0) + 50;
    const id = uuidv4();

    const newNode = {
      id: id,
      position: { x: 100, y: yPos.current ? yPos.current : 0 },
      type: 'process',
      data: { onChange: onChange, label: newNodeLabel },
      origin: [0.5, 0.0],
      selected: false,
      style: {
        width: 240,
        minHeight: 36,
        background: 'rgba(241, 241, 249, 1)',
        border: '1px solid #E1E1E1',
        borderRadius: 4,
      },
    };
    takeSnapshot();

    setNodes((nds) => nds.concat(newNode));
    setNewNodeLabel('');
  }, [nodes, newNodeLabel, takeSnapshot]);

  const addDecisionNode = useCallback(() => {
    yPos.current = (yPos.current ? yPos.current : 0) + 100;
    const id = uuidv4();
    const newNode = {
      id: id,
      position: { x: 100, y: yPos.current ? yPos.current : 0 },
      type: 'decision',
      width: 100,
      height: 100,
      data: { onChange: onChange, label: `New Decision` },
      origin: [0.5, 0.0],
      selected: false,
    };
    takeSnapshot();

    setNodes((nds) => nds.concat(newNode));
  }, [nodes, newNodeLabel, takeSnapshot]);

  // Create connection
  const onConnectEnd = useCallback(
    (_event: MouseEvent | TouchEvent, params?: any) => {
      const { connectionLinePath } = useAppStore.getState();
      const connection = {
        source: params?.fromNode?.id,
        target: params?.toNode?.id,
        sourceHandle: params?.fromHandle?.id,
        targetHandle: params?.toHandle?.id,
      };

      const edge: EditableEdge = {
        ...connection,
        id: `${Date.now()}-${connection.source}-${connection.target}`,
        type: 'editable-edge',
        selected: true,
        markerEnd: {
          type: MarkerType.ArrowClosed,
          width: 12,
          height: 12,
          color: COLORS[Label.Regular],
        },
        data: {
          algorithm: { name: 'linear' },
          points: connectionLinePath.map(
            (point, i) =>
              ({
                ...point,
                id: window.crypto.randomUUID(),
                prev: i === 0 ? undefined : connectionLinePath[i - 1],
                active: true,
              } as ControlPointData),
          ),
          label: Label.Regular,
        },
      };

      setEdges((edges: Edge[]) => [...edges, edge]);
    },
    [],
  );

  const onNodeDragStart: OnNodeDrag = useCallback(() => {
    // 👇 make dragging a node undoable
    takeSnapshot();
    // 👉 you can place your event handlers here
  }, [takeSnapshot]);

  const onSelectionDragStart: SelectionDragHandler = useCallback(() => {
    // 👇 make dragging a selection undoable
    takeSnapshot();
  }, [takeSnapshot]);

  const onNodesDelete: OnNodesDelete = useCallback(() => {
    // 👇 make deleting nodes undoable
    takeSnapshot();
  }, [takeSnapshot]);

  const onEdgesDelete: OnEdgesDelete = useCallback(() => {
    // 👇 make deleting edges undoable
    takeSnapshot();
  }, [takeSnapshot]);

  return (
    <div className="h-[100%] w-[100%] flex flex-col">
      {
        <div className="flex w-full p-[1.5vw] pt-0 justify-between ">
          <div className="flex gap-3">
            <TertiaryButton
              text="Add process"
              icon={<ProcessNodeIcon aria-hidden="true" color={'#00797D'} />}
              onClick={addProcessNode}
              size="medium"
              width="30"
            />
            <TertiaryButton
              text="Add decision"
              icon={<DecisionNodeIcon aria-hidden="true" color={'#00797D'} />}
              onClick={addDecisionNode}
              size="medium"
            />
            <Tooltip>
              <TooltipTrigger>
                <div className="w-30 h-full flex items-center gap-1">
                  <div className="w-6">
                    <InfoCircle height="24" width="24" color="#989898" />
                  </div>
                  <div className="w-30 font-grey-300">How to use</div>
                </div>
              </TooltipTrigger>
              <TooltipContent
                side="bottom"
                sideOffset={4}
                className="bg-white-100 rounded-lg p-1 shadow-shadow-2"
              >
                <FlowInstructions />
              </TooltipContent>
            </Tooltip>
          </div>
          <div className="flex gap-3">
            <PrimaryButton
              type="button"
              onClick={() => {
                onSave();
                setEditFlow(false);
              }}
              text="Confirm"
              size="small"
            />

            <SecondaryButton
              type="button"
              onClick={() => setEditFlow(false)}
              data-autofocus
              text="Cancel"
              size="small"
            />
          </div>
        </div>
      }
      <ReactFlow
        key="builder"
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        // onConnect={onConnect}
        onNodeDragStart={onNodeDragStart}
        onSelectionDragStart={onSelectionDragStart}
        onNodesDelete={onNodesDelete}
        onEdgesDelete={onEdgesDelete}
        onInit={setRfInstance}
        fitView
        // onConnectStart={onConnectStart}
        onConnectEnd={onConnectEnd}
        className="overview flex-1"
        proOptions={{ hideAttribution: true }}
      >
        {
          <>
            <Controls />
            <Background />
            <Panel position="top-left">
              <Toolbar />
            </Panel>
          </>
        }
      </ReactFlow>
    </div>
  );
};

export default Flow;
