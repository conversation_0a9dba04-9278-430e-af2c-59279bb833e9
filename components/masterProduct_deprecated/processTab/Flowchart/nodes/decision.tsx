import { useState } from 'react';

import {
  <PERSON>le,
  NodeResizer,
  Position,
  useReactFlow,
  useStore,
} from '@xyflow/react';
import { TNodeProps } from '@/interfaces/masterProduct';
import Shape from './shape';
import CustomNodeToolbar from '../utils/nodeToolbar';

const MAX_LINES = 3;

function useNodeDimensions(id: string) {
  const node = useStore((state) => state.nodeLookup.get(id));
  return {
    width: node?.measured?.width || 0,
    height: node?.measured?.height || 0,
  };
}

export const DecisionNode = ({
  id,
  data,
  sourcePosition = Position.Left,
  targetPosition = Position.Right,
}: TNodeProps) => {
  const { width, height } = useNodeDimensions(id);
  const [hover, setHover] = useState(false);
  const { setNodes } = useReactFlow();
  const onDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
  };

  return (
    <div
      onMouseEnter={() => {
        setHover(true);
      }}
      onMouseLeave={() => {
        setHover(false);
      }}
    >
      {!data.disabled && <CustomNodeToolbar onDelete={onDelete} />}

      {!data.disabled && hover && (
        <NodeResizer
          minWidth={40}
          minHeight={40}
          isVisible={data.selected}
          keepAspectRatio={true}
        />
      )}

      <Shape
        type={'diamond'}
        width={width}
        height={height}
        fill={'rgba(241, 241, 249, 1)'}
        strokeWidth={1}
        stroke={'#E1E1E1'}
        fillOpacity={0.8}
      />
      <textarea
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 
             w-full h-full bg-transparent border-none outline-none shadow-none text-center
             text-[var(--text)] placeholder:text-[var(--text)] placeholder:opacity-50 resize-none
             flex items-center justify-center"
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          lineHeight: '1.5', // Adjust for better vertical alignment
          paddingTop: '35%', // Moves text towards center
          overflow: 'hidden', // Prevents scrolling
        }}
        rows={MAX_LINES}
        onChange={(e) => (data.label = e.target.value)}
        defaultValue={data.label}
        disabled={data.disabled}
        placeholder={'Enter decision'}
      />
      <Handle id="top" type="target" position={Position.Top} />
      <Handle id="right" type="target" position={Position.Right} />
      <Handle id="bottom" type="target" position={Position.Bottom} />
      <Handle id="left" type="target" position={Position.Left} />
      <Handle id="top" type="source" position={Position.Top} />
      <Handle id="right" type="source" position={Position.Right} />
      <Handle id="bottom" type="source" position={Position.Bottom} />
      <Handle id="left" type="source" position={Position.Left} />
    </div>
  );
};
