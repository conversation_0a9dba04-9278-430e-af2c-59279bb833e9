import { useRef, useState } from 'react';

import { TNodeProps } from '@/interfaces/masterProduct';
import { <PERSON>le, NodeResizer, Position, useReactFlow } from '@xyflow/react';

import CustomNodeToolbar from '../utils/nodeToolbar';

export const ProcessNode = ({
  id,
  sourcePosition = Position.Left,
  targetPosition = Position.Right,
  data,
}: TNodeProps) => {
  const [hover, setHover] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null); // Reference to the input box
  const { setNodes } = useReactFlow();

  const onDelete = () => {
    setNodes((nodes) => nodes.filter((node) => node.id !== id));
  };

  return (
    <div
      onMouseEnter={() => {
        setHover(true);
      }}
      onMouseLeave={() => {
        setHover(false);
      }}
    >
      {!data.disabled && <CustomNodeToolbar onDelete={onDelete} />}
      {!data.disabled && hover && <NodeResizer minWidth={50} minHeight={50} />}
      <input
        className="w-[100%] h-[100%] min-h-[36px] p-4 bg-white"
        type="text"
        onChange={(e) => (data.label = e.target.value)}
        defaultValue={data.label}
        disabled={data.disabled}
        ref={inputRef}
      />

      <div className="w-200">
        <Handle id="a" type="source" position={Position.Top} />
        <Handle id="a" type="target" position={Position.Top} />
        <Handle id="b" type="target" position={Position.Left} />
        <Handle id="b" type="source" position={Position.Left} />
        <Handle id="c" type="target" position={Position.Right} />
        <Handle id="c" type="source" position={Position.Right} />
        <Handle id="d" type="source" position={Position.Bottom} />
        <Handle id="d" type="target" position={Position.Bottom} />
      </div>
    </div>
  );
};
