import { ChangeEventHand<PERSON> } from "react";

import {
  EdgeMarkerType,
  MarkerType,
  useEdges,
  useReactFlow,
} from '@xyflow/react';

import { EditableEdge } from "../edges/EditableEdge";
import { COLORS, Label } from '../edges/EditableEdge/constants';
import css from './Toolbar.module.css';

const edgeVariants: { [s: string]: string }[] = [
  {
    algorithm: 'linear',
    label: 'Reject',
  },
  {
    algorithm: 'linear',
    label: 'Approve',
  },
  {
    algorithm: 'linear',
    label: 'Regular',
  },
];

// A toolbar that allows the user to change the algorithm of the selected edge
export function Toolbar() {
  const edges = useEdges();
  const { setEdges } = useReactFlow();

  const selectedEdge = edges.find((edge) => edge.selected) as
    | EditableEdge
    | undefined;
  const onChange: ChangeEventHandler<HTMLInputElement> = (e) => {
    setEdges((edges) => {
      return edges.map((edge) => {
        if (edge.id === selectedEdge?.id) {
          return {
            ...edge,
            markerEnd: {
              type: MarkerType.ArrowClosed,
              width: 12,
              height: 12,
              color: COLORS[e.target.value],
            },
            data: {
              ...edge.data,
              label: e.target.value,
            },
          };
        }
        return edge;
      });
    });
  };

  return (
    <>
      {selectedEdge ? (
        <div className={css.toolbar}>
          {
            <div className={css.edgevariants}>
              {edgeVariants.map((edgeVariant) => (
                <div key={edgeVariant.label} className={css.edge}>
                  <input
                    type="radio"
                    id={edgeVariant.label}
                    name="label"
                    value={edgeVariant.label}
                    checked={selectedEdge?.data?.label === edgeVariant.label}
                    disabled={!selectedEdge}
                    style={{
                      accentColor: COLORS[edgeVariant.label],
                      color: COLORS[edgeVariant.label],
                    }}
                    onChange={onChange}
                  />
                  <label
                    htmlFor={edgeVariant.label}
                    style={{
                      color: selectedEdge ? COLORS[edgeVariant.label] : '',
                    }}
                  >
                    {edgeVariant.label}
                  </label>
                </div>
              ))}
            </div>
          }
        </div>
      ) : (
        <></>
      )}
    </>
  );
}
