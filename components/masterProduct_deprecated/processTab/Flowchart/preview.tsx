import { useCallback, useEffect } from 'react';

import {
  Background, Controls, ReactFlow,
  useEdgesState, useNodesState, useReactFlow
} from '@xyflow/react';

import { nodeTypes } from './constant';
import { edgeTypes } from './edges/EditableEdge';
import '@xyflow/react/dist/style.css';


const PreviewFlow = ({ data }: { data: any }) => {
  const [nodes, setNodes, onNodesChange] = useNodesState<any>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<any>([]);
  const { setViewport } = useReactFlow();

  useEffect(() => {
    onRestore();
  }, [data]);

  const onRestore = useCallback(() => {
    const restoreFlow = async () => {
      const flow =
        data?.record?.steps?.length > 0
          ? JSON.parse(data?.record?.diagram_json)
          : null;

      if (flow) {
        // const { x = 0, y = 0, zoom = 1 } = flow.viewport;
        let restoredNodes;
        if (flow.nodes) {
          restoredNodes = flow.nodes?.map((node: any) => {
            return { ...node, data: { ...node.data, disabled: true } };
          });
        }

        setNodes(restoredNodes || []);
        setEdges(flow.edges || []);

        // setViewport({ x, y, zoom });
      }
    };

    restoreFlow();
  }, [setNodes, setViewport, data]);

  return (
    <div
      style={{
        width: '77vw', // Replace with the desired width
        height: '45vh', // Replace with the desired height
      }}
    >
      <ReactFlow
        key="preview"
        nodes={nodes}
        edges={edges}
        nodeTypes={nodeTypes}
        panOnScroll
        edgeTypes={edgeTypes}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        // snapToGrid={true}
        fitView
        nodesDraggable={false}
        nodesConnectable={false}
        className="overview"
        proOptions={{ hideAttribution: true }}
      >
        <>
          <Controls />
          <Background />
        </>
      </ReactFlow>
    </div>
  );
};

export default PreviewFlow;
