import { useRouter } from 'next/router';
import React, { useState } from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from '@/components/common/dialog';
import Loader from '@/components/common/loader';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';
import { ReactFlowProvider } from '@xyflow/react';

import useFetch from '../../../hooks/useFetch';
import Flow from './Flowchart/flow';
import PreviewFlow from './Flowchart/preview';
import MasterProductTable from './processTable';

const ProcessTab = ({ data }: { data: any }) => {
  const user = useAuthStore((state) => state.user);

  const [editFlow, setEditFlow] = useState<boolean>(false);
  const accessToken = useAuthStore((state) => state.accessToken);
  const router = useRouter();
  const {
    data: processData,
    isLoading: processLoading,
    reFetch,
  } = useFetch(accessToken, `products/${router.query.productId}/process`);

  return (
    <div>
      {/* <FlowChartModal
        editFlow={editFlow}
        setEditFlow={setEditFlow}
        handleConfirm={undefined}
        data={processData}
        reFetch={reFetch}
      /> */}
      {processLoading ? (
        <Loader className="h-[700px]" />
      ) : (
        <div className="w-300 h-90 gap-5 mb-10">
          <ReactFlowProvider>
            <div className="w-300 h-90 border border-[rgba(221,221,221,1)] relative rounded-lg">
              <PreviewFlow data={processData} />
              {hasAccess(
                AccessActions.CanEditSpecificProduct,
                user,
                data?.assignees?.some(
                  (assignee: any) => assignee.id === user?.id,
                ),
              ) && (
                <Dialog open={editFlow} onOpenChange={setEditFlow}>
                  <DialogTrigger>
                    <div className=" absolute bottom-3 right-3 py-1.5 px-3">
                      <PrimaryButton
                        // onClick={() => setEditFlow(true)}
                        text="Edit Flow"
                        size="medium"
                      />
                    </div>
                  </DialogTrigger>
                  <DialogContent
                    className="!max-h-[90vh] h-[90vh] !max-w-[90vw] "
                    showClose={false}
                  >
                    <ReactFlowProvider>
                      <Flow
                        setEditFlow={setEditFlow}
                        data={processData}
                        reFetch={reFetch}
                      />
                    </ReactFlowProvider>
                  </DialogContent>
                </Dialog>
              )}
            </div>
          </ReactFlowProvider>
          <MasterProductTable
            editFlow={editFlow}
            data={processData}
            reFetch={reFetch}
            edit={hasAccess(
              AccessActions.CanEditSpecificProduct,
              user,
              data?.assignees?.some(
                (assignee: any) => assignee.id === user?.id,
              ),
            )}
          />
        </div>
      )}
    </div>
  );
};

export default ProcessTab;
