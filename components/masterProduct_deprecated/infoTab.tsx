import { useRouter } from 'next/router';
import React, { useState } from 'react';

import EditIcon from '@/assets/outline/edit';
import PlusIcon from '@/assets/outline/plus';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { TProductData } from '@/interfaces/masterProduct';
import { IAttachment } from '@/interfaces/misc';
import { hasAccess } from '@/utils/roleAccessConfig';
import { truncateWithEllipsis } from '@/utils/truncateText';

import { useDelete } from '../../hooks/useDelete';
import DeleteButton from '../common/button/deleteButton';
import PrimaryButton from '../common/button/primaryButton';
import SecondaryButton from '../common/button/secondaryButton';
import TertiaryButton from '../common/button/tertiaryButton';
import { Dialog, DialogContent } from '../common/dialog';
import { FileCard } from '../common/fileCard';
import { DetailsText, IDetailsText } from '../common/infoDetail';
import DeleteModal from '../common/modals/deleteModal';
import UploadComponent from '../common/uploadComponent';
import CreateProductModal from './modals/createProductModal';

interface User {
  id: string;
  full_name: string;
}

export interface TProductInfo {
  id: string;
  launch_date: string;
  name: string;
  product_id: string;
  status: string;
  version: number;
  approvers: User[];
  assignees: User[];
  attachments?: IAttachment[];
}

const InfoTabContent: React.FC<{ data: TProductInfo; reFetch: any }> = ({
  data,
  reFetch,
}: {
  data: TProductInfo;
  reFetch: any;
}) => {
  const [editProduct, setEditProduct] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDeleteFileModal, setShowDeleteFileModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<IAttachment | undefined>(
    undefined,
  );
  const [uploading, setUploading] = useState(false);
  const [addedFiles, setAddedFiles] = useState<IAttachment[]>([]);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const user = useAuthStore((state) => state.user);
  const { accessToken } = useAuthStore();
  const { deleteData } = useDelete();
  const { postData } = usePost();
  const router = useRouter();

  const onEdit = () => {
    setEditProduct(true);
  };

  const onDelete = () => {
    async function fetch() {
      await deleteData(accessToken as string, `products/${data?.id}`);
    }
    fetch();
    router.push(`/product-hub`);
  };

  const handleDeleteFile = () => {
    async function fetch() {
      await deleteData(
        accessToken as string,
        `products/${data?.id}/attachments/${selectedFile?.id}`,
      );
    }
    fetch().then(() => {
      reFetch();
      setSelectedFile(undefined);
      setShowDeleteFileModal(false);
    });
  };

  const handleFileSubmit = async () => {
    setUploading(true);
    await postData(
      accessToken as string,
      `products/${data?.id}/attachments`,
      addedFiles as unknown as Record<string, unknown>,
    )
      .then(() => {
        setShowUploadModal(false);
        reFetch();
        setAddedFiles([]);
      })
      .then(() => setUploading(false));
  };

  return (
    <>
      <Dialog
        open={editProduct}
        onOpenChange={(e) => {
          setEditProduct(false);
        }}
      >
        {/* check later */}
        <CreateProductModal
          edit={true}
          productData={data as unknown as TProductData}
          reFetch={reFetch}
        />
      </Dialog>
      <Dialog
        open={showDeleteModal}
        onOpenChange={(e) => {
          setShowDeleteModal(false);
        }}
      >
        <DeleteModal
          title={`Delete Product`}
          infoText={'Are you sure you want to delete this product?'}
          btnText={'Delete'}
          onClick={onDelete}
        />
      </Dialog>
      <Dialog
        open={showUploadModal}
        onOpenChange={(e) => {
          setShowUploadModal(false);
        }}
      >
        <DialogContent>
          <UploadComponent
            setOpenUploadModal={setShowUploadModal}
            refetch={reFetch}
            isMulti
            addedFiles={addedFiles}
            setAddedFiles={setAddedFiles}
            documentFor="product_hub"
          />
          <div className="w-full flex justify-end mt-2.5">
            <PrimaryButton
              size="medium"
              onClick={handleFileSubmit}
              text="Submit"
              isLoading={uploading}
            />
          </div>
        </DialogContent>
      </Dialog>
      <Dialog
        open={showDeleteFileModal}
        onOpenChange={(e) => {
          setShowDeleteFileModal(false);
        }}
      >
        <DeleteModal
          title={`Delete File`}
          infoText={'Are you sure you want to delete this file?'}
          btnText={'Delete'}
          onClick={handleDeleteFile}
        />
      </Dialog>
      <div className="">
        {data && (
          <>
            <div className="flex border border-grey-100 bg-white items-start justify-between p-2 rounded-lg">
              <div className="p-2 flex flex-col gap-3">
                <DetailsText
                  label="ID"
                  value={
                    truncateWithEllipsis(data.product_id as string, 40) || '-'
                  }
                />
                <DetailsText
                  label="Assignee"
                  value={data.assignees as IDetailsText[]}
                  multiValue
                />
                <DetailsText
                  label="Approver"
                  value={data.approvers as IDetailsText[]}
                  multiValue
                />
              </div>
              <div className="p-2 flex flex-col gap-3">
                <DetailsText
                  label="Launch date"
                  value={data.launch_date || '-'}
                />
                <DetailsText
                  label="Version"
                  value={String(data.version) || '-'}
                />
              </div>
              <div className="flex items-center gap-3 ">
                {user &&
                  hasAccess(AccessActions.CanAddOrEditProducts, user) && (
                    <SecondaryButton
                      onClick={onEdit}
                      text="Edit"
                      size="medium"
                      icon={<EditIcon color="#016366" />}
                    />
                  )}
                {user &&
                  hasAccess(AccessActions.CanDeleteSpecificProduct, user) && (
                    <DeleteButton
                      onClick={() => {
                        setShowDeleteModal(true);
                      }}
                      text="Delete"
                    />
                  )}
              </div>
            </div>
          </>
        )}
      </div>

      <div className="mt-4">
        <div className="text-base leading-6 font-medium text-dark-100 mb-3">
          Attach file
        </div>

        {data?.attachments && data?.attachments?.length > 0 ? (
          <div className="flex items-center flex-wrap gap-2">
            {data?.attachments?.map((file, index) => (
              <FileCard
                key={index}
                filepath={file.file_path}
                handleDelete={() => {
                  setShowDeleteFileModal(true);
                  setSelectedFile(file);
                }}
                file_extension={file.file_extension}
              />
            ))}
            <TertiaryButton
              icon={<PlusIcon height="20px" width="20px" />}
              size="medium"
              text=""
              width="10"
              onClick={() => {
                setShowUploadModal(true);
              }}
            />
          </div>
        ) : (
          <TertiaryButton
            icon={<PlusIcon height="20px" width="20px" />}
            text="Upload"
            size="medium"
            onClick={() => {
              setShowUploadModal(true);
            }}
          />
        )}
      </div>
    </>
  );
};

export default InfoTabContent;
