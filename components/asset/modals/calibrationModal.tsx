import axios from 'axios';
import moment from 'moment';
import React, { ReactNode, useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import {
  Dialog<PERSON>ontent,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
// import { ORGANIZATION_HEADER_KEY, ORGANIZATION_SESSION_KEY } from '@/constants/common';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import { IDocumentDetails } from '@/interfaces/document';

import TertiaryButton from '../../../components/common/button/tertiaryButton';
import SingleSelect, {
  IOption,
} from '../../../components/common/creatableSingleSelect';
import FileCard from '../../../components/common/modals/uploadModal/fileCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../components/common/select';

interface IExtendedDocumentDetails extends IDocumentDetails {
  path?: string;
  file_path?: string;
  name?: string;
}

const acceptFileTypes = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
    '.doc',
  ],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
  'application/vnd.ms-excel': ['.xls', '.csv'],
};

interface IProps {
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  showModal: boolean;
  edit?: boolean;
  assetId: string;
  calibrationId?: string;
  onSuccess?: () => void;
  // Whether calibration is required for this asset
  calibrationRequired?: boolean;
  // Calibration data for edit mode
  calibrationData?: {
    id?: string;
    document_type?: string;
    file_path?: string;
    file_name?: string;
    document_id?: string;
    calibration_date?: string;
    calibration_cert_num?: string;
    outcome?: string;
    comment?: string;
  };
}

export const CalibrationModal = ({
  setShowModal,
  showModal,
  edit = false,
  assetId,
  calibrationId,
  onSuccess,
  calibrationRequired = true,
  calibrationData,
}: IProps) => {
  const { accessToken } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [uploadingFile, setUploadingFile] = useState(false);
  const [addedFile, setAddedFile] = React.useState<File[] | null>(null);

  // Calibration-specific state
  const [outcome, setOutcome] = useState<string>(
    calibrationData?.outcome || 'pass',
  );
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>(
    calibrationData?.document_id || '',
  );
  const [documentType, setDocumentType] = useState<string>(
    calibrationData?.document_type || 'doc_hub',
  );
  const [fileName, setFileName] = useState<string>(
    calibrationData?.file_name || '',
  );
  const [filePath, setFilePath] = useState<string>(
    calibrationData?.file_path || '',
  );
  const [calibrationDate, setCalibrationDate] = useState<string>(
    calibrationData?.calibration_date || '',
  );
  const [calibrationCertNum, setCalibrationCertNum] = useState<string>(
    calibrationData?.calibration_cert_num || '',
  );
  const [comment, setComment] = useState<string>(
    calibrationData?.comment || '',
  );

  // Track if there are changes in edit mode
  const [hasChanges, setHasChanges] = useState(false);

  // Store original values for comparison
  const [originalValues, setOriginalValues] = useState({
    outcome: calibrationData?.outcome || 'pass',
    documentType: calibrationData?.document_type || 'doc_hub',
    documentId: calibrationData?.document_id || '',
    filePath: calibrationData?.file_path || '',
    fileName: calibrationData?.file_name || '',
    calibrationDate: calibrationData?.calibration_date || '',
    calibrationCertNum: calibrationData?.calibration_cert_num || '',
    comment: calibrationData?.comment || '',
  });

  useEffect(() => {
    // Only use calibrationData if we're in edit mode
    if (edit && calibrationData) {
      // Initialize form values with the provided data
      setDocumentType(calibrationData.document_type || 'doc_hub');
      setSelectedDocumentId(calibrationData.document_id || '');
      setFileName(calibrationData.file_name || '');
      setFilePath(calibrationData.file_path || '');
      setCalibrationDate(calibrationData.calibration_date || '');
      setCalibrationCertNum(calibrationData.calibration_cert_num || '');
      setOutcome(calibrationData.outcome || 'pass');
      setComment(calibrationData.comment || '');

      // Store original values for comparison
      setOriginalValues({
        outcome: calibrationData.outcome || 'pass',
        documentType: calibrationData.document_type || 'doc_hub',
        documentId: calibrationData.document_id || '',
        filePath: calibrationData.file_path || '',
        fileName: calibrationData.file_name || '',
        calibrationDate: calibrationData.calibration_date || '',
        calibrationCertNum: calibrationData.calibration_cert_num || '',
        comment: calibrationData.comment || '',
      });

      // Reset changes flag when modal opens with data
      setHasChanges(false);
    }
  }, [edit, calibrationData]);

  // Fetch documents for document hub
  const { data: publishedDocuments } = useFetch<{
    records: IExtendedDocumentDetails[];
    total: number;
  }>(accessToken, `documents?status=Published`);

  // Format documents as options for CreatableSingleSelect
  const documentOptions: IOption[] = React.useMemo(() => {
    if (!publishedDocuments?.records?.length) return [];

    return publishedDocuments.records.map((doc) => ({
      value: doc.id || '',
      label: doc.title || 'Untitled Document',
    }));
  }, [publishedDocuments?.records]);

  // Selected document option for CreatableSingleSelect
  const selectedDocumentOption = React.useMemo(() => {
    if (!selectedDocumentId || !documentOptions.length) return undefined;

    return (
      documentOptions.find((option) => option.value === selectedDocumentId) || {
        value: selectedDocumentId,
        label: 'Selected Document',
      }
    );
  }, [selectedDocumentId, documentOptions]);

  // Check for changes in edit mode
  useEffect(() => {
    if (edit) {
      const currentValues = {
        outcome,
        documentType,
        documentId: selectedDocumentId,
        filePath,
        fileName,
        calibrationDate,
        calibrationCertNum,
        comment,
      };

      const hasChanged = Object.keys(originalValues).some(
        (key) =>
          originalValues[key as keyof typeof originalValues] !==
          currentValues[key as keyof typeof currentValues],
      );

      const fileChanged = addedFile && addedFile.length > 0 ? true : false;

      setHasChanges(hasChanged || fileChanged);
    }
  }, [
    outcome,
    documentType,
    selectedDocumentId,
    filePath,
    fileName,
    calibrationDate,
    calibrationCertNum,
    comment,
    addedFile,
    edit,
    originalValues,
  ]);

  // File dropzone setup
  const onDrop = useCallback((acceptedFiles: File[]) => {
    // Set document type to 'independent' when uploading a file
    setDocumentType('independent');
    // Clear selected document ID when uploading a file
    setSelectedDocumentId('');
    // Store the uploaded file
    setAddedFile(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: acceptFileTypes,
    maxSize: 10000000, // 10MB
  });

  // Hooks for API calls
  const {
    postData,
    isLoading: isPosting,
    response: postResponse,
  } = usePost<any, Record<string, string>>();
  const {
    putData,
    isLoading: isPutting,
    response: putResponse,
  } = usePut<any, Record<string, string>>();

  // Handle file upload
  const handleFileUpload = async (
    file: File,
  ): Promise<{ file_path: string; file_extension: string } | null> => {
    try {
      setUploadingFile(true);
      const formData = new FormData();
      formData.append('file', file);

      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=asset_hub&sub_path=/${assetId}/evaluations`;
      const response = await axios.post(url, formData, config);

      setUploadingFile(false);
      return {
        file_path: response.data.file_path,
        file_extension: response.data.file_extension,
      };
    } catch (error) {
      setUploadingFile(false);
      console.error('Error uploading file:', error);
      toast.error('File upload failed');
      return null;
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsLoading(true);

      // Get the path and name for uploaded file
      let uploadedFilePath = '';
      let uploadedFileName = '';

      // If a file was added, upload it
      if (addedFile && addedFile.length > 0) {
        const result = await handleFileUpload(addedFile[0]);
        if (result) {
          uploadedFilePath = result.file_path;
          uploadedFileName = addedFile[0].name;
        }
      } else if (documentType === 'doc_hub' && selectedDocumentId) {
        // If selecting from doc hub
        const selectedDoc = publishedDocuments?.records.find(
          (doc) => doc.id === selectedDocumentId,
        );
        uploadedFileName = selectedDoc?.title || '';
      }

      // Prepare request body
      const body: {
        document_type: string;
        file_name: string;
        file_path?: string;
        document_id?: string;
        calibration_date: string;
        calibration_cert_num: string;
        outcome: string;
        comment: string;
      } = {
        document_type: documentType,
        file_name: uploadedFileName,
        file_path: uploadedFilePath,
        document_id: selectedDocumentId,
        calibration_date: calibrationDate ?? '',
        calibration_cert_num: calibrationCertNum ?? '',
        outcome: outcome.toLowerCase(), // API expects lowercase
        comment: comment ?? '',
      };

      // If document type is 'independent', remove file_path and file_name
      if (documentType === 'independent') {
        delete body.document_id;
      } else {
        delete body.file_path;
      }

      // API integration
      // Edit mode - PUT call to update calibration
      if (edit && assetId && calibrationId) {
        await putData(
          accessToken || '',
          `assets/${assetId}/calibrations/${calibrationId}`,
          body as Record<string, string>,
        );
      }
      // Create mode - POST call to create calibration
      else if (assetId) {
        await postData(
          accessToken || '',
          `assets/${assetId}/calibrations`,
          body as Record<string, string>,
        );
      }
    } catch (error) {
      console.error('Error handling form submission:', error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!showModal) {
      // Reset all form values when modal closes
      setAddedFile(null);
      setSelectedDocumentId('');
      setDocumentType('independent');
      setFileName('');
      setFilePath('');
      setOutcome('pass');
      setCalibrationDate('');
      setCalibrationCertNum('');
      setComment('');
      setHasChanges(false);
    }
  }, [showModal]);

  useEffect(() => {
    setIsLoading(isPosting || isPutting || uploadingFile);
  }, [isPosting, isPutting, uploadingFile]);

  useEffect(() => {
    if (postResponse || putResponse) {
      if (onSuccess) {
        onSuccess();
      }
      setShowModal(false);
      toast.success(
        edit
          ? 'Calibration updated successfully'
          : 'Calibration added successfully',
      );
    }
  }, [postResponse, putResponse, edit]);

  return (
    <DialogContent className="max-h-[90vh] min-w-[65vw] w-[65vw] overflow-y-auto">
      {/* Warning banner when calibration is not required but viewing/editing certificates */}
      {!calibrationRequired && (
        <div className="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
          <div className="flex items-center">
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                Calibration setting for this asset has been disabled.{' '}
                {edit
                  ? 'You are viewing this certificate in read-only mode.'
                  : 'Enable calibration to add / edit certificates.'}
              </p>
            </div>
          </div>
        </div>
      )}
      <DialogHeader>
        <DialogTitle>
          {edit ? (calibrationRequired ? 'Edit' : 'View') : 'Add'} Calibration
        </DialogTitle>
      </DialogHeader>
      <div className="mt-2  w-full">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Link Document from document hub
          <span className="text-[#F55D5D]">*</span>
        </div>
        <SingleSelect
          selectedOption={selectedDocumentOption}
          options={documentOptions}
          onChange={(selected) => {
            if (calibrationRequired) {
              setSelectedDocumentId(selected?.value || '');
              setDocumentType('doc_hub');
              // Clear any uploaded file when selecting a document
              setAddedFile([]);
              console.log('Document selected:', selected);
            }
          }}
          placeholder="Select documents"
          isDisabled={!calibrationRequired}
          hasError={false}
        />
      </div>

      <div className="w-full h-0 border-b border-white-300 relative my-4">
        <div className="absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 bg-white-100 px-4 text-sm font-medium text-grey-300">
          Or
        </div>
      </div>

      <div className="">
        <div className="text-base leading-6 font-medium text-dark-100 mb-2.5">
          Attach document
        </div>

        <div>
          <div
            className={`min-h-28 bg-white-100 border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 ${
              calibrationRequired
                ? 'hover:bg-[#F8F8F8]'
                : 'opacity-70 cursor-not-allowed'
            } p-2`}
            {...(calibrationRequired
              ? getRootProps()
              : { onClick: (e) => e.preventDefault() })}
          >
            {!(addedFile?.length && addedFile?.length > 0) && (
              <div className="text-sm font-medium leading-5 text-[#49474E]">
                Upload or Drag and drop to upload your files
              </div>
            )}

            <input {...getInputProps()} />
            <div className="flex justify-center items-center flex-wrap gap-2">
              {addedFile?.map((file, index) => (
                <FileCard key={index} file={file} setAddedFile={setAddedFile} />
              ))}
              {(!addedFile || addedFile.length === 0) &&
                calibrationData?.file_path && (
                  <FileCard
                    key={calibrationData.file_path}
                    prefillFileName={calibrationData.file_name ?? ''}
                  />
                )}
            </div>
            <TertiaryButton
              text={
                (addedFile?.length && addedFile?.length > 0) ||
                (calibrationData?.file_path &&
                  calibrationData.file_path.length > 0)
                  ? 'Replace'
                  : 'Select files'
              }
              size="small"
              disabled={!calibrationRequired}
            />
          </div>
        </div>
      </div>

      <div className="flex gap-4 item-center">
        <div className="mt-2 overflow-hidden w-full">
          <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
            Calibration date
            <span className="text-[#F55D5D]">*</span>
          </div>

          <Calendar
            selectedDate={calibrationDate}
            allowPastDates
            onDateChange={(date) => {
              if (calibrationRequired) {
                setCalibrationDate(moment(date as string).format('YYYY-MM-DD'));
              }
            }}
            disabled={!calibrationRequired}
          />
        </div>

        <div className="mt-2 overflow-hidden w-full">
          <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
            Certificate No.
            <span className="text-[#F55D5D]">*</span>
          </div>
          <Input
            placeholder="Enter certificate number"
            value={calibrationCertNum}
            onChange={(e) =>
              calibrationRequired && setCalibrationCertNum(e.target.value)
            }
            disabled={!calibrationRequired}
          />
        </div>

        <div className="mt-2 overflow-hidden w-full">
          <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
            Outcome
          </div>
          <Select
            value={outcome}
            onValueChange={(value) => calibrationRequired && setOutcome(value)}
            disabled={!calibrationRequired}
          >
            <SelectTrigger id="outcome">
              <SelectValue placeholder="Select outcome" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pass">Pass</SelectItem>
              <SelectItem value="fail">Fail</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="mt-2 overflow-hidden w-full">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Comments
        </div>
        <Textarea
          value={comment}
          onChange={(e) => calibrationRequired && setComment(e.target.value)}
          placeholder="Add your comments here"
          disabled={!calibrationRequired}
        />
      </div>

      <DialogFooter className="mt-2 gap-2">
        <TertiaryButton
          onClick={() => {
            setShowModal(false);
          }}
          text={calibrationRequired ? 'Cancel' : 'Close'}
          size="medium"
        ></TertiaryButton>
        {calibrationRequired && (
          <PrimaryButton
            onClick={handleSubmit}
            isLoading={isLoading}
            text={edit ? 'Save' : 'Add'}
            size="medium"
          ></PrimaryButton>
        )}
      </DialogFooter>
    </DialogContent>
  );
};
