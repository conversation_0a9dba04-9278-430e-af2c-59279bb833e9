// Using require for moment to avoid TypeScript import issues
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { toast } from 'react-toastify';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import { IOption } from '@/components/common/creatableSelect';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
// import { validateForm } from "@/hooks/useValidator";
import { TAssetData as BaseAssetData } from '@/interfaces/asset';
import { IUser } from '@/interfaces/user';
import { removeEmptyFields } from '@/utils/removeEmptyFields';
import { getMonthsFromPeriod, getSubscriptionPeriod } from '@/utils/time';

// Extended asset data interface to include additional properties
interface TAssetData extends BaseAssetData {
  calibration_by?: string;
  justification?: string;
  calibration_period_unit?: string;
  calibration_period_value?: number;
}

interface IData extends Record<string, unknown> {
  asset_id: string;
  name: string;
  description: string;
  owner: string;
  location: string;
  purchase_date: string;
  status: string;
  calibration: boolean;
  calibration_by: string;
  calibration_period_value?: number;
  calibration_period_unit?: string;
  justification?: string;
}

const CreateAssetModal = ({
  edit,
  assetData,
  setOpenEdit,
  reFetch,
}: {
  edit?: boolean;
  assetData?: TAssetData;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  reFetch?: () => void;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { data: users, isLoading: userLoading } = useFetch<
    { records: IUser[] },
    { asset_admin: boolean }
  >(accessToken, `users`, {
    asset_admin: true,
  });
  const { putData, response, isLoading } = usePut();
  // For checking asset ID uniqueness
  const [checkingAssetId, setCheckingAssetId] = useState(false);
  const [assetIdExists, setAssetIdExists] = useState(false);
  const assetIdTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const {
    postData,
    response: createResponse,
    isLoading: createIsloading,
    error: createError,
  } = usePost();

  const [data, setData] = useState<IData>({
    asset_id: '',
    name: '',
    description: '',
    owner: '',
    location: '',
    purchase_date: '',
    status: '',
    calibration: true,
    calibration_by: 'internal',
    calibration_period_value: 1,
    calibration_period_unit: 'months',
    justification: '',
  });
  const [error, setError] = useState<Record<string, string> | undefined>();
  const [localErrors, setLocalErrors] = useState<Record<string, string>>({});

  const createAssetSchema = {
    asset_id: z.string().nonempty('Asset ID is required'),
    name: z.string().nonempty('Name is required'),
    description: z.string().optional(),
    location: z.string().optional(),
    owner: z.string().nonempty('Owner is required'),
    status: z.string().nonempty('Status is required'),
    calibration: z.boolean(),
    calibration_period_value: z.number().optional(),
    calibration_period_unit: z.string().optional(),
    calibration_by: z.string().nonempty('Calibrated by is required'),
    justification: z.string().optional(),
    // .refine(
    //   (val) => {
    //     if (!data.calibration && val?.trim() !== '' && val !== undefined)
    //       return true;

    //     return false;
    //   },
    //   {
    //     message: 'Justification is required when calibration is not required',
    //   },
    // ),
  };
  const { validationErrors, startValidation, reset } = useValidators({
    schemas: createAssetSchema,
    values: data,
  });

  const userData = users?.records?.map((e) => ({
    label: e.full_name,
    value: e.id,
  })) as IOption[];

  const handleSubmit = async () => {
    setError(undefined);
    const { hasValidationErrors } = await startValidation();

    // Check asset ID uniqueness first (if not in edit mode or if ID changed in edit mode)
    if (
      assetIdExists &&
      (!edit || (edit && assetData && data.asset_id !== assetData.asset_id))
    ) {
      setError({
        asset_id: 'Asset ID already exists',
      });
      return;
    }
    if (!hasValidationErrors) {
      const payload: any = { ...data };

      // Backend directly accepts calibration_period_value and calibration_period_unit
      // No need to convert to calibration_period as the backend doesn't use it

      // Handle fields based on calibration setting
      if (!payload?.calibration) {
        // Remove calibration-related fields when calibration is not required
        delete payload.calibration_period_value;
        delete payload.calibration_period_unit;
        delete payload.calibration_by;

        // Ensure justification is present when calibration is not required
        if (!payload.justification) {
          setError({
            justification:
              'Justification is required when calibration is not required',
          });
          return;
        }
      } else {
        // When calibration is required, validate necessary fields
        if (!payload.calibration_by) {
          setError({ calibration_by: 'Calibrated by is required' });
          return;
        }

        if (
          !payload.calibration_period_value ||
          payload.calibration_period_value <= 0
        ) {
          setError({
            calibration_period_value:
              'Calibration period value must be greater than 0',
          });
          return;
        }

        if (!payload.calibration_period_unit) {
          setError({
            calibration_period_unit: 'Calibration period unit is required',
          });
          return;
        }

        // No need for justification when calibration is required
        delete payload.justification;
      }

      // Hit API and handle toasts based on API response via hook state
      if (edit && assetData) {
        await putData(
          accessToken as string,
          `assets/${assetData.id}`,
          removeEmptyFields({ ...payload, id: assetData.id }),
        );
      } else {
        await postData(
          accessToken as string,
          'assets',
          removeEmptyFields(payload),
        );
      }
    }
  };

  useEffect(() => {
    if (edit && assetData) {
      setData({
        asset_id: assetData?.asset_id,
        name: assetData?.name,
        description: assetData?.description,
        owner: assetData?.owner?.id,
        location: assetData?.location,
        purchase_date: assetData?.purchase_date,
        status: assetData?.status,
        calibration_date: assetData?.calibration_date,
        calibration: assetData?.calibration,
        calibration_cert_num: assetData?.calibration_cert_num,
        calibration_by: assetData?.calibration_by || 'internal',
        justification: assetData?.justification || '',
        // Handle period conversion from the API's month value to our new format
        calibration_period_value: assetData?.calibration_period_value || 1,
        calibration_period_unit: assetData.calibration_period_unit || 'months',
      });
    }
  }, [assetData, edit]);
  // Toasts based on API hook responses
  useEffect(() => {
    if (edit) {
      if (response) {
        toast.success('Asset updated successfully');
        if (setOpenEdit) setOpenEdit(false);
        reFetch && reFetch();
      }
    } else {
      if (createResponse) {
        toast.success('Asset created successfully');
        if (setOpenEdit) setOpenEdit(false);
        reFetch && reFetch();
      }
    }
  }, [response, createResponse, edit]);

  // Error toasts based on API hook errors
  useEffect(() => {
    if (edit) {
      if (error) {
        // usePut already toasts detailed message, but ensure UX consistency
        // no-op here to avoid duplicate toasts
      }
    } else {
      if (createError) {
        // usePost already toasts detailed message, avoid duplicate toasts
        // no-op here
      }
    }
  }, [error, createError, edit]);

  return (
    <DialogContent className="min-w-[65vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle>{edit ? 'Edit' : 'Create'} Asset</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="asset_id"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Asset ID<span className="text-red-200">*</span>
            </Label>

            <Input
              placeholder="Enter asset ID"
              id="assetID"
              type="text"
              name="asset_id"
              value={data?.asset_id}
              required
              onChange={(e) => {
                const newAssetId = e.target.value;
                setData((pre) => ({
                  ...pre,
                  asset_id: newAssetId,
                }));
              }}
              errorMsg={localErrors.asset_id || validationErrors?.asset_id?.[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="title"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Asset Name<span className="text-red-200">*</span>
            </Label>
            <Input
              type="text"
              name="name"
              value={data?.name}
              placeholder="Enter Asset name"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, name: e.target.value }))
              }
              errorMsg={validationErrors?.name[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="description"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Description
            </Label>
            <Input
              placeholder="Enter description"
              id="description"
              type="text"
              name="description"
              value={data?.description}
              onChange={(e) =>
                setData((pre) => ({ ...pre, description: e.target.value }))
              }
              errorMsg={validationErrors?.description?.[0]}
            />
          </div>
        </div>
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="owner"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Asset owner
              <span className="text-[#F55D5D]">*</span>
            </Label>
            <Select
              value={data?.owner}
              onValueChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  owner: value,
                }));
              }}
            >
              <SelectTrigger
                className={validationErrors?.owner?.[0] ? 'border-red-200' : ''}
                id="owner"
                disabled={userLoading}
              >
                <SelectValue placeholder="Select owner" />
              </SelectTrigger>
              <SelectContent>
                {userData?.length > 0 ? (
                  userData?.map((e, i) => (
                    <SelectItem key={i.toString()} value={e.value}>
                      {e.label}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-users" disabled>
                    No users available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
            {userLoading && (
              <div className="text-xs text-gray-500 mt-1">Loading users...</div>
            )}
            {validationErrors?.owner?.[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.owner[0]}
              </div>
            ) : null}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="location"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Asset Location
            </Label>
            <Input
              type="text"
              name="name"
              value={data?.location}
              placeholder="Enter location"
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, location: e.target.value }))
              }
              errorMsg={validationErrors?.location[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1 relative">
            <Label
              htmlFor="purchase_date"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Purchase Date
            </Label>
            <Calendar
              selectedDate={data?.purchase_date}
              onDateChange={(date) => {
                if (date) {
                  setData((prev) => ({
                    ...prev,
                    purchase_date: moment(date as string).format('YYYY-MM-DD'),
                  }));
                } else {
                  setData((prev) => ({
                    ...prev,
                    purchase_date: '',
                  }));
                }
              }}
              allowPastDates
              // className={'absolute top-0 w-full h-11'}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="status"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Asset Status<span className="text-red-200">*</span>
            </Label>
            <Select
              value={data.status}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, status: value }));
              }}
            >
              <SelectTrigger
                className={validationErrors?.status[0] ? 'border-red-200' : ''}
                id="status"
              >
                <SelectValue placeholder="Enter status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="In use">In use</SelectItem>
                <SelectItem value="Maintenance">Maintenance</SelectItem>
                <SelectItem value="Breakdown">Breakdown</SelectItem>
                <SelectItem value="Calibration">Calibration</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors?.status[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.status[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-2.5 flex-1 mb-5">
          <Label
            htmlFor="calibration"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Calibration Required<span className="text-red-200">*</span>
          </Label>
          <Select
            value={data.calibration ? 'True' : 'False'}
            onValueChange={(value) => {
              setData((pre) => ({
                ...pre,
                calibration: value == 'True' ? true : false,
              }));
            }}
          >
            <SelectTrigger
              className={
                validationErrors?.calibration[0] ? 'border-red-200' : ''
              }
              id="calibration"
            >
              <SelectValue placeholder="Select calibration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="True">Yes</SelectItem>
              <SelectItem value="False">No</SelectItem>
            </SelectContent>
          </Select>
          {validationErrors?.calibration[0] ? (
            <div className="text-xs font-semibold leading-5 text-left text-red-200">
              {validationErrors?.calibration[0]}
            </div>
          ) : (
            <></>
          )}
        </div>

        {!data?.calibration && (
          <div className="flex flex-col">
            <Label
              htmlFor="justification"
              className="text-base font-medium leading-6 text-dark-100 mb-2.5"
            >
              Justification
              <span className="text-[#F55D5D]">*</span>
            </Label>
            <Textarea
              placeholder="Enter justification"
              id="justification"
              name="justification"
              rows={5}
              onChange={(e) =>
                setData((pre) => ({
                  ...pre,
                  justification: e.target.value,
                }))
              }
              value={data?.justification}
              required
            />
            {(validationErrors?.justification?.[0] ||
              (!data?.calibration &&
                !data?.justification &&
                error?.justification)) && (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.justification?.[0] ||
                  error?.justification ||
                  'Justification is required when calibration is not required'}
              </div>
            )}
          </div>
        )}

        {data?.calibration && (
          <div className="flex gap-4">
            <div className="flex flex-1 flex-col gap-2.5 w-full">
              <Label
                htmlFor="calibration_by"
                className="text-base font-medium leading-6 text-dark-100"
              >
                Calibrated by
                <span className="text-[#F55D5D]">*</span>
              </Label>
              <Select
                value={data.calibration_by}
                onValueChange={(value) => {
                  setData((pre) => ({
                    ...pre,
                    calibration_by: value,
                  }));
                }}
              >
                <SelectTrigger
                  className={
                    validationErrors?.calibration_by?.[0]
                      ? 'border-red-200'
                      : ''
                  }
                  id="calibration_by"
                >
                  <SelectValue placeholder="Select who performed calibration" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="internal">Internal</SelectItem>
                  <SelectItem value="customer">Customer</SelectItem>
                  <SelectItem value="vendor">Vendor</SelectItem>
                </SelectContent>
              </Select>
              {validationErrors?.calibration_by?.[0] ? (
                <div className="text-xs font-semibold leading-5 text-left text-red-200">
                  {validationErrors?.calibration_by?.[0]}
                </div>
              ) : null}
            </div>
            {/* Calibration Date field removed as requested - it belongs in calibrationModal */}
            {/* Calibration Certificate No. field removed as requested - it belongs in calibrationModal */}
            <div className="flex flex-col gap-2.5 flex-1">
              <Label
                htmlFor="calibration_period"
                className="text-base font-medium leading-6 text-dark-100"
              >
                Calibration Period
                <span className="text-[#F55D5D]">*</span>
              </Label>
              <div className="flex gap-2">
                <div className="flex-1">
                  <Input
                    placeholder="Enter number"
                    id="calibration_period_value"
                    type="number"
                    min={0}
                    name="calibration_period_value"
                    value={data?.calibration_period_value?.toString()}
                    onChange={(e) =>
                      setData((pre) => ({
                        ...pre,
                        calibration_period_value: parseInt(e.target.value),
                      }))
                    }
                    disabled={!data?.calibration}
                    errorMsg={validationErrors?.calibration_period_value?.[0]}
                  />
                </div>
                <div className="">
                  <Select
                    value={data.calibration_period_unit}
                    onValueChange={(value) => {
                      setData((pre) => ({
                        ...pre,
                        calibration_period_unit: value,
                      }));
                    }}
                    disabled={!data?.calibration}
                  >
                    <SelectTrigger
                      className={
                        validationErrors?.calibration_period_unit?.[0]
                          ? 'border-red-200 max-w-[10rem]'
                          : 'max-w-[10rem]'
                      }
                      id="calibration_period_unit"
                    >
                      <SelectValue placeholder="Select unit" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="days">Days</SelectItem>
                      <SelectItem value="weeks">Weeks</SelectItem>
                      <SelectItem value="months">Months</SelectItem>
                      <SelectItem value="quarters">Quarters</SelectItem>
                      <SelectItem value="years">Years</SelectItem>
                    </SelectContent>
                  </Select>
                  {validationErrors?.calibration_period_unit?.[0] ? (
                    <div className="text-xs font-semibold leading-5 text-left text-red-200">
                      {validationErrors?.calibration_period_unit?.[0]}
                    </div>
                  ) : null}
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end mt-6">
          <PrimaryButton
            size="medium"
            text="Submit"
            onClick={handleSubmit}
            isLoading={Boolean(isLoading || createIsloading)}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default CreateAssetModal;
