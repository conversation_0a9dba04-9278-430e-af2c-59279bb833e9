import React, { useCallback, useState } from 'react';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';

import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import UploadComponent from '../common/uploadComponent';
import { IAttachment } from '@/interfaces/misc';
import { handleFilesUpload } from '@/utils/handleFileUpload';
import { useRouter } from 'next/router';
import { usePost } from '@/hooks/usePost';
import { Upload, X } from 'lucide-react';
import TertiaryButton from '../common/button/tertiaryButton';
import { useDropzone } from 'react-dropzone';
import DocumentIcon from '@/assets/outline/document';

interface AddInvestigatorModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
  title?: string;
  isMulti?: boolean;
}

const acceptFileTypes = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
    '.doc',
  ],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
  'application/vnd.ms-excel': ['.xls', '.csv'],
};

const EvidenceUploadModal: React.FC<AddInvestigatorModalProps> = ({
  open,
  onOpenChange,
  onSuccess,
  title,
  isMulti = false,
}) => {
  const router = useRouter();
  const { ncId } = router.query as { ncId?: string };
  const accessToken = useAuthStore((s) => s.accessToken);
  const [addedFiles, setAddedFiles] = React.useState<File[] | null>(null);

  const {
    postData,
    isLoading: isPosting,
    response: postResponse,
    error: postError,
  } = usePost<any, Record<string, string>>();

  const handleConfirm = async () => {
    let uploadFiles: any = [];

    if (addedFiles && addedFiles.length > 0) {
      uploadFiles = await handleFilesUpload(
        addedFiles,
        accessToken || '',
        `supplier-quality/non-conformances/${ncId}`,
        'supplier_hub',
      );
    }

    const body = {
      files: uploadFiles,
    };

    await postData(
      accessToken || '',
      `supplier-quality/non-conformances/${ncId}/evidence`,
      body as Record<string, string>,
    );

    toast.success('Evidence Uploaded successfully');
    onOpenChange(false);
    if (onSuccess) onSuccess();
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setAddedFiles(acceptedFiles);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: isMulti,
    accept: acceptFileTypes,
  });

  return (
    <>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>

        <div className="mt-2">
          <div className="text-base leading-6 font-medium text-dark-100 mb-2.5">
            Attach document
          </div>

          <div>
            <div
              className=" min-h-28 bg-white-100 border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2"
              {...getRootProps()}
            >
              {!(addedFiles?.length && addedFiles?.length > 0) && (
                <div className="text-sm font-medium leading-5 text-[#49474E]">
                  Upload or Drag and drop to upload your file
                </div>
              )}

              <input {...getInputProps()} />
              <div className="flex justify-center items-center flex-wrap gap-2">
                {addedFiles?.map((file, index) => (
                  <FileCard
                    key={index}
                    file={file}
                    setAddedFile={setAddedFiles}
                  />
                ))}
              </div>
              <TertiaryButton
                text={
                  isMulti
                    ? 'Add files'
                    : addedFiles?.length && addedFiles?.length > 0
                    ? 'Replace'
                    : 'Upload file'
                }
                size="small"
              />
            </div>
          </div>

          <div className="mt-5 flex justify-end gap-2">
            <PrimaryButton
              size="medium"
              text="Upload"
              style={{ width: '96px' }}
              isLoading={isPosting}
              onClick={handleConfirm}
              disabled={!addedFiles || addedFiles.length === 0}
            />
          </div>
        </div>
      </DialogContent>
    </>
  );
};

const FileCard = ({
  file,
  setAddedFile,
}: {
  file: File;
  setAddedFile: React.Dispatch<React.SetStateAction<File[] | null>>;
}) => {
  return (
    <div
      className="p-1.5 rounded-md bg-white-100 border border-white-300 flex items-center gap-2 relative group"
      onClick={(e) => e.stopPropagation()}
    >
      <div
        className="h-3 w-3 rounded-full bg-dark-300 items-center justify-center absolute -top-1.5 -right-1.5 hidden group-hover:flex cursor-pointer"
        onClick={() => {
          setAddedFile((pre) => {
            return pre ? pre?.filter((e) => e.name !== file.name) : null;
          });
        }}
      >
        <X className="h-2.5 w-2.5" color="#fff" />
      </div>
      <div className="h-8 w-8 flex items-center justify-center rounded bg-[#5A91FF]">
        <DocumentIcon height="20" width="20" color="#fff" />
      </div>
      <div>{file.name}</div>
    </div>
  );
};

export default EvidenceUploadModal;
