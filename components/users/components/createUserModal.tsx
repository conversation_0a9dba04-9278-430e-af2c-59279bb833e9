import React, { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
import { TCurrentUser } from '@/interfaces/user';
import { useRouter } from 'next/router';
import { Checkbox } from '../../common/checkbox';

export const createUserSchema = {
  first_name: z.string().nonempty('First Name is required'),
  last_name: z.string().nonempty('Last Name is required'),
  job_title: z.string().nonempty('Job Title is required'),
  email: z.string().nonempty('Email is required'),
};

interface IData extends Record<string, unknown> {
  first_name: string;
  last_name: string;
  is_active: boolean;
  job_title: string;
  email: string;
  company: string;
  is_admin: boolean;
}

const CreateUserModal = ({
  edit,
  userData,
  setOpenEdit,
  reFetch,
}: {
  edit?: boolean;
  userData?: TCurrentUser;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  reFetch?: () => void;
}) => {
  const { accessToken } = useAuthStore();
  const router = useRouter();

  const [isActive, setIsActive] = useState(true);

  const [data, setData] = useState<IData>({
    first_name: '',
    last_name: '',
    is_active: isActive,
    email: '',
    job_title: '',
    company: '',
    is_admin: false,
  });

  const { postData, response, isLoading: submitLoading, error } = usePost();
  const {
    putData,
    isLoading: editUserLoading,
    error: editUserError,
    response: editUserResponse,
  } = usePut();

  const { validationErrors, startValidation } = useValidators({
    schemas: createUserSchema,
    values: data,
  });

  const handleSubmit = async () => {
    const { hasValidationErrors } = await startValidation();
    const payload = {
      ...data,
      company: data.company || 'Null',
    };

    if (!hasValidationErrors) {
      if (edit && userData) {
        await putData(
          accessToken as string,
          `company/users/${userData?.id}`,
          payload,
        );
        if (setOpenEdit) setOpenEdit(false);
        reFetch && reFetch();
      } else {
        await postData(accessToken as string, 'company/users', payload);
      }
    } else {
    }
  };

  useEffect(() => {
    if (error) {
      console.log('error', error);
    }
    if (response) {
      router.push(`/setting/users/${(response as { id: string }).id}`);
    }
  }, [response, error]);

  useEffect(() => {
    if (edit && userData) {
      setData({
        first_name: userData?.first_name,
        last_name: userData?.last_name,
        is_active: userData?.is_active,
        job_title: userData?.job_title,
        email: userData?.email,
        company: userData?.company?.name,
        is_admin: userData?.is_admin,
      });
    }
  }, [userData, edit]);

  return (
    <DialogContent className="min-w-[55vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle>{edit ? 'Edit User' : 'Add a new User'} </DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="first_name"
              className="text-base font-medium leading-6 text-dark-100"
            >
              First Name<span className="text-[#F55D5D]">*</span>
            </Label>
            <Input
              placeholder="First Name"
              id="firstName"
              type="text"
              name="first_name"
              value={data?.first_name}
              required
              onChange={(e) =>
                setData((pre) => ({ ...pre, first_name: e.target.value }))
              }
              errorMsg={validationErrors?.first_name[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="lastName"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Last Name<span className="text-[#F55D5D]">*</span>
            </Label>
            <Input
              placeholder="Last Name"
              id="lastName"
              value={data?.last_name}
              onChange={(e) =>
                setData((pre) => ({ ...pre, last_name: e.target.value }))
              }
              errorMsg={validationErrors?.last_name[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="email"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Email<span className="text-[#F55D5D]">*</span>
            </Label>
            <Input
              placeholder="Email"
              disabled={edit}
              id="email"
              value={data?.email}
              onChange={(e) =>
                setData((pre) => ({ ...pre, email: e.target.value }))
              }
              errorMsg={
                validationErrors?.email?.[0] ||
                // Transforming error message "user already exists with email: (capital U)""
                (error as any)?.response?.data?.error.charAt(0).toUpperCase() +
                  (error as any)?.response?.data?.error
                    .slice(1)
                    .toLowerCase() ||
                ''
              }
            />
          </div>

          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="jobTitle"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Job Title<span className="text-[#F55D5D]">*</span>
            </Label>
            <Input
              placeholder="Job Title"
              id="jobTitle"
              value={data?.job_title}
              onChange={(e) =>
                setData((pre) => ({ ...pre, job_title: e.target.value }))
              }
              errorMsg={validationErrors?.job_title[0]}
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="company"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Company
            </Label>
            <Input
              placeholder="Company"
              id="company"
              value={data?.company}
              onChange={(e) =>
                setData((pre) => ({ ...pre, company: e.target.value }))
              }
            />
          </div>
        </div>

        <div className="flex justify-end items-center gap-8 mt-6">
          {!edit && (
            <div className="flex items-center gap-2">
              <Checkbox
                id="admin"
                checked={data?.is_admin}
                onCheckedChange={(checked) => {
                  setData((pre) => ({ ...pre, is_admin: !!checked }));
                }}
              />
              <label
                htmlFor="admin"
                className="text-base font-medium leading-6 text-dark-300"
              >
                Admin
              </label>
            </div>
          )}
          <div>
            <PrimaryButton
              size="medium"
              text="Submit"
              style={{ width: '96px' }}
              onClick={handleSubmit}
              isLoading={submitLoading || editUserLoading}
            />
          </div>
        </div>
      </div>
    </DialogContent>
  );
};

export default CreateUserModal;
