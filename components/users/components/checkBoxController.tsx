import { useEffect, useState } from 'react';

import { Checkbox } from '@/components/common/checkbox';
import { useAuthStore } from '@/globalProvider/authStore';
import { HubsRecords, TRole } from '@/interfaces/role';
import { showMenuItemForCompany } from '@/utils/showMenuItemForCompany';

interface IProps {
  data: HubsRecords;
  allChecked: boolean;
  roles: string[];
  updateRoles: (roles: string[]) => void;
}

const CheckBoxController = ({
  data,
  roles,
  allChecked,
  updateRoles,
}: IProps) => {
  const [checkedRoles, setCheckedRoles] = useState<Set<string>>(new Set(roles));
  const { user } = useAuthStore();

  useEffect(() => {
    const normalizedCheckedRoles = roles.reduce((acc, role) => {
      if (typeof role === 'string') {
        Object.keys(data).forEach((hub) => {
          data[hub].forEach((record: TRole) => {
            if (record.name === role) {
              acc.add(record.id);
            }
          });
        });
      } else {
        acc.add(role);
      }
      return acc;
    }, new Set<string>());

    setCheckedRoles(normalizedCheckedRoles);
  }, [roles, data]);

  const handleCheckboxChange = (recordId: string) => {
    setCheckedRoles((prevState) => {
      const newCheckedRoles = new Set(prevState);
      if (newCheckedRoles.has(recordId)) {
        newCheckedRoles.delete(recordId);
      } else {
        newCheckedRoles.add(recordId);
      }

      updateRoles(Array.from(newCheckedRoles));
      return newCheckedRoles;
    });
  };

  // specific ordering of hubs for display
  const orderedHubs = [
    'Standard Hub',
    'Document Hub',
    'People Hub',
    'Asset Hub',
    'Inventory Hub',
    'Improvement Hub',
    'Vendor Hub',
    'AuditHub',
    'Master Product',
    'Production Hub',
    'Risk Hub',
    'Supplier',
  ];

  // Filter to only include hubs that exist in the data, maintaining the defined order
  const hubsToRender = orderedHubs.filter((hub) => hub in data);

  // Format role name by removing the hub prefix for display
  const formatRoleName = (hubName: string, roleName: string): string => {
    if (hubName === 'Production Hub') {
      return roleName
        .replace(new RegExp(`^${hubName.replace(/\s/g, '')}`), '')
        .trim();
    }

    return roleName.replace(
      new RegExp(`^${hubName.replace(/\s?Hub$/, '').replace(/\s/g, '')}`),
      '',
    );
  };

  // Check if hub should be hidden based on company settings
  const shouldHideHub = (hubName: string): boolean => {
    return (
      (hubName === 'Production Hub' || hubName === 'Product Hub') &&
      !showMenuItemForCompany(user?.company?.id || '')
    );
  };

  return (
    <div className="grid grid-cols-3 justify-between gap-3 w-full">
      {hubsToRender.map((hub) =>
        shouldHideHub(hub) ? null : (
          <div
            key={hub}
            className="w-full bg-white-150 rounded-lg p-4 overflow-auto"
          >
            {/* Hub title */}
            <p className="text-base leading-6 font-medium pb-5">
              <span className="text-dark-300 py-2">
                {hub === 'Master Product' ? 'Product Hub' : hub}
              </span>
            </p>

            {/* Role checkboxes */}
            <ul className="flex gap-8 mb-0 pt-2">
              {data[hub].map((record: TRole) => (
                <li key={record.id}>
                  <div className="flex items-center gap-3">
                    <Checkbox
                      id={record.id}
                      value={record.id}
                      checked={allChecked || checkedRoles.has(record.id)}
                      onCheckedChange={
                        !allChecked
                          ? () => handleCheckboxChange(record.id)
                          : undefined
                      }
                      disabled={allChecked}
                    />
                    <label
                      htmlFor={record.id}
                      className="text-base font-medium leading-6 text-dark-300"
                    >
                      {formatRoleName(hub, record.name)}
                    </label>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ),
      )}
    </div>
  );
};

export default CheckBoxController;
