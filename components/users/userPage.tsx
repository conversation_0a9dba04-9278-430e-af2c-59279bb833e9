import { EditIcon } from 'lucide-react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import InfoCircle from '@/assets/outline/infoCircle';
import { Checkbox } from '@/components/common/checkbox';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import { HubsRecords } from '@/interfaces/role';
import { TCurrentUser } from '@/interfaces/user';

import Breadcrumb from '../common/breadcrumb';
import DeleteButton from '../common/button/deleteButton';
import SecondaryButton from '../common/button/secondaryButton';
import Loader from '../common/loader';
import ConfirmModal from '../common/modals/confirmModal';
import DeleteModal from '../common/modals/deleteModal';
import SideBarWrapper from '../common/sidebar/layout';
import CheckBoxController from './components/checkBoxController';
import CreateUserModal from './components/createUserModal';

const UserView = () => {
  const { accessToken, user } = useAuthStore();
  const { deleteData, isLoading: deleteDataLoading } = useDelete();
  const { setIsLoading } = useAuthStore((state) => state);

  const router = useRouter();
  const param = useParams();

  const [editUser, setEditUser] = useState<boolean>(false);

  const {
    data: userData,
    isLoading: userLoading,
    reFetch: userReFetch,
  } = useFetch<{
    record: TCurrentUser;
  }>(accessToken, `company/users/${param?.userId}`, {});

  const { postData } = usePost();
  const {
    putData: updateUserActive,
    isLoading: updateUserActiveLoading,
    response: updateUserActiveResponse,
    error: updateUserActiveError,
  } = usePut();

  const {
    putData: updateUserAdmin,
    isLoading: updateUserAdminLoading,
    response: updateUserAdminResponse,
    error: updateUserAdminError,
  } = usePut();

  const { data: roles, isLoading: rolesLoading } = useFetch<{
    records: HubsRecords;
  }>(accessToken, `roles`, {});

  const [isAdminChecked, setIsAdminChecked] = useState<boolean>(false);
  const [isActiveChecked, setIsActiveChecked] = useState<boolean>(false);
  const [rolesData, setRolesData] = useState<HubsRecords>({});

  const [adminDeactivateModal, setAdminDeactivateModal] =
    useState<boolean>(false);
  const [adminInactiveModal, setAdminInactiveModal] = useState<boolean>(false);

  const breadcrumbData = [
    {
      name: 'Setting',
      link: '/setting',
    },
    {
      name: 'User management',
      link: '/setting/users',
    },
    {
      name: `${
        userData?.record?.first_name + ' ' + userData?.record.last_name
      }`,
      link: '#',
    },
  ];

  useEffect(() => {
    if (!roles?.records) return;

    const sortedData: HubsRecords = { ...roles?.records };

    for (const hub in sortedData) {
      sortedData[hub].sort((a, b) => {
        const roleOrder = ['View', 'Editor', 'Admin'];
        const aType = roleOrder.findIndex((role) => a.name.includes(role));
        const bType = roleOrder.findIndex((role) => b.name.includes(role));
        return aType - bType;
      });
    }

    setRolesData(sortedData);
  }, [roles]);

  const updateRoles = async (newCheckedRoleIds: string[]) => {
    if (!param?.userId) {
      console.error('User ID is not available');
      return;
    }
    const payload = {
      user_id: param.userId,
      role_ids: newCheckedRoleIds,
    };

    await postData(accessToken as string, 'users/role', payload);
  };

  const handleActiveChange = () => {
    async function fetch() {
      await updateUserActive(
        accessToken as string,
        `company/users/${param?.userId}`,
        {
          is_active: !userData?.record.is_active,
        },
      );
    }
    fetch();
  };

  const handleAdminChange = () => {
    async function fetch() {
      await updateUserAdmin(
        accessToken as string,
        `company/users/${param?.userId}`,
        {
          is_admin: !userData?.record.is_admin,
        },
      );
    }
    fetch();
  };

  const handleUserUpdate = (isAdminUpdate: boolean) => {
    if (userData?.record.id !== user?.id) {
      userReFetch();
    } else {
      if (isAdminUpdate) {
        window.location.href = '/standard';
        // Redirect to document hub for marking not an admin
      } else {
        setAdminDeactivateModal(false);
        handleLogout(); // Logout user for self inactive
      }
    }
  };

  // User making changes for active checkbox
  useEffect(() => {
    if (updateUserActiveError) {
      alert('Oops! Something went wrong while updating user status');
    }
    if (updateUserActiveResponse) {
      setAdminInactiveModal(false);
      handleUserUpdate(false); // 'false' expect current user refetch should work for others
    }
  }, [updateUserActiveResponse, updateUserActiveError]);

  // User making changes for admin checkbox
  useEffect(() => {
    if (updateUserAdminError) {
      alert('Oops! Something went wrong while updating admin status');
    }
    if (updateUserAdminResponse) {
      setAdminDeactivateModal(false);
      handleUserUpdate(true); // 'true' expect current user refetch should work for others
    }
  }, [updateUserAdminResponse, updateUserAdminError]);

  const loadingData =
    userLoading ||
    rolesLoading ||
    updateUserAdminLoading ||
    updateUserActiveLoading;

  const handleLogout = async () => {
    setIsLoading(true);
    window.location.href = '/api/auth/logout';
  };

  return loadingData ? (
    <Loader className="h-[80vh]" />
  ) : (
    <SideBarWrapper>
      <div className="flex flex-col flex-1">
        <div className="my-5">
          <Breadcrumb data={breadcrumbData} />
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5">
            {userData?.record &&
              userData?.record.first_name + ' ' + userData?.record.last_name}
          </div>
        </div>
        <div className="flex-1">
          <div className="flex border border-grey-100 bg-white items-start justify-between p-2 rounded-lg">
            <div className="p-2 flex flex-col gap-3">
              <DetailsText
                label="First name"
                value={userData?.record.first_name || '-'}
              />
              <DetailsText
                label="Last name"
                value={userData?.record.last_name || '-'}
              />
              <DetailsText
                label="Email"
                value={userData?.record.email || '-'}
              />
            </div>
            <div className="p-2 flex flex-col gap-3">
              <DetailsText
                label="Company name"
                value={userData?.record.company.name || '-'}
              />
              <DetailsText
                label="Job title"
                value={userData?.record.job_title || '-'}
              />
              <DetailsText
                label="Active"
                value={userData?.record.is_active ? 'Yes' : 'No'}
              />
            </div>

            <div className="flex items-center gap-3">
              <div>
                <SecondaryButton
                  size="medium"
                  icon={<EditIcon color="#016366" className="h-5 w-5" />}
                  onClick={() => setEditUser(true)}
                  text="Edit"
                />

                <Dialog open={editUser} onOpenChange={setEditUser}>
                  {editUser && (
                    <CreateUserModal
                      edit={editUser}
                      userData={userData?.record}
                      setOpenEdit={setEditUser}
                      reFetch={userReFetch}
                    />
                  )}
                </Dialog>
              </div>

              <Dialog>
                <DialogTrigger asChild>
                  <DeleteButton />
                </DialogTrigger>
                <DeleteModal
                  title="Delete"
                  infoText="Are you sure, you want to delete this user?"
                  btnText="Delete"
                  btnLoading={deleteDataLoading}
                  onClick={() => {
                    async function fetch() {
                      await deleteData(
                        accessToken as string,
                        `company/users/${param?.userId}`,
                      );
                    }
                    fetch();
                    router.push('/setting/users');
                  }}
                >
                  <div className="p-2 border flex flex-col gap-4 border-white-300 bg-white-100 px-2.5 py-2 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium leading-5 text-grey-300">
                        ID
                      </div>
                      <div className="text-base font-medium leading-6 text-dark-300">
                        {userData?.record?.id}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium leading-5 text-grey-300">
                        Email
                      </div>
                      <div className="text-base font-medium leading-6 text-dark-300">
                        {userData?.record?.email}
                      </div>
                    </div>
                  </div>
                </DeleteModal>
              </Dialog>
            </div>
          </div>

          <div className="flex items-center justify-between my-6 mx-1">
            <p className="text-dark-300 font-medium text-lg">
              Roles and permissions
            </p>
            <div className="flex items-start justify-between gap-6">
              <p className="text-base leading-6 font-medium">
                <span className="text-grey-300">
                  Admin user will have all permissions{' '}
                </span>
              </p>

              {userData?.record?.is_admin ? (
                <Dialog
                  open={adminDeactivateModal}
                  onOpenChange={setAdminDeactivateModal}
                >
                  <DialogTrigger asChild>
                    <div className="flex items-center gap-2">
                      <Checkbox
                        id="admin"
                        checked={userData?.record.is_admin ? true : false}
                        onCheckedChange={(checked) => {
                          setAdminDeactivateModal(true);
                        }}
                      />
                      <label
                        htmlFor="admin"
                        className="text-base font-medium leading-6 text-dark-300"
                      >
                        Admin
                      </label>
                    </div>
                  </DialogTrigger>
                  <ConfirmModal
                    infoText=""
                    title={'Remove Admin'}
                    btnText={'Confirm'}
                    onClick={() => {
                      handleAdminChange();
                    }}
                    btnLoading={updateUserAdminLoading}
                    dialogClass="min-w-[28.5rem]"
                  >
                    {user?.id === param?.userId ? (
                      <div className="mt-2">
                        <p className="mb-4 text-base leading-6 font-medium">
                          Are you sure you want to remove admin access for
                          yourself?
                        </p>
                        <div className="flex items-center gap-2 text-sm font-medium leading-5 text-dark-300 p-3 rounded-lg bg-white-150 mb-2">
                          <div className="h-9 w-9 bg-[#91909A29] flex items-center justify-center rounded-full">
                            <InfoCircle />
                          </div>
                          You will lose access to the user management module
                          unless another admin gives you access
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-sm font-medium leading-5 text-dark-300 p-3 rounded-lg bg-white-150 mb-2">
                        <div className="h-9 w-9 bg-[#91909A29] flex items-center justify-center rounded-full">
                          <InfoCircle />
                        </div>
                        <p>
                          Are you sure you want to remove admin access for{' '}
                          <strong>
                            {userData?.record.first_name +
                              ' ' +
                              userData?.record.last_name}
                          </strong>
                          ?
                        </p>
                      </div>
                    )}
                  </ConfirmModal>
                </Dialog>
              ) : (
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="admin"
                    checked={userData?.record.is_admin ? true : false}
                    onCheckedChange={(checked) => {
                      handleAdminChange();
                    }}
                  />
                  <label
                    htmlFor="admin"
                    className="text-base font-medium leading-6 text-dark-300"
                  >
                    Admin
                  </label>
                </div>
              )}

              {userData?.record.is_active ? (
                <Dialog
                  open={adminInactiveModal}
                  onOpenChange={setAdminInactiveModal}
                >
                  <DialogTrigger asChild>
                    <div className="flex items-center gap-2">
                      <Checkbox
                        id="active"
                        checked={userData?.record.is_active ? true : false}
                        onCheckedChange={(checked) => {
                          setAdminInactiveModal(true);
                        }}
                      />
                      <label
                        htmlFor="active"
                        className="text-base font-medium leading-6 text-dark-300"
                      >
                        Active
                      </label>
                    </div>
                  </DialogTrigger>
                  <ConfirmModal
                    infoText=""
                    title={'Deactivate account'}
                    btnText={'Confirm'}
                    onClick={() => {
                      handleActiveChange();
                    }}
                    btnLoading={updateUserActiveLoading}
                    dialogClass="min-w-[28.5rem]"
                  >
                    {user?.id === param?.userId ? (
                      <div className="mt-2">
                        <p className="mb-4 text-base leading-6 font-medium">
                          Are you sure you want to deactivate yourself as a
                          user?
                        </p>
                        <div className="flex items-center gap-2 text-sm font-medium leading-5 text-dark-300 p-3 rounded-lg bg-white-150 mb-2">
                          <div className="h-9 w-9 bg-[#91909A29] flex items-center justify-center rounded-full">
                            <InfoCircle />
                          </div>
                          You will lose access to the platform unless another
                          admin gives you access.
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-sm font-medium leading-5 text-dark-300 p-3 rounded-lg bg-white-150 mb-2">
                        <div className="h-9 w-9 bg-[#91909A29] flex items-center justify-center rounded-full">
                          <InfoCircle />
                        </div>
                        <p>
                          Are you sure you want to deactivate the user -{' '}
                          <strong>
                            {userData?.record.first_name +
                              ' ' +
                              userData?.record.last_name}
                          </strong>
                          ?
                        </p>
                      </div>
                    )}
                  </ConfirmModal>
                </Dialog>
              ) : (
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="active"
                    checked={userData?.record.is_active ? true : false}
                    onCheckedChange={(checked) => {
                      handleActiveChange();
                    }}
                  />
                  <label
                    htmlFor="active"
                    className="text-base font-medium leading-6 text-dark-300"
                  >
                    Active
                  </label>
                </div>
              )}
            </div>
          </div>

          <div className="flex border border-grey-100 bg-white items-start justify-between p-3 rounded-lg">
            {roles && roles?.records && (
              <CheckBoxController
                allChecked={userData?.record?.is_admin || false}
                data={rolesData}
                roles={userData?.record.roles ?? []}
                updateRoles={updateRoles}
              />
            )}
          </div>
        </div>
      </div>
    </SideBarWrapper>
  );
};

const DetailsText = ({ label, value }: { label: string; value: string }) => {
  return (
    <div className="text-grey-300 font-medium text-base leading-6 flex items-center gap-2">
      {label}: <span className="text-dark-300">{value}</span>
    </div>
  );
};

export default UserView;
