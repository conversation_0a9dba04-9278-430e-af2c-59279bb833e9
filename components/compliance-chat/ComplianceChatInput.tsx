import React, { useState, useRef, useEffect } from 'react';
import { Paperclip, Send, X, FileText } from 'lucide-react';
import { cn } from '@/utils/styleUtils';
import PrimaryButton from '../common/button/primaryButton';
import { Textarea } from '../common/textarea';
// import { toast } from "sonner";

interface ComplianceChatInputProps {
  onSendMessage: (text: string, file?: File) => void;
  className?: string;
}

export function ComplianceChatInput({
  onSendMessage,
  className,
}: ComplianceChatInputProps) {
  const [message, setMessage] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSend = () => {
    if (message.trim() || selectedFile) {
      onSendMessage(message.trim(), selectedFile || undefined);
      setMessage('');
      setSelectedFile(null);

      // Focus back on textarea after sending
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      // Reset the input so the same file can be selected again
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleClearText = () => {
    setMessage('');
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  };

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height =
        textareaRef.current.scrollHeight + 'px';
    }
  }, [message]);

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-start">
        {!selectedFile && (
          <button
            type="button"
            onClick={() => fileInputRef.current?.click()}
            className="text-gray-400 hover:text-primary-500 transition-colors p-1 rounded-full hover:bg-gray-100"
            title="Attach document"
          >
            <Paperclip className="h-4 w-4" />
          </button>
        )}
      </div>

      {selectedFile && (
        <div className="flex items-center gap-2 px-3 py-2 bg-gray-50 rounded-md border border-gray-200">
          <FileText size={16} className="text-primary-500 flex-shrink-0" />
          <span className="text-xs text-gray-700 font-medium flex-1 truncate">
            {selectedFile.name}
          </span>
          <button
            onClick={() => setSelectedFile(null)}
            className="text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-200 p-1"
            title="Remove file"
          >
            <X className="h-3 w-3" />
          </button>
        </div>
      )}

      <div className="flex items-end gap-2">
        <Textarea
          ref={textareaRef}
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Ask me anything about compliance..."
          className={cn(
            'bg-white border-gray-200 rounded-lg resize-y w-full text-sm',
            className,
          )}
        />

        <PrimaryButton
          size="medium"
          onClick={handleSend}
          disabled={!message.trim() && !selectedFile}
          icon={<Send className="h-4 w-4" />}
          className={`text-white py-2.5 px-2.5 rounded-md ${
            !message.trim() && !selectedFile
              ? 'bg-gray-400 cursor-not-allowed opacity-50'
              : 'bg-primary-400 hover:bg-primary-500'
          }`}
          text=""
        />
      </div>

      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        onChange={handleFileChange}
        accept=".pdf,.doc,.docx,.txt"
      />
    </div>
  );
}
