import React from 'react';
import { SuggestedQuestion } from './types';
import { Button } from '../common/button/lightButton';

interface SuggestedQuestionsProps {
  questions: SuggestedQuestion[];
  onQuestionSelect: (text: string) => void;
}

export function SuggestedQuestions({
  questions,
  onQuestionSelect,
}: SuggestedQuestionsProps) {
  return (
    <div className="flex flex-wrap gap-2">
      {questions.map((question) => (
        <button
          key={question.id}
          onClick={() => onQuestionSelect(question.text)}
          className="font-medium px-2 py-1 bg-white border border-gray-300 rounded-lg text-xs hover:bg-gray-100"
        >
          {question.text}
        </button>
      ))}
    </div>
  );
}
