import React, { useState } from 'react';
// Drag functionality removed
import {
  <PERSON>et,
  <PERSON><PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  Sheet<PERSON>rigger,
  SheetClose,
} from '@/components/common/sheet';
// import { Button } from "@/components/ui/button";
import {
  Expand,
  FileSearch,
  Minimize,
  PhoneCall,
  Sparkle,
  Star,
  X,
} from 'lucide-react';
import { ComplianceChatMessages } from './ComplianceChatMessages';
import { ComplianceChatInput } from './ComplianceChatInput';
import { SuggestedQuestions } from './SuggestedQuestions';
import { useComplianceChat } from './hooks/useComplianceChat';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../common/dialog';
import PrimaryButton from '../common/button/primaryButton';
import {
  ResizableHandle,
  ResizablePanel,
  ResizablePanelGroup,
} from '../common/resizable';
import { useAuthStore } from '@/globalProvider/authStore';
import { AgentOctoIcon } from '@/assets/agentIcon';
import { Button } from '../common/button/lightButton';
// import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
// import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";

const suggestedQuestions = [
  // { id: '1', text: 'What are the key requirements of ISO 9001?' },
  // {
  //   id: '2',
  //   text: 'How do I implement an environmental management system under ISO 14001?',
  // },
  // { id: '3', text: 'What documentation is required for ISO 9001 compliance?' },
  // {
  //   id: '4',
  //   text: 'What are the internal audit requirements under ISO 14001?',
  // },
  // {
  //   id: '5',
  //   text: 'How do ISO 9001 and ISO 14001 differ in terms of risk management?',
  // },
  // {
  //   id: '6',
  //   text: 'What should I do in terms of compliance if I want to open a new plant in Hyderabad?',
  // },
  { id: '1', text: 'What are the latest compliance requirements?' },
  { id: '2', text: 'How do I submit a vendor approval request?' },
  { id: '3', text: 'What documents are required for ISO certification?' },
];

export function ComplianceChat() {
  const [isOpen, setIsOpen] = useState(false);
  const [showSupport, setShowSupport] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showTooltip, setShowTooltip] = useState(true);
  // Ref for the icon
  const iconRef = React.useRef<HTMLDivElement>(null);

  // Show tooltip only on first visit
  React.useEffect(() => {
    // Check if user has seen the tooltip before
    const hasSeenTooltip = localStorage.getItem('hasSeenOctoTooltip');

    if (!hasSeenTooltip) {
      // Show tooltip and set a flag in localStorage
      setShowTooltip(true);

      // Hide tooltip after 10 seconds
      const timer = setTimeout(() => {
        setShowTooltip(false);
        localStorage.setItem('hasSeenOctoTooltip', 'true');
      }, 10000);
      return () => clearTimeout(timer);
    } else {
      setShowTooltip(false);
    }
  }, []);
  const {
    messages,
    isAnalyzing,
    setIsAnalyzing,
    handleSendMessage,
    handleClearChat,
  } = useComplianceChat();

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setIsExpanded(false);
    }
  };

  // Get organization ID from auth store
  const user = useAuthStore((state) => state.user);

  const handleQuestionSelect = (text: string) => {
    handleSendMessage(text);
  };
  
  const handleUserMessage = (text: string, file?: File) => {
    handleSendMessage(text, file);
  };

  const SupportDialog = () => (
    <Dialog open={showSupport} onOpenChange={setShowSupport}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Contact Support</DialogTitle>
          <DialogDescription>
            Our support team is here to help you with compliance queries.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="space-y-2">
            <h4 className="font-medium">Support Hours</h4>
            <p className="text-sm text-gray-500">
              Monday - Friday: 9 AM - 6 PM EST
            </p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">Contact Information</h4>
            <p className="text-sm text-gray-500">
              Email: <EMAIL>
            </p>
            <p className="text-sm text-gray-500">Phone: +****************</p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">Response Time</h4>
            <p className="text-sm text-gray-500">
              We typically respond within 4 hours during business hours
            </p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Sheet open={isOpen} onOpenChange={handleOpenChange}>
        <div className="relative">
          <div
            ref={iconRef}
            className="flex items-center justify-center w-14 h-14 rounded-full bg-white text-primary-600 shadow-md hover:shadow-xl cursor-pointer transition-all duration-300 transform hover:scale-110 hover:shadow-primary-200 p-2"
            onClick={() => handleOpenChange(!isOpen)}
            onMouseEnter={() => setShowTooltip(false)}
          >
            <AgentOctoIcon size={40} className="w-full h-full" />
          </div>

          {/* Temporary tooltip that fades out - only shown on first visit */}
          {showTooltip && !isOpen && (
            <div className="absolute -top-10 right-0 bg-teal-600 text-white px-3 py-1 rounded-full text-sm font-medium shadow-sm whitespace-nowrap animate-fade-in-out">
              Ask Octo!
              <div className="absolute -bottom-1 right-4 w-3 h-3 bg-teal-600 transform rotate-45"></div>
            </div>
          )}
        </div>
        <SheetContent
          side={isExpanded ? 'center' : 'bottom-right'}
          style={
            !isExpanded
              ? {
                  position: 'fixed',
                  bottom: '4rem',
                  right: '1rem',
                  transform: 'none',
                  left: 'auto',
                  top: 'auto',
                  height: window.innerWidth < 768 ? '90vh' : '780px', // Responsive
                  maxHeight: '90vh', // Safety
                }
              : undefined
          }
          className={`p-0 shadow-xl bg-white transition-all duration-300 overflow-hidden ${
            isExpanded ? 'w-[100vw] h-[100vh]' : 'w-[350px] lg:w-[450px]'
          }`}
        >
          <ResizablePanelGroup
            direction="vertical"
            className={
              isExpanded
                ? 'h-full w-full min-h-[300px] min-w-[300px]'
                : 'w-full h-full min-w-[300px]'
            }
          >
            <ResizablePanel defaultSize={100} minSize={20}>
              <div className="h-full flex flex-col">
                {/* Header */}
                <div className="p-3 border-b bg-teal-600 flex items-center gap-3 rounded-t-lg shrink-0">
                  <div className="w-8 h-8 flex-shrink-0">
                    <AgentOctoIcon size={32} className="w-full h-full" />
                  </div>
                  <div className="flex flex-col justify-center text-left flex-1">
                    <h3 className="text-white text-sm font-semibold mb-0">
                      Octo
                    </h3>
                    <span className="text-xs font-medium text-teal-100">
                      Compliance Assistant
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => setIsExpanded(!isExpanded)}
                      className="text-white p-1 rounded-full hover:bg-teal-700"
                    >
                      {isExpanded ? (
                        <Minimize size={18} />
                      ) : (
                        <Expand size={18} />
                      )}
                    </button>
                    <SheetClose asChild>
                      <button className="text-white p-1 rounded-full hover:bg-teal-700">
                        <X size={18} />
                      </button>
                    </SheetClose>
                  </div>
                </div>

                {/* Content */}
                <div className="flex flex-col flex-1 min-h-0">
                  <div className="flex-1 overflow-y-auto pb-2 space-y-4 bg-white min-h-0">
                    <div className="bg-green-50 p-4 py-2 text-xs text-primary-400">
                      <p className="text-primary-400 text-sm font-bold flex gap-2 items-center">
                        <Sparkle width={16} height={16} />
                        Beta
                      </p>{' '}
                      <p className="pt-1 font-medium">
                        Octo may make mistakes. Please verify important
                        compliance information with your compliance team.
                      </p>
                    </div>
                    {/* Chat messages */}
                    {messages.length > 0 ? (
                      <ComplianceChatMessages
                        messages={messages}
                        isLoading={isAnalyzing}
                      />
                    ) : (
                      <div className="text-gray-700 text-sm">
                        {/* Hello! I&apos;m Octo, your compliance assistant. I can
                        help you with regulatory requirements, documentation,
                        and compliance-related queries for electronics
                        manufacturing. How may I assist you today? */}
                      </div>
                    )}
                  </div>

                  {/* Suggested questions above input */}
                  {messages.length === 0 && (
                    <div className="p-3 border-t flex-shrink-0 bg-gray-50">
                      <p className="text-xs pb-1 font-semibold text-gray-700 mb-2">
                        Suggested questions
                      </p>

                      <SuggestedQuestions
                        questions={suggestedQuestions}
                        onQuestionSelect={handleQuestionSelect}
                      />
                    </div>
                  )}

                  {/* Footer */}
                  <div className="p-3 border-t bg-white flex-shrink-0 rounded-b-lg">
                    {/* <div className="flex flex-wrap gap-2 mb-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex items-center gap-2 text-xs"
                        onClick={() => setShowSupport(true)}
                      >
                        <PhoneCall className="h-3 w-3" />
                        Contact Support
                      </Button>
                    </div> */}
                    <ComplianceChatInput
                      onSendMessage={handleUserMessage}
                      className="min-h-[40px]"
                    />
                  </div>
                </div>
              </div>
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel
              defaultSize={0}
              minSize={0}
              maxSize={0}
              className="hidden"
            >
              {/* Hidden panel to enable resizing */}
            </ResizablePanel>
          </ResizablePanelGroup>
        </SheetContent>
      </Sheet>
      <SupportDialog />
    </div>
  );
}
