
import { useState } from 'react';
import axios from 'axios';
import { ComplianceMessage } from '../types';
import { useAuthStore } from '@/globalProvider/authStore';

// Backend base URL – set NEXT_PUBLIC_CHAT_API_BASE in .env.local, else relative
const API_BASE = process.env.NEXT_PUBLIC_AI_URL || '';


export function useComplianceChat() {
  const [messages, setMessages] = useState<ComplianceMessage[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [conversationId, setConversationId] = useState<string | undefined>();
    const { user } = useAuthStore();
  

  const handleSendMessage = async (text: string, file?: File) => {
    if (!text.trim() && !file) return;

    const userMessage: ComplianceMessage = {
      id: Date.now().toString(),
      text: file ? `${text} (Uploaded document: ${file.name})` : text,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages((prev) => [...prev, userMessage]);
    setIsAnalyzing(true);

    try {
      // Create FormData to handle file upload
      const formData = new FormData();
      formData.append('question', text);
      
      if (conversationId) {
        formData.append('conversation_id', conversationId);
      }
      
      formData.append('organization_id', user?.company?.id || '');
      
      // Add document file if provided
      if (file) {
        formData.append('document', file);
      }

      const { data } = await axios.post(`${API_BASE}/ask`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      const { answer, conversation_id } = data;

      if (!conversationId) setConversationId(conversation_id);

      const assistantMessage: ComplianceMessage = {
        id: (Date.now() + 1).toString(),
        text: answer,
        sender: 'assistant',
        timestamp: new Date(),
      };

      setMessages((prev) => [...prev, assistantMessage]);
    } catch (err: any) {
      const errorMessage: ComplianceMessage = {
        id: (Date.now() + 1).toString(),
        text: "Sorry, something went wrong while contacting the server.",
        sender: 'assistant',
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleClearChat = async () => {
    if (conversationId) {
      try {
        await axios.delete(`${API_BASE}/history/${conversationId}`);
      } catch (_) {
        // ignore errors in clearing history on backend
      }
    }
    setMessages([]);
    setConversationId(undefined);
  };

  return {
    messages,
    isAnalyzing,
    setIsAnalyzing,
    handleSendMessage,
    conversationId,
    handleClearChat
  };
}
