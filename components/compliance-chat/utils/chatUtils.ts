
export function generateResponse(input: string): string {
  if (input.toLowerCase().includes('iso')) {
    return "Based on the current documentation, this appears to align with ISO 9001:2015 requirements, specifically regarding document control and quality management processes. Would you like me to analyze specific sections?";
  }
  if (input.toLowerCase().includes('review')) {
    return "The next compliance review is scheduled for June 15th, 2024. Would you like me to send you a reminder or check the full review schedule?";
  }
  if (input.toLowerCase().includes('non-conformance')) {
    return "To correct a minor non-conformance: 1) Document the issue 2) Analyze root cause 3) Implement corrective action 4) Verify effectiveness. Would you like a detailed procedure template?";
  }
  return "I understand your question about compliance. To provide the most accurate information, could you provide more specific details about your concern?";
}
