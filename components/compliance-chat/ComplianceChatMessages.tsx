import React, { useEffect, useRef } from 'react';
import { ComplianceMessage } from './types';
import { format } from 'date-fns';
import { FileText } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { AgentOctoIcon } from '@/assets/agentIcon';

interface ComplianceChatMessagesProps {
  messages: ComplianceMessage[];
  isLoading?: boolean;
}

const components: { [key: string]: React.ElementType } = {
  h1: (props) => <h1 className="text-xl font-bold my-2" {...props} />,
  h2: (props) => <h2 className="text-lg font-bold my-2" {...props} />,
  h3: (props) => <h3 className="text-md font-bold my-2" {...props} />,
  ul: (props) => <ul className="list-disc list-outside pl-6 ml-2 text-sm" {...props} />,
  ol: (props) => <ol className="list-decimal list-outside pl-6 ml-2 text-sm" {...props} />,
  li: (props) => <li className="mb-1 pl-1 text-sm" {...props} />,
  p: (props) => <p className="mb-1 text-sm font-normal" {...props} />,
};

export function ComplianceChatMessages({
  messages,
  isLoading = false,
}: ComplianceChatMessagesProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to the bottom when messages change or when loading state changes
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isLoading]);

  return (
    <div className="space-y-6">
      {messages.map((message) => {
        const isUser = message.sender === 'user';
        return (
          <div
            key={message.id}
            className={`flex items-start gap-2 mx-3 ${
              isUser ? 'justify-end' : 'justify-start'
            }`}
          >
            {/* Avatar */}
            {!isUser && (
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-teal-600 flex items-center justify-center text-white font-bold text-xs">
                <AgentOctoIcon size={16} />
              </div>
            )}
            {isUser && (
              <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center text-white font-bold text-xs order-2">
                You
              </div>
            )}

            {/* Message bubble */}
            <div
              className={`max-w-[85%] rounded-lg px-3 py-2 ${
                isUser
                  ? 'bg-teal-600 text-white order-1'
                  : 'bg-gray-100 text-gray-800'
              }`}
            >
              <ReactMarkdown
                remarkPlugins={[remarkGfm]}
                components={components as any}
              >
                {message.text}
              </ReactMarkdown>

              {message.attachment && (
                <div className="mt-2 flex items-center gap-2 text-xs">
                  <FileText className="h-4 w-4" />
                  <span>{message.attachment.name}</span>
                </div>
              )}

              <div className="text-xs mt-2 opacity-70 text-right">
                {format(message.timestamp, 'HH:mm')}
              </div>
            </div>
          </div>
        );
      })}

      {/* Loading indicator */}
      {isLoading && (
        <div className="flex items-start gap-2 mx-3">
          {/* Octo avatar */}
          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-teal-600 flex items-center justify-center text-white font-bold text-xs">
            <AgentOctoIcon size={16} />
          </div>
          <div className="max-w-[85%] rounded-lg p-4 bg-gray-100 text-gray-800">
            <div className="flex space-x-1 mb-1">
              <div
                className="h-2 w-2 bg-teal-600 rounded-full animate-bounce"
                style={{ animationDelay: '0ms' }}
              ></div>
              <div
                className="h-2 w-2 bg-teal-600 rounded-full animate-bounce"
                style={{ animationDelay: '150ms' }}
              ></div>
              <div
                className="h-2 w-2 bg-teal-600 rounded-full animate-bounce"
                style={{ animationDelay: '300ms' }}
              ></div>
            </div>
          </div>
        </div>
      )}

      {/* Invisible element for scrolling to */}
      <div ref={messagesEndRef} />
    </div>
  );
}
