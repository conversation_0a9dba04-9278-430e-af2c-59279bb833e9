import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useFetch from '@/hooks/useFetch';
import { toast } from 'react-toastify';
import { Tooltip, TooltipContent, TooltipTrigger } from '../common/tooltip';
import InfoCircle from '@/assets/outline/infoCircle';
import Link from 'next/link';

interface Category {
  id: string;
  name: string;
}

interface CategoryResponse {
  records: Category[];
}

interface CreateSkillsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  skillData?: {
    id?: string;
    name: string;
    description: string;
    category: {
      id: string;
      name: string;
    };
  };
  edit?: boolean;
  reFetch: () => void;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  skillId?: string;
}

const CreateSkillsModal: React.FC<CreateSkillsModalProps> = ({
  open,
  onOpenChange,
  skillData,
  edit,
  reFetch,
  setOpenEdit,
  skillId,
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { postData, response: postResponse, isLoading } = usePost();
  const { putData, response: putResponse, isLoading: putLoading } = usePut();

  const { data: categories } = useFetch<CategoryResponse>(
    accessToken,
    'training/categories',
    {},
  );

  const [data, setData] = useState({
    name: '',
    description: '',
    category_id: '',
  });

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSave = async () => {
    const payload = {
      name: data.name,
      description: data.description,
      category_id: data.category_id,
    };

    if (edit && skillData) {
      await putData(
        accessToken as string,
        `employee/skills/${skillId}`,
        payload,
      );
    } else {
      await postData(accessToken as string, 'employee/skills', payload);
    }
  };

  useEffect(() => {
    if (putResponse) {
      onOpenChange(false);
      reFetch();
      toast.success('Skill updated successfully');
    }
  }, [putResponse]);

  useEffect(() => {
    if (postResponse) {
      onOpenChange(false);
      reFetch();
      toast.success('Skill created successfully');
    }
  }, [postResponse]);

  useEffect(() => {
    if (edit && skillData) {
      setData({
        name: skillData?.name,
        description: skillData?.description,
        category_id: skillData?.category?.id,
      });
    }
  }, [skillData, edit]);

  useEffect(() => {
    if (open && !edit) {
      // Reset form when opening for create
      setData({
        name: '',
        description: '',
        category_id: '',
      });
    }
  }, [open, edit]);

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{edit ? 'Edit' : 'Create'} Skill</DialogTitle>
        </DialogHeader>

        <div className=" mt-2">
          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="skill_name"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Skill Name
            </Label>
            <Input
              id="skill_name"
              placeholder="Enter skill name"
              value={data.name}
              onChange={(e) =>
                setData((pre) => ({ ...pre, name: e.target.value }))
              }
            />
          </div>

          <div className="flex flex-col mt-4">
            <div className="flex items-center gap-2 flex-nowrap">
              <Label
                htmlFor="category"
                className="text-base font-medium leading-6 text-dark-100 mb-3"
              >
                Category{' '}
              </Label>
              {categories?.records?.length === 0 && (
                <Tooltip>
                  <TooltipTrigger>
                    <div
                      className="mb-2 w-10 h-10 flex items-center justify-centerrounded-full cursor-pointer"
                      onClick={() => console.log('first')}
                    >
                      <InfoCircle />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-sm text-dark-300">
                      You can create category from{' '}
                      <Link
                        href="/people/training-adminstration"
                        className="text-primary-500"
                      >
                        Training Administration
                      </Link>
                    </div>
                  </TooltipContent>
                </Tooltip>
              )}
            </div>
            <Select
              value={data.category_id}
              onValueChange={(value) =>
                setData((pre) => ({ ...pre, category_id: value }))
              }
            >
              <SelectTrigger id="category">
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                {categories?.records?.map((cat) => (
                  <SelectItem key={cat.id} value={cat.id}>
                    {cat.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="flex flex-col gap-2.5 mt-4">
            <Label
              htmlFor="description"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Enter description"
              rows={3}
              value={data.description}
              onChange={(e) =>
                setData((pre) => ({ ...pre, description: e.target.value }))
              }
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t mt-6">
          <SecondaryButton text="Cancel" size="medium" onClick={handleClose} />
          <PrimaryButton
            text="Save"
            size="medium"
            disabled={
              isLoading ||
              putLoading ||
              data.name === '' ||
              data.description === '' ||
              data.category_id === ''
            }
            onClick={handleSave}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateSkillsModal;
