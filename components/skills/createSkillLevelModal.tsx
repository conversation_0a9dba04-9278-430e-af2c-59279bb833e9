import React, { useEffect, useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import { toast } from 'react-toastify';

interface CreateSkillLevelModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  trainingId?: string;
  trainingData?: unknown;
  refetch?: () => void;
  skillData?: {
    id?: string;
    name: string;
    description: string;
    requirements: string[];
  };
  edit?: boolean;
  reFetch: () => void;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  levelId?: string;
}

const CreateSkillLevelModal: React.FC<CreateSkillLevelModalProps> = ({
  open,
  onOpenChange,
  skillData,
  edit,
  reFetch,
  setOpenEdit,
  levelId,
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const { postData, response: postResponse, isLoading } = usePost();
  const { putData, response: putResponse, isLoading: putLoading } = usePut();

  const [data, setData] = useState({
    name: '',
    description: '',
    requirements: '', // textarea stores newline-separated requirements
  });

  const handleClose = () => {
    onOpenChange(false);
  };

  const handleSave = async () => {
    const payload = {
      name: data.name,
      description: data.description,
      requirements: data.requirements
        .split('\n')
        .map((r) => r.trim())
        .filter(Boolean),
    };

    if (edit && skillData) {
      await putData(
        accessToken as string,
        `employee/skill-levels/${levelId}`,
        payload,
      );
    } else {
      await postData(accessToken as string, 'employee/skill-levels', payload);
    }
  };

  useEffect(() => {
    if (putResponse) {
      onOpenChange(false);
      reFetch();
      toast.success('Skill level updated successfully');
    }
  }, [putResponse]);

  useEffect(() => {
    if (postResponse) {
      onOpenChange(false);
      reFetch();
      toast.success('Skill level created successfully');
    }
  }, [postResponse]);

  useEffect(() => {
    if (edit && skillData) {
      setData({
        name: skillData?.name,
        description: skillData?.description,
        requirements: skillData?.requirements?.join('\n') || '', // join array into string
      });
    }
  }, [skillData, edit]);

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{edit ? 'Edit' : 'Create'} Skill Level</DialogTitle>
        </DialogHeader>

        <div className="space-y-5 mt-2">
          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="level_name"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Level Name
            </Label>
            <Input
              id="level_name"
              placeholder="Enter level name (e.g., Intermediate)"
              value={data.name}
              onChange={(e) =>
                setData((pre) => ({ ...pre, name: e.target.value }))
              }
            />
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="description"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Description
            </Label>
            <Textarea
              id="description"
              placeholder="Enter description"
              rows={3}
              value={data.description}
              onChange={(e) =>
                setData((pre) => ({ ...pre, description: e.target.value }))
              }
            />
          </div>

          <div className="flex flex-col gap-2.5">
            <Label
              htmlFor="requirements"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Pre-requisites
            </Label>
            <Textarea
              id="requirements"
              placeholder="Enter each requirement on a new line"
              rows={4}
              value={data.requirements}
              onChange={(e) =>
                setData((pre) => ({ ...pre, requirements: e.target.value }))
              }
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3 pt-6 border-t mt-6">
          <SecondaryButton text="Cancel" size="medium" onClick={handleClose} />
          <PrimaryButton
            text="Save"
            size="medium"
            disabled={
              isLoading ||
              putLoading ||
              data.name === '' ||
              data.description === ''
            }
            onClick={handleSave}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateSkillLevelModal;
