import React from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import { IAttachment } from '@/interfaces/misc';

interface CloseNcModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ncId: string;
  addedFiles?: IAttachment[];
  onSuccess?: () => void;
}

const CloseNcModal: React.FC<CloseNcModalProps> = ({
  open,
  onOpenChange,
  ncId,
  addedFiles = [],
  onSuccess,
}) => {
  const accessToken = useAuthStore((s) => s.accessToken);
  const [comments, setComments] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);

  const handleConfirm = async () => {
    if (!ncId) return;
    setIsLoading(true);
    try {
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const mockUser =
        typeof window !== 'undefined'
          ? localStorage.getItem('x-mock-user')
          : null;
      const orgId =
        typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;

      const res = await axios.request({
        method: 'POST',
        url: `${baseUrl}/${productVersion}/supplier-quality/non-conformances/${ncId}/close`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          ...(mockUser ? { 'x-mock-user': mockUser } : {}),
          ...(orgId ? { 'x-org': orgId } : {}),
        },
        data: {
          comments,
          evidence_file_paths: addedFiles.map((f) => ({
            file_path: f.file_path,
            file_extension: f.file_extension,
          })),
        },
      });
      if (res.status === 200) {
        toast.success('NC closed');
        setComments('');
        onOpenChange(false);
        if (onSuccess) onSuccess();
      }
    } catch (err: any) {
      toast.error(err?.response?.data?.detail || 'Failed to close NC');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        onOpenChange(isOpen);
        if (!isOpen) setComments('');
      }}
    >
      <div className="w-[520px] bg-white rounded-lg p-6 shadow-xl">
        <h3 className="text-lg font-semibold mb-2">Close NC</h3>
        <p className="text-sm text-gray-600 mb-4">
          Add any closing comments (optional). Evidence can be uploaded in the
          Attachments section above before closing.
        </p>
        <div className="space-y-2">
          <Label htmlFor="close_comments">Closing comments (optional)</Label>
          <Textarea
            id="close_comments"
            rows={3}
            value={comments}
            onChange={(e) => setComments(e.target.value)}
            placeholder="Comments"
          />
        </div>
        <div className="mt-5 flex justify-end gap-2">
          <button
            className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </button>
          <PrimaryButton
            text="Close NC"
            onClick={handleConfirm}
            isLoading={isLoading}
          />
        </div>
      </div>
    </Dialog>
  );
};

export default CloseNcModal;

