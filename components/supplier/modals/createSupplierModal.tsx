import { LoaderCircle, Plus, Trash2 } from 'lucide-react';
import React, { useEffect, useMemo, useState } from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  IOption as IMultiOption,
  ReactSelectMulti,
} from '@/components/common/multiSelectInput';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import {
  RiskTier,
  SupplierCreatePayload,
  SupplierDetail,
  SupplierListItem,
  SupplierStatus,
} from '@/interfaces/supplier';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import axios from 'axios';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';

const statusOptions: { label: string; value: SupplierStatus }[] = [
  { label: 'Draft', value: 'draft' },
  { label: 'Prospect', value: 'prospect' },
  { label: 'Active', value: 'active' },
  { label: 'Inactive', value: 'inactive' },
  { label: 'Blocked', value: 'blocked' },
];

const riskTierOptions: { label: string; value: RiskTier }[] = [
  { label: 'Low', value: 'low' },
  { label: 'Medium', value: 'medium' },
  { label: 'High', value: 'high' },
];

const toMultiSelect = (recs?: { id: string; name: string }[]): IMultiOption[] =>
  (recs || []).map((r) => ({ value: r.id, label: r.name }));

const CreateSupplierModal = ({
  edit,
  supplier,
  setOpen,
  reFetch,
  setSelectedId,
}: {
  edit?: boolean;
  supplier?: SupplierListItem;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  reFetch?: () => void;
  setSelectedId?: React.Dispatch<React.SetStateAction<string | null>>;
}) => {
  const accessToken = useAuthStore((s) => s.accessToken);
  const { data: supplierTypesData } = useFetch<{
    records: {
      id: string;
      name: string;
      categories: { id: string; name: string }[];
    }[];
  }>(accessToken, 'supplier-types');

  const [filteredCategories, setFilteredCategories] = useState<{
    records: { id: string; name: string; description?: string }[];
  }>({ records: [] });

  const {
    postData,
    response: postRes,
    error: postErr,
    isLoading: isPost,
  } = usePost<{ id: string }, SupplierCreatePayload>();
  const {
    putData,
    response: putRes,
    error: putErr,
    isLoading: isPut,
  } = usePut<{ id: string }, Partial<SupplierCreatePayload>>();
  const {
    postData: postCategoriesByTypes,
    response: categoriesByTypesRes,
    error: categoriesByTypesErr,
    isLoading: isCategoriesByTypesLoading,
  } = usePost<
    {
      records: { id: string; name: string; description?: string }[];
    },
    { supplier_type_ids: string[] }
  >();

  const [form, setForm] = useState<SupplierCreatePayload>({
    legal_name: '',
    website: '',
    supplier_type_ids: [],
    status: 'draft',
    risk_tier: 'medium',
    criticality_level: 3,
    notes: '',
    primary_contact: { name: '', email: '', phone: '' },
    additional_contacts: [],
    primary_address: {
      country: '',
      address_line1: '',
      address_line2: '',
      city: '',
      state_province: '',
      postal_code: '',
    },
    additional_addresses: [],
    category_ids: [],
  });
  const [isLoading, setIsLoading] = useState(false);

  // Fetch categories by selected supplier types
  useEffect(() => {
    const selectedTypeIds = form.supplier_type_ids || [];
    if (selectedTypeIds.length > 0 && accessToken) {
      postCategoriesByTypes(accessToken, 'supply-categories/by-types', {
        supplier_type_ids: selectedTypeIds,
      });
    } else {
      // Clear categories when no supplier types are selected
      setFilteredCategories({ records: [] });
      // Clear selected categories from form
      setForm((s) => ({ ...s, category_ids: [] }));
    }
  }, [form.supplier_type_ids, accessToken]);

  // Handle categories by types response
  useEffect(() => {
    if (categoriesByTypesRes && !categoriesByTypesErr) {
      setFilteredCategories(categoriesByTypesRes);
    }
  }, [categoriesByTypesRes, categoriesByTypesErr]);

  // Reset form and pre-fill on edit
  useEffect(() => {
    if (edit && supplier) {
      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };
      setIsLoading(true);
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      axios
        .get(`${baseUrl}/${productVersion}/suppliers/${supplier.id}`, config)
        .then((res: any) => {
          setIsLoading(false);
          const supplierDetailData = res.data;
          const primaryContact = supplierDetailData.contacts?.find(
            (c: any) => c.is_primary,
          );
          const additionalContacts =
            supplierDetailData.contacts?.filter((c: any) => !c.is_primary) ||
            [];
          const primaryAddress = supplierDetailData.addresses?.find(
            (a: any) => a.is_primary,
          );
          const additionalAddresses =
            supplierDetailData.addresses?.filter((a: any) => !a.is_primary) ||
            [];

          setForm({
            legal_name: supplierDetailData.legal_name,
            website: supplierDetailData.website || '',
            supplier_type_ids: (supplierDetailData.types || []).map(
              (t: any) => t.id,
            ),
            status: supplierDetailData.status,
            risk_tier: supplierDetailData.risk_tier,
            criticality_level: supplierDetailData.criticality_level,
            notes: supplierDetailData.notes || '',
            primary_contact: {
              name: primaryContact?.name || '',
              email: primaryContact?.email || '',
              phone: primaryContact?.phone || '',
            },
            additional_contacts: additionalContacts.map((c: any) => ({
              name: c.name,
              email: c.email || '',
              phone: c.phone || '',
            })),
            primary_address: {
              country: primaryAddress?.country || '',
              address_line1: primaryAddress?.address_line1 || '',
              address_line2: primaryAddress?.address_line2 || '',
              city: primaryAddress?.city || '',
              state_province: primaryAddress?.state_province || '',
              postal_code: primaryAddress?.postal_code || '',
            },
            additional_addresses: additionalAddresses.map((a: any) => ({
              country: a.country,
              address_line1: a.address_line1,
              address_line2: a.address_line2 || '',
              city: a.city,
              state_province: a.state_province || '',
              postal_code: a.postal_code || '',
            })),
            category_ids: (supplierDetailData.categories || []).map(
              (c: any) => c.id,
            ),
          });
        })
        .catch((err: any) => {
          console.log(err);
          setIsLoading(false);
        });
    } else if (!edit) {
      // Reset form for create mode
      setForm({
        legal_name: '',
        website: '',
        supplier_type_ids: [],
        status: 'draft',
        risk_tier: 'medium',
        criticality_level: 3,
        notes: '',
        primary_contact: { name: '', email: '', phone: '' },
        additional_contacts: [],
        primary_address: {
          country: '',
          address_line1: '',
          address_line2: '',
          city: '',
          state_province: '',
          postal_code: '',
        },
        additional_addresses: [],
        category_ids: [],
      });
    }
  }, [edit, supplier]);

  // Handle API completion
  useEffect(() => {
    if (postRes || putRes) {
      setSelectedId && setSelectedId(null);
      setOpen(false);
      reFetch?.();
    }
  }, [postRes, putRes, setOpen, reFetch]);

  // Helper functions for managing additional contacts
  const addAdditionalContact = () => {
    setForm((s) => ({
      ...s,
      additional_contacts: [
        ...(s.additional_contacts || []),
        { name: '', email: '', phone: '' },
      ],
    }));
  };

  const removeAdditionalContact = (index: number) => {
    setForm((s) => ({
      ...s,
      additional_contacts:
        s.additional_contacts?.filter((_, i) => i !== index) || [],
    }));
  };

  const updateAdditionalContact = (
    index: number,
    field: string,
    value: string,
  ) => {
    setForm((s) => ({
      ...s,
      additional_contacts:
        s.additional_contacts?.map((contact, i) =>
          i === index ? { ...contact, [field]: value } : contact,
        ) || [],
    }));
  };

  // Helper functions for managing additional addresses
  const addAdditionalAddress = () => {
    setForm((s) => ({
      ...s,
      additional_addresses: [
        ...(s.additional_addresses || []),
        {
          country: '',
          address_line1: '',
          address_line2: '',
          city: '',
          state_province: '',
          postal_code: '',
        },
      ],
    }));
  };

  const removeAdditionalAddress = (index: number) => {
    setForm((s) => ({
      ...s,
      additional_addresses:
        s.additional_addresses?.filter((_, i) => i !== index) || [],
    }));
  };

  const updateAdditionalAddress = (
    index: number,
    field: string,
    value: string,
  ) => {
    setForm((s) => ({
      ...s,
      additional_addresses:
        s.additional_addresses?.map((address, i) =>
          i === index ? { ...address, [field]: value } : address,
        ) || [],
    }));
  };

  const onSubmit = () => {
    const payload = { ...form };
    // Required field validation per spec
    if (!payload.legal_name?.trim()) return;
    if (!payload.supplier_type_ids || payload.supplier_type_ids.length === 0)
      return;

    // Contacts: primary name and email required; any additional contact present must have both name and email
    if (!payload.primary_contact?.name?.trim()) return;
    if (!payload.primary_contact?.email?.trim()) return;
    if (
      (payload.additional_contacts || []).some(
        (c) => !c.name?.trim() || !c.email?.trim(),
      )
    )
      return;

    // Address: country, address_line1, city, state_province, postal_code required
    const addr = payload.primary_address;
    if (
      !addr?.country?.trim() ||
      !addr?.address_line1?.trim() ||
      !addr?.city?.trim() ||
      !addr?.state_province?.trim() ||
      !addr?.postal_code?.trim()
    )
      return;

    // Risk Tier required when status is not Draft
    if (payload.status !== 'draft' && !payload.risk_tier) return;

    if (edit && supplier?.id) {
      putData(accessToken as string, `suppliers/${supplier.id}`, payload);
    } else {
      // On create, backend forces Draft status; we send supplier_type_ids and category_ids only
      const { status, ...rest } = payload; // exclude status
      postData(
        accessToken as string,
        'suppliers',
        rest as SupplierCreatePayload,
      );
    }
  };

  const categoryOptions = useMemo(
    () => toMultiSelect(filteredCategories.records),
    [filteredCategories],
  );

  return (
    <DialogContent className="min-w-[65vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle>{edit ? 'Edit' : 'Create'} Supplier</DialogTitle>
      </DialogHeader>
      {isLoading ? (
        <>
          <LoaderCircle />
        </>
      ) : (
        <div className="mt-2 space-y-6">
          {/* Basic Details */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Basic Details</h3>
            <div className="grid grid-cols-2 gap-5">
              <div className="flex flex-col gap-2.5">
                <Label
                  htmlFor="legal_name"
                  className="text-base font-medium leading-6 text-dark-100"
                >
                  Legal Name<span className="text-red-200">*</span>
                </Label>
                <Input
                  id="legal_name"
                  value={form.legal_name}
                  onChange={(e) =>
                    setForm((s) => ({ ...s, legal_name: e.target.value }))
                  }
                  placeholder="Enter legal name"
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label
                  htmlFor="website"
                  className="text-base font-medium leading-6 text-dark-100"
                >
                  Website
                </Label>
                <Input
                  id="website"
                  value={form.website || ''}
                  onChange={(e) =>
                    setForm((s) => ({ ...s, website: e.target.value }))
                  }
                  placeholder="Enter website"
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Supplier Type<span className="text-red-200">*</span>
                </Label>
                <ReactSelectMulti
                  options={(supplierTypesData?.records || []).map((t) => ({
                    value: t.id,
                    label: t.name,
                  }))}
                  value={
                    supplierTypesData?.records
                      ? (supplierTypesData.records || [])
                          .filter((t) =>
                            (form.supplier_type_ids || []).includes(t.id),
                          )
                          .map((t) => ({ value: t.id, label: t.name }))
                      : []
                  }
                  onChange={(opts) =>
                    setForm((s) => ({
                      ...s,
                      supplier_type_ids: (opts || []).map((o) => o.value),
                    }))
                  }
                  placeholder="Select supplier types"
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Supply Category
                </Label>
                <ReactSelectMulti
                  options={categoryOptions}
                  value={categoryOptions.filter((o) =>
                    form.category_ids?.includes(o.value),
                  )}
                  onChange={(opts) =>
                    setForm((s) => ({
                      ...s,
                      category_ids: (opts || []).map((o) => o.value),
                    }))
                  }
                  placeholder={
                    form.supplier_type_ids?.length
                      ? 'Select supply categories'
                      : 'Select supplier types first'
                  }
                  isDisabled={!form.supplier_type_ids?.length}
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Status<span className="text-red-200">*</span>
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <Select
                      value={form.status}
                      onValueChange={(v) =>
                        setForm((s) => ({ ...s, status: v as SupplierStatus }))
                      }
                      disabled={!edit}
                    >
                      <SelectTrigger id="status">
                        <SelectValue placeholder="Select status" />
                      </SelectTrigger>
                      <SelectContent>
                        {statusOptions.map((o) => (
                          <SelectItem key={o.value} value={o.value}>
                            {o.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </TooltipTrigger>
                  <TooltipContent>
                    You can edit the status after creating a supplier. But
                    first, you can only create it with Draft state.
                  </TooltipContent>
                </Tooltip>
              </div>
            </div>
          </div>

          {/* Contacts */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Contacts</h3>
            <div className="grid grid-cols-3 gap-5">
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Primary Contact Name<span className="text-red-200">*</span>
                </Label>
                <Input
                  value={form.primary_contact.name}
                  onChange={(e) =>
                    setForm((s) => ({
                      ...s,
                      primary_contact: {
                        ...s.primary_contact,
                        name: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter name"
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Primary Contact Email<span className="text-red-200">*</span>
                </Label>
                <Input
                  value={form.primary_contact.email || ''}
                  onChange={(e) =>
                    setForm((s) => ({
                      ...s,
                      primary_contact: {
                        ...s.primary_contact,
                        email: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter email"
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Phone
                </Label>
                <Input
                  value={form.primary_contact.phone || ''}
                  onChange={(e) =>
                    setForm((s) => ({
                      ...s,
                      primary_contact: {
                        ...s.primary_contact,
                        phone: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter phone"
                />
              </div>
            </div>

            {/* Additional Contacts */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-700">
                  Additional Contacts
                </h4>
                <SecondaryButton
                  text="Add Contact"
                  size="small"
                  icon={<Plus className="w-4 h-4" />}
                  onClick={addAdditionalContact}
                />
              </div>

              {form.additional_contacts &&
                form.additional_contacts.length > 0 && (
                  <div className="space-y-4">
                    {form.additional_contacts.map((contact, index) => (
                      <div
                        key={index}
                        className="border border-gray-200 rounded-lg p-4"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-medium text-gray-600">
                            Contact {index + 1}
                          </span>
                          <button
                            type="button"
                            onClick={() => removeAdditionalContact(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                        <div className="grid grid-cols-3 gap-4">
                          <div className="flex flex-col gap-2">
                            <Label className="text-sm font-medium text-gray-700">
                              Name<span className="text-red-200">*</span>
                            </Label>
                            <Input
                              value={contact.name}
                              onChange={(e) =>
                                updateAdditionalContact(
                                  index,
                                  'name',
                                  e.target.value,
                                )
                              }
                              placeholder="Enter name"
                            />
                          </div>
                          <div className="flex flex-col gap-2">
                            <Label className="text-sm font-medium text-gray-700">
                              Email<span className="text-red-200">*</span>
                            </Label>
                            <Input
                              value={contact.email || ''}
                              onChange={(e) =>
                                updateAdditionalContact(
                                  index,
                                  'email',
                                  e.target.value,
                                )
                              }
                              placeholder="Enter email"
                            />
                          </div>
                          <div className="flex flex-col gap-2">
                            <Label className="text-sm font-medium text-gray-700">
                              Phone
                            </Label>
                            <Input
                              value={contact.phone || ''}
                              onChange={(e) =>
                                updateAdditionalContact(
                                  index,
                                  'phone',
                                  e.target.value,
                                )
                              }
                              placeholder="Enter phone"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
            </div>
          </div>

          {/* Addresses */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Addresses</h3>
            <div className="grid grid-cols-3 gap-5">
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Country<span className="text-red-200">*</span>
                </Label>
                <Input
                  value={form.primary_address.country}
                  onChange={(e) =>
                    setForm((s) => ({
                      ...s,
                      primary_address: {
                        ...s.primary_address,
                        country: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter country"
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Address Line 1<span className="text-red-200">*</span>
                </Label>
                <Input
                  value={form.primary_address.address_line1}
                  onChange={(e) =>
                    setForm((s) => ({
                      ...s,
                      primary_address: {
                        ...s.primary_address,
                        address_line1: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter address line 1"
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Address Line 2
                </Label>
                <Input
                  value={form.primary_address.address_line2 || ''}
                  onChange={(e) =>
                    setForm((s) => ({
                      ...s,
                      primary_address: {
                        ...s.primary_address,
                        address_line2: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter address line 2"
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  City<span className="text-red-200">*</span>
                </Label>
                <Input
                  value={form.primary_address.city}
                  onChange={(e) =>
                    setForm((s) => ({
                      ...s,
                      primary_address: {
                        ...s.primary_address,
                        city: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter city"
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  State/Province<span className="text-red-200">*</span>
                </Label>
                <Input
                  value={form.primary_address.state_province || ''}
                  onChange={(e) =>
                    setForm((s) => ({
                      ...s,
                      primary_address: {
                        ...s.primary_address,
                        state_province: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter state/province"
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Postal Code<span className="text-red-200">*</span>
                </Label>
                <Input
                  value={form.primary_address.postal_code || ''}
                  onChange={(e) =>
                    setForm((s) => ({
                      ...s,
                      primary_address: {
                        ...s.primary_address,
                        postal_code: e.target.value,
                      },
                    }))
                  }
                  placeholder="Enter postal code"
                />
              </div>
            </div>

            {/* Additional Addresses */}
            <div className="mt-6">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-700">
                  Additional Addresses
                </h4>
                <SecondaryButton
                  text="Add Address"
                  size="small"
                  icon={<Plus className="w-4 h-4" />}
                  onClick={addAdditionalAddress}
                />
              </div>

              {form.additional_addresses &&
                form.additional_addresses.length > 0 && (
                  <div className="space-y-4">
                    {form.additional_addresses.map((address, index) => (
                      <div
                        key={index}
                        className="border border-gray-200 rounded-lg p-4"
                      >
                        <div className="flex items-center justify-between mb-3">
                          <span className="text-sm font-medium text-gray-600">
                            Address {index + 1}
                          </span>
                          <button
                            type="button"
                            onClick={() => removeAdditionalAddress(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                        <div className="grid grid-cols-3 gap-4">
                          <div className="flex flex-col gap-2">
                            <Label className="text-sm font-medium text-gray-700">
                              Country
                            </Label>
                            <Input
                              value={address.country}
                              onChange={(e) =>
                                updateAdditionalAddress(
                                  index,
                                  'country',
                                  e.target.value,
                                )
                              }
                              placeholder="Enter country"
                            />
                          </div>
                          <div className="flex flex-col gap-2">
                            <Label className="text-sm font-medium text-gray-700">
                              Address Line 1
                            </Label>
                            <Input
                              value={address.address_line1}
                              onChange={(e) =>
                                updateAdditionalAddress(
                                  index,
                                  'address_line1',
                                  e.target.value,
                                )
                              }
                              placeholder="Enter address line 1"
                            />
                          </div>
                          <div className="flex flex-col gap-2">
                            <Label className="text-sm font-medium text-gray-700">
                              Address Line 2
                            </Label>
                            <Input
                              value={address.address_line2 || ''}
                              onChange={(e) =>
                                updateAdditionalAddress(
                                  index,
                                  'address_line2',
                                  e.target.value,
                                )
                              }
                              placeholder="Enter address line 2"
                            />
                          </div>
                          <div className="flex flex-col gap-2">
                            <Label className="text-sm font-medium text-gray-700">
                              City
                            </Label>
                            <Input
                              value={address.city}
                              onChange={(e) =>
                                updateAdditionalAddress(
                                  index,
                                  'city',
                                  e.target.value,
                                )
                              }
                              placeholder="Enter city"
                            />
                          </div>
                          <div className="flex flex-col gap-2">
                            <Label className="text-sm font-medium text-gray-700">
                              State/Province
                            </Label>
                            <Input
                              value={address.state_province || ''}
                              onChange={(e) =>
                                updateAdditionalAddress(
                                  index,
                                  'state_province',
                                  e.target.value,
                                )
                              }
                              placeholder="Enter state/province"
                            />
                          </div>
                          <div className="flex flex-col gap-2">
                            <Label className="text-sm font-medium text-gray-700">
                              Postal Code
                            </Label>
                            <Input
                              value={address.postal_code || ''}
                              onChange={(e) =>
                                updateAdditionalAddress(
                                  index,
                                  'postal_code',
                                  e.target.value,
                                )
                              }
                              placeholder="Enter postal code"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
            </div>
          </div>

          {/* Supply Profile */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Supply Profile</h3>
            <div className="grid grid-cols-3 gap-5">
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Risk Tier
                  {form.status !== 'draft' && (
                    <span className="text-red-200">*</span>
                  )}
                </Label>
                <Select
                  value={form.risk_tier}
                  onValueChange={(v) =>
                    setForm((s) => ({ ...s, risk_tier: v as RiskTier }))
                  }
                >
                  <SelectTrigger id="risk_tier">
                    <SelectValue placeholder="Select risk tier" />
                  </SelectTrigger>
                  <SelectContent>
                    {riskTierOptions.map((o) => (
                      <SelectItem key={o.value} value={o.value}>
                        {o.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Criticality Level (1-5)
                </Label>
                <Input
                  type="number"
                  value={form.criticality_level}
                  onChange={(e) =>
                    setForm((s) => ({
                      ...s,
                      criticality_level: Number(e.target.value),
                    }))
                  }
                />
              </div>
              <div className="flex flex-col gap-2.5">
                <Label className="text-base font-medium leading-6 text-dark-100">
                  Notes
                </Label>
                <Input
                  value={form.notes || ''}
                  onChange={(e) =>
                    setForm((s) => ({ ...s, notes: e.target.value }))
                  }
                  placeholder="Enter notes"
                />
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end">
            <PrimaryButton
              size="medium"
              text={edit ? 'Save Changes' : 'Create Supplier'}
              onClick={onSubmit}
              isLoading={isPut || isPost}
            />
          </div>
        </div>
      )}
    </DialogContent>
  );
};

export default CreateSupplierModal;
