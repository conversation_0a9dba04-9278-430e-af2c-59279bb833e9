import React from 'react';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';

import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';

interface AddInvestigatorModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ncId: string;
  onSuccess?: () => void;
}

const AddInvestigatorModal: React.FC<AddInvestigatorModalProps> = ({
  open,
  onOpenChange,
  ncId,
  onSuccess,
}) => {
  const accessToken = useAuthStore((s) => s.accessToken);
  const [selectedInvestigator, setSelectedInvestigator] =
    React.useState<string>('');

  // Fetch users for investigator assignment
  const { data: usersData } = useFetch<{
    records: Array<{ id: string; full_name: string; email: string }>;
  }>(accessToken, open ? 'users' : undefined);

  const handleConfirm = async () => {
    if (!selectedInvestigator) {
      toast.error('Please select an investigator');
      return;
    }
    // TODO: Implement API call to add investigator
    // This would typically be a POST request to assign the investigator to the NC
    toast.success('Investigator added successfully');
    setSelectedInvestigator('');
    onOpenChange(false);
    if (onSuccess) onSuccess();
  };

  return (
    <>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Investigator</DialogTitle>
        </DialogHeader>

        <div className="mt-2">
          <div className="space-y-2">
            <Label
              htmlFor="first_name"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Select a user to assign as an investigator for this NC.
            </Label>
            <Select
              value={selectedInvestigator}
              onValueChange={(v) => setSelectedInvestigator(v)}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select user" />
              </SelectTrigger>
              <SelectContent>
                {(usersData?.records || []).map((user) => (
                  <SelectItem key={user.id} value={user.id}>
                    {user.full_name} ({user.email})
                  </SelectItem>
                ))}
                {(usersData?.records || []).length === 0 ? (
                  <div className="px-3 py-2 text-sm text-gray-500">
                    No users available
                  </div>
                ) : null}
              </SelectContent>
            </Select>
          </div>

          <div className="mt-5 flex justify-end gap-2">
            <PrimaryButton
              size="medium"
              text="Submit"
              style={{ width: '96px' }}
              onClick={handleConfirm}
              disabled={!selectedInvestigator}
            />
          </div>
        </div>
      </DialogContent>
    </>
  );
};

export default AddInvestigatorModal;
