import React from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';

interface RaiseInternalCapaModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ncId: string;
  onSuccess?: () => void;
}

const RaiseInternalCapaModal: React.FC<RaiseInternalCapaModalProps> = ({
  open,
  onOpenChange,
  ncId,
  onSuccess,
}) => {
  const accessToken = useAuthStore((s) => s.accessToken);
  const [description, setDescription] = React.useState('');
  const [error, setError] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);

  const handleConfirm = async () => {
    if (!ncId) return;
    if (!description.trim()) {
      setError('Description is required');
      return;
    }
    setIsLoading(true);
    try {
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const mockUser =
        typeof window !== 'undefined'
          ? localStorage.getItem('x-mock-user')
          : null;
      const orgId =
        typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;
      const res = await axios.request({
        method: 'POST',
        url: `${baseUrl}/${productVersion}/supplier-quality/non-conformances/${ncId}/raise-capa`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          ...(mockUser ? { 'x-mock-user': mockUser } : {}),
          ...(orgId ? { 'x-org': orgId } : {}),
        },
        data: {
          description,
          corrective_action_users: [],
          preventive_action_users: [],
        },
      });
      if (res.status === 200) {
        toast.success('CAPA raised');
        setDescription('');
        setError('');
        onOpenChange(false);
        if (onSuccess) onSuccess();
      }
    } catch (err: any) {
      toast.error(err?.response?.data?.detail || 'Failed to raise CAPA');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        onOpenChange(isOpen);
        if (!isOpen) {
          setDescription('');
          setError('');
        }
      }}
    >
      <div className="w-[520px] bg-white rounded-lg p-6 shadow-xl">
        <h3 className="text-lg font-semibold mb-2">Raise Internal CAPA</h3>
        <p className="text-sm text-gray-600 mb-4">
          Provide a clear description of the corrective/preventive action to be
          initiated.
        </p>
        <div className="space-y-2">
          <Label htmlFor="capa_desc">Description</Label>
          <Textarea
            id="capa_desc"
            rows={4}
            value={description}
            onChange={(e) => {
              setDescription(e.target.value);
              if (error) setError('');
            }}
            placeholder="Describe the CAPA to be raised"
          />
          {error ? (
            <p className="text-sm text-red-600 mt-1">{error}</p>
          ) : null}
        </div>
        <div className="mt-5 flex justify-end gap-2">
          <button
            className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </button>
          <PrimaryButton
            text="Raise CAPA"
            onClick={handleConfirm}
            isLoading={isLoading}
          />
        </div>
      </div>
    </Dialog>
  );
};

export default RaiseInternalCapaModal;

