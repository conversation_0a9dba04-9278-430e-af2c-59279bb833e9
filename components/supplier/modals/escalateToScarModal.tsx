import React from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';

interface EscalateToScarModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ncId: string;
  onSuccess?: () => void;
}

const EscalateToScarModal: React.FC<EscalateToScarModalProps> = ({
  open,
  onOpenChange,
  ncId,
  onSuccess,
}) => {
  const accessToken = useAuthStore((s) => s.accessToken);
  const [comment, setComment] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);

  const handleConfirm = async () => {
    if (!ncId) return;
    setIsLoading(true);
    try {
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const mockUser =
        typeof window !== 'undefined'
          ? localStorage.getItem('x-mock-user')
          : null;
      const orgId =
        typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;
      const res = await axios.request({
        method: 'POST',
        url: `${baseUrl}/${productVersion}/supplier-quality/non-conformances/${ncId}/escalate-to-scar`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          ...(mockUser ? { 'x-mock-user': mockUser } : {}),
          ...(orgId ? { 'x-org': orgId } : {}),
        },
        data: { manual: true, comment },
      });
      if (res.status === 200) {
        toast.success('Escalated to SCAR');
        setComment('');
        onOpenChange(false);
        if (onSuccess) onSuccess();
      }
    } catch (err: any) {
      toast.error(err?.response?.data?.detail || 'Failed to escalate');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Escalate to SCAR</DialogTitle>
        </DialogHeader>

        <div className="w-[520px] bg-white rounded-lg p-6 shadow-xl">
          <p className="text-sm text-gray-600 mb-4">
            This will create a new SCAR linked to this NC. Provide any context
            or justification (optional).
          </p>
          <div className="space-y-2">
            <Label htmlFor="escalate_comment">Comment (optional)</Label>
            <Textarea
              id="escalate_comment"
              rows={4}
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Add any note for the SCAR..."
            />
          </div>
          <div className="mt-5 flex justify-end gap-2">
            <button
              className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
              onClick={() => onOpenChange(false)}
            >
              Cancel
            </button>
            <PrimaryButton
              text="Create SCAR"
              onClick={handleConfirm}
              isLoading={isLoading}
            />
          </div>
        </div>
      </DialogContent>
    </>
  );
};

export default EscalateToScarModal;
