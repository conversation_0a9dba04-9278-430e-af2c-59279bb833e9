import PrimaryButton from '@/components/common/button/primaryButton';
import TertiaryButton from '@/components/common/button/tertiaryButton';
import Calendar from '@/components/common/calendar';
import {
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import FileCard from '@/components/common/modals/uploadModal/fileCard';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import useValidators from '@/hooks/useValidator';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import { IDocumentDetails } from '@/interfaces/document';
import moment from 'moment';
import React, { useCallback, useState, useEffect } from 'react';
import { useDropzone } from 'react-dropzone';
import { z } from 'zod';
import { useRouter } from 'next/router';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import axios from 'axios';
import { toast } from 'react-toastify';
import { Checkbox } from '@/components/common/checkbox';

const validateDocumentSource = (
  data: IData,
  addedFile: File[] | null,
  isEdit: boolean,
  documentData?: IProps['documentData'],
): boolean => {
  // Has linked document selected
  if (data.linked_document) return true;

  // Has new file uploaded
  if (addedFile && addedFile.length > 0) return true;

  // In edit mode with existing file
  if (isEdit && documentData?.file_path) return true;

  // In edit mode with existing linked document
  if (isEdit && documentData?.document?.id) return true;

  return false;
};

const documentSchema = {
  document_name: z
    .string({
      required_error: 'Document name is required',
      invalid_type_error: 'Document name must be a string',
    })
    .min(1, 'Document name is required')
    .min(3, 'Document name must be at least 3 characters')
    .max(200, 'Document name cannot exceed 200 characters')
    .trim(),

  category: z
    .string({
      required_error: 'Category is required',
      invalid_type_error: 'Category must be a string',
    })
    .min(1, 'Please select a category'),

  description: z
    .string()
    .optional()
    .or(z.literal(''))
    .refine(
      (val) => !val || val.length <= 500,
      'Description cannot exceed 500 characters',
    ),

  reminder_days: z
    .number({
      required_error: 'Reminder days is required',
      invalid_type_error: 'Reminder days must be a number',
    })
    .int('Reminder days must be a whole number')
    .min(0, 'Reminder days must be 0 or greater')
    .max(365, 'Reminder days cannot exceed 365'),

  issue_date: z
    .string({
      required_error: 'Issue date is required',
      invalid_type_error: 'Issue date must be a string',
    })
    .min(1, 'Issue date is required')
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Issue date must be in YYYY-MM-DD format'),

  expiry_date: z
    .string({
      required_error: 'Expiry date is required',
      invalid_type_error: 'Expiry date must be a string',
    })
    .min(1, 'Expiry date is required')
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Expiry date must be in YYYY-MM-DD format'),

  linked_document: z.string().optional().or(z.literal('')),
};

interface IExtendedDocumentDetails extends IDocumentDetails {
  path?: string;
  file_path?: string;
  name?: string;
}

interface IProps {
  setShowModal: React.Dispatch<React.SetStateAction<boolean>>;
  showModal: boolean;
  edit?: boolean;
  documentData?: {
    id?: string;
    name?: string;
    document_type?: string;
    description?: string;
    reminder_days?: number;
    issue_date?: string;
    expiry_date?: string;
    is_required?: boolean;
    file_path?: string;
    file_extension?: string;
    document?: {
      id: string;
      title: string;
      doc_id?: string;
      status?: string;
    };
  };
  onSuccess?: () => void;
  reFetch?: () => void;
}

interface IData {
  document_name: string;
  category: string;
  description: string;
  reminder_days: number;
  issue_date: string;
  expiry_date: string;
  linked_document: string;
}

// API Payload Interface
interface APIPayload {
  document_id?: string | null;
  name: string;
  document_type: string;
  description: string;
  reminder_days: number;
  issue_date: string;
  expiry_date: string;
  is_required?: boolean;
  file_path?: string | null;
  file_extension?: string | null;
  [key: string]: unknown;
}

const AddDocumentModal = ({
  setShowModal,
  showModal,
  edit = false,
  documentData,
  onSuccess,
  reFetch,
}: IProps) => {
  const [issueDate, setIssueDate] = useState<string>('');
  const [expiryDate, setExpiryDate] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [addedFile, setAddedFile] = React.useState<File[] | null>(null);
  const [isRequired, setIsRequired] = useState<boolean>(false);
  const { accessToken } = useAuthStore();
  const router = useRouter();
  const { supplierId } = router.query;

  // Form state
  const [data, setData] = useState<IData>({
    document_name: '',
    category: '',
    description: '',
    reminder_days: 1,
    issue_date: '',
    expiry_date: '',
    linked_document: '',
  });

  // API hooks
  const { postData, response, isLoading: submitLoading, error } = usePost();
  const {
    putData,
    isLoading: editLoading,
    error: editError,
    response: editResponse,
  } = usePut();

  // Form validation
  const { startValidation, validationErrors } = useValidators({
    schemas: documentSchema,
    values: data,
  });

  // Populate form data in edit mode
  useEffect(() => {
    if (edit && documentData && showModal) {
      // Determine linked_document value
      let linkedDocumentId = '';
      if (documentData.document?.id) {
        // Case: Document from document hub
        linkedDocumentId = documentData.document.id;
      }

      // Populate form fields
      setData({
        document_name: documentData.name || '',
        category: documentData.document_type || '',
        description: documentData.description || '',
        reminder_days: documentData.reminder_days || 1,
        issue_date: documentData.issue_date || '',
        expiry_date: documentData.expiry_date || '',
        linked_document: linkedDocumentId,
      });

      // Set is_required checkbox
      setIsRequired(documentData.is_required || false);

      // Set date states for calendar components
      if (documentData.issue_date) {
        setIssueDate(documentData.issue_date);
      }
      if (documentData.expiry_date) {
        setExpiryDate(documentData.expiry_date);
      }

      // Clear file if it's a linked document
      if (documentData.document?.id) {
        setAddedFile(null);
      }
    }
  }, [edit, documentData, showModal]);

  useEffect(() => {
    if (!showModal) {
      setData({
        document_name: '',
        category: '',
        description: '',
        reminder_days: 1,
        issue_date: '',
        expiry_date: '',
        linked_document: '',
      });
      setIssueDate('');
      setExpiryDate('');
      setAddedFile(null);
      setIsRequired(false);
    }
  }, [showModal]);

  // Handle success responses
  useEffect(() => {
    if (error) {
      toast.error('Failed to create document');
    }
    if (response) {
      if (onSuccess) {
        onSuccess();
      }
      if (reFetch) {
        reFetch();
      }
      setShowModal(false);
    }
  }, [response, error]);

  useEffect(() => {
    if (editError) {
      toast.error('Failed to edit document');
    }
    if (editResponse) {
      if (onSuccess) {
        onSuccess();
      }
      if (reFetch) {
        reFetch();
      }
      setShowModal(false);
    }
  }, [editResponse, editError]);

  const handleIssueDateChange = (date: string | null) => {
    if (date) {
      const formattedDate = moment(date).format('YYYY-MM-DD');
      setIssueDate(formattedDate);
      setData((prev) => ({
        ...prev,
        issue_date: formattedDate,
      }));
    } else {
      setIssueDate('');
      setData((prev) => ({
        ...prev,
        issue_date: '',
      }));
    }
  };

  const handleExpiryDateChange = (date: string | null) => {
    if (date) {
      const formattedDate = moment(date).format('YYYY-MM-DD');
      setExpiryDate(formattedDate);
      setData((prev) => ({
        ...prev,
        expiry_date: formattedDate,
      }));
    } else {
      setExpiryDate('');
      setData((prev) => ({
        ...prev,
        expiry_date: '',
      }));
    }
  };

  const acceptFileTypes = {
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
      '.docx',
      '.doc',
    ],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
      '.xlsx',
    ],
    'application/vnd.ms-excel': ['.xls', '.csv'],
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    setAddedFile(acceptedFiles);
    // Clear linked_document when uploading a file
    setData((prev) => ({
      ...prev,
      linked_document: '',
    }));
  }, []);

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    multiple: false,
    accept: acceptFileTypes,
  });

  const { data: publishedDocuments } = useFetch<{
    records: IExtendedDocumentDetails[];
    total: number;
  }>(accessToken, `documents?status=Published`);

  // Helper function to get file extension
  const getFileNameFromPath = (filePath: string): string => {
    const parts = filePath.split('/');
    return parts[parts.length - 1] || 'document';
  };

  const fileUpload = async (supplierId: string, files: any) => {
    const responses: any = [];
    setIsLoading(true);

    if (files.length > 0) {
      await Promise.all(
        files.map(async (file: any) => {
          const formData = new FormData();
          formData.append('file', file);

          const baseUrl = process.env.NEXT_PUBLIC_URL;
          const productVersion = process.env.NEXT_PUBLIC_VERSION;

          const url = `${baseUrl}/${productVersion}/file/upload?document_for=supplier_hub&sub_path=/suppliers/${supplierId}/documents`;

          const orgId =
            typeof window !== 'undefined'
              ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
              : null;

          const config = {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: `Bearer ${accessToken}`,
              ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
            },
          };

          await axios.post(url, formData, config).then((response) => {
            if (response.status == 200) {
              responses.push({
                file_path: response.data.file_path,
                file_extension: response.data.file_ext,
              });
              setIsLoading(false);
            } else {
              console.error('Error uploading file:', file.name);
              setIsLoading(false);
            }
          });
        }),
      );
    }

    return responses;
  };

  const createAPIPayload = async (): Promise<APIPayload> => {
    const payload: APIPayload = {
      name: data.document_name,
      document_type: data.category,
      description: data.description || '',
      reminder_days: data.reminder_days,
      issue_date: data.issue_date,
      expiry_date: data.expiry_date,
      is_required: isRequired,
    };

    if (addedFile && addedFile.length > 0) {
      try {
        const uploadedFileInfo = await fileUpload(
          supplierId as string,
          addedFile,
        );

        if (uploadedFileInfo && uploadedFileInfo.length > 0) {
          payload.file_path = uploadedFileInfo[0].file_path;
          payload.file_extension = uploadedFileInfo[0].file_extension;

          if (edit) {
            payload.document_id = null;
          }
        } else {
          throw new Error('File upload failed - no file info returned');
        }
      } catch (uploadError) {
        throw new Error('File upload failed. Please try again.');
      }
    } else if (data.linked_document) {
      payload.document_id = data.linked_document;

      if (edit) {
        payload.file_path = null;
        payload.file_extension = null;
      }
    } else if (edit && documentData) {
      // Keep existing in edit mode
      if (documentData.document?.id) {
        // Keep linked document
        payload.document_id = documentData.document.id;
      } else if (documentData.file_path) {
        // Keep file
        payload.file_path = documentData.file_path;
        payload.file_extension = documentData.file_extension || '';
      }
    }

    return payload;
  };

  // Check if all required fields are filled
  const isFormValid = () => {
    const hasDocumentSource = validateDocumentSource(
      data,
      addedFile,
      edit,
      documentData,
    );

    return (
      data.document_name.trim().length >= 3 &&
      data.category.trim().length > 0 &&
      data.reminder_days >= 0 &&
      data.issue_date.length > 0 &&
      data.expiry_date.length > 0 &&
      hasDocumentSource
    );
  };

  const handleSubmit = async () => {
    const { hasValidationErrors } = await startValidation();

    // Validate that either linked document or file is present
    const hasDocumentSource = validateDocumentSource(
      data,
      addedFile,
      edit,
      documentData,
    );

    if (!hasDocumentSource) {
      console.error('Either link a document or upload a file');
      return;
    }

    if (!hasValidationErrors) {
      try {
        const payload = await createAPIPayload();
        console.log('API Payload:', payload);

        if (edit && documentData?.id) {
          // Update existing document
          await putData(
            accessToken as string,
            `suppliers/${supplierId}/documents/${documentData.id}`,
            payload as Record<string, unknown>,
          );
        } else {
          // Create new document
          await postData(
            accessToken as string,
            `suppliers/${supplierId}/documents`,
            payload,
          );
        }
      } catch (err) {
        console.error('Submission error:', err);
      }
    }
  };

  // Handle dropdown selection
  const handleSelectChange = (name: keyof IData, value: string) => {
    setData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Document categories
  const documentCategories = [
    'Certification',
    'Legal',
    'Insurance',
    'Compliance',
    'Environmental',
    'Finance',
    'Regulatory',
    'Other',
  ];

  return (
    <DialogContent
      className="min-w-[43.75rem] h-[85vh] p-6 overflow-y-auto"
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      <DialogHeader>
        <DialogTitle>
          {edit ? 'Edit document' : 'Add New Document'}{' '}
        </DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div>
          <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
            Link Document from document hub
            <span className="text-[#F55D5D]">*</span>
          </div>
          <Select
            value={data.linked_document}
            onValueChange={(value) => {
              handleSelectChange('linked_document', value);
              setAddedFile(null);
            }}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select Document" />
            </SelectTrigger>
            <SelectContent>
              {publishedDocuments?.records.map((document) => (
                <SelectItem key={document.id} value={document.id}>
                  {document.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {validationErrors.linked_document &&
            validationErrors.linked_document.length > 0 && (
              <p className="text-red-500 text-sm mt-1">
                {validationErrors.linked_document[0]}
              </p>
            )}
        </div>
        <div className="w-full h-0 border-b border-white-300 relative my-4">
          <div className="absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 bg-white-100 px-4 text-sm font-medium text-grey-300">
            Or
          </div>
        </div>

        <div className="text-base leading-6 font-medium text-dark-100 mb-2.5">
          Attach document
        </div>
        <div>
          <div
            className="min-h-28 bg-white-100 border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2"
            {...getRootProps()}
          >
            {/* Show upload prompt only if no file and no linked document */}
            {!(addedFile?.length && addedFile?.length > 0) &&
              !documentData?.file_path &&
              !documentData?.document && (
                <div className="text-sm font-medium leading-5 text-[#49474E]">
                  Upload or Drag and drop to upload your file
                </div>
              )}

            <input {...getInputProps()} />

            <div className="flex justify-center items-center flex-wrap gap-2">
              {/* Show newly added file */}
              {addedFile?.map((file, index) => (
                <FileCard key={index} file={file} setAddedFile={setAddedFile} />
              ))}

              {/* Show existing file (only if no new file and has file_path) */}
              {!addedFile &&
                documentData?.file_path &&
                !documentData?.document && (
                  <FileCard
                    key={documentData.file_path}
                    prefillFileName={getFileNameFromPath(
                      documentData.file_path,
                    )}
                  />
                )}

              {/* Show message if document is linked (not a file) */}
              {!addedFile &&
                documentData?.document &&
                !documentData?.file_path && (
                  <div className="text-sm text-gray-600 bg-blue-50 px-4 py-2 rounded-lg">
                    Linked to document:{' '}
                    <span className="font-medium">
                      {documentData.document.title}
                    </span>
                  </div>
                )}
            </div>

            <TertiaryButton
              text={
                (addedFile?.length && addedFile?.length > 0) ||
                documentData?.file_path ||
                documentData?.document
                  ? 'Replace'
                  : 'Select file'
              }
              size="small"
            />
          </div>
        </div>

        {/* First Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 my-6">
          <div>
            <Label
              htmlFor="document_name"
              className="text-base font-medium leading-6 text-dark-100 mb-2.5"
            >
              Document Name<span className="text-[#F55D5D]">*</span>
            </Label>
            <Input
              id="document_name"
              name="document_name"
              value={data.document_name}
              onChange={(e) =>
                setData((prev) => ({ ...prev, document_name: e.target.value }))
              }
              placeholder="Enter document name"
              className="mt-1"
              errorMsg={validationErrors.document_name?.[0]}
            />
          </div>
          <div>
            <Label
              htmlFor="category"
              className="text-base font-medium leading-6 text-dark-100 mb-2.5"
            >
              Document Type<span className="text-[#F55D5D]">*</span>
            </Label>
            <Select
              value={data.category}
              onValueChange={(value) => handleSelectChange('category', value)}
            >
              <SelectTrigger className="mt-1">
                <SelectValue placeholder="Select Category" />
              </SelectTrigger>
              <SelectContent>
                {documentCategories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {validationErrors.category &&
              validationErrors.category.length > 0 && (
                <p className="text-red-500 text-sm mt-1">
                  {validationErrors.category[0]}
                </p>
              )}
          </div>
        </div>

        {/* Description */}
        <div className="">
          <Label
            htmlFor="description"
            className="text-base font-medium leading-6 text-dark-100 mb-2.5"
          >
            Description
          </Label>
          <Textarea
            id="description"
            name="description"
            value={data.description}
            onChange={(e) =>
              setData((prev) => ({ ...prev, description: e.target.value }))
            }
            rows={3}
            placeholder="Additional details about the document"
            className="mt-1"
            errorMsg={validationErrors.description?.[0]}
          />
        </div>
      </div>
      {/* Third Row */}
      <div className="grid grid-cols-1 md:grid-cols-1 gap-6 ">
        <div>
          <Label
            htmlFor="reminder_days"
            className="text-base font-medium leading-6 text-dark-100 mb-2.5"
          >
            Reminder Days<span className="text-[#F55D5D]">*</span>
          </Label>
          <Input
            id="reminder_days"
            name="reminder_days"
            type="number"
            value={data.reminder_days}
            onChange={(e) =>
              setData((prev) => ({
                ...prev,
                reminder_days: Number(e.target.value),
              }))
            }
            className="mt-1"
            errorMsg={validationErrors.reminder_days?.[0]}
          />
        </div>
      </div>
      {/* Fourth Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 ">
        <div className="flex flex-col flex-1">
          <Label
            htmlFor="issue_date"
            className="text-base font-medium leading-6 text-dark-100 mb-2.5"
          >
            Issue date<span className="text-[#F55D5D]">*</span>
          </Label>

          <Calendar
            selectedDate={issueDate}
            onDateChange={handleIssueDateChange}
            allowPastDates={false}
            disableFutureDate={false}
          />
          {validationErrors.issue_date &&
            validationErrors.issue_date.length > 0 && (
              <p className="text-red-500 text-sm mt-1">
                {validationErrors.issue_date[0]}
              </p>
            )}
        </div>
        <div className="flex flex-col flex-1">
          <Label
            htmlFor="expiry_date"
            className="text-base font-medium leading-6 text-dark-100 mb-2.5"
          >
            Expiry date<span className="text-[#F55D5D]">*</span>
          </Label>

          <Calendar
            selectedDate={expiryDate}
            onDateChange={handleExpiryDateChange}
            allowPastDates={false}
            disableFutureDate={false}
          />
          {validationErrors.expiry_date &&
            validationErrors.expiry_date.length > 0 && (
              <p className="text-red-500 text-sm mt-1">
                {validationErrors.expiry_date[0]}
              </p>
            )}
        </div>

        <div className="justify-between flex items-center gap-8 md:col-span-2">
          {(error || editError) && (
            <p className="text-red-500 text-sm mr-auto">
              {(error as any)?.response?.data?.error ||
                (editError as any)?.response?.data?.error ||
                'An error occurred'}
            </p>
          )}
          <div className="flex items-center gap-2">
            <Checkbox
              id="is_required"
              checked={isRequired}
              onCheckedChange={(checked) => setIsRequired(checked as boolean)}
            />
            <label
              htmlFor="is_required"
              className="text-base font-medium leading-6 text-dark-300"
            >
              This is a required document
            </label>
          </div>
          <div>
            <PrimaryButton
              size="medium"
              text="Submit"
              style={{ width: '96px' }}
              onClick={handleSubmit}
              isLoading={submitLoading || editLoading || isLoading}
              disabled={
                !isFormValid() || submitLoading || editLoading || isLoading
              }
            />
          </div>
        </div>
      </div>
    </DialogContent>
  );
};

export default AddDocumentModal;
