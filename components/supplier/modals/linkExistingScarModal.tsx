import React from 'react';
import axios from 'axios';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import { Dialog } from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';

interface LinkExistingScarModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  ncId: string;
  supplierId?: string;
  onSuccess?: () => void;
}

const LinkExistingScarModal: React.FC<LinkExistingScarModalProps> = ({
  open,
  onOpenChange,
  ncId,
  supplierId,
  onSuccess,
}) => {
  const accessToken = useAuthStore((s) => s.accessToken);
  const [selectedScarId, setSelectedScarId] = React.useState<string>('');
  const [isLoading, setIsLoading] = React.useState(false);

  // Fetch available SCARs for linking (filtered by same supplier)
  const { data: scarList, isLoading: scarsLoading } = useFetch<{
    records: any[];
  }>(accessToken, open ? 'supplier-quality/scars' : undefined);

  const eligibleScars = React.useMemo(
    () =>
      (scarList?.records || []).filter(
        (s: any) => !supplierId || s.supplier_id === supplierId,
      ),
    [scarList, supplierId],
  );

  const handleConfirm = async () => {
    if (!ncId || !selectedScarId) {
      toast.error('Please select a SCAR to link');
      return;
    }
    setIsLoading(true);
    try {
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;
      const mockUser =
        typeof window !== 'undefined'
          ? localStorage.getItem('x-mock-user')
          : null;
      const orgId =
        typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;
      const res = await axios.request({
        method: 'POST',
        url: `${baseUrl}/${productVersion}/supplier-quality/non-conformances/${ncId}/link-scar`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          ...(mockUser ? { 'x-mock-user': mockUser } : {}),
          ...(orgId ? { 'x-org': orgId } : {}),
        },
        data: { scar_id: selectedScarId },
      });
      if (res.status === 200) {
        toast.success('Linked to existing SCAR');
        setSelectedScarId('');
        onOpenChange(false);
        if (onSuccess) onSuccess();
      }
    } catch (err: any) {
      toast.error(err?.response?.data?.detail || 'Failed to link SCAR');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(isOpen) => {
        onOpenChange(isOpen);
        if (!isOpen) setSelectedScarId('');
      }}
    >
      <div className="w-[520px] bg-white rounded-lg p-6 shadow-xl">
        <h3 className="text-lg font-semibold mb-2">Link Existing SCAR</h3>
        <p className="text-sm text-gray-600 mb-4">
          Select an existing SCAR to associate with this NC. Only SCARs from
          the same supplier are eligible.
        </p>
        <div className="space-y-2">
          <Label>SCAR</Label>
          <Select
            value={selectedScarId}
            onValueChange={(v) => setSelectedScarId(v)}
            disabled={scarsLoading}
          >
            <SelectTrigger className="mt-1">
              <SelectValue
                placeholder={scarsLoading ? 'Loading SCARs...' : 'Select SCAR'}
              />
            </SelectTrigger>
            <SelectContent>
              {(eligibleScars || []).map((s: any) => (
                <SelectItem key={s.id} value={s.id}>
                  {s.title} — {s.status}
                </SelectItem>
              ))}
              {!scarsLoading && (eligibleScars || []).length === 0 ? (
                <div className="px-3 py-2 text-sm text-gray-500">
                  No eligible SCARs
                </div>
              ) : null}
            </SelectContent>
          </Select>
        </div>
        <div className="mt-5 flex justify-end gap-2">
          <button
            className="px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            onClick={() => onOpenChange(false)}
          >
            Cancel
          </button>
          <PrimaryButton
            text="Link SCAR"
            onClick={handleConfirm}
            disabled={!selectedScarId || isLoading}
            isLoading={isLoading}
          />
        </div>
      </div>
    </Dialog>
  );
};

export default LinkExistingScarModal;

