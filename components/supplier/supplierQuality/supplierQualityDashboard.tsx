import {
  Alert<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>3,
  FileText,
  LucideIcon,
  <PERSON>tings,
  Shield,
  Users,
} from 'lucide-react';
import { useRouter } from 'next/router';
import React from 'react';

import Layout from '@/components/common/sidebar/layout';
// Using simple div-based cards for now
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import Loader from '@/components/common/loader';

interface QuickActionItem {
  title: string;
  icon: React.ReactNode;
  path: string;
  description: string;
}

interface SupplierStatusCounts {
  prospect: number;
  active: number;
  inactive: number;
  blocked: number;
}

interface SupplierStatusResponse {
  counts: SupplierStatusCounts;
  total: number;
}

interface StatusMapping {
  key: keyof SupplierStatusCounts;
  label: string;
  colorClass: string;
}
interface QualityMetric {
  label: string;
  value: number;
  colorClass: string;
}

const QUICK_ACTIONS: QuickActionItem[] = [
  {
    title: 'Supplier Directory',
    icon: <Users className="w-8 h-8 text-blue-600" />,
    path: '/supplier-quality/supplier',
    description: 'Manage supplier information and contacts',
  },
  {
    title: 'Supplier Forms',
    icon: <FileText className="w-8 h-8 text-green-600" />,
    path: '/supplier/forms',
    description: 'Access supplier assessment forms',
  },
  {
    title: 'Supplier Audits',
    icon: <Shield className="w-8 h-8 text-purple-600" />,
    path: '/supplier/audits',
    description: 'Schedule and manage supplier audits',
  },
  {
    title: 'SCAR Management',
    icon: <AlertTriangle className="w-8 h-8 text-orange-600" />,
    path: '/supplier-quality/scars',
    description: 'Supplier Corrective Action Requests',
  },
  {
    title: 'Non Conformance',
    icon: <Settings className="w-8 h-8 text-gray-600" />,
    path: '/supplier-quality/supplier-non-conformance',
    description: 'Configure scoring criteria and weights',
  },
];

const STATUS_MAPPINGS: StatusMapping[] = [
  { key: 'active', label: 'Approved', colorClass: 'text-green-600' },
  { key: 'prospect', label: 'Pending Approval', colorClass: 'text-yellow-600' },
  {
    key: 'inactive',
    label: 'Requires Follow-up',
    colorClass: 'text-orange-600',
  },
  { key: 'blocked', label: 'Disapproved', colorClass: 'text-red-600' },
];

const QUALITY_METRICS: QualityMetric[] = [
  { label: 'Active SCARs', value: 10, colorClass: 'text-green-600' },
  { label: 'Total Non-Conformances', value: 2, colorClass: 'text-yellow-600' },
];

const StatusCard: React.FC<{
  title: string;
  icon: LucideIcon;
  children: React.ReactNode;
}> = ({ title, icon: Icon, children }) => (
  <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
    <div className="p-6">
      <h3 className="text-lg font-semibold flex items-center space-x-2 mb-4">
        <Icon className="w-5 h-5" />
        <span>{title}</span>
      </h3>
      {children}
    </div>
  </div>
);

const StatusRow: React.FC<{
  label: string;
  value: number | undefined;
  colorClass: string;
}> = ({ label, value, colorClass }) => (
  <div className="flex justify-between items-center">
    <span className="text-gray-600">{label}</span>
    <span className={`font-semibold ${colorClass}`}>{value ?? 0}</span>
  </div>
);

const QuickActionCard: React.FC<{
  action: QuickActionItem;
  onClick: (path: string) => void;
}> = ({ action, onClick }) => {
  return (
    <div
      onClick={() => onClick(action.path)}
      className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:border-gray-300 hover:shadow-sm cursor-pointer transition-all duration-200"
    >
      <div className="mb-3">{action.icon}</div>
      <h3 className="font-medium text-gray-900 text-center mb-1">
        {action.title}
      </h3>
      <p className="text-xs text-gray-500 text-center">{action.description}</p>
    </div>
  );
};


const useSupplierData = () => {
  const { accessToken } = useAuthStore();

  const {
    data: statusCount,
    isLoading,
    error,
  } = useFetch<SupplierStatusResponse>(
    accessToken,
    'suppliers-status-counts',
    {},
  );

  return { statusCount, isLoading, error };
};

const SupplierQualityDashboard: React.FC = () => {
  const router = useRouter();
  const { statusCount, isLoading, error } = useSupplierData();

  const handleQuickAction = (path: string) => router.push(path);

  if (isLoading) {
    return (
      <Layout>
        <Loader className="h-[80vh]" />
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="p-6 flex items-center justify-center min-h-96">
          <div className="text-lg text-red-600">
            Error loading data. Please try again.
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-teal-100 rounded-lg flex items-center justify-center">
            <BarChart3 className="w-6 h-6 text-teal-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Supplier Quality
            </h1>
            <p className="text-gray-600">
              Comprehensive supplier quality management system
            </p>
          </div>
        </div>

        {/* Quick Actions */}
        <StatusCard title="Quick Actions" icon={Settings}>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {QUICK_ACTIONS.map((action, index) => (
              <QuickActionCard
                key={`${action.title}-${index}`}
                action={action}
                onClick={handleQuickAction}
              />
            ))}
          </div>
        </StatusCard>

        {/* Dashboard Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Supplier Status Overview */}
          <StatusCard title="Supplier Status Overview" icon={Users}>
            <div className="space-y-4">
              {STATUS_MAPPINGS.map(({ key, label, colorClass }) => (
                <StatusRow
                  key={key}
                  label={label}
                  value={statusCount?.counts[key]}
                  colorClass={colorClass}
                />
              ))}
            </div>
          </StatusCard>

          {/* Supplier Quality Overview */}
          <StatusCard title="Supplier Quality Overview" icon={Users}>
            <div className="space-y-4">
              {QUALITY_METRICS.map(({ label, value, colorClass }) => (
                <StatusRow
                  key={label}
                  label={label}
                  value={value}
                  colorClass={colorClass}
                />
              ))}
            </div>
          </StatusCard>
        </div>
      </div>
    </Layout>
  );
};

export default SupplierQualityDashboard;
