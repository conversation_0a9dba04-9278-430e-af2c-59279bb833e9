import { useRouter } from 'next/router';
import React from 'react';

import DeleteIcon from '@/assets/outline/delete';
import EditIcon from '@/assets/outline/edit';
import PrimaryButton from '@/components/common/button/primaryButton';
import { AccessActions } from '@/constants/access';
import { hasAccess } from '@/utils/roleAccessConfig';

import { SUPPLIER_HUB_ADMIN } from '../../constants/role';

interface SupplierActionsProps {
  supplierId: string;
  currentUser: any;
  onDelete: () => void;
  onEdit?: () => void;
}

const SupplierActions = ({
  supplierId,
  currentUser,
  onDelete,
  onEdit,
}: SupplierActionsProps) => {
  const router = useRouter();

  return (
    <div className="flex justify-end mb-2">
      {hasAccess(AccessActions.EDIT_SUPPLIER, currentUser) && (
        <PrimaryButton
          onClick={
            onEdit || (() => router.push(`/supplier/${supplierId}/edit`))
          }
          className="mr-2"
          text="Edit"
          icon={<EditIcon />}
        />
      )}

      {hasAccess(AccessActions.IsSupplierAdmin, currentUser) && (
        <PrimaryButton onClick={onDelete} text="Delete" icon={<DeleteIcon />} />
      )}
    </div>
  );
};

export default SupplierActions;
