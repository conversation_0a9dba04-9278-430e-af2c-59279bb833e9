// components/supplier/nc_scar/CreateNCFormEnhanced.tsx
import axios from 'axios';
import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useEffect, useMemo, useState } from 'react';
import { toast } from 'react-toastify';
import { z } from 'zod';
import BackButton from '@/assets/backButton.svg';

import PrimaryButton from '@/components/common/button/primaryButton';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import useValidators from '@/hooks/useValidator';

import AdditionalDetailsSection from './sections/AdditionalDetailsSection';
import AdditionalFieldsSection from './sections/AdditionalFieldsSection';
import CoreInformationSection from './sections/CoreInformationSection';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import Link from 'next/link';
import Image from 'next/image';
import { handleFilesUpload } from '@/utils/handleFileUpload';

export interface FormData {
  nc_title: string;
  supplier: string;
  person_responsible: string;
  issue_description: string;
  severity: 'Critical' | 'Major' | 'Minor';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  repeat_count: number;
  nc_type: string;

  // NEW
  department_id: string;
  category_ids: string[];

  // Product
  part_number: string;
  lot_number: string;
  batch_id: string;

  // Service
  service_category: string;

  // Process
  process_name: string;
  process_stage: string;

  // Documentation
  document_id: string;
  revision_number: string;

  // Delivery
  delivery_date?: string;
  po_number: string;

  // System
  system_module_name: string;
  impacted_users: string;

  // Other
  type_description: string;
  other_information: string;

  additional_notes: string;
  due_date?: string;

  // NEW
  supporting_documents: File[];
  custom_fields: Record<string, string>;
}

interface CreateNCFormEnhancedProps {
  isEdit?: boolean;
  initialData?: Partial<FormData>;
  ncId?: string;
}

const CreateNCFormEnhanced: React.FC<CreateNCFormEnhancedProps> = ({
  isEdit = false,
  initialData,
  ncId,
}) => {
  const router = useRouter();

  const [error, setError] = useState<string | undefined>();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState<FormData>({
    nc_title: '',
    supplier: '',
    person_responsible: '',
    issue_description: '',
    severity: 'Minor',
    priority: 'Medium',
    repeat_count: 1,
    nc_type: '',
    department_id: '',
    category_ids: [],

    // Product
    part_number: '',
    lot_number: '',
    batch_id: '',

    // Service
    service_category: '',

    // Process
    process_name: '',
    process_stage: '',

    // Documentation
    document_id: '',
    revision_number: '',

    // Delivery
    delivery_date: '',
    po_number: '',

    // System
    system_module_name: '',
    impacted_users: '',

    // Other
    type_description: '',
    other_information: '',

    additional_notes: '',
    due_date: '',
    supporting_documents: [],
    custom_fields: {},
  });

  // Prefill when editing
  // React.useEffect(() => {
  //   if (isEdit && initialData) {
  //     setFormData((prev) => ({
  //       ...prev,
  //       ...initialData,
  //     }));
  //   }
  // }, [isEdit, initialData]);

  const accessToken = useAuthStore((s) => s.accessToken);

  const { data: suppliersResp } = useFetch<{
    records: Array<{ id: string; legal_name: string }>;
  }>(accessToken, 'suppliers');
  const supplierOptions = useMemo(
    () => suppliersResp?.records ?? [],
    [suppliersResp],
  );

  const { data: usersResp } = useFetch<{
    records: Array<{ id: string; full_name: string }>;
  }>(accessToken, 'users');
  const userOptions = useMemo(() => usersResp?.records ?? [], [usersResp]);

  // NEW: Fetch departments
  const { data: departmentsResp } = useFetch<{
    records: Array<{ id: string; name: string }>;
  }>(accessToken, 'departments');
  const departmentOptions = useMemo(
    () => departmentsResp?.records ?? [],
    [departmentsResp],
  );

  // NEW: Fetch categories
  const {
    data: categoriesResp,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useFetch<{
    records: Array<{ id: string; name: string }>;
  }>(accessToken, 'nc-categories');
  const categoryOptions = useMemo(
    () => categoriesResp?.records ?? [],
    [categoriesResp],
  );

  // Form validation with updated schema
  const dynamicSchemas = useMemo(() => {
    const base: Record<string, z.ZodTypeAny> = {
      nc_title: z
        .string()
        .min(5, 'Title must be at least 5 characters')
        .max(200, 'Title must not exceed 200 characters'),
      supplier: z.string().min(1, 'Please select a supplier'),
      person_responsible: z
        .string()
        .min(1, 'Please select a responsible person'),
      issue_description: z
        .string()
        .min(10, 'Description must be at least 10 characters')
        .max(1000, 'Description must not exceed 1000 characters'),
      severity: z.string().min(1, 'Please select severity level'),
      priority: z.string().min(1, 'Please select priority level'),
      repeat_count: z.number().int().min(1, 'Repeat count must be at least 1'),
      nc_type: z.string().min(1, 'Please select NC type'),
      department_id: z.string().min(1, 'Please select a department'),
      category_ids: z.array(z.string()).optional(),

      // Optional defaults
      part_number: z.string().optional().or(z.literal('')),
      lot_number: z.string().optional().or(z.literal('')),
      batch_id: z.string().optional().or(z.literal('')),
      service_category: z.string().optional().or(z.literal('')),
      process_name: z.string().optional().or(z.literal('')),
      process_stage: z.string().optional().or(z.literal('')),
      document_id: z.string().optional().or(z.literal('')),
      revision_number: z.string().optional().or(z.literal('')),
      delivery_date: z.string().optional().or(z.literal('')),
      po_number: z.string().optional().or(z.literal('')),
      system_module_name: z.string().optional().or(z.literal('')),
      impacted_users: z.string().optional().or(z.literal('')),
      type_description: z.string().optional().or(z.literal('')),
      other_information: z.string().optional().or(z.literal('')),
      additional_notes: z.string().optional().or(z.literal('')),
      due_date: z.string().optional().or(z.literal('')),
    };

    // Type-specific required fields
    switch (formData.nc_type) {
      case 'Product':
        base.part_number = z
          .string({ required_error: 'Part Number is required' })
          .min(1, 'Part Number is required');
        break;
      case 'Service':
        base.service_category = z
          .string()
          .min(1, 'Service Category is required');
        break;
      case 'Process':
        base.process_name = z.string().min(1, 'Process Name is required');
        break;
      case 'Documentation':
        base.document_id = z.string().min(1, 'Document ID is required');
        break;
      case 'Delivery':
        base.delivery_date = z.string().min(1, 'Delivery Date is required');
        break;
      case 'System':
        base.system_module_name = z
          .string()
          .min(1, 'System/Module Name is required');
        break;
      case 'Other':
        base.type_description = z
          .string()
          .min(1, 'Type Description is required');
        break;
    }

    return base;
  }, [formData.nc_type]);

  const { startValidation, validationErrors } = useValidators({
    schemas: dynamicSchemas,
    values: formData,
  });

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(undefined);
    setIsSubmitting(true);

    console.log('formData', {
      ...formData,
      category_ids: formData.category_ids.map((item) => item.id),
      person_responsible: formData.person_responsible.id,
    });

    const { hasValidationErrors } = await startValidation();

    if (hasValidationErrors) {
      setIsSubmitting(false);
      return;
    }

    try {
      const supplierId = formData.supplier;
      const supplierName =
        supplierOptions.find((s) => s.id === supplierId)?.legal_name || '';

      if (!supplierId) {
        setError('Please select a supplier');
        setIsSubmitting(false);
        return;
      }

      let uploadFiles: any = [];

      if (
        formData.supporting_documents &&
        formData.supporting_documents.length > 0
      ) {
        uploadFiles = await handleFilesUpload(
          formData.supporting_documents,
          accessToken || '',
          'supplier-quality/non-conformances',
          'supplier_hub',
        );
      }

      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      const body = {
        title: formData.nc_title,
        supplier_id: supplierId,
        supplier_name: supplierName,
        issue_description: formData.issue_description,
        severity: formData.severity,
        priority: formData.priority,
        type: formData.nc_type,
        repeat_count: formData.repeat_count,
        responsible_person: formData.person_responsible || undefined,
        department_id: formData.department_id || undefined,
        category_ids:
          formData.category_ids.length > 0 ? formData.category_ids : undefined,

        // Type-specific mapping
        part_number:
          formData.nc_type === 'Product'
            ? formData.part_number || undefined
            : undefined,
        lot_number:
          formData.nc_type === 'Product'
            ? formData.lot_number || undefined
            : undefined,
        batch_id:
          formData.nc_type === 'Product'
            ? formData.batch_id || undefined
            : undefined,
        process_step:
          formData.nc_type === 'Process'
            ? formData.process_name || undefined
            : undefined,
        process_stage:
          formData.nc_type === 'Process'
            ? formData.process_stage || undefined
            : undefined,
        document_type:
          formData.nc_type === 'Documentation'
            ? formData.document_id || undefined
            : undefined,
        revision_number:
          formData.nc_type === 'Documentation'
            ? formData.revision_number || undefined
            : undefined,
        delivery_reference:
          formData.nc_type === 'Delivery'
            ? formData.po_number || undefined
            : undefined,
        service_category:
          formData.nc_type === 'Service'
            ? formData.service_category || undefined
            : undefined,
        system_area:
          formData.nc_type === 'System'
            ? formData.system_module_name || undefined
            : undefined,
        impacted_users:
          formData.nc_type === 'System'
            ? formData.impacted_users || undefined
            : undefined,
        type_description:
          formData.nc_type === 'Other'
            ? formData.type_description || undefined
            : undefined,
        other_information:
          formData.nc_type === 'Other'
            ? formData.other_information || undefined
            : undefined,

        date_reported: new Date().toISOString().slice(0, 10),
        due_date:
          formData.nc_type === 'Delivery'
            ? formData.delivery_date || formData.due_date || undefined
            : formData.due_date || undefined,

        // NEW: Custom fields - properly spread into the body
        ...(Object.keys(formData.custom_fields).length > 0
          ? { custom_fields: formData.custom_fields }
          : {}),

        // NEW: Supporting documents (would need multipart/form-data handling)
        ...(formData.supporting_documents.length > 0
          ? {
              supporting_documents: uploadFiles,
            }
          : {}),
      };

      console.log('Submitting body:', JSON.stringify(body, null, 2));

      const mockUser =
        typeof window !== 'undefined'
          ? localStorage.getItem('x-mock-user')
          : null;
      const orgId =
        typeof window !== 'undefined' ? sessionStorage.getItem('oid') : null;

      const method = isEdit && ncId ? 'PUT' : 'POST';
      const url =
        isEdit && ncId
          ? `${baseUrl}/${productVersion}/supplier-quality/non-conformances/${ncId}`
          : `${baseUrl}/${productVersion}/supplier-quality/non-conformances`;

      const res = await axios.request<{ id: string }>({
        method,
        url,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
          ...(mockUser ? { 'x-mock-user': mockUser } : {}),
          ...(orgId ? { 'x-org': orgId } : {}),
        },
        data: body,
      });

      if (res.status === 200) {
        if (isEdit && ncId) {
          toast.success('Non-Conformance updated');
          router.push(`/supplier-quality/${ncId}`);
        } else if (res.data?.id) {
          toast.success('Non-Conformance created');
          router.push(`/supplier-quality/${res.data.id}`);
        }
      }
    } catch (err: any) {
      setError(
        err?.response?.data?.detail ||
          'Failed to submit form. Please try again.',
      );
      console.error('Submission error:', err);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === 'repeat_count' ? Number(value) : value,
    }));
    setError(undefined);
  };

  const handleSelectChange = (name: keyof FormData, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    setError(undefined);
  };

  const handleCategoryToggle = (categoryId: string) => {
    setFormData((prev) => {
      // When category_ids is undefined or null, currentCategories becomes []
      const currentCategories = prev.category_ids
        ? prev.category_ids.map((cat) =>
            typeof cat === 'object' ? cat.id : cat,
          )
        : []; // ← This handles undefined/null case

      // Since currentCategories is [], includes() returns false
      // So it will add the categoryId
      const newCategories = currentCategories.includes(categoryId)
        ? currentCategories.filter((id) => id !== categoryId)
        : [...currentCategories, categoryId]; // ← Creates new array with first category

      return {
        ...prev,
        category_ids: newCategories, // ← Now it's ["new-category-id"]
      };
    });
  };

  // NEW: Handle custom fields change
  const handleCustomFieldsChange = (customFields: Record<string, string>) => {
    setFormData((prev) => ({
      ...prev,
      custom_fields: customFields,
    }));
  };

  // NEW: Handle files change
  const handleFilesChange = (files: File[]) => {
    setFormData((prev) => ({
      ...prev,
      supporting_documents: files,
    }));
  };

  const detectNcType = (data: any): string => {
    // Check in order of specificity
    if (data.part_number) return 'Product';
    if (data.service_category) return 'Service';
    if (data.process_step || data.process_stage) return 'Process';
    if (data.document_type || data.revision_number) return 'Documentation';
    if (data.delivery_reference || data.delivery_date) return 'Delivery';
    if (data.system_area || data.impacted_users) return 'System';
    if (data.type_description || data.other_information) return 'Other';

    return 'Product'; // default
  };

  useEffect(() => {
    if (initialData) {
      const detectedType = detectNcType(initialData);

      console.log('initialData', initialData);

      setFormData((prev) => ({
        nc_title: initialData.nc_title ?? '',
        supplier: initialData.supplier ?? '',
        person_responsible: initialData.person_responsible ?? '',
        issue_description: initialData.issue_description ?? '',
        severity: initialData.severity ?? 'Minor',
        priority: initialData.priority ?? 'Medium',
        repeat_count: initialData.repeat_count ?? 1,
        nc_type: detectedType,
        department_id: initialData.department_id ?? '',
        category_ids: initialData.categories ?? [],
        part_number: initialData.part_number ?? '',
        lot_number: initialData.lot_number ?? '',
        batch_id: initialData.batch_id ?? '',
        service_category: initialData.service_category ?? '',
        process_name:
          (initialData as any).process_step ?? initialData.process_name ?? '',
        process_stage: initialData.process_stage ?? '',
        document_id:
          (initialData as any).document_type ?? initialData.document_id ?? '',
        revision_number: initialData.revision_number ?? '',
        delivery_date: initialData.delivery_date ?? '',
        po_number:
          (initialData as any).delivery_reference ??
          initialData.po_number ??
          '',
        system_module_name:
          (initialData as any).system_area ??
          initialData.system_module_name ??
          '',
        impacted_users: initialData.impacted_users ?? '',
        type_description: initialData.type_description ?? '',
        other_information: initialData.other_information ?? '',
        additional_notes: initialData.additional_notes ?? '',
        due_date: initialData.due_date ?? '',
        supporting_documents: initialData.supporting_documents ?? [],
        custom_fields: initialData.custom_fields ?? {},
      }));
    }
  }, [isEdit, initialData]);

  return (
    <div className="bg-white min-h-screen">
      <div className="my-5">
        <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-5">
          <Tooltip>
            <TooltipTrigger>
              <Link
                className="w-10 h-10 flex items-center justify-center bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                href={`/supplier-quality/supplier-non-conformance`}
              >
                <Image src={BackButton} alt="" />
              </Link>
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm text-dark-300">Back</div>
            </TooltipContent>
          </Tooltip>
          {isEdit ? 'Edit Non-Conformance' : 'Create New Non-Conformance'}
        </div>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        {!categoriesLoading && (
          <CoreInformationSection
            formData={formData}
            validationErrors={validationErrors}
            handleChange={handleChange}
            handleSelectChange={handleSelectChange}
            suppliers={supplierOptions}
            users={userOptions}
            departments={departmentOptions}
            categories={categoryOptions}
            isEdit={isEdit}
            onCategoryToggle={handleCategoryToggle}
          />
        )}

        <AdditionalFieldsSection
          formData={formData}
          validationErrors={validationErrors}
          handleChange={handleChange}
          handleSelectChange={handleSelectChange}
          onCustomFieldsChange={handleCustomFieldsChange}
          onFilesChange={handleFilesChange}
          isEdit={isEdit}
        />

        {!isEdit && (
          <AdditionalDetailsSection
            formData={formData}
            validationErrors={validationErrors}
            handleChange={handleChange}
            isEdit={isEdit}
          />
        )}

        <div className="flex justify-end">
          <PrimaryButton
            type="submit"
            size="medium"
            text={
              isSubmitting
                ? 'Submitting...'
                : isEdit
                ? 'Update Non-Conformance'
                : 'Create Non-Conformance'
            }
            disabled={isSubmitting}
          />
        </div>
      </form>
    </div>
  );
};

export default CreateNCFormEnhanced;
