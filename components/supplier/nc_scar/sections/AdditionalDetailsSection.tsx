import { Label } from '@/components/common/label';
import { FormData } from '../CreateNCFormEnhanced';

import { Textarea } from '@/components/common/textarea';

interface AdditionalDetailsSectionProps {
  formData: FormData;
  validationErrors: Record<string, string[]>;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  isEdit: boolean;
}

const AdditionalDetailsSection: React.FC<AdditionalDetailsSectionProps> = ({
  formData,
  validationErrors,
  handleChange,
  isEdit,
}) => {
  return (
    <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-sm">
      <h2 className="text-lg font-semibold text-gray-900 mb-6 border-b border-gray-200 pb-2">
        Additional Details / Notes
      </h2>

      {/* Mitigation Description */}
      <div className="mt-6 mb-3">
        <Label
          htmlFor="additional_notes"
          className="text-sm font-medium text-gray-700"
        >
          Additional Details / Notes
        </Label>
        <Textarea
          id="additional_notes"
          name="additional_notes"
          value={formData.additional_notes}
          onChange={handleChange}
          rows={5}
          placeholder="Add any extra information, observations, or context here"
          className="mt-1"
          errorMsg={validationErrors.additional_notes[0]}
        />
      </div>

      <p className="text-sm text-gray-600">
        Optional field for any additional context, temporary custom fields, or
        SME notes.
      </p>
    </div>
  );
};

export default AdditionalDetailsSection;
