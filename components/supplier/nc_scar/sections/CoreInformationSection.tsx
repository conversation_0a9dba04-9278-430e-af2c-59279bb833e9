import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { Textarea } from '@/components/common/textarea';
import { FormData } from '../CreateNCFormEnhanced';
import { X } from 'lucide-react';
import { ReactSelectMulti } from '@/components/common/multiSelectInput';
import { se } from 'date-fns/locale';

interface CoreInformationSectionProps {
  formData: FormData;
  validationErrors: Record<string, string[]>;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  handleSelectChange: (name: keyof FormData, value: string) => void;
  suppliers: Array<{ id: string; legal_name: string }>;
  users: Array<{ id: string; full_name: string }>;
  departments: Array<{ id: string; name: string }>; // NEW
  categories: Array<{ id: string; name: string }>; // NEW
  onCategoryToggle?: (categoryId: string) => void; // NEW
  isEdit?: boolean;
}

const CoreInformationSection: React.FC<CoreInformationSectionProps> = ({
  formData,
  validationErrors,
  handleChange,
  handleSelectChange,
  suppliers,
  users,
  departments = [],
  categories = [],
  onCategoryToggle,
  isEdit,
}) => {
  const severityLevels = [
    { value: 'Critical', label: 'Critical' },
    { value: 'Major', label: 'Major' },
    { value: 'Minor', label: 'Minor' },
  ];

  const priorityLevels = [
    { value: 'Low', label: 'Low' },
    { value: 'Medium', label: 'Medium' },
    { value: 'High', label: 'High' },
    { value: 'Critical', label: 'Critical' },
  ];

  const ncTypes = [
    'Product',
    'Service',
    'Process',
    'Documentation',
    'Delivery',
    'System',
    'Other',
  ];

  const selectedCategories = formData.category_ids
    ? formData.category_ids.map((cat) =>
        typeof cat === 'object' ? cat.id : cat,
      )
    : [];

  return (
    <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-sm">
      <h2 className="text-lg font-semibold text-gray-900 mb-6 border-b border-gray-200 pb-2">
        Core Information
      </h2>
      <p className="text-sm text-gray-600 mb-6">
        Provide basic information about the Non-Conformance
      </p>

      {/* First row: NC Title, Supplier */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <Label
            htmlFor="nc_title"
            className="text-sm font-medium text-gray-700"
          >
            NC Title*
          </Label>
          <Input
            id="nc_title"
            name="nc_title"
            value={formData.nc_title}
            onChange={handleChange}
            placeholder="Brief descriptive title"
            className="mt-1"
            errorMsg={validationErrors.nc_title?.[0]}
          />
        </div>
        <div>
          <Label
            htmlFor="supplier"
            className="text-sm font-medium text-gray-700"
          >
            Supplier*
          </Label>
          <Select
            value={formData.supplier}
            onValueChange={(value) => handleSelectChange('supplier', value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select supplier" />
            </SelectTrigger>
            <SelectContent>
              {suppliers.map((s) => (
                <SelectItem key={s.id} value={s.id}>
                  {s.legal_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {validationErrors.supplier && (
            <p className="text-red-500 text-sm mt-1">
              {validationErrors.supplier[0]}
            </p>
          )}
        </div>
      </div>

      {/* Second row: Person Responsible, Department */}
      {/* Second row: Person Responsible, Department */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <Label
            htmlFor="person_responsible"
            className="text-sm font-medium text-gray-700"
          >
            Assign To*
          </Label>
          <Select
            value={
              typeof formData.person_responsible === 'object'
                ? formData.person_responsible?.id
                : formData.person_responsible
            }
            onValueChange={(value) =>
              handleSelectChange('person_responsible', value)
            }
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select person" />
            </SelectTrigger>
            <SelectContent>
              {users.map((u) => (
                <SelectItem key={u.id} value={u.id}>
                  {u.full_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {validationErrors.person_responsible && (
            <p className="text-red-500 text-sm mt-1">
              {validationErrors.person_responsible[0]}
            </p>
          )}
        </div>

        <div>
          <Label
            htmlFor="department_id"
            className="text-sm font-medium text-gray-700"
          >
            Department*
          </Label>
          <Select
            value={formData.department_id}
            onValueChange={(value) =>
              handleSelectChange('department_id', value)
            }
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select department" />
            </SelectTrigger>
            <SelectContent>
              {departments.length === 0 ? (
                <SelectItem value="no-departments" disabled>
                  No departments available
                </SelectItem>
              ) : (
                departments.map((dept) => (
                  <SelectItem key={dept.id} value={dept.id}>
                    {dept.name}
                  </SelectItem>
                ))
              )}
            </SelectContent>
          </Select>
          {validationErrors.department_id && (
            <p className="text-red-500 text-sm mt-1">
              {validationErrors.department_id[0]}
            </p>
          )}
        </div>
      </div>

      {/* Third row: Issue Description */}
      <div className="mb-6">
        <Label
          htmlFor="issue_description"
          className="text-sm font-medium text-gray-700"
        >
          Issue Description*
        </Label>
        <Textarea
          id="issue_description"
          name="issue_description"
          value={formData.issue_description}
          onChange={handleChange}
          rows={3}
          placeholder="Enter Issue Description"
          className="mt-1"
          errorMsg={validationErrors.issue_description?.[0]}
        />
      </div>

      {/* Fourth row: Severity, Priority, Repeat Count, NC Type */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
        <div>
          <Label
            htmlFor="severity"
            className="text-sm font-medium text-gray-700"
          >
            Severity*
          </Label>
          <Select
            value={formData.severity}
            onValueChange={(value) => handleSelectChange('severity', value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select severity" />
            </SelectTrigger>
            <SelectContent>
              {severityLevels.map((level) => (
                <SelectItem key={level.value} value={level.value}>
                  {level.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {validationErrors.severity && (
            <p className="text-red-500 text-sm mt-1">
              {validationErrors.severity[0]}
            </p>
          )}
        </div>

        <div>
          <Label
            htmlFor="priority"
            className="text-sm font-medium text-gray-700"
          >
            Priority*
          </Label>
          <Select
            value={formData.priority}
            onValueChange={(value) => handleSelectChange('priority', value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select priority" />
            </SelectTrigger>
            <SelectContent>
              {priorityLevels.map((level) => (
                <SelectItem key={level.value} value={level.value}>
                  {level.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {validationErrors.priority && (
            <p className="text-red-500 text-sm mt-1">
              {validationErrors.priority[0]}
            </p>
          )}
        </div>

        <div>
          <Label
            htmlFor="repeat_count"
            className="text-sm font-medium text-gray-700"
          >
            Repeat Count*
          </Label>
          <Input
            id="repeat_count"
            name="repeat_count"
            type="number"
            min="1"
            value={formData.repeat_count}
            onChange={handleChange}
            className="mt-1"
            errorMsg={validationErrors.repeat_count?.[0]}
          />
        </div>

        <div>
          <Label
            htmlFor="nc_type"
            className="text-sm font-medium text-gray-700"
          >
            NC Type*
          </Label>
          <Select
            value={formData.nc_type}
            onValueChange={(value) => handleSelectChange('nc_type', value)}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              {ncTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {validationErrors.nc_type && (
            <p className="text-red-500 text-sm mt-1">
              {validationErrors.nc_type[0]}
            </p>
          )}
        </div>
      </div>

      {/* Fifth row: Categories (Multi-select) */}
      <div className="mb-6">
        <Label className="text-sm font-medium text-gray-700">Categories</Label>
        <p className="text-xs text-gray-500 mb-2">
          Select applicable categories for this non-conformance
        </p>

        {/* Selected categories */}
        {selectedCategories.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-3">
            {selectedCategories.map((catId) => {
              const category = categories.find((c) => c.id === catId);
              return category ? (
                <span
                  key={catId}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                >
                  {category.name}
                  <button
                    type="button"
                    onClick={() => onCategoryToggle?.(catId)}
                    className="hover:bg-blue-200 rounded-full p-0.5"
                  >
                    <X className="w-3 h-3" />
                  </button>
                </span>
              ) : null;
            })}
          </div>
        )}

        {/* Category selector */}
        {categories.length === 0 ? (
          <div className="mt-1 p-3 border border-gray-200 rounded-md bg-gray-50 text-sm text-gray-500">
            No categories available
          </div>
        ) : (
          <Select
            value=""
            onValueChange={(value) => {
              if (value && !selectedCategories.includes(value)) {
                onCategoryToggle?.(value);
              }
            }}
          >
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Add a category..." />
            </SelectTrigger>
            <SelectContent>
              {categories
                .filter((cat) => !selectedCategories.includes(cat.id))
                .map((cat) => (
                  <SelectItem key={cat.id} value={cat.id}>
                    {cat.name}
                  </SelectItem>
                ))}
              {categories.filter((cat) => !selectedCategories.includes(cat.id))
                .length === 0 && (
                <SelectItem value="all-selected" disabled>
                  All categories selected
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        )}
        {validationErrors.category_ids && (
          <p className="text-red-500 text-sm mt-1">
            {validationErrors.category_ids[0]}
          </p>
        )}
      </div>

      {/* Sixth row: Due Date */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <Label
            htmlFor="due_date"
            className="text-sm font-medium text-gray-700"
          >
            Target Due Date
          </Label>
          <Input
            id="due_date"
            name="due_date"
            type="date"
            value={formData.due_date || ''}
            onChange={handleChange}
            className="mt-1"
            errorMsg={validationErrors.due_date?.[0]}
          />
        </div>
      </div>
    </div>
  );
};

export default CoreInformationSection;
