import React, { useState, useEffect } from 'react';
import { Plus, X, Upload } from 'lucide-react';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { FormData } from '../CreateNCFormEnhanced';
import PrimaryButton from '@/components/common/button/primaryButton';

interface AdditionalFieldsSectionProps {
  formData: FormData;
  validationErrors: Record<string, string[]>;
  handleChange: (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => void;
  handleSelectChange?: (name: keyof FormData, value: string) => void;
  onCustomFieldsChange?: (customFields: Record<string, string>) => void;
  onFilesChange?: (files: File[]) => void;
  isEdit: boolean;
}

const AdditionalFieldsSection: React.FC<AdditionalFieldsSectionProps> = ({
  formData,
  validationErrors,
  handleChange,
  handleSelectChange,
  onCustomFieldsChange,
  onFilesChange,
  isEdit,
}) => {
  const [customFieldLabel, setCustomFieldLabel] = useState('');
  const [customFieldValue, setCustomFieldValue] = useState('');
  const [customFieldErrors, setCustomFieldErrors] = useState<{
    label?: string;
    value?: string;
  }>({});
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const serviceCategories = [
    'communication',
    'response time',
    'documentation support',
    'technical support',
    'customer service',
    'training',
  ];

  // Convert label to valid key
  const sanitizeKey = (label: string): string => {
    return label
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '_')
      .replace(/[^a-z0-9_]/g, '');
  };

  // Validate custom field
  const validateCustomFieldInput = (): boolean => {
    const errors: { label?: string; value?: string } = {};

    if (!customFieldLabel.trim()) {
      errors.label = 'Label is required';
    } else if (customFieldLabel.trim().length < 2) {
      errors.label = 'Label must be at least 2 characters';
    } else if (customFieldLabel.trim().length > 50) {
      errors.label = 'Label must not exceed 50 characters';
    } else {
      const key = sanitizeKey(customFieldLabel);
      if (!key) {
        errors.label = 'Label must contain at least one alphanumeric character';
      } else if (formData.custom_fields && key in formData.custom_fields) {
        errors.label = `Field "${key}" already exists. Please use a different label.`;
      }
    }

    if (!customFieldValue.trim()) {
      errors.value = 'Value is required';
    } else if (customFieldValue.trim().length > 200) {
      errors.value = 'Value must not exceed 200 characters';
    }

    setCustomFieldErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Add custom field
  const addCustomField = () => {
    if (!validateCustomFieldInput()) {
      return;
    }

    const key = sanitizeKey(customFieldLabel);
    const updatedFields = {
      ...(formData.custom_fields || {}),
      [key]: customFieldValue.trim(),
    };

    console.log('Adding custom field:', key, '=', customFieldValue.trim());
    console.log('Updated custom fields:', updatedFields);

    onCustomFieldsChange?.(updatedFields);
    setCustomFieldLabel('');
    setCustomFieldValue('');
    setCustomFieldErrors({});
  };

  // Remove custom field
  const removeCustomField = (key: string) => {
    const { [key]: removed, ...rest } = formData.custom_fields || {};
    onCustomFieldsChange?.(rest);
  };

  // Handle file upload
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const newFiles = [...uploadedFiles, ...files];
    setUploadedFiles(newFiles);
    onFilesChange?.(newFiles);
  };

  // Remove file
  const removeFile = (index: number) => {
    const newFiles = uploadedFiles.filter((_, i) => i !== index);
    setUploadedFiles(newFiles);
    onFilesChange?.(newFiles);
  };

  return (
    <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-sm">
      <div className="flex justify-between items-center w-full mb-6">
        <h2 className="text-lg font-semibold text-gray-900">
          Additional Fields (based on NC Type)
        </h2>
      </div>

      {/* Type-specific fields */}
      {formData.nc_type === 'Product' && (
        <div>
          <p className="text-sm text-gray-600 mb-6">Product Information</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <Label
                htmlFor="part_number"
                className="text-sm font-medium text-gray-700"
              >
                Part Number*
              </Label>
              <Input
                id="part_number"
                name="part_number"
                value={formData.part_number}
                onChange={handleChange}
                placeholder="e.g., ABC123"
                className="mt-1"
                errorMsg={validationErrors.part_number?.[0]}
              />
            </div>
            <div>
              <Label
                htmlFor="lot_number"
                className="text-sm font-medium text-gray-700"
              >
                Lot Number
              </Label>
              <Input
                id="lot_number"
                name="lot_number"
                value={formData.lot_number}
                onChange={handleChange}
                placeholder="Optional"
                className="mt-1"
                errorMsg={validationErrors.lot_number?.[0]}
              />
            </div>
            <div>
              <Label
                htmlFor="batch_id"
                className="text-sm font-medium text-gray-700"
              >
                Batch ID
              </Label>
              <Input
                id="batch_id"
                name="batch_id"
                value={formData.batch_id}
                onChange={handleChange}
                placeholder="Optional"
                className="mt-1"
                errorMsg={validationErrors.batch_id?.[0]}
              />
            </div>
          </div>
        </div>
      )}

      {formData.nc_type === 'Service' && (
        <div>
          <p className="text-sm text-gray-600 mb-6">Service Information</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <Label
                htmlFor="service_category"
                className="text-sm font-medium text-gray-700"
              >
                Service Category*
              </Label>
              <Select
                value={formData.service_category}
                onValueChange={(v) =>
                  handleSelectChange?.('service_category', v)
                }
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {serviceCategories.map((cat) => (
                    <SelectItem key={cat} value={cat}>
                      {cat}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {validationErrors.service_category && (
                <p className="text-red-500 text-sm mt-1">
                  {validationErrors.service_category[0]}
                </p>
              )}
            </div>
          </div>
        </div>
      )}

      {formData.nc_type === 'Process' && (
        <div>
          <p className="text-sm text-gray-600 mb-6">Process Information</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <Label
                htmlFor="process_name"
                className="text-sm font-medium text-gray-700"
              >
                Process Name*
              </Label>
              <Input
                id="process_name"
                name="process_name"
                value={formData.process_name}
                onChange={handleChange}
                placeholder="e.g., Assembly"
                className="mt-1"
                errorMsg={validationErrors.process_name?.[0]}
              />
            </div>
            <div>
              <Label
                htmlFor="process_stage"
                className="text-sm font-medium text-gray-700"
              >
                Step/Stage
              </Label>
              <Input
                id="process_stage"
                name="process_stage"
                value={formData.process_stage}
                onChange={handleChange}
                placeholder="Optional"
                className="mt-1"
                errorMsg={validationErrors.process_stage?.[0]}
              />
            </div>
          </div>
        </div>
      )}

      {formData.nc_type === 'Documentation' && (
        <div>
          <p className="text-sm text-gray-600 mb-6">
            Documentation Information
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <Label
                htmlFor="document_id"
                className="text-sm font-medium text-gray-700"
              >
                Document ID*
              </Label>
              <Input
                id="document_id"
                name="document_id"
                value={formData.document_id}
                onChange={handleChange}
                placeholder="e.g., SOP-001"
                className="mt-1"
                errorMsg={validationErrors.document_id?.[0]}
              />
            </div>
            <div>
              <Label
                htmlFor="revision_number"
                className="text-sm font-medium text-gray-700"
              >
                Revision Number
              </Label>
              <Input
                id="revision_number"
                name="revision_number"
                value={formData.revision_number}
                onChange={handleChange}
                placeholder="Optional - e.g., Rev 1.2"
                className="mt-1"
                errorMsg={validationErrors.revision_number?.[0]}
              />
            </div>
          </div>
        </div>
      )}

      {formData.nc_type === 'Delivery' && (
        <div>
          <p className="text-sm text-gray-600 mb-6">Delivery Information</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <Label
                htmlFor="delivery_date"
                className="text-sm font-medium text-gray-700"
              >
                Delivery Date*
              </Label>
              <Input
                id="delivery_date"
                name="delivery_date"
                type="date"
                value={formData.delivery_date || ''}
                onChange={handleChange}
                placeholder="dd/mm/yyyy"
                className="mt-1"
                errorMsg={validationErrors.delivery_date?.[0]}
              />
            </div>
            <div>
              <Label
                htmlFor="po_number"
                className="text-sm font-medium text-gray-700"
              >
                PO Number
              </Label>
              <Input
                id="po_number"
                name="po_number"
                value={formData.po_number}
                onChange={handleChange}
                placeholder="Optional - Purchase Order"
                className="mt-1"
                errorMsg={validationErrors.po_number?.[0]}
              />
            </div>
          </div>
        </div>
      )}

      {formData.nc_type === 'System' && (
        <div>
          <p className="text-sm text-gray-600 mb-6">System Information</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <Label
                htmlFor="system_module_name"
                className="text-sm font-medium text-gray-700"
              >
                System/Module Name*
              </Label>
              <Input
                id="system_module_name"
                name="system_module_name"
                value={formData.system_module_name}
                onChange={handleChange}
                placeholder="e.g., Quality Management System"
                className="mt-1"
                errorMsg={validationErrors.system_module_name?.[0]}
              />
            </div>
            <div>
              <Label
                htmlFor="impacted_users"
                className="text-sm font-medium text-gray-700"
              >
                Impacted Users
              </Label>
              <Input
                id="impacted_users"
                name="impacted_users"
                value={formData.impacted_users}
                onChange={handleChange}
                placeholder="Optional - number or department"
                className="mt-1"
                errorMsg={validationErrors.impacted_users?.[0]}
              />
            </div>
          </div>
        </div>
      )}

      {formData.nc_type === 'Other' && (
        <div>
          <p className="text-sm text-gray-600 mb-6">Other Information</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <Label
                htmlFor="type_description"
                className="text-sm font-medium text-gray-700"
              >
                Type Description*
              </Label>
              <Input
                id="type_description"
                name="type_description"
                value={formData.type_description}
                onChange={handleChange}
                placeholder="Please describe the type of non-conformance"
                className="mt-1"
                errorMsg={validationErrors.type_description?.[0]}
              />
            </div>
            <div>
              <Label
                htmlFor="other_information"
                className="text-sm font-medium text-gray-700"
              >
                Other Information
              </Label>
              <Input
                id="other_information"
                name="other_information"
                value={formData.other_information}
                onChange={handleChange}
                placeholder="Additional context"
                className="mt-1"
                errorMsg={validationErrors.other_information?.[0]}
              />
            </div>
          </div>
        </div>
      )}

      {/* Supporting Documents */}

      {!isEdit && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h3 className="text-base font-semibold text-gray-900 mb-4">
            Supporting Documents
          </h3>
          <div className="mb-4">
            <label
              htmlFor="file-upload"
              className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 cursor-pointer transition-colors"
            >
              <Upload className="w-4 h-4" />
              Upload Files
            </label>
            <input
              id="file-upload"
              type="file"
              multiple
              onChange={handleFileChange}
              className="hidden"
              accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png"
            />
            <p className="text-xs text-gray-500 mt-2">
              Supported formats: PDF, DOC, DOCX, XLS, XLSX, JPG, PNG
            </p>
          </div>

          {uploadedFiles.length > 0 && (
            <div className="space-y-2">
              {uploadedFiles.map((file, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md"
                >
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-700">{file.name}</span>
                    <span className="text-xs text-gray-500">
                      ({(file.size / 1024).toFixed(1)} KB)
                    </span>
                  </div>
                  <button
                    type="button"
                    onClick={() => removeFile(index)}
                    className="p-1 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Custom Fields Section */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <h3 className="text-base font-semibold text-gray-900 mb-4">
          Custom Fields
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          Add multiple custom fields specific to your organization's needs. Each
          field will be stored as a key-value pair.
        </p>

        {/* Display added custom fields FIRST */}
        {formData.custom_fields &&
          Object.keys(formData.custom_fields).length > 0 && (
            <div className="space-y-4 mb-6">
              <p className="text-sm font-medium text-gray-700">
                Custom Fields Added (
                {Object.keys(formData.custom_fields).length}):
              </p>

              {Object.entries(formData.custom_fields).map(
                ([key, value], index) => (
                  <div
                    key={index}
                    className="flex items-start gap-3 p-4 bg-gray-50 border border-gray-200 rounded-lg"
                  >
                    <div className="flex-1 grid grid-cols-2 gap-3">
                      <div>
                        <label className="text-xs text-gray-600 mb-1 block">
                          Field Key
                        </label>
                        <input
                          type="text"
                          value={key}
                          disabled
                          className="w-full border-gray-300 rounded-md text-sm px-2 py-1.5 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                        />
                      </div>

                      <div>
                        <label className="text-xs text-gray-600 mb-1 block">
                          Field Value
                        </label>
                        <input
                          type="text"
                          value={value}
                          disabled
                          className="w-full border-gray-300 rounded-md text-sm px-2 py-1.5 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                        />
                      </div>
                    </div>

                    <button
                      type="button"
                      onClick={() => removeCustomField(key)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors mt-6"
                      title="Remove field"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                ),
              )}
            </div>
          )}

        {/* Add custom field form */}
        <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
          <p className="text-sm font-medium text-blue-900 mb-3">
            Add New Custom Field
          </p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
            <div>
              <Label
                htmlFor="custom_field_label"
                className="text-sm font-medium text-gray-700"
              >
                Field Label (Key)*
              </Label>
              <Input
                id="custom_field_label"
                value={customFieldLabel}
                onChange={(e) => {
                  setCustomFieldLabel(e.target.value);
                  setCustomFieldErrors({});
                }}
                placeholder="e.g., Inspector Name"
                className="mt-1"
                errorMsg={customFieldErrors.label}
                disabled={Object.keys(formData.custom_fields || {}).length >= 3}
              />
              {customFieldLabel && !customFieldErrors.label && (
                <p className="text-xs text-gray-500 mt-1">
                  Will be stored as:{' '}
                  <code className="bg-gray-200 px-1 py-0.5 rounded font-mono">
                    {sanitizeKey(customFieldLabel)}
                  </code>
                </p>
              )}
            </div>

            <div>
              <Label
                htmlFor="custom_field_value"
                className="text-sm font-medium text-gray-700"
              >
                Field Value*
              </Label>
              <Input
                id="custom_field_value"
                value={customFieldValue}
                onChange={(e) => {
                  setCustomFieldValue(e.target.value);
                  setCustomFieldErrors({});
                }}
                placeholder="Enter value"
                className="mt-1"
                errorMsg={customFieldErrors.value}
                disabled={Object.keys(formData.custom_fields || {}).length >= 3}
              />
            </div>
          </div>

          <div className="flex justify-between items-center">
            <p className="text-xs text-gray-600">
              You can add 3 custom fields. Each will be saved separately.
            </p>
            <PrimaryButton
              text="Add Field"
              icon={<Plus size={16} />}
              size="small"
              onClick={addCustomField}
              disabled={Object.keys(formData.custom_fields || {}).length >= 3}
              type="button"
            />
          </div>
        </div>

        {/* Empty state message */}
        {(!formData.custom_fields ||
          Object.keys(formData.custom_fields).length === 0) && (
          <div className="mt-4 p-4 bg-gray-50 border border-gray-200 rounded-md">
            <p className="text-sm text-gray-500 text-center">
              No custom fields added yet. Use the form above to add your first
              custom field.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdditionalFieldsSection;
