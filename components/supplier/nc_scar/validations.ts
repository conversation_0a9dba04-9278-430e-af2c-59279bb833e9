import { z } from 'zod';

export const nonConformanceSchema = {
  nc_title: z
    .string({
      required_error: 'NC Title is required',
      invalid_type_error: 'NC Title must be a string'
    })
    .min(1, 'NC Title is required')
    .min(5, 'Title must be at least 5 characters')
    .max(200, 'Title cannot exceed 200 characters')
    .trim(),
  
  supplier: z
    .string({
      required_error: 'Supplier selection is required'
    })
    .min(1, 'Please select a supplier'),
  
  person_responsible: z
    .string({
      required_error: 'Person responsible is required'
    })
    .min(1, 'Please select a responsible person'),
  
  issue_description: z
    .string({
      required_error: 'Issue description is required'
    })
    .min(1, 'Issue description is required')
    .min(10, 'Description must be at least 10 characters')
    .max(1000, 'Description cannot exceed 1000 characters')
    .trim(),
  
  severity: z
    .string({
      required_error: 'Please select severity level'
    })
    .min(1, 'Please select severity level'),
  
  repeat_count: z
    .number({
      required_error: 'Repeat count is required',
      invalid_type_error: 'Repeat count must be a number'
    })
    .int('Repeat count must be a whole number')
    .min(1, 'Repeat count must be at least 1')
    .max(999, 'Repeat count cannot exceed 999'),
  
  nc_type: z
    .string({
      required_error: 'Please select NC type'
    })
    .min(1, 'Please select NC type'),
  
  // Optional fields
 part_number: z
    .string({
      required_error: 'Part number is required'
    })
    .min(1, 'Part number is required')
    .regex(/^[A-Za-z0-9\-_]+$/, 'Part number can only contain letters, numbers, hyphens, and underscores'),
  
  lot_number: z
    .string()
    .optional()
    .or(z.literal('')),
  
  batch_id: z
    .string()
    .optional()
    .or(z.literal('')),
  
  additional_notes: z
    .string()
    .optional()
    .or(z.literal(''))
};

