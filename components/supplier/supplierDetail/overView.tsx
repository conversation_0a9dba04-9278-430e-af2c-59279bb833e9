import { Mail, MapPin, Phone, User } from 'lucide-react';

import { Supplier } from '@/pages/supplier-quality/supplier/[supplierId]';

type OverViewProps = {
  supplierData: Supplier | undefined; // handle first-render undefined
};

const OverView = ({ supplierData }: OverViewProps) => {
  return (
    <>
      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Basic Information Card */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold mb-6 text-gray-900">
            Basic Information
          </h2>


          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Row 1 */}
            <div>
              <p className="text-sm font-medium text-gray-500 mb-1">
                Supplier Status
              </p>
              <p className="text-base font-semibold text-gray-900">
                {supplierData?.status
                  ? supplierData.status.charAt(0).toUpperCase() +
                    supplierData.status.slice(1)
                  : 'NA'}
              </p>
            </div>

            <div className="text-right">
              <p className="text-sm font-medium text-gray-500 mb-1">
                Supplier Type
              </p>
              <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                {supplierData?.supplier_type || 'NA'}
              </span>
            </div>

            {/* Row 2 */}
            <div>
              <p className="text-sm font-medium text-gray-500 mb-1">
                Supply Category
              </p>
              <div className="flex flex-wrap gap-2">
                {supplierData?.categories &&
                supplierData.categories.length > 0 ? (
                  supplierData.categories.map((cat, index) => (
                    <span
                      key={cat.id || index}
                      className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {cat.name}
                    </span>
                  ))
                ) : (
                  <span className="text-sm text-gray-600">
                    No categories available
                  </span>
                )}
              </div>
            </div>

            <div className="text-right">
              <p className="text-sm font-medium text-gray-500 mb-1">
                Risk Tier
              </p>
              <span
                className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${
                  supplierData?.risk_tier?.toLowerCase() === 'high'
                    ? 'bg-red-100 text-red-800'
                    : supplierData?.risk_tier?.toLowerCase() === 'low'
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}
              >
                {supplierData?.risk_tier || '—'}
              </span>
            </div>

            {/* Row 3 - Full width items */}
            <div>
              <p className="text-sm font-medium text-gray-500 mb-1">Website</p>
              {supplierData?.website ? (
                <a
                  href={
                    supplierData.website.startsWith('http')
                      ? supplierData.website
                      : `https://${supplierData.website}`
                  }
                  target="_blank"
                  rel="noreferrer"
                  className="inline-flex items-center text-sm text-blue-700 hover:underline truncate max-w-full"
                  title={supplierData.website}
                >
                  {(() => {
                    try {
                      const urlObj = new URL(
                        supplierData.website.startsWith('http')
                          ? supplierData.website
                          : `https://${supplierData.website}`,
                      );
                      return urlObj.hostname.replace('www.', '');
                    } catch {
                      return supplierData.website.length > 30
                        ? supplierData.website.substring(0, 30) + '...'
                        : supplierData.website;
                    }
                  })()}
                </a>
              ) : (
                <span className="text-sm text-gray-600">—</span>
              )}
            </div>

            <div className="text-right">
              <p className="text-sm font-medium text-gray-500 mb-1">Location</p>
              <div className="flex items-center justify-end text-gray-900">
                <span className="text-sm truncate mr-1">
                  {(() => {
                    const primary =
                      supplierData?.addresses?.find((a) => a.is_primary) ||
                      supplierData?.addresses?.[0];
                    if (!primary) return '—';
                    const cityState = [primary.city, primary.state_province]
                      .filter(Boolean)
                      .join(', ');
                    const country = primary.country || '';
                    return [cityState, country].filter(Boolean).join(', ');
                  })()}
                </span>
                <MapPin className="w-4 h-4 flex-shrink-0" />
              </div>
            </div>
          </div>
        </div>

        {/* Primary Contact Card */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h2 className="text-lg font-semibold mb-6 text-gray-900">
            Primary Contact
          </h2>

          {(() => {
            const primaryContact = supplierData?.contacts?.find(
              (c) => c.is_primary,
            );
            if (!primaryContact) {
              return (
                <p className="text-gray-600">No primary contact available.</p>
              );
            }

            return (
              <div className="space-y-4">
                {/* Name */}
                <div className="flex items-start gap-3">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      {primaryContact.name}
                    </p>
                    <p className="text-sm text-gray-500">Primary Contact</p>
                  </div>
                </div>

                {/* Email */}
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <Mail className="w-4 h-4 text-gray-600" />
                  </div>
                  <div className="min-w-0">
                    {' '}
                    {/* ensures truncation works inside flex */}
                    <p
                      className="text-gray-900 truncate max-w-xs sm:max-w-sm md:max-w-md"
                      title={primaryContact.email || ''}
                    >
                      {primaryContact.email || '—'}
                    </p>
                    <p className="text-sm text-gray-500">Email Address</p>
                  </div>
                </div>

                {/* Phone */}
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    <Phone className="w-4 h-4 text-gray-600" />
                  </div>
                  <div>
                    <p className="text-gray-900">
                      {primaryContact.phone || '—'}
                    </p>
                    <p className="text-sm text-gray-500">Phone Number</p>
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      </div>


      {/* Additional Contacts Section */}
      <div className="mt-8 bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold mb-6 text-gray-900">
          Additional Contacts
        </h2>

        {(() => {
          const others = (supplierData?.contacts ?? []).filter(
            (c) => !c.is_primary,
          );

          if (!others.length) {
            return <p className="text-gray-600">No additional contacts.</p>;
          }

          return (
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
              {others.map((c) => (
                <div
                  key={c.id}
                  className="border border-gray-200 rounded-lg p-4"
                >
                  <div className="flex items-center gap-2 mb-2">
                    <User className="w-4 h-4 text-gray-500" />
                    {/* Name */}
                    <h3 className="font-semibold text-gray-900">
                      {c.name || '—'}
                    </h3>
                  </div>

                  {/* Email (truncate/ellipsis) */}
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 text-sm">
                      <Mail className="w-4 h-4 text-gray-400 shrink-0" />
                      <a
                        href={c.email ? `mailto:${c.email}` : undefined}
                        className="text-gray-600 block max-w-full truncate"
                        title={c.email || ''}
                      >
                        {c.email || '—'}
                      </a>
                    </div>

                    {/* Phone */}
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className="w-4 h-4 text-gray-400 shrink-0" />
                      {c.phone ? (
                        <a href={`tel:${c.phone}`} className="text-gray-600">
                          {c.phone}
                        </a>
                      ) : (
                        <span className="text-gray-600">—</span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          );
        })()}
      </div>

      {/* Site Locations Section */}
      <div className="mt-8 bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold mb-6 text-gray-900">
          Site Locations
        </h2>

        {supplierData?.addresses?.length ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-stretch">
            {supplierData.addresses.map((addr) => (
              <div
                key={addr.id}
                className="border border-gray-200 rounded-lg p-6 h-full flex flex-col"
              >
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {addr.city || addr.country || 'Location'}
                  </h3>
                  {addr.is_primary && (
                    <span className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      Primary
                    </span>
                  )}
                </div>
                <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-800">
                  Address
                </span>

                <div className="flex items-start gap-2 mb-2 mt-4 text-sm">
                  <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                  <div className="min-w-0">
                    <p className="text-gray-900 break-words">
                      {[addr.address_line1, addr.address_line2]
                        .filter(Boolean)
                        .join(', ')}
                    </p>
                    <p className="text-gray-900 break-words">
                      {[addr.city, addr.state_province, addr.postal_code]
                        .filter(Boolean)
                        .join(', ')}
                    </p>
                    <p className="text-gray-900">{addr.country}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-600">No addresses found.</p>
        )}

      </div>
    </>
  );
};

export default OverView;
