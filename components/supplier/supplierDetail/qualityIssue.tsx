import React, { useState } from 'react';
import { ClipboardList } from 'lucide-react'; // for the icon
import CommonTable from '@/components/common/table';

const QualityIssues = () => {
  const [activeTab, setActiveTab] = useState('scars');
  // Dummy evaluation data
  const dummyDocuments = [
    {
      file_name: 'compliance_report_q1.pdf',
      document_name: 'Compliance Report Q1',
      document_type: 'Compliance',
      description: 'Quarterly compliance report for Q1',
      reminder_days: 30,
      issue_Date: '2025-01-15',
      expiry_Date: '2025-12-31',
    },
    {
      file_name: 'safety_certificate.png',
      document_name: 'Safety Certificate',
      document_type: 'Certification',
      description: 'Workplace safety compliance certificate',
      reminder_days: 60,
      issue_Date: '2024-11-01',
      expiry_Date: '2025-11-01',
    },
    {
      file_name: 'vendor_contract.pdf',
      document_name: 'Vendor Contract',
      document_type: 'Contract',
      description: 'Annual contract agreement with vendor',
      reminder_days: 90,
      issue_Date: '2025-02-01',
      expiry_Date: '2026-01-31',
    },
  ];

  // Dummy document columns
  const getDocumentColumns = () => [
    {
      headerName: 'File Name',
      field: 'file_name',
      cellRenderer: (params: any) => params.data.file_name || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.file_name || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 150,
    },
    {
      headerName: 'Document Name',
      field: 'document_name',
      cellRenderer: (params: any) => params.data.document_name || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.document_name || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 180,
    },
    {
      headerName: 'Document Type',
      field: 'document_type',
      cellRenderer: (params: any) => params.data.document_type || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.document_type || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 150,
    },
    {
      headerName: 'Description',
      field: 'description',
      cellRenderer: (params: any) => params.data.description || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.description || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 200,
      flex: 2,
    },
    {
      headerName: 'Reminder Days',
      field: 'reminder_days',
      cellRenderer: (params: any) => params.data.reminder_days ?? '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) =>
        params.data.reminder_days ? String(params.data.reminder_days) : '',
      valueFormatter: (params: any) => params.value ?? '-',
      filter: true,
      minWidth: 150,
    },
    {
      headerName: 'Issue Date',
      field: 'issue_Date',
      cellRenderer: (params: any) => params.data.issue_Date || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.issue_Date || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 150,
    },
    {
      headerName: 'Expiry Date',
      field: 'expiry_Date',
      cellRenderer: (params: any) => params.data.expiry_Date || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.expiry_Date || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 150,
    },
  ];

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      {/* Header row */}
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">
          Quality Issues & Actions
        </h2>
      </div>

      {/* Tabs */}
      <div className="flex items-center gap-2 bg-gray-100 p-1 rounded-xl w-fit mb-6">
        {/* SCARs */}
        <button
          onClick={() => setActiveTab('scars')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
            activeTab === 'scars'
              ? 'bg-white text-teal-600 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          SCARs
        </button>
        {/* Non-Conformances */}
        <button
          onClick={() => setActiveTab('nonconformances')}
          className={`px-4 py-2 rounded-lg text-sm font-medium transition-all ${
            activeTab === 'nonconformances'
              ? 'bg-white text-teal-600 shadow-sm'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          Non-Conformances
        </button>
      </div>

      {/* Tab Content */}
      {/* <div className="bg-gray-50 rounded-lg p-10 flex flex-col items-center justify-center text-center"> */}
      <div>
        {/* <ClipboardList size={40} className="text-gray-400 mb-4" /> */}
        {activeTab === 'scars' ? (
          <>
            <div className="w-full">
              <CommonTable
                data={{ records: dummyDocuments }}
                columnDefs={getDocumentColumns()}
                searchPlaceholder="Search by ID, name, description, location, owner or status"
                isLoading={false}
                searchBox={false}
              />
            </div>
          </>
        ) : (
          <>
            {/* <p className="text-gray-700 font-medium">
              No Non-Conformances found
            </p>
            <p className="text-gray-500 text-sm">
              Non-Conformances will appear once created
            </p> */}
            <div className="w-full">
              <CommonTable
                data={{ records: dummyDocuments }}
                columnDefs={getDocumentColumns()}
                searchPlaceholder="Search by ID, name, description, location, owner or status"
                isLoading={false}
                searchBox={false}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default QualityIssues;
