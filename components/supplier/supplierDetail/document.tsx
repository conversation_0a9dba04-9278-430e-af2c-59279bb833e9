import React, { useEffect, useState } from 'react';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import LinkButton from '@/components/common/button/linkButton';
import { ChevronDown, ChevronUp, Eye, EyeIcon, Plus } from 'lucide-react';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import { EvaluationModal } from '@/components/vendor/modal/evaluationModal';
import AddDocumentModal from '../modals/addDocumentModal';
import useFetch from '@/hooks/useFetch';
import { useAuthStore } from '@/globalProvider/authStore';
import { useRouter } from 'next/router';
import DocumentAlertsList from '../alerts/documentAlertList';
import { formatDate } from '@/utils/time';
import { useDelete } from '@/hooks/useDelete';
import { toast } from 'react-toastify';
import DeleteModal from '@/components/common/modals/deleteModal';
import DocumentViewModal from '@/components/common/modals/documentViewModal';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import axios from 'axios';
import DocumentViewModalWithSidebar from '@/components/document/components/modals/documentViewModalWithSidebar';
import { SourceTypes } from '@/components/common/constants/constant';

interface ISupplierDocuments {
  records: Document[];
  alert_summary?: any;
}

interface Document {
  id: string;
  name?: string;
  document_type?: string;
  description?: string;
  reminder_days?: number;
  issue_date?: string;
  expiry_date?: string;
  is_required?: boolean;
  file_path?: string;
  file_extension?: string;
  document?: {
    id: string;
    title: string;
    doc_id?: string;
    status?: string;
  };
}

const Document: React.FC = () => {
  const { accessToken, user } = useAuthStore();
  const router = useRouter();
  const { supplierId } = router.query;

  const [openEvaluationModal, setOpenEvaluationModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(
    null,
  );
  const [editSupplier, setEditSupplier] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [documentViewModal, setDocumentViewModal] = useState(false);
  const [documentViewModalWithSideBar, setDocumentViewModalWithSideBar] =
    useState(false);

  const [documentData, setDocumentData] = useState<any>(null);

  const { data, isLoading, reFetch } = useFetch<ISupplierDocuments>(
    accessToken,
    supplierId ? `suppliers/${supplierId}/documents` : undefined,
  );

  const {
    deleteData,
    isLoading: isDeleting,
    response: onDelete,
  } = useDelete<any>();

  const handleEdit = (rowData: Document) => {
    setSelectedDocument(rowData);
    setEditSupplier(true);
  };

  const handleDeleteModal = (rowData: Document) => {
    setSelectedDocument(rowData);
    setShowDeleteModal(true);
  };

  const handleDelete = (documentId: string) => {
    async function fetch() {
      await deleteData(
        accessToken,
        `suppliers/${supplierId}/documents/${documentId}`,
      );
    }
    fetch();
  };

  useEffect(() => {
    if (onDelete) {
      setShowDeleteModal(false);
      toast.success('Document deleted successfully');
      reFetch();
    }
  }, [onDelete]);

  const orgId =
    typeof window !== 'undefined'
      ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
      : null;

  const NEXT_PUBLIC_URL = process.env.NEXT_PUBLIC_URL;
  const NEXT_PUBLIC_VERSION = process.env.NEXT_PUBLIC_VERSION;

  const handleGetDocument = async (documentId: string) => {
    if (documentId) {
      const baseUrl = NEXT_PUBLIC_URL;
      const productVersion = NEXT_PUBLIC_VERSION;

      const config = {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };

      const url = `${baseUrl}/${productVersion}/documents/${documentId}?source=${SourceTypes.SUPPLIER}&id=${supplierId}`;
      try {
        const response = await axios.get(url, config);
        setDocumentData(response.data.record);
        setDocumentViewModalWithSideBar(true);
        return;
      } catch (error) {
        console.error('Error fetching clauses:', error);
      }
    }
  };

  const columnDefs: any[] = [
    {
      headerName: 'Document Name',
      field: 'document_name',
      cellRenderer: (params: any) => params.data.name || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.name || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 180,
    },
    {
      headerName: 'Document Type',
      field: 'document_type',
      cellRenderer: (params: any) => params.data.document_type || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.document_type || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 150,
    },
    {
      headerName: 'Description',
      field: 'description',
      cellRenderer: (params: any) => params.data.description || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.description || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 200,
      flex: 2,
    },
    {
      headerName: 'Reminder Days',
      field: 'reminder_days',
      cellRenderer: (params: any) => params.data.reminder_days ?? '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) =>
        params.data.reminder_days ? String(params.data.reminder_days) : '',
      valueFormatter: (params: any) => params.value ?? '-',
      filter: true,
      minWidth: 150,
    },
    {
      headerName: 'Issue Date',
      field: 'issue_Date',
      cellRenderer: (params: any) =>
        formatDate(params.data.issue_date, false) || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.issue_date || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 150,
    },
    {
      headerName: 'Expiry Date',
      field: 'expiry_Date',
      cellRenderer: (params: any) =>
        formatDate(params.data.expiry_date, false) || '-',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => params.data.expiry_date || '',
      valueFormatter: (params: any) => params.value || '-',
      filter: true,
      minWidth: 150,
    },

    {
      headerName: 'Action',
      field: 'action',
      resizable: false,
      filter: false,
      sortable: false,
      width: 50,
      cellRenderer: (params: any) => (
        <div className="flex items-start mt-1">
          <Tooltip>
            <TooltipTrigger>
              {params.data.document && (
                <div
                  className="text-black bg-white-150 rounded-full cursor-pointer h-10 w-10 flex items-center justify-center"
                  onClick={() => {
                    handleGetDocument(params.data.document.id);
                  }}
                >
                  <EyeIcon className="h-6 w-6" />
                </div>
              )}
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm text-dark-300">View Document</div>
            </TooltipContent>
          </Tooltip>
          <Tooltip>
            <TooltipTrigger>
              {params.data.file_path && (
                <div
                  className="text-black bg-white-150 rounded-full cursor-pointer h-10 w-10 flex items-center justify-center"
                  onClick={() => {
                    setDocumentViewModal(true);
                    setSelectedDocument(params.data);
                  }}
                >
                  <EyeIcon className="h-6 w-6" />
                </div>
              )}
            </TooltipTrigger>
            <TooltipContent>
              <div className="text-sm text-dark-300">View Document</div>
            </TooltipContent>
          </Tooltip>
        </div>
      ),
    },

    {
      headerName: 'Manage',
      field: 'manage',
      sortable: false,
      resizable: false,
      getQuickFilterText: () => '',
      valueFormatter: () => '',
      cellRenderer: (params: any) => (
        <ManageCellRenderer
          rowData={params.data}
          handleEdit={handleEdit}
          handleDelete={handleDeleteModal}
          hideDelete={false}
        />
      ),
      width: 100,
      pinned: 'right',
      filter: false,
    },
  ];

  return (
    <div className="mb-5">
      <DocumentAlertsList
        alertSummary={data?.alert_summary}
        isLoading={isLoading}
      />

      <div className="px-7 py-6 border border-white-300 rounded-lg mt-5 mb-6">
        <div className="flex items-center justify-between mb-5">
          <h2 className="text-lg font-semibold text-gray-900">
            Documents & Certifications
          </h2>

          {documentViewModal && (
            <Dialog
              open={documentViewModal}
              onOpenChange={setDocumentViewModal}
            >
              {documentViewModal && selectedDocument && (
                <DocumentViewModal
                  title={selectedDocument?.name || ''}
                  filePath={selectedDocument?.file_path || ''}
                  extension={
                    (selectedDocument?.file_extension as
                      | 'pdf'
                      | 'html'
                      | 'docx'
                      | 'doc'
                      | 'png'
                      | 'jpeg'
                      | 'jpg') || 'pdf'
                  }
                  dialogClass="min-w-[95%]"
                />
              )}
            </Dialog>
          )}

          <Dialog
            open={documentViewModalWithSideBar}
            onOpenChange={setDocumentViewModalWithSideBar}
          >
            {documentViewModalWithSideBar && (
              <DocumentViewModalWithSidebar
                setDocumentViewModal={setDocumentViewModalWithSideBar}
                extension={documentData?.document_version?.file_extension}
                filePath={documentData?.document_version?.file_path}
                title={documentData?.title}
                version={documentData?.document_version?.version_number}
                publishDate={documentData?.publish_date}
                status={documentData?.status}
                next_review_date={documentData?.next_review_date}
                review_period={documentData?.review_period}
                source={SourceTypes.SUPPLIER}
              />
            )}
          </Dialog>

          <Dialog
            open={showDeleteModal}
            onOpenChange={() => setShowDeleteModal(false)}
          >
            <DeleteModal
              title="Delete Document"
              infoText="Are you sure you want to delete this document? This action cannot be undone."
              btnText="Delete"
              onClick={() => {
                if (selectedDocument) {
                  handleDelete(selectedDocument.id);
                }
              }}
              btnLoading={isDeleting}
            />
          </Dialog>

          {editSupplier && selectedDocument && (
            <Dialog
              open={editSupplier}
              onOpenChange={(isOpen) => {
                if (!isOpen) {
                  setEditSupplier(false);
                  setSelectedDocument(null);
                }
              }}
            >
              <AddDocumentModal
                setShowModal={setEditSupplier}
                showModal={editSupplier}
                edit={true}
                documentData={selectedDocument}
                onSuccess={() => {
                  toast.success('Document updated successfully');
                }}
                reFetch={reFetch}
              />
            </Dialog>
          )}

          {openEvaluationModal}

          <Dialog
            open={openEvaluationModal}
            onOpenChange={setOpenEvaluationModal}
          >
            <Tooltip>
              <TooltipTrigger asChild>
                <DialogTrigger asChild>
                  <LinkButton
                    size="medium"
                    icon={<Plus className="h-5 w-5" />}
                    text="Add Document"
                    onClick={() => setOpenEvaluationModal(true)}
                  />
                </DialogTrigger>
              </TooltipTrigger>
              <TooltipContent
                side="right"
                sideOffset={4}
                className="bg-white-100 rounded-lg p-1 shadow-shadow-2"
              >
                <div>Click to add a new document.</div>
              </TooltipContent>
            </Tooltip>
            {openEvaluationModal && (
              <AddDocumentModal
                setShowModal={setOpenEvaluationModal}
                showModal={openEvaluationModal}
                edit={false}
                onSuccess={() => {
                  toast.success('Document added successfully');
                }}
                reFetch={reFetch}
              />
            )}
          </Dialog>
        </div>

        <div className="mb-5">
          <CommonTable
            data={{ records: data?.records || [] }}
            columnDefs={columnDefs}
            searchPlaceholder="Search by ID, name, description, location, owner or status"
            isLoading={false}
            searchBox={false}
          />
        </div>
      </div>
    </div>
  );
};

export default Document;
