import { Filter, Plus, Search, Upload, Users } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useEffect, useMemo, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import SettingIcon from '@/assets/outline/settting';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Dialog } from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import CreateSupplierModal from '@/components/supplier/modals/createSupplierModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { SupplierDetail, SupplierListItem } from '@/interfaces/supplier';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import axios from 'axios';

const SupplierDirectory = () => {
  const router = useRouter();
  const accessToken = useAuthStore((state) => state.accessToken);
  const currentUser = useAuthStore((state) => state.user);
  const [selectedSupplier, setSelectedSupplier] =
    useState<SupplierDetail | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showBulkUploadModal, setShowBulkUploadModal] = useState(false);
  const [createOpen, setCreateOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);

 

  // const { data: selectedDetail } = useFetch<SupplierDetail>(
  //   accessToken,
  //   selectedId ? `suppliers/${selectedId}` : undefined,
  // );



  // Fetch suppliers data from API
  const {
    data: suppliersData,
    isLoading,
    error,
    reFetch,
  } = useFetch<{
    data: SupplierListItem[];
    total: number;
  }>(accessToken, 'suppliers');

  const { deleteData, isLoading: isDeleting } = useDelete<any>();

  const handleDelete = useCallback(async () => {
    if (selectedSupplier) {
      await deleteData(accessToken, `suppliers/${selectedSupplier.id}`);
      setShowDeleteModal(false);
      setSelectedSupplier(null);
      reFetch();
    }
  }, [selectedSupplier, deleteData]);

  const baseColumns = [
    {
      headerName: 'Supplier ID',
      field: 'id',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'id').slice(0, 8);
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.id || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'id').slice(0, 8);
      },
      filter: false,
      width: 120,
    },
    {
      headerName: 'Legal Name',
      field: 'legal_name',
      cellRenderer: (params: any) => {
        return (
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              router.push(`/supplier/${params.data.id}`);
            }}
            className="text-blue-600 hover:text-blue-800 font-medium"
          >
            {getValueOrDefault(params.data, 'legal_name')}
          </button>
        );
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.legal_name || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'legal_name');
      },
      filter: false,
      minWidth: 250,
      flex: 2,
    },
    {
      headerName: 'Website',
      field: 'website',
      cellRenderer: (params: any) => {
        const website = getValueOrDefault(params.data, 'website');
        return website ? (
          <a
            href={website.startsWith('http') ? website : `https://${website}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-600 hover:text-blue-800"
            onClick={(e) => e.stopPropagation()}
          >
            {website}
          </a>
        ) : (
          '-'
        );
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.website || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'website');
      },
      filter: false,
      minWidth: 200,
    },
    {
      headerName: 'Type',
      field: 'types',
      cellRenderer: (params: any) => {
        const names = (
          (params.data?.types as Array<{ name: string }> | undefined) || []
        )
          .map((t) => t.name)
          .join(', ');
        const fallback = getValueOrDefault(params.data, 'supplier_type');
        const display = names || fallback || '';
        return (
          <span className="px-2 py-1 bg-gray-100 text-gray-800 rounded-full text-sm">
            {display}
          </span>
        );
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        const names = (
          (params.data?.types as Array<{ name: string }> | undefined) || []
        )
          .map((t) => t.name)
          .join(', ');
        return names || params.data.supplier_type || '';
      },
      valueFormatter: (params: any) => {
        const names = (
          (params.data?.types as Array<{ name: string }> | undefined) || []
        )
          .map((t) => t.name)
          .join(', ');
        return names || getValueOrDefault(params.data, 'supplier_type');
      },
      filter: false,
      minWidth: 150,
    },
    {
      headerName: 'Status',
      field: 'status',
      cellRenderer: (params: any) => {
        const status = getValueOrDefault(params.data, 'status');
        const statusColors = {
          active: 'bg-green-100 text-green-800',
          prospect: 'bg-yellow-100 text-yellow-800',
          inactive: 'bg-gray-100 text-gray-800',
          blocked: 'bg-red-100 text-red-800',
        };
        return (
          <span
            className={`px-2 py-1 rounded-full text-sm ${
              statusColors[status as keyof typeof statusColors] ||
              'bg-gray-100 text-gray-800'
            }`}
          >
            {status}
          </span>
        );
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.status || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'status');
      },
      filter: false,
      minWidth: 120,
    },
    {
      headerName: 'Risk Tier',
      field: 'risk_tier',
      cellRenderer: (params: any) => {
        const risk = getValueOrDefault(params.data, 'risk_tier');
        const riskColors = {
          low: 'bg-green-100 text-green-800',
          medium: 'bg-yellow-100 text-yellow-800',
          high: 'bg-red-100 text-red-800',
        };
        return (
          <span
            className={`px-2 py-1 rounded-full text-sm ${
              riskColors[risk as keyof typeof riskColors] ||
              'bg-gray-100 text-gray-800'
            }`}
          >
            {risk}
          </span>
        );
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.risk_tier || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'risk_tier');
      },
      filter: false,
      minWidth: 120,
    },
    {
      headerName: 'Criticality',
      field: 'criticality_level',
      cellRenderer: (params: any) => {
        const level = getValueOrDefault(params.data, 'criticality_level');
        return (
          <div className="flex items-center">
            <span className="mr-2">{level}</span>
            <div className="w-16 bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full"
                style={{ width: `${(level / 5) * 100}%` }}
              ></div>
            </div>
          </div>
        );
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.criticality_level?.toString() || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'criticality_level');
      },
      filter: false,
      minWidth: 140,
    },
  ];

  const columnDefs = useMemo(() => {
    const cols = [...baseColumns];
    if (hasAccess(AccessActions.EDIT_SUPPLIER, currentUser)) {
      cols.push({
        headerName: 'Manage',
        field: 'manage',
        cellRenderer: (params: any) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={(rowData) => {
              setEditOpen(true);
            }}
            handleDelete={(rowData) => {
              setSelectedSupplier(rowData);
              setShowDeleteModal(true);
            }}
            hideDelete={!hasAccess(AccessActions.IsSupplierAdmin, currentUser)}
          />
        ),
        sortable: false,
        resizable: true,
        getQuickFilterText: (params: any) => {
          return '';
        },
        valueFormatter: (params: any) => {
          return '';
        },
        filter: false,
        width: 100,
      });
    }
    return cols;
  }, [currentUser]);

  return (
    <Layout>
      {editOpen && selectedSupplier && (
        <Dialog open={editOpen} onOpenChange={setEditOpen} >
          <CreateSupplierModal
            edit
            supplier={selectedSupplier}
            setOpen={setEditOpen}
            reFetch={reFetch}
          />
        </Dialog>
      )}
      {createOpen && (
        <Dialog open={createOpen} onOpenChange={setCreateOpen}>
          <CreateSupplierModal setOpen={setCreateOpen} reFetch={reFetch} />
        </Dialog>
      )}

      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                Supplier Directory
              </h1>
              <p className="text-gray-600">
                Manage your supplier information and relationships
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            {hasAccess(AccessActions.IsSupplierAdmin, currentUser) && (
              <Tooltip>
                <TooltipTrigger>
                  <Link href={'/supplier/supplier-administration'}>
                    <SecondaryButton
                      icon={<SettingIcon />}
                      text=""
                      size="medium"
                      buttonClasses="!p-2.5"
                    />
                  </Link>
                </TooltipTrigger>
                <TooltipContent>Supplier Administration</TooltipContent>
              </Tooltip>
            )}

            {hasAccess(AccessActions.CREATE_SUPPLIER, currentUser) && (
              <Tooltip>
                <TooltipTrigger>
                  <SecondaryButton
                    text="Bulk Upload"
                    size="medium"
                    buttonClasses="!px-4 !py-2"
                    onClick={() => setShowBulkUploadModal(true)}
                    icon={<Upload className="w-4 h-4" />}
                  />
                </TooltipTrigger>
                <TooltipContent>
                  Bulk Upload Suppliers (Create access)
                </TooltipContent>
              </Tooltip>
            )}

            {hasAccess(AccessActions.CREATE_SUPPLIER, currentUser) && (
              <PrimaryButton
                text="Add Supplier"
                buttonClasses="!px-5 !py-2"
                onClick={() => setCreateOpen(true)}
                icon={<PlusIcon color="white" />}
              />
            )}
          </div>
        </div>

        {/* Table */}
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          <CommonTable
            data={suppliersData}
            columnDefs={columnDefs}
            isLoading={isLoading}
            searchPlaceholder="Search suppliers by name, type, or status..."
          />
        </div>
      </div>

      <Dialog
        open={showDeleteModal}
        onOpenChange={() => setShowDeleteModal(false)}
      >
        <DeleteModal
          title="Delete Supplier"
          infoText="Are you sure you want to delete this supplier? This action cannot be undone."
          btnText="Delete"
          onClick={handleDelete}
          btnLoading={isDeleting}
        />
      </Dialog>
    </Layout>
  );
};

export default SupplierDirectory;
