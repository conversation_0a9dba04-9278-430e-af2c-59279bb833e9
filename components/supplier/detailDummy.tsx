import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/router';
import React, { useState } from 'react';

import EditIcon from '@/assets/outline/edit';
import PrimaryButton from '@/components/common/button/primaryButton';
import { DetailsText, DetailsTextNew } from '@/components/common/infoDetail';
import Loader from '@/components/common/loader';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import SupplierActions from '@/components/supplier/supplierActions';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { SupplierDetail } from '@/interfaces/supplier';
import { hasAccess } from '@/utils/roleAccessConfig';
import { Dialog } from '@radix-ui/react-dialog';

interface ISupplierDetailsText {
  label: string;
  value: string;
}

const SupplierDetailsText = ({ data }: { data: ISupplierDetailsText[] }) => {
  return (
    <div className="space-y-3">
      {data.map((item, index) => (
        <div key={index} className="flex flex-col">
          <span className="text-sm font-medium text-gray-500">
            {item.label}
          </span>
          <span className="text-base text-gray-900">{item.value}</span>
        </div>
      ))}
    </div>
  );
};
const SupplierDetails = () => {
  const router = useRouter();
  const { supplierId } = router.query;
  const accessToken = useAuthStore((state) => state.accessToken);
  const currentUser = useAuthStore((state) => state.user);

  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [openEditModal, setOpenEditModal] = useState(false);

  const {
    data: supplierData,
    isLoading,
    reFetch: refetchSupplierData,
  } = useFetch<SupplierDetail>(accessToken, `suppliers/${supplierId}`, {});

  const { deleteData, isLoading: isDeleting } = useDelete();

  const handleDelete = async () => {
    if (!supplierId) return;

    await deleteData(accessToken, `suppliers/${supplierId}`);
    setShowDeleteModal(false);
    router.push('/supplier');
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-screen">
          <Loader />
        </div>
      </Layout>
    );
  }

  if (!supplierData) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-screen">
          <div>Supplier not found</div>
        </div>
      </Layout>
    );
  }

  const primaryContact = supplierData.contacts.find((c) => c.is_primary);
  const additionalContacts = supplierData.contacts.filter((c) => !c.is_primary);
  const primaryAddress = supplierData.addresses.find((a) => a.is_primary);
  const additionalAddresses = supplierData.addresses.filter(
    (a) => !a.is_primary,
  );

  const supplierDetails: ISupplierDetailsText[] = [
    { label: 'Legal Name', value: supplierData.legal_name },
    { label: 'Website', value: supplierData.website || '-' },
    {
      label: 'Supplier Types',
      value: (supplierData.types || []).map((t) => t.name).join(', ') || '-',
    },
    { label: 'Status', value: supplierData.status },
    { label: 'Risk Tier', value: supplierData.risk_tier },
    {
      label: 'Criticality Level',
      value: supplierData.criticality_level.toString(),
    },
    { label: 'Notes', value: supplierData.notes || '-' },
  ];

  const contactDetails: ISupplierDetailsText[] = primaryContact
    ? [
        { label: 'Primary Contact Name', value: primaryContact.name },
        { label: 'Primary Contact Email', value: primaryContact.email || '-' },
        { label: 'Primary Contact Phone', value: primaryContact.phone || '-' },
      ]
    : [];

  const addressDetails: ISupplierDetailsText[] = primaryAddress
    ? [
        { label: 'Country', value: primaryAddress.country },
        { label: 'Address Line 1', value: primaryAddress.address_line1 },
        { label: 'Address Line 2', value: primaryAddress.address_line2 || '-' },
        { label: 'City', value: primaryAddress.city },
        {
          label: 'State/Province',
          value: primaryAddress.state_province || '-',
        },
        { label: 'Postal Code', value: primaryAddress.postal_code || '-' },
      ]
    : [];

  return (
    <Layout>
      <div className="px-6 py-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <button
              onClick={() => router.back()}
              className="flex items-center justify-center w-10 h-10 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <h1 className="text-2xl font-semibold text-gray-900">
                {supplierData.legal_name}
              </h1>
              <p className="text-sm text-gray-500">Supplier Details</p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Basic Information */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-lg font-semibold mb-4">Basic Information</h2>
            <SupplierDetailsText data={supplierDetails} />
          </div>

          {/* Contact Information */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-lg font-semibold mb-4">Contact Information</h2>
            {primaryContact ? (
              <SupplierDetailsText data={contactDetails} />
            ) : (
              <p className="text-gray-500">No contact information available</p>
            )}
          </div>

          {/* Address Information */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-lg font-semibold mb-4">Address Information</h2>
            {primaryAddress ? (
              <SupplierDetailsText data={addressDetails} />
            ) : (
              <p className="text-gray-500">No address information available</p>
            )}
          </div>

          {/* Supply Categories */}
          <div className="bg-white rounded-lg border p-6">
            <h2 className="text-lg font-semibold mb-4">Supply Categories</h2>
            {supplierData.categories.length > 0 ? (
              <div className="flex flex-wrap gap-2">
                {supplierData.categories.map((category) => (
                  <span
                    key={category.id}
                    className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"
                  >
                    {category.name}
                  </span>
                ))}
              </div>
            ) : (
              <p className="text-gray-500">No categories assigned</p>
            )}
          </div>
        </div>

        {/* Additional Contacts */}
        {additionalContacts.length > 0 && (
          <div className="mt-8 bg-white rounded-lg border p-6">
            <h2 className="text-lg font-semibold mb-4">Additional Contacts</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {additionalContacts.map((contact, index) => (
                <div
                  key={contact.id || index}
                  className="border rounded-lg p-4"
                >
                  <h3 className="font-medium">{contact.name}</h3>
                  <p className="text-sm text-gray-600">
                    {contact.email || '-'}
                  </p>
                  <p className="text-sm text-gray-600">
                    {contact.phone || '-'}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Additional Addresses */}
        {additionalAddresses.length > 0 && (
          <div className="mt-8 bg-white rounded-lg border p-6">
            <h2 className="text-lg font-semibold mb-4">Additional Addresses</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {additionalAddresses.map((address, index) => (
                <div
                  key={address.id || index}
                  className="border rounded-lg p-4"
                >
                  <p className="font-medium">{address.address_line1}</p>
                  {address.address_line2 && (
                    <p className="text-sm">{address.address_line2}</p>
                  )}
                  <p className="text-sm">
                    {address.city}, {address.state_province}{' '}
                    {address.postal_code}
                  </p>
                  <p className="text-sm text-gray-600">{address.country}</p>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <Dialog
        open={showDeleteModal}
        onOpenChange={() => setShowDeleteModal(false)}
      >
        <DeleteModal
          title="Delete Supplier"
          infoText="Are you sure you want to delete this supplier? This action cannot be undone."
          btnText="Delete"
          onClick={handleDelete}
          btnLoading={isDeleting}
        />
      </Dialog>
    </Layout>
  );
};

export default SupplierDetails;
