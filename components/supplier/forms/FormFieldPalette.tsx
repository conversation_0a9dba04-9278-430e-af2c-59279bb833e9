import React from 'react';

import { FieldType } from '@/interfaces/formBuilder';

const FIELD_TYPES: { type: FieldType; label: string; hint?: string }[] = [
  { type: 'text', label: 'Text Input', hint: 'Short single line' },
  { type: 'textarea', label: 'Text Area', hint: 'Long text' },
  { type: 'email', label: 'Email', hint: 'Valid email format' },
  { type: 'number', label: 'Number', hint: 'Numeric input' },
  { type: 'dropdown', label: 'Dropdown', hint: 'Single choice' },
  { type: 'radio', label: 'Radio Buttons', hint: 'Single choice' },
  { type: 'checkbox', label: 'Checkboxes', hint: 'Multiple choice' },
  { type: 'boolean', label: 'Yes/No', hint: 'Toggle' },
  { type: 'date', label: 'Date Picker', hint: 'Select a date' },
  { type: 'file', label: 'File Upload', hint: 'Upload evidence' },
  { type: 'section-header', label: 'Section Header', hint: 'Title only' },
];

export const FormFieldPalette = ({
  onAdd,
}: {
  onAdd: (t: FieldType) => void;
}) => {
  return (
    <div className="space-y-3">
      <div className="text-xs text-gray-500 font-medium uppercase tracking-wider">
        Field Components
      </div>
      <div className="grid grid-cols-1 gap-2">
        {FIELD_TYPES.map((f) => (
          <button
            key={f.type}
            className="w-full text-left border rounded-md px-3 py-2 hover:bg-blue-50 hover:border-blue-400 transition-colors"
            onClick={() => onAdd(f.type)}
          >
            <div className="text-sm font-medium">{f.label}</div>
            {f.hint && <div className="text-xs text-gray-500">{f.hint}</div>}
          </button>
        ))}
      </div>
    </div>
  );
};

export default FormFieldPalette;
