import React from 'react';

import DeleteIcon from '@/assets/outline/delete';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Checkbox } from '@/components/common/checkbox';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import { Field, FieldOption, Section } from '@/interfaces/formBuilder';

import { useFormStore } from './store';

const FieldRow = ({ sectionId, f }: { sectionId: string; f: Field }) => {
  const { updateField, removeField, moveField, selectedFieldId, selectField } =
    useFormStore();

  const isSelected = selectedFieldId === f.id;

  const set = (patch: Partial<Field>) => updateField(sectionId, f.id, patch);

  const addOption = () => {
    if (f.type === 'dropdown' || f.type === 'radio' || f.type === 'checkbox') {
      const options = [
        ...((f as any).options || []),
        {
          id: `${Date.now()}`,
          label: `Option ${((f as any).options?.length || 0) + 1}`,
          value: `option_${((f as any).options?.length || 0) + 1}`,
        },
      ];
      set({ ...(f as any), options } as any);
    }
  };

  return (
    <div
      className={`relative rounded-md p-3 border ${
        isSelected
          ? 'border-primary-400 ring-1 ring-primary-100'
          : 'border-grey-100'
      } ${
        f.type === 'section-header'
          ? 'bg-white-100 border-dashed'
          : 'bg-white-100 shadow-sm'
      } ${
        isSelected
          ? 'border-l-4 border-l-primary-400'
          : 'border-l-4 border-l-grey-100'
      }`}
      onClick={(e) => {
        e.stopPropagation();
        selectField(f.id);
      }}
    >
      {isSelected && (
        <span className="absolute -top-2 right-2 text-[10px] px-2 py-0.5 rounded bg-primary-400 text-white">
          Selected
        </span>
      )}
      <div className="flex items-center gap-2">
        <Input
          value={f.label}
          onChange={(e) => set({ label: e.target.value })}
          placeholder={
            f.type === 'section-header' ? 'Section title' : 'Field label'
          }
        />
        <span
          className={`text-[11px] uppercase tracking-wide px-2 py-1 rounded ${
            f.type === 'section-header'
              ? 'bg-primary-50 text-primary-400'
              : 'bg-grey-50 text-dark-300'
          }`}
        >
          {f.type === 'section-header' ? 'Section' : f.type}
        </span>
        {f.type !== 'section-header' && (
          <label className="flex items-center gap-2 text-sm">
            <Checkbox
              checked={!!f.required}
              onCheckedChange={(v: any) => set({ required: !!v })}
            />
            Required
          </label>
        )}
        <div className="ml-auto flex items-center gap-2 text-sm">
          <button
            className="px-2 py-1 border rounded"
            onClick={() => moveField(sectionId, f.id, 'up')}
          >
            ↑
          </button>
          <button
            className="px-2 py-1 border rounded"
            onClick={() => moveField(sectionId, f.id, 'down')}
          >
            ↓
          </button>
          <button
            className="text-red-600"
            onClick={() => removeField(sectionId, f.id)}
          >
            Delete
          </button>
        </div>
      </div>

      {f.type !== 'section-header' && (
        <Input
          className="mt-2"
          value={(f as any).placeholder || ''}
          onChange={(e) => set({ placeholder: e.target.value })}
          placeholder="Placeholder"
        />
      )}

      {/* Help text */}
      <Textarea
        className="mt-2"
        value={(f as any).helpText || ''}
        onChange={(e) => set({ helpText: e.target.value } as any)}
        placeholder="Help text (optional)"
      />

      {(f.type === 'dropdown' ||
        f.type === 'radio' ||
        f.type === 'checkbox') && (
        <div className="mt-2 space-y-2">
          <div className="text-sm text-gray-500">Options</div>
          {((f as any).options || []).map((opt: FieldOption, idx: number) => (
            <div key={opt.id} className="flex items-center gap-2">
              <Input
                value={opt.label}
                onChange={(e) => {
                  const options = [...(f as any).options];
                  options[idx] = { ...opt, label: e.target.value };
                  set({ ...(f as any), options } as any);
                }}
              />
              <button
                className="text-red-600 text-sm"
                onClick={() => {
                  const options = (f as any).options.filter(
                    (o: FieldOption) => o.id !== opt.id,
                  );
                  set({ ...(f as any), options } as any);
                }}
              >
                Remove
              </button>
              {/* Default selected for checkbox multi-select */}
              {f.type === 'checkbox' && (
                <label className="ml-2 text-xs flex items-center gap-1">
                  <Checkbox
                    checked={
                      Array.isArray((f as any).defaultValue)
                        ? (f as any).defaultValue.includes(opt.value)
                        : false
                    }
                    onCheckedChange={(v: any) => {
                      const current: string[] = Array.isArray(
                        (f as any).defaultValue,
                      )
                        ? ([...(f as any).defaultValue] as string[])
                        : [];
                      if (v) {
                        if (!current.includes(opt.value))
                          current.push(opt.value);
                      } else {
                        const i = current.indexOf(opt.value);
                        if (i > -1) current.splice(i, 1);
                      }
                      set({ defaultValue: current } as any);
                    }}
                  />
                  Default
                </label>
              )}
            </div>
          ))}
          <button
            className="text-sm border rounded-md px-2 py-1"
            onClick={addOption}
          >
            + Add option
          </button>
        </div>
      )}

      {/* Default value editors */}
      {(f.type === 'text' || f.type === 'email' || f.type === 'date') && (
        <Input
          className="mt-2"
          value={(f as any).defaultValue ?? ''}
          onChange={(e) => set({ defaultValue: e.target.value } as any)}
          placeholder="Default value"
        />
      )}
      {f.type === 'textarea' && (
        <Textarea
          className="mt-2"
          value={(f as any).defaultValue ?? ''}
          onChange={(e) => set({ defaultValue: e.target.value } as any)}
          placeholder="Default value"
        />
      )}
      {f.type === 'number' && (
        <Input
          className="mt-2"
          type="number"
          value={(f as any).defaultValue ?? ''}
          onChange={(e) =>
            set({
              defaultValue:
                e.target.value === '' ? undefined : Number(e.target.value),
            } as any)
          }
          placeholder="Default value"
        />
      )}
      {f.type === 'boolean' && (
        <label className="mt-2 flex items-center gap-2 text-sm">
          <Checkbox
            checked={!!(f as any).defaultValue}
            onCheckedChange={(v: any) => set({ defaultValue: !!v } as any)}
          />
          Default checked
        </label>
      )}
      {(f.type === 'dropdown' || f.type === 'radio') && (
        <select
          className="mt-2 border rounded-md px-3 py-2"
          value={(f as any).defaultValue ?? ''}
          onChange={(e) => set({ defaultValue: e.target.value } as any)}
        >
          <option value="">No default</option>
          {((f as any).options || []).map((opt: FieldOption) => (
            <option key={opt.id} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </select>
      )}

      {/* Validation editors */}
      {(f.type === 'text' || f.type === 'textarea' || f.type === 'email') && (
        <div className="mt-2 grid grid-cols-3 gap-2">
          <Input
            type="number"
            placeholder="Min length"
            value={(f as any).validation?.minLength ?? ''}
            onChange={(e) =>
              set({
                validation: {
                  ...(f as any).validation,
                  minLength:
                    e.target.value === '' ? undefined : Number(e.target.value),
                },
              } as any)
            }
          />
          <Input
            type="number"
            placeholder="Max length"
            value={(f as any).validation?.maxLength ?? ''}
            onChange={(e) =>
              set({
                validation: {
                  ...(f as any).validation,
                  maxLength:
                    e.target.value === '' ? undefined : Number(e.target.value),
                },
              } as any)
            }
          />
          <Input
            placeholder="Pattern (regex)"
            value={(f as any).validation?.pattern ?? ''}
            onChange={(e) =>
              set({
                validation: {
                  ...(f as any).validation,
                  pattern: e.target.value || undefined,
                },
              } as any)
            }
          />
        </div>
      )}
      {f.type === 'number' && (
        <div className="mt-2 grid grid-cols-2 gap-2">
          <Input
            type="number"
            placeholder="Min"
            value={(f as any).validation?.min ?? ''}
            onChange={(e) =>
              set({
                validation: {
                  ...(f as any).validation,
                  min:
                    e.target.value === '' ? undefined : Number(e.target.value),
                },
              } as any)
            }
          />
          <Input
            type="number"
            placeholder="Max"
            value={(f as any).validation?.max ?? ''}
            onChange={(e) =>
              set({
                validation: {
                  ...(f as any).validation,
                  max:
                    e.target.value === '' ? undefined : Number(e.target.value),
                },
              } as any)
            }
          />
        </div>
      )}
    </div>
  );
};

const SectionHeader = ({ section }: { section: Section }) => {
  const { updateSection, removeSection, currentForm } = useFormStore();
  const canDelete = (currentForm.sections?.length || 0) > 1;
  const [showDelete, setShowDelete] = React.useState(false);
  return (
    <div className="mb-3 border-l-4 border-l-primary-400 bg-white-100 rounded-md p-3">
      <div className="flex items-center justify-between mb-1">
        <div className="text-xs font-semibold text-primary-400 uppercase tracking-wide">
          Section
        </div>
        <button
          className="text-xs text-red-600 hover:underline disabled:text-grey-200"
          onClick={(e) => {
            e.stopPropagation();
            if (!canDelete) return;
            setShowDelete(true);
          }}
          disabled={!canDelete}
        >
          Delete section
        </button>
      </div>
      <Input
        value={section.title}
        onChange={(e) => updateSection(section.id, { title: e.target.value })}
        placeholder="Section title"
      />
      <Textarea
        className="mt-2"
        value={section.description || ''}
        onChange={(e) =>
          updateSection(section.id, { description: e.target.value })
        }
        placeholder="Click to add description"
      />

      {/* Delete confirm dialog */}
      <Dialog open={showDelete} onOpenChange={setShowDelete}>
        <DialogContent className="min-w-[24rem]">
          <DialogHeader>
            <DialogTitle>Delete Section</DialogTitle>
          </DialogHeader>
          <div className="mt-2">
            <div className="flex items-center gap-2 text-sm font-medium leading-5 text-[#F04040] p-3 rounded-lg bg-[#FDE8E866] mb-2">
              <div className="h-9 w-9 min-h-9 min-w-9 bg-[#F040401F] flex items-center justify-center rounded-full">
                <DeleteIcon color="#F04040" height="20" width="20" />
              </div>
              This will permanently remove the section and all its fields.
            </div>
            <div className="flex justify-end gap-3 mt-5">
              <SecondaryButton
                size="medium"
                text="Cancel"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowDelete(false);
                }}
              />
              <PrimaryButton
                size="medium"
                text="Delete"
                onClick={(e) => {
                  e.stopPropagation();
                  if (!canDelete) return;
                  removeSection(section.id);
                  setShowDelete(false);
                }}
              />
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export const FormBuilderCanvas = ({ section }: { section: Section }) => {
  const { addField } = useFormStore();

  return (
    <div className="space-y-3">
      <SectionHeader section={section} />

      <div className="space-y-4">
        {section.fields.map((f) => (
          <FieldRow key={f.id} sectionId={section.id} f={f} />
        ))}
      </div>

      <button
        className="mt-3 px-3 py-2 rounded-md border"
        onClick={() => addField(section.id, 'text')}
      >
        + Add Field
      </button>
    </div>
  );
};

export default FormBuilderCanvas;
