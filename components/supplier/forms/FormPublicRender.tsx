import React from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import { Checkbox } from '@/components/common/checkbox';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import { Field, FormResponsePayload, FormSchema } from '@/interfaces/formBuilder';

const RenderField = ({
  f,
  value,
  onChange,
}: {
  f: Field;
  value: any;
  onChange: (v: any) => void;
}) => {
  switch (f.type) {
    case 'text':
    case 'email':
    case 'number':
      return (
        <Input
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={f.placeholder}
        />
      );
    case 'date':
      return (
        <Input
          type="date"
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={f.placeholder}
        />
      );
    case 'textarea':
      return (
        <Textarea
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={f.placeholder}
        />
      );
    case 'boolean':
      return (
        <Checkbox
          checked={!!value}
          onCheckedChange={(v: any) => onChange(!!v)}
        />
      );
    case 'checkbox':
      return (
        <div className="space-y-2">
          {(f as any).options?.map((opt: any) => (
            <label key={opt.id} className="flex items-center gap-2">
              <Checkbox
                checked={
                  Array.isArray(value) ? value.includes(opt.value) : false
                }
                onCheckedChange={(v: any) => {
                  const arr = Array.isArray(value) ? [...value] : [];
                  if (v) arr.push(opt.value);
                  else {
                    const i = arr.indexOf(opt.value);
                    if (i > -1) arr.splice(i, 1);
                  }
                  onChange(arr);
                }}
              />
              {opt.label}
            </label>
          ))}
        </div>
      );
    case 'radio':
    case 'dropdown':
      return (
        <select
          className="border rounded-md px-3 py-2"
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
        >
          <option value="" disabled>
            Select
          </option>
          {(f as any).options?.map((opt: any) => (
            <option key={opt.id} value={opt.value}>
              {opt.label}
            </option>
          ))}
        </select>
      );
    case 'file':
      return <input type="file" onChange={() => {}} />;
    case 'section-header':
      return null;
    default:
      return null;
  }
};

const FormPublicRender = ({
  form,
  onSubmit,
}: {
  form: FormSchema;
  onSubmit: (payload: FormResponsePayload) => void;
}) => {
  const initialAnswers = React.useMemo(() => {
    const ans: Record<string, any> = {};
    form.sections.forEach((s) => {
      s.fields.forEach((f) => {
        if (f.type === 'section-header') return;
        if ((f as any).defaultValue !== undefined) {
          ans[f.id] = (f as any).defaultValue;
        }
      });
    });
    return ans;
  }, [form]);

  const [answers, setAnswers] =
    React.useState<Record<string, any>>(initialAnswers);
  const [errors, setErrors] = React.useState<
    Record<string, string | undefined>
  >({});

  const handleSubmit = () => {
    const newErrors: Record<string, string | undefined> = {};
    form.sections.forEach((s) => {
      s.fields.forEach((f) => {
        if (f.type === 'section-header') return;
        const v = answers[f.id];
        const isEmpty =
          v === undefined ||
          v === null ||
          (typeof v === 'string' && v.trim() === '') ||
          (Array.isArray(v) && v.length === 0);
        if (f.required && isEmpty) newErrors[f.id] = 'This field is required';

        // Additional validation
        const val = (f as any).validation;
        if (val) {
          if (typeof v === 'string') {
            if (val.minLength !== undefined && v.length < val.minLength) {
              newErrors[f.id] = `Minimum length is ${val.minLength}`;
            }
            if (val.maxLength !== undefined && v.length > val.maxLength) {
              newErrors[f.id] = `Maximum length is ${val.maxLength}`;
            }
            if (val.pattern) {
              try {
                const re = new RegExp(val.pattern);
                if (!re.test(v)) newErrors[f.id] = 'Invalid format';
              } catch (e) {
                // ignore bad regex
              }
            }
          }
          if (typeof v === 'number') {
            if (val.min !== undefined && v < val.min) {
              newErrors[f.id] = `Minimum is ${val.min}`;
            }
            if (val.max !== undefined && v > val.max) {
              newErrors[f.id] = `Maximum is ${val.max}`;
            }
          }
        }
      });
    });
    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) return;

    const payload: FormResponsePayload = {
      form_id: form.id as string,
      answers,
    };
    onSubmit(payload);
  };

  return (
    <div className="max-w-3xl mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold">{form.name}</h1>
        {form.description && (
          <p className="text-gray-600 mt-1">{form.description}</p>
        )}
      </div>
      <div className="space-y-8">
        {form.sections.map((s) => (
          <div key={s.id} className="bg-white border rounded-lg p-4">
            <h2 className="text-lg font-semibold mb-2">{s.title}</h2>
            {s.description && (
              <p className="text-gray-500 mb-3">{s.description}</p>
            )}
            <div className="space-y-4">
              {s.fields.map((f) => (
                <div key={f.id}>
                  {f.type !== 'section-header' && (
                    <label className="block text-sm font-medium mb-1">
                      {f.label}{' '}
                      {f.required && <span className="text-red-500">*</span>}
                    </label>
                  )}
                  <RenderField
                    f={f}
                    value={answers[f.id]}
                    onChange={(v) => setAnswers((a) => ({ ...a, [f.id]: v }))}
                  />
                  {errors[f.id] && (
                    <p className="text-xs text-red-600 mt-1">{errors[f.id]}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
      <div className="mt-6">
        <PrimaryButton text="Submit" onClick={handleSubmit} />
      </div>
    </div>
  );
};

export default FormPublicRender;
