import { useRouter } from 'next/router';
import React from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Input } from '@/components/common/input';
import Layout from '@/components/common/sidebar/layout';

import FormBuilderCanvas from './FormBuilderCanvas';
import FormFieldPalette from './FormFieldPalette';
import { useFormStore } from './store';

const BuilderPage = () => {
  const router = useRouter();
  const {
    currentForm: form,
    setName,
    addSection,
    addField,
    selectSection,
    selectedSectionId,
    setStatus,
  } = useFormStore();

  const handlePreview = () => {
    router.push(`/supplier/forms/${form.id}/preview`);
  };

  const saveDraft = () => setStatus('draft');
  const publish = () => setStatus('published');

  return (
    <Layout>
      <div className="p-6 space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Input
              className="text-xl font-semibold"
              value={form.name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Untitled Form"
            />
          </div>
          <div className="flex items-center gap-2">
            <SecondaryButton text="Preview" onClick={handlePreview} />
            <SecondaryButton text="Save Draft" onClick={saveDraft} />
            <PrimaryButton text="Publish" onClick={publish} />
          </div>
        </div>

        <div className="grid grid-cols-12 gap-4">
          {/* Left palette */}
          <div className="col-span-3">
            {!selectedSectionId && (
              <div className="mb-2 text-xs text-gray-500">
                Tip: Select a section on the right to add fields into it.
              </div>
            )}
            <FormFieldPalette
              onAdd={(t) => selectedSectionId && addField(selectedSectionId, t)}
            />
          </div>

          {/* Canvas full-width (properties panel removed) */}
          <div className="col-span-9">
            <div className="space-y-6">
              {form.sections.map((section) => (
                <div
                  key={section.id}
                  className={`rounded-lg p-4 border ${
                    selectedSectionId === section.id
                      ? 'border-blue-500 ring-2 ring-blue-200'
                      : 'border-gray-200'
                  }`}
                  onClick={() => selectSection(section.id)}
                >
                  <FormBuilderCanvas section={section} />
                </div>
              ))}
              <button
                className="text-sm border rounded-md px-3 py-2"
                onClick={addSection}
              >
                + Add Section
              </button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default BuilderPage;
