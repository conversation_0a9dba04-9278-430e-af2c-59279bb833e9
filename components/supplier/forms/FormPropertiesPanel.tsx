import React from 'react';

import { Checkbox } from '@/components/common/checkbox';
import { Input } from '@/components/common/input';
import { Textarea } from '@/components/common/textarea';
import { Field, FieldOption } from '@/interfaces/formBuilder';

import { useFormStore } from './store';

const ChoiceOptionsEditor = ({
  sectionId,
  field,
  onChange,
}: {
  sectionId: string;
  field: Field;
  onChange: (opts: FieldOption[]) => void;
}) => {
  const opts = (field as any).options as FieldOption[] | undefined;
  if (!opts) return null;

  const handleAdd = () => {
    const next = [
      ...opts,
      {
        id: `${Date.now()}`,
        label: `Option ${opts.length + 1}`,
        value: `option_${opts.length + 1}`,
      },
    ];
    onChange(next);
  };

  return (
    <div className="space-y-2">
      <div className="text-xs font-medium text-gray-600">Options</div>
      {opts.map((opt, idx) => (
        <div key={opt.id} className="flex items-center gap-2">
          <Input
            value={opt.label}
            onChange={(e) => {
              const next = [...opts];
              next[idx] = { ...opt, label: e.target.value };
              onChange(next);
            }}
          />
          <button
            className="text-xs text-red-600"
            onClick={() => onChange(opts.filter((o) => o.id !== opt.id))}
          >
            Remove
          </button>
        </div>
      ))}
      <button className="text-sm border rounded-md px-2 py-1" onClick={handleAdd}>
        + Add option
      </button>
    </div>
  );
};

const FieldProperties = ({ sectionId, field }: { sectionId: string; field: Field }) => {
  const { updateField, removeField, moveField } = useFormStore();
  const set = (patch: Partial<Field>) => updateField(sectionId, field.id, patch);

  return (
    <div className="space-y-3">
      <div>
        <div className="text-[11px] uppercase tracking-wide text-gray-500 mb-1">Type</div>
        <div className="text-sm font-medium">{field.type}</div>
      </div>
      <div>
        <div className="text-sm font-medium mb-1">Label</div>
        <Input value={field.label} onChange={(e) => set({ label: e.target.value })} />
      </div>
      {field.type !== 'section-header' && (
        <div className="flex items-center gap-2">
          <Checkbox checked={!!field.required} onCheckedChange={(v: any) => set({ required: !!v })} />
          <span className="text-sm">Required</span>
        </div>
      )}
      {field.type !== 'section-header' && (
        <div>
          <div className="text-sm font-medium mb-1">Placeholder</div>
          <Input
            value={(field as any).placeholder || ''}
            onChange={(e) => set({ placeholder: e.target.value })}
          />
        </div>
      )}
      <div>
        <div className="text-sm font-medium mb-1">Help text</div>
        <Textarea
          value={(field as any).helpText || ''}
          onChange={(e) => set({ helpText: e.target.value } as any)}
        />
      </div>

      {(field.type === 'dropdown' || field.type === 'radio' || field.type === 'checkbox') && (
        <ChoiceOptionsEditor
          sectionId={sectionId}
          field={field}
          onChange={(opts) => set({ ...(field as any), options: opts } as any)}
        />
      )}

      <div className="pt-2 flex items-center gap-2 text-sm">
        <button className="px-2 py-1 border rounded" onClick={() => moveField(sectionId, field.id, 'up')}>
          Move up
        </button>
        <button className="px-2 py-1 border rounded" onClick={() => moveField(sectionId, field.id, 'down')}>
          Move down
        </button>
        <button className="ml-auto text-red-600" onClick={() => removeField(sectionId, field.id)}>
          Delete field
        </button>
      </div>
    </div>
  );
};

const SectionProperties = () => {
  const { getSelectedSection, updateSection } = useFormStore();
  const section = getSelectedSection();
  if (!section) return null;
  return (
    <div className="space-y-3">
      <div>
        <div className="text-sm font-medium mb-1">Section title</div>
        <Input value={section.title} onChange={(e) => updateSection(section.id, { title: e.target.value })} />
      </div>
      <div>
        <div className="text-sm font-medium mb-1">Section description</div>
        <Textarea
          value={section.description || ''}
          onChange={(e) => updateSection(section.id, { description: e.target.value })}
        />
      </div>
    </div>
  );
};

const FormPropertiesPanel = () => {
  const { selectedSectionId, selectedFieldId, getSelectedSection } = useFormStore();
  const section = getSelectedSection();
  const field = section?.fields.find((f) => f.id === selectedFieldId);

  return (
    <aside className="bg-white border rounded-lg p-4">
      <div className="text-xs text-gray-500 font-medium uppercase tracking-wider mb-3">Properties</div>
      {!selectedSectionId && !selectedFieldId && (
        <div className="text-sm text-gray-500">Select a section or a field to edit its properties.</div>
      )}
      {selectedSectionId && !selectedFieldId && <SectionProperties />}
      {selectedSectionId && selectedFieldId && section && field && (
        <FieldProperties sectionId={section.id} field={field} />
      )}
    </aside>
  );
};

export default FormPropertiesPanel;

