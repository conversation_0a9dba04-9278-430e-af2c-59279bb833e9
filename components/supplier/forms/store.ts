import { create } from 'zustand';
import { persist } from 'zustand/middleware';

import {
    defaultNewForm, Field, FieldOption, FieldType, FormSchema, Section
} from '@/interfaces/formBuilder';

export type FormStore = {
  currentForm: FormSchema;
  selectedSectionId?: string;
  selectedFieldId?: string;
  // selectors
  getSelectedSection: () => Section | undefined;
  getSelectedField: () => Field | undefined;
  // actions
  newForm: () => void;
  setForm: (form: FormSchema) => void;
  setName: (name: string) => void;
  setDescription: (desc: string) => void;
  setStatus: (status: FormSchema['status']) => void;
  selectSection: (sectionId?: string) => void;
  selectField: (fieldId?: string) => void;
  addSection: () => void;
  updateSection: (sectionId: string, patch: Partial<Section>) => void;
  removeSection: (sectionId: string) => void;
  addField: (sectionId: string, type: FieldType) => void;
  updateField: (
    sectionId: string,
    fieldId: string,
    patch: Partial<Field>,
  ) => void;
  removeField: (sectionId: string, fieldId: string) => void;
  moveField: (
    sectionId: string,
    fieldId: string,
    direction: 'up' | 'down',
  ) => void;
};

const makeId = () => `${Date.now()}-${Math.random().toString(36).slice(2, 8)}`;

const createField = (type: FieldType): Field => {
  const base: any = { id: makeId(), type, label: 'Untitled', required: false };
  if (type === 'dropdown' || type === 'radio' || type === 'checkbox') {
    base.options = [
      { id: makeId(), label: 'Option 1', value: 'option_1' },
      { id: makeId(), label: 'Option 2', value: 'option_2' },
    ];
  }
  if (type === 'section-header') {
    base.label = 'Section Title';
    base.description = '';
  }
  return base as Field;
};

export const useFormStore = create<FormStore>()(
  persist(
    (set, get) => ({
      currentForm: defaultNewForm(),
      selectedSectionId: undefined,
      selectedFieldId: undefined,

      getSelectedSection: () =>
        get().currentForm.sections.find(
          (s) => s.id === get().selectedSectionId,
        ),
      getSelectedField: () => {
        const s = get().getSelectedSection();
        return s?.fields.find((f) => f.id === get().selectedFieldId);
      },

      newForm: () =>
        set({
          currentForm: { ...defaultNewForm(), id: makeId() },
          selectedFieldId: undefined,
          selectedSectionId: undefined,
        }),
      setForm: (form) =>
        set({ currentForm: { ...form, id: form.id || makeId() } }),
      setName: (name) => set({ currentForm: { ...get().currentForm, name } }),
      setDescription: (description) =>
        set({ currentForm: { ...get().currentForm, description } }),
      setStatus: (status) =>
        set({ currentForm: { ...get().currentForm, status } }),

      selectSection: (sectionId) =>
        set({ selectedSectionId: sectionId, selectedFieldId: undefined }),
      selectField: (fieldId) => set({ selectedFieldId: fieldId }),

      addSection: () =>
        set({
          currentForm: {
            ...get().currentForm,
            sections: [
              ...get().currentForm.sections,
              {
                id: makeId(),
                title: 'New Section',
                description: '',
                fields: [],
              },
            ],
          },
        }),

      updateSection: (sectionId, patch) =>
        set({
          currentForm: {
            ...get().currentForm,
            sections: get().currentForm.sections.map((s) =>
              s.id === sectionId ? { ...s, ...patch } : s,
            ),
          },
        }),

      removeSection: (sectionId) =>
        set({
          currentForm: {
            ...get().currentForm,
            sections: get().currentForm.sections.filter(
              (s) => s.id !== sectionId,
            ),
          },
          selectedSectionId: undefined,
          selectedFieldId: undefined,
        }),

      addField: (sectionId, type) =>
        set({
          currentForm: {
            ...get().currentForm,
            sections: get().currentForm.sections.map((s) =>
              s.id === sectionId
                ? { ...s, fields: [...s.fields, createField(type)] }
                : s,
            ),
          },
        }),

      updateField: (sectionId, fieldId, patch) =>
        set({
          currentForm: {
            ...get().currentForm,
            sections: get().currentForm.sections.map((s) => {
              if (s.id !== sectionId) return s;
              return {
                ...s,
                fields: s.fields.map((f) =>
                  f.id === fieldId ? ({ ...f, ...(patch as any) } as Field) : f,
                ),
              };
            }),
          },
        }),

      removeField: (sectionId, fieldId) =>
        set({
          currentForm: {
            ...get().currentForm,
            sections: get().currentForm.sections.map((s) =>
              s.id === sectionId
                ? { ...s, fields: s.fields.filter((f) => f.id !== fieldId) }
                : s,
            ),
          },
          selectedFieldId: undefined,
        }),

      moveField: (sectionId, fieldId, direction) =>
        set({
          currentForm: {
            ...get().currentForm,
            sections: get().currentForm.sections.map((s) => {
              if (s.id !== sectionId) return s;
              const idx = s.fields.findIndex((f) => f.id === fieldId);
              if (idx < 0) return s;
              const target = direction === 'up' ? idx - 1 : idx + 1;
              if (target < 0 || target >= s.fields.length) return s;
              const arr = [...s.fields];
              const [item] = arr.splice(idx, 1);
              arr.splice(target, 0, item);
              return { ...s, fields: arr };
            }),
          },
        }),
    }),
    { name: 'supplier-form-builder' },
  ),
);
