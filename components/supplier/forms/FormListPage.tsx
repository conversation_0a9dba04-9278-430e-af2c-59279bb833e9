import Link from 'next/link';
import React, { useMemo } from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import Layout from '@/components/common/sidebar/layout';
import CommonTable from '@/components/common/table';

import { useFormStore } from './store';

const FormListPage = () => {
  const { currentForm } = useFormStore();

  const rows = useMemo(
    () => ({
      records: [
        {
          id: currentForm.id as string,
          name: currentForm.name,
          status: currentForm.status,
          updated_at: new Date().toISOString(),
        },
      ],
    }),
    [currentForm],
  );

  const baseColumns = [
    {
      headerName: 'Name',
      field: 'name',
      cellRenderer: (params: any) => (
        <Link
          href={`/supplier/forms/${params.data.id}/edit`}
          className="text-blue-600 hover:underline"
        >
          {params.data.name}
        </Link>
      ),
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => String(params.data?.name || ''),
      valueFormatter: (params: any) => String(params.data?.name || ''),
      filter: true,
      flex: 2,
    },
    {
      headerName: 'Status',
      field: 'status',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => String(params.data?.status || ''),
      valueFormatter: (params: any) => String(params.data?.status || ''),
      filter: true,
      flex: 1,
    },
    {
      headerName: 'Updated',
      field: 'updated_at',
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) =>
        String(params.data?.updated_at || ''),
      valueFormatter: (params: any) => String(params.data?.updated_at || ''),
      filter: true,
      flex: 1,
    },
  ];

  return (
    <Layout>
      <div className="p-6 space-y-4">
        <div className="flex items-center justify-between">
          <div className="text-2xl font-semibold">Supplier Forms</div>
          <Link href="/supplier/forms/new">
            <PrimaryButton text="Create Form" />
          </Link>
        </div>
        <div className=" ">
          <CommonTable
            data={rows as any}
            isLoading={false}
            columnDefs={baseColumns}
          />
        </div>
      </div>
    </Layout>
  );
};

export default FormListPage;
