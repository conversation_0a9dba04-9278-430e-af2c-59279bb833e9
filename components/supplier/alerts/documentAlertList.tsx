import React, { useState } from 'react';
import { Alert<PERSON>ircle, ChevronDown, ChevronUp } from 'lucide-react';

interface AlertItem {
  id: string;
  name: string;
  description: string;
}

interface AlertSummary {
  critical: AlertItem[];
  warning: AlertItem[];
}

interface AlertData {
  alert_summary: AlertSummary;
}

interface DocumentAlertsProps {
  title: string;
  status: 'critical' | 'warning';
  description: string;
  id: string;
}

const DocumentAlerts: React.FC<DocumentAlertsProps> = ({
  title,
  status,
  description,
}) => {
  const statusColors = {
    critical: 'bg-red-50 border-red-200 text-red-800',
    warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
  };

  const iconColors = {
    critical: 'text-red-500',
    warning: 'text-yellow-500',
  };

  return (
    <div
      className={`flex items-start gap-3 py-2 px-4 rounded-lg border ${statusColors[status]}`}
    >
      <AlertCircle
        className={`w-5 h-5 mt-0.5 flex-shrink-0 ${iconColors[status]}`}
      />
      <div className="flex w-full items-center justify-between">
        <div>
          <h4 className="font-semibold text-base mb-0.5">{title}</h4>
          <p className="text-sm font-medium">{description}</p>
        </div>
        <div></div>
      </div>
    </div>
  );
};

interface DocumentAlertsListProps {
  alertSummary?: AlertSummary;
  isLoading?: boolean;
}

const DocumentAlertsList: React.FC<DocumentAlertsListProps> = ({
  alertSummary,
  isLoading = false,
}) => {
  const [showAll, setShowAll] = useState(false);

  // Show loading state
  if (isLoading) {
    return (
      <div className="w-full">
        <div className="text-center py-8 text-gray-500">
          <p className="text-sm">Loading alerts...</p>
        </div>
      </div>
    );
  }

  // Handle no data
  if (!alertSummary) {
    return null;
  }

  // Flatten all alerts into a single array with status
  const allAlerts = [
    ...(alertSummary.critical || []).map((alert) => ({
      ...alert,
      status: 'critical' as const,
    })),
    ...(alertSummary.warning || []).map((alert) => ({
      ...alert,
      status: 'warning' as const,
    })),
  ];

  const INITIAL_DISPLAY_COUNT = 2;
  const displayedAlerts = showAll
    ? allAlerts
    : allAlerts.slice(0, INITIAL_DISPLAY_COUNT);
  const hasMore = allAlerts.length > INITIAL_DISPLAY_COUNT;

  if (allAlerts.length === 0) {
    return (
      <div className="w-full">
        <div className="text-center py-8 text-gray-500">
          <p className="text-sm">No alerts at this time</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="space-y-2">
        {displayedAlerts.map((alert) => (
          <DocumentAlerts
            key={alert.id}
            id={alert.id}
            title={alert.name}
            status={alert.status}
            description={alert.description}
          />
        ))}
      </div>

      {hasMore && (
        <button
          onClick={() => setShowAll(!showAll)}
          className="mt-4 flex gap-4 items-center text-primary-500 font-semibold text-sm"
        >
          {showAll ? (
            <>
              Show Less
              <ChevronUp className="w-4 h-4" />
            </>
          ) : (
            <>
              Show {allAlerts.length - INITIAL_DISPLAY_COUNT} More Alert
              {allAlerts.length - INITIAL_DISPLAY_COUNT > 1 ? 's' : ''}
              <ChevronDown className="w-4 h-4" />
            </>
          )}
        </button>
      )}
    </div>
  );
};

export default DocumentAlertsList;
