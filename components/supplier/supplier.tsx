import { Upload } from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useCallback, useMemo, useState } from 'react';

import PlusIcon from '@/assets/outline/plus';
import SettingIcon from '@/assets/outline/settting';
import Breadcrumb from '@/components/common/breadcrumb';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { Dialog } from '@/components/common/dialog';
import DeleteModal from '@/components/common/modals/deleteModal';
import Layout from '@/components/common/sidebar/layout';
import CommonTable, { ManageCellRenderer } from '@/components/common/table';
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from '@/components/common/tooltip';
import CreateSupplierModal from '@/components/supplier/modals/createSupplierModal';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { SupplierDetail, SupplierListItem } from '@/interfaces/supplier';
import { getValueOrDefault } from '@/utils/general';
import { hasAccess } from '@/utils/roleAccessConfig';
import { formatDate } from '@/utils/time';

const SupplierHub = () => {
  const router = useRouter();
  const accessToken = useAuthStore((state) => state.accessToken);
  const currentUser = useAuthStore((state) => state.user);
  const [selectedSupplier, setSelectedSupplier] =
    useState<SupplierListItem | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showBulkUploadModal, setShowBulkUploadModal] = useState(false);
  const [createOpen, setCreateOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [selectedId, setSelectedId] = useState<string | null>(null);
  const { data: selectedDetail } = useFetch<SupplierDetail>(
    accessToken,
    selectedId ? `suppliers/${selectedId}` : undefined,
  );

  // Fetch suppliers data from API
  const {
    data: suppliersData,
    isLoading,
    error,
    reFetch,
  } = useFetch<{
    data: SupplierListItem[];
    total: number;
  }>(accessToken, 'suppliers');

  const { deleteData, isLoading: isDeleting } = useDelete<any>();

  const handleDelete = useCallback(async () => {
    if (selectedSupplier) {
      await deleteData(accessToken, `suppliers/${selectedSupplier.id}`);
      setShowDeleteModal(false);
      setSelectedSupplier(null);
      reFetch(); // Refresh the data after deletion
    }
  }, [selectedSupplier, deleteData]);

  const handleDeleteModal = useCallback((rowData: SupplierListItem) => {
    setSelectedSupplier(rowData);
    setShowDeleteModal(true);
  }, []);

  const baseColumns = [
    {
      headerName: 'Legal Name',
      field: 'legal_name',
      cellRenderer: (params: any) => {
        return (
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              router.push(`/supplier-quality/supplier/${params.data.id}`);
            }}
          >
            {getValueOrDefault(params.data, 'legal_name')}
          </button>
        );
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.legal_name || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'legal_name');
      },
      filter: false,
      minWidth: 250,
      flex: 2,
    },
    {
      headerName: 'Website',
      field: 'website',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'website');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.website || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'website');
      },
      filter: false,
      minWidth: 150,
    },
    {
      headerName: 'Type',
      field: 'supplier_type',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'supplier_type');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.supplier_type || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'supplier_type');
      },
      filter: false,
      minWidth: 150,
    },
    {
      headerName: 'Status',
      field: 'status',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'status');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.status || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'status');
      },
      filter: false,
      minWidth: 150,
    },
    {
      headerName: 'Risk Tier',
      field: 'risk_tier',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'risk_tier');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.risk_tier || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'risk_tier');
      },
      filter: false,
    },
    {
      headerName: 'Criticality',
      field: 'criticality_level',
      cellRenderer: (params: any) => {
        return getValueOrDefault(params.data, 'criticality_level');
      },
      sortable: true,
      resizable: true,
      getQuickFilterText: (params: any) => {
        return params.data.criticality_level?.toString() || '';
      },
      valueFormatter: (params: any) => {
        return getValueOrDefault(params.data, 'criticality_level');
      },
      filter: false,
    },
  ];

  const columnDefs = useMemo(() => {
    const cols = [...baseColumns];
    if (hasAccess(AccessActions.EDIT_SUPPLIER, currentUser)) {
      cols.push({
        headerName: 'Manage',
        field: 'manage',
        cellRenderer: (params: any) => (
          <ManageCellRenderer
            rowData={params.data}
            handleEdit={(rowData) => {
              setSelectedId(rowData.id);
              setEditOpen(true);
            }}
            handleDelete={(rowData) => {
              setSelectedSupplier(rowData);
              setShowDeleteModal(true);
            }}
            hideDelete={!hasAccess(AccessActions.IsSupplierAdmin, currentUser)}
          />
        ),
        sortable: false,
        resizable: true,
        getQuickFilterText: (params: any) => {
          return '';
        },
        valueFormatter: (params: any) => {
          return '';
        },
        filter: false,
      });
    }
    return cols;
  }, [currentUser]);
  
  return (
    <Layout>
      {editOpen && selectedDetail && (
        <Dialog open={editOpen} onOpenChange={setEditOpen}>
          <CreateSupplierModal
            edit
            supplier={selectedDetail}
            setOpen={setEditOpen}
            reFetch={reFetch}
          />
        </Dialog>
      )}
      {createOpen && (
        <Dialog open={createOpen} onOpenChange={setCreateOpen}>
          <CreateSupplierModal setOpen={setCreateOpen} reFetch={reFetch} />
        </Dialog>
      )}
      <div className="my-5">
        <div className="flex flex-col">
          <Breadcrumb
            data={[
              { name: 'Supplier Dashboard', link: '/supplier-quality' },
              { name: 'Supplier', link: '#' },
            ]}
          />
          <div className="text-dark-300 font-semibold text-3xl leading-10">
            Supplier Hub
          </div>
        </div>

        <div className="mt-5 mb-5">
          <CommonTable
            data={suppliersData}
            columnDefs={columnDefs}
            isLoading={isLoading}
            searchRightSideElement={
              <div className="flex gap-4">
                <Tooltip>
                  <TooltipTrigger>
                    {hasAccess(AccessActions.IsSupplierAdmin, currentUser) && (
                      <Link href={'/supplier-quality/supplier/administration'}>
                        <SecondaryButton
                          icon={<SettingIcon />}
                          text=""
                          size="medium"
                          buttonClasses="!p-2.5"
                        />
                      </Link>
                    )}
                  </TooltipTrigger>
                  <TooltipContent>Supplier Administration</TooltipContent>
                </Tooltip>

                {/* Always show Add Supplier button for now */}
                {hasAccess(AccessActions.CREATE_SUPPLIER, currentUser) && (
                  <PrimaryButton
                    text="Add Supplier"
                    buttonClasses="!px-5 !py-2"
                    onClick={() => setCreateOpen(true)}
                    icon={<PlusIcon color="white" />}
                  />
                )}
              </div>
            }
          />
        </div>
      </div>
      <Dialog
        open={showDeleteModal}
        onOpenChange={() => setShowDeleteModal(false)}
      >
        <DeleteModal
          title="Delete Supplier"
          infoText="Are you sure you want to delete this supplier? This action cannot be undone."
          btnText="Delete"
          onClick={handleDelete}
          btnLoading={isDeleting}
        />
      </Dialog>
    </Layout>
  );
};

export default SupplierHub;
