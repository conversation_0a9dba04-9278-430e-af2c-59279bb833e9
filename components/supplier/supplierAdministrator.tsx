import { Check, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

import DeleteIcon from '@/assets/outline/delete';
import EditIcon from '@/assets/outline/edit';
import PlusIcon from '@/assets/outline/plus';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/common/accordion';
import Breadcrumb from '@/components/common/breadcrumb';
import LinkButton from '@/components/common/button/linkButton';
import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import Loader from '@/components/common/loader';
import DeleteModal from '@/components/common/modals/deleteModal';
import {
  IOption as ISelectOption,
  ReactSelectMulti,
} from '@/components/common/multiSelectInput';
import SideBarWrapper from '@/components/common/sidebar/layout';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import { cn } from '@/utils/styleUtils';

interface ISupplyCategory {
  id: string;
  name: string;
  description?: string;
  supplier_types: { id: string; name: string }[];
}

interface ISupplierType {
  id: string;
  name: string;
  description?: string;
  categories: { id: string; name: string }[];
}

interface IData {
  value: string;
  id: string;
}

const SupplierAdministrator = () => {
  const [supplyCategories, setSupplyCategories] = React.useState<IData[]>([]);
  const [supplierTypes, setSupplierTypes] = React.useState<IData[]>([]);
  const { accessToken } = useAuthStore();

  const {
    data: categories,
    reFetch: reFetchCategories,
    isLoading: categoryLoading,
  } = useFetch<{ records: ISupplyCategory[] }>(
    accessToken,
    `supply-categories`,
    {},
  );

  const {
    data: types,
    reFetch: reFetchTypes,
    isLoading: typesLoading,
  } = useFetch<{ records: ISupplierType[] }>(accessToken, `supplier-types`, {});

  useEffect(() => {
    if (categories) {
      setSupplyCategories(
        categories.records.map((item) => ({ value: item.name, id: item.id })),
      );
    }
  }, [categories]);

  useEffect(() => {
    if (types) {
      setSupplierTypes(
        types.records.map((item) => ({ value: item.name, id: item.id })),
      );
    }
  }, [types]);

  return (
    <SideBarWrapper>
      <div className="flex flex-col flex-1">
        <div className=" my-5">
          <div>
            <Breadcrumb
              data={[
                { name: 'Supplier Quality', link: '/supplier-quality' },
                { name: 'Supplier', link: '/supplier-quality/supplier' },
                { name: 'Supplier Administration', link: '#' },
              ]}
            />
          </div>
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 ">
            Supplier Administration
          </div>
        </div>

        <div>
          <Accordion type="multiple" className="w-full">
            <AccordionItem value="item-1" className="">
              <AccordionTrigger>
                Manage Supplier Types{' '}
                {supplierTypes.length > 0 ? `(${supplierTypes.length})` : ''}
              </AccordionTrigger>
              <AccordionContent className="p-5">
                {typesLoading ? (
                  <Loader className="h-[150px]" />
                ) : (
                  <div className="grid grid-cols-2 gap-5">
                    {supplierTypes.map((item, index) => (
                      <SupplierTypeInput
                        key={index}
                        data={item}
                        setState={setSupplierTypes}
                        index={index}
                        refetch={reFetchTypes}
                      />
                    ))}
                    <AddSupplierType
                      onClick={() => {
                        setSupplierTypes((pre) => [
                          ...pre,
                          { value: '', id: '' },
                        ]);
                      }}
                    />
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-2" className="mt-4">
              <AccordionTrigger>
                Manage Supply Categories{' '}
                {supplyCategories.length > 0
                  ? `(${supplyCategories.length})`
                  : ''}
              </AccordionTrigger>
              <AccordionContent className="p-5">
                {categoryLoading ? (
                  <Loader className="h-[150px]" />
                ) : supplierTypes.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p className="mb-2">No supplier types available</p>
                    <p className="text-sm">
                      Please create supplier types first before adding
                      categories
                    </p>
                  </div>
                ) : (
                  <SupplyCategoryManager supplierTypes={supplierTypes} />
                )}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </SideBarWrapper>
  );
};

const EditedInput = ({
  data,
  title,
  setState,
  index,
  refetch,
}: {
  data: IData;
  title: string;
  setState: React.Dispatch<React.SetStateAction<IData[]>>;
  index: number;
  refetch: () => void;
}) => {
  const [isEdit, setIsEdit] = useState(false);
  const [name, setName] = useState(data.value);
  const [originalValue, setOriginalValue] = useState(data.value);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const { accessToken } = useAuthStore();
  const { postData, response, error: postError } = usePost();
  const { putData, response: putResponse, error: putError } = usePut();
  const {
    deleteData,
    response: deletedResponse,
    error: deleteError,
    isLoading: deleteLoading,
  } = useDelete();

  useEffect(() => {
    if (isEdit && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEdit]);

  const mapUrl = (title: string) => {
    switch (title) {
      case 'supply-category':
        return 'supply-categories';
      case 'supplier-type':
        return 'supplier-types';
      default:
        return '';
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && name.trim() !== '' && name !== originalValue) {
      handleSubmit(title);
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const handleSubmit = async (title: string) => {
    const url = mapUrl(title);
    if (!url || name.trim() === '' || name === originalValue) return;

    setIsSubmitting(true);
    try {
      if (data.id === '') {
        await postData(accessToken as string, url, { name: name.trim() });
      } else {
        await putData(accessToken as string, `${url}/${data.id}`, {
          name: name.trim(),
        });
      }
    } catch (error) {
      console.error('Error submitting:', error);
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (response && !postError) {
      setState((pre) => {
        const newState = [...pre];
        newState[index] = {
          id: (response as any)?.id || (response as any)?.data?.id || '',
          value: name,
        };
        return newState;
      });
      setOriginalValue(name);
      setIsSubmitting(false);
      setIsEdit(false);
      refetch();
    } else if (putResponse && !putError) {
      setState((pre) => {
        const newState = [...pre];
        newState[index] = {
          id: data.id,
          value: name,
        };
        return newState;
      });
      setOriginalValue(name);
      setIsSubmitting(false);
      setIsEdit(false);
    } else if (postError != null) {
      setIsSubmitting(false);
      setIsEdit(true);
    } else if (
      putError != null &&
      typeof putError === 'object' &&
      Object.keys(putError).length > 0
    ) {
      setIsSubmitting(false);
      setIsEdit(true);
    }
  }, [response, putResponse, postError, putError]);

  useEffect(() => {
    if (data.value === '' && data.id === '') {
      setIsEdit(true);
      setOriginalValue('');
    }
    if (!isEdit && !isSubmitting) {
      setName(data.value);
      setOriginalValue(data.value);
    }
  }, [data, isEdit, isSubmitting]);

  useEffect(() => {
    if (deletedResponse && !deleteError) {
      refetch();
      setOpenDeleteModal(false);
    } else if (deleteError) {
      setOpenDeleteModal(false);
    }
  }, [deletedResponse, deleteError]);

  const handleDelete = async () => {
    const url = mapUrl(title);
    if (url && data.id) {
      try {
        await deleteData(accessToken as string, `${url}/${data.id}`);
      } catch (error) {
        console.error('Error deleting item:', error);
      }
    }
  };

  const handleCancel = () => {
    setName(originalValue);
    setIsEdit(false);

    if (data.id === '' && originalValue === '') {
      setState((pre) => pre.filter((_, i) => i !== index));
    }
  };

  return (
    <div className="flex items-center gap-2.5 flex-1">
      <Input
        placeholder={'Add ' + title.replace('-', ' ')}
        value={name}
        containerClass="flex-1"
        disabled={!isEdit || isSubmitting}
        ref={inputRef}
        onChange={(e) => setName(e.target.value)}
        onKeyDown={handleKeyDown}
      />
      {isEdit ? (
        <>
          <div
            className={cn(
              'h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer',
              name === originalValue || isSubmitting || name.trim() === ''
                ? 'cursor-not-allowed opacity-50'
                : '',
            )}
            onClick={() =>
              !isSubmitting &&
              name.trim() !== '' &&
              name !== originalValue &&
              handleSubmit(title)
            }
          >
            <Check className="h-5 w-5" />
          </div>
          <div
            className={cn(
              'h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer',
              isSubmitting ? 'cursor-not-allowed opacity-50' : '',
            )}
            onClick={() => !isSubmitting && handleCancel()}
          >
            <X className="h-5 w-5" />
          </div>
        </>
      ) : (
        <>
          <div
            className="h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer"
            onClick={() => {
              setIsEdit(true);
            }}
          >
            <EditIcon />
          </div>
          <Dialog open={openDeleteModal} onOpenChange={setOpenDeleteModal}>
            <DialogTrigger asChild>
              <div className="h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer">
                <DeleteIcon height="20" width="20" />
              </div>
            </DialogTrigger>
            <DeleteModal
              title={'Delete '}
              infoText={'Are you sure you want to delete "' + data.value + '"'}
              btnText={'Delete'}
              onClick={handleDelete}
              btnLoading={deleteLoading}
            />
          </Dialog>
        </>
      )}
    </div>
  );
};

const AddCategory = ({
  text,
  onClick,
}: {
  text: string;
  onClick?: () => void;
}) => {
  return (
    <div className="flex items-center">
      <LinkButton
        size="large"
        text={text}
        icon={<PlusIcon />}
        iconPosition="left"
        onClick={onClick}
      />
    </div>
  );
};

const AddSupplierType = ({ onClick }: { onClick?: () => void }) => {
  return (
    <div className="flex items-center">
      <LinkButton
        size="medium"
        text="Add Supplier Type"
        icon={<PlusIcon />}
        iconPosition="left"
        onClick={onClick}
      />
    </div>
  );
};

const SupplierTypeInput = ({
  data,
  setState,
  index,
  refetch,
}: {
  data: IData;
  setState: React.Dispatch<React.SetStateAction<IData[]>>;
  index: number;
  refetch: () => void;
}) => {
  const [isEdit, setIsEdit] = useState(false);
  const [name, setName] = useState(data.value);
  const [description, setDescription] = useState('');
  const [originalValue, setOriginalValue] = useState(data.value);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const { accessToken } = useAuthStore();
  const { postData, response, error: postError } = usePost();
  const { putData, response: putResponse, error: putError } = usePut();
  const {
    deleteData,
    response: deletedResponse,
    error: deleteError,
    isLoading: deleteLoading,
  } = useDelete();

  useEffect(() => {
    if (isEdit && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEdit]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && name.trim() !== '' && name !== originalValue) {
      handleSubmit();
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const handleSubmit = async () => {
    if (name.trim() === '' || name === originalValue) return;

    setIsSubmitting(true);
    try {
      const payload: any = { name: name.trim() };
      if (description.trim()) {
        payload.description = description.trim();
      }

      if (data.id === '') {
        await postData(accessToken as string, 'supplier-types', payload);
      } else {
        await putData(
          accessToken as string,
          `supplier-types/${data.id}`,
          payload,
        );
      }
    } catch (error) {
      console.error('Error submitting:', error);
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (response && !postError) {
      setState((pre) => {
        const newState = [...pre];
        newState[index] = {
          id: (response as any)?.id || (response as any)?.data?.id || '',
          value: name,
        };
        return newState;
      });
      setOriginalValue(name);
      setIsSubmitting(false);
      setIsEdit(false);
      setDescription('');
      refetch();
    } else if (putResponse && !putError) {
      setState((pre) => {
        const newState = [...pre];
        newState[index] = {
          id: data.id,
          value: name,
        };
        return newState;
      });
      setOriginalValue(name);
      setIsSubmitting(false);
      setIsEdit(false);
      setDescription('');
    } else if (postError != null) {
      setIsSubmitting(false);
      setIsEdit(true);
    } else if (
      putError != null &&
      typeof putError === 'object' &&
      Object.keys(putError).length > 0
    ) {
      setIsSubmitting(false);
      setIsEdit(true);
    }
  }, [response, putResponse, postError, putError]);

  useEffect(() => {
    if (data.value === '' && data.id === '') {
      setIsEdit(true);
      setOriginalValue('');
    }
    if (!isEdit && !isSubmitting) {
      setName(data.value);
      setOriginalValue(data.value);
      setDescription('');
    }
  }, [data, isEdit, isSubmitting]);

  useEffect(() => {
    if (deletedResponse && !deleteError) {
      refetch();
      setOpenDeleteModal(false);
    } else if (deleteError) {
      setOpenDeleteModal(false);
    }
  }, [deletedResponse, deleteError]);

  const handleDelete = async () => {
    if (data.id) {
      try {
        await deleteData(accessToken as string, `supplier-types/${data.id}`);
      } catch (error) {
        console.error('Error deleting item:', error);
      }
    }
  };

  const handleCancel = () => {
    setName(originalValue);
    setDescription('');
    setIsEdit(false);

    if (data.id === '' && originalValue === '') {
      setState((pre) => pre.filter((_, i) => i !== index));
    }
  };

  return (
    <div className="flex flex-col gap-2.5 flex-1">
      <div className="flex items-center gap-2.5">
        <Input
          placeholder="Add supplier type"
          value={name}
          containerClass="flex-1"
          disabled={!isEdit || isSubmitting}
          ref={inputRef}
          onChange={(e) => setName(e.target.value)}
          onKeyDown={handleKeyDown}
        />
        {isEdit ? (
          <>
            <div
              className={cn(
                'h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer',
                name === originalValue || isSubmitting || name.trim() === ''
                  ? 'cursor-not-allowed opacity-50'
                  : '',
              )}
              onClick={() =>
                !isSubmitting &&
                name.trim() !== '' &&
                name !== originalValue &&
                handleSubmit()
              }
            >
              <Check className="h-5 w-5" />
            </div>
            <div
              className={cn(
                'h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer',
                isSubmitting ? 'cursor-not-allowed opacity-50' : '',
              )}
              onClick={() => !isSubmitting && handleCancel()}
            >
              <X className="h-5 w-5" />
            </div>
          </>
        ) : (
          <>
            <div
              className="h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer"
              onClick={() => {
                setIsEdit(true);
              }}
            >
              <EditIcon />
            </div>
            <Dialog open={openDeleteModal} onOpenChange={setOpenDeleteModal}>
              <DialogTrigger asChild>
                <div className="h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer">
                  <DeleteIcon height="20" width="20" />
                </div>
              </DialogTrigger>
              <DeleteModal
                title={'Delete '}
                infoText={
                  'Are you sure you want to delete "' + data.value + '"'
                }
                btnText={'Delete'}
                onClick={handleDelete}
                btnLoading={deleteLoading}
              />
            </Dialog>
          </>
        )}
      </div>
    </div>
  );
};
interface ISupplyCategoryRecord {
  id: string;
  name: string;
  description?: string;
  supplier_types: { id: string; name: string }[];
}

const SupplyCategoryManager = ({
  supplierTypes,
}: {
  supplierTypes: IData[];
}) => {
  const { accessToken } = useAuthStore();
  const { data, reFetch, isLoading } = useFetch<{
    records: ISupplyCategoryRecord[];
  }>(accessToken, `supply-categories`, {});
  const { postData, response: postRes, error: postErr } = usePost();
  const { putData, response: putRes, error: putErr } = usePut();
  const {
    deleteData,
    response: delRes,
    error: delErr,
    isLoading: delLoading,
  } = useDelete();

  const [categories, setCategories] = useState<
    {
      id: string;
      name: string;
      typeIds: string[];
    }[]
  >([]);

  const [openCreate, setOpenCreate] = useState(false);
  const [createName, setCreateName] = useState('');
  const [createDescription, setCreateDescription] = useState('');
  const [createTypes, setCreateTypes] = useState<ISelectOption[]>([]);

  useEffect(() => {
    if (data) {
      setCategories(
        data.records.map((c) => ({
          id: c.id,
          name: c.name,
          typeIds: c.supplier_types.map((t) => t.id),
        })),
      );
    }
  }, [data]);

  useEffect(() => {
    if ((postRes && !postErr) || (putRes && !putErr) || (delRes && !delErr)) {
      reFetch();
    }
  }, [postRes, postErr, putRes, putErr, delRes, delErr]);

  const typeOptions = supplierTypes.map((t) => ({
    value: t.id,
    label: t.value,
  }));

  return (
    <div className="flex flex-col gap-4">
      {isLoading ? (
        <Loader className="h-[150px]" />
      ) : (
        <>
          {/* Header */}
          <div className="grid grid-cols-12 gap-4 px-4 py-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="col-span-3 font-semibold text-sm text-gray-700 uppercase tracking-wide">
              Category Name
            </div>
            <div className="col-span-7 font-semibold text-sm text-gray-700 uppercase tracking-wide">
              Allowed Supplier Types
            </div>
            <div className="col-span-2 font-semibold text-sm text-gray-700 uppercase tracking-wide text-center">
              Actions
            </div>
          </div>

          {/* Category Rows */}
          <div className="space-y-2">
            {categories.map((c, idx) => (
              <CategoryTableRow
                key={c.id || idx}
                category={c}
                onChange={(next) =>
                  setCategories((prev) =>
                    prev.map((p) => (p.id === c.id ? next : p)),
                  )
                }
                onSave={async (next) => {
                  if (!next.name.trim() || next.typeIds.length === 0) return;
                  const payload: any = {
                    name: next.name.trim(),
                    supplier_type_ids: next.typeIds,
                  };
                  if (next.description?.trim()) {
                    payload.description = next.description.trim();
                  }
                  if (!next.id) {
                    await postData(
                      accessToken as string,
                      'supply-categories',
                      payload,
                    );
                  } else {
                    await putData(
                      accessToken as string,
                      `supply-categories/${next.id}`,
                      payload,
                    );
                  }
                }}
                onDelete={async () => {
                  if (c.id)
                    await deleteData(
                      accessToken as string,
                      `supply-categories/${c.id}`,
                    );
                }}
                typeOptions={typeOptions}
                deleting={delLoading}
              />
            ))}
          </div>

          <div className="mt-4">
            <LinkButton
              size="medium"
              text="Add Supply Category"
              icon={<PlusIcon />}
              iconPosition="left"
              onClick={() => {
                setCreateName('');
                setCreateDescription('');
                setCreateTypes([]);
                setOpenCreate(true);
              }}
            />
          </div>

          <Dialog
            open={openCreate}
            onOpenChange={(o) => {
              setOpenCreate(o);
              if (!o) {
                setCreateName('');
                setCreateDescription('');
                setCreateTypes([]);
              }
            }}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Supply Category</DialogTitle>
              </DialogHeader>

              <div className="flex flex-col gap-4">
                <div>
                  <Label className="mb-1">Category name</Label>
                  <Input
                    placeholder="Category name"
                    value={createName}
                    onChange={(e) => setCreateName(e.target.value)}
                  />
                </div>
                <div>
                  <Label className="mb-1">Description (optional)</Label>
                  <Input
                    placeholder="Category description"
                    value={createDescription}
                    onChange={(e) => setCreateDescription(e.target.value)}
                  />
                </div>
                <div>
                  <Label className="mb-1">Supplier Types</Label>
                  <ReactSelectMulti
                    options={typeOptions}
                    value={createTypes}
                    placeholder="Select supplier types"
                    onChange={(val) => setCreateTypes(val)}
                    hasError={
                      createTypes.length === 0 && createName.trim() !== ''
                    }
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Supply category requires at least one supplier type.
                  </p>
                </div>
              </div>

              <DialogFooter>
                <SecondaryButton
                  text="Cancel"
                  onClick={() => setOpenCreate(false)}
                />
                <PrimaryButton
                  text="Create"
                  disabled={!(createName.trim() && createTypes.length > 0)}
                  onClick={async () => {
                    if (!createName.trim() || createTypes.length === 0) return;
                    const payload: any = {
                      name: createName.trim(),
                      supplier_type_ids: createTypes.map((t) => t.value),
                    };
                    if (createDescription.trim()) {
                      payload.description = createDescription.trim();
                    }
                    await postData(
                      accessToken as string,
                      'supply-categories',
                      payload,
                    );
                    setOpenCreate(false);
                    setCreateName('');
                    setCreateDescription('');
                    setCreateTypes([]);
                  }}
                />
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </>
      )}
    </div>
  );
};

const CategoryTableRow = ({
  category,
  onChange,
  onSave,
  onDelete,
  typeOptions,
  deleting,
}: {
  category: { id: string; name: string; typeIds: string[] };
  onChange: (next: { id: string; name: string; typeIds: string[] }) => void;
  onSave: (next: {
    id: string;
    name: string;
    typeIds: string[];
    description?: string;
  }) => Promise<void>;
  onDelete: () => Promise<void>;
  typeOptions: ISelectOption[];
  deleting: boolean;
}) => {
  const [openEdit, setOpenEdit] = useState(false);
  const [editName, setEditName] = useState(category.name);
  const [editDescription, setEditDescription] = useState('');
  const [editTypes, setEditTypes] = useState<ISelectOption[]>(
    typeOptions.filter((o) => category.typeIds.includes(o.value)),
  );

  useEffect(() => {
    if (!openEdit) {
      setEditName(category.name);
      setEditDescription('');
      setEditTypes(
        typeOptions.filter((o) => category.typeIds.includes(o.value)),
      );
    }
  }, [category, openEdit, typeOptions]);

  const canSave = editName.trim() !== '' && editTypes.length > 0;

  return (
    <>
      <div className="grid grid-cols-12 gap-4 px-4 py-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
        {/* Category Name */}
        <div className="col-span-3 flex items-center">
          <span className="font-medium text-gray-900">{category.name}</span>
        </div>

        {/* Supplier Types Pills */}
        <div className="col-span-7 flex items-center">
          <div className="flex flex-wrap gap-2">
            {typeOptions
              .filter((o) => category.typeIds.includes(o.value))
              .map((o) => (
                <span
                  key={o.value}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary-100 text-primary-800 border border-primary-200"
                >
                  {o.label}
                </span>
              ))}
            {category.typeIds.length === 0 && (
              <span className="text-gray-500 text-sm italic">
                No types assigned
              </span>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="col-span-2 flex items-center justify-center gap-2">
          <div
            className="h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer"
            onClick={() => setOpenEdit(true)}
          >
            <EditIcon />
          </div>
          <Dialog>
            <DialogTrigger asChild>
              <div className="h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer">
                <DeleteIcon height="20" width="20" />
              </div>
            </DialogTrigger>
            <DeleteModal
              title={'Delete '}
              infoText={
                'Are you sure you want to delete "' + category.name + '"'
              }
              btnText={'Delete'}
              onClick={async () => await onDelete()}
              btnLoading={deleting}
            />
          </Dialog>
        </div>
      </div>

      {/* Edit Modal */}
      <Dialog open={openEdit} onOpenChange={setOpenEdit}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Supply Category</DialogTitle>
          </DialogHeader>
          <div className="flex flex-col gap-4">
            <div>
              <Label className="mb-1">Category name</Label>
              <Input
                placeholder="Category name"
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
              />
            </div>
            <div>
              <Label className="mb-1">Description (optional)</Label>
              <Input
                placeholder="Category description"
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
              />
            </div>
            <div>
              <Label className="mb-1">Supplier Types</Label>
              <ReactSelectMulti
                options={typeOptions}
                value={editTypes}
                placeholder="Select supplier types"
                onChange={(val) => setEditTypes(val)}
                hasError={editTypes.length === 0}
              />
            </div>
          </div>
          <DialogFooter>
            <SecondaryButton text="Cancel" onClick={() => setOpenEdit(false)} />
            <PrimaryButton
              text="Save"
              disabled={!canSave}
              onClick={async () => {
                if (!canSave) return;
                const next: any = {
                  id: category.id,
                  name: editName.trim(),
                  typeIds: editTypes.map((t) => t.value),
                };
                if (editDescription.trim()) {
                  next.description = editDescription.trim();
                }
                await onSave(next);
                onChange(next);
                setOpenEdit(false);
              }}
            />
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default SupplierAdministrator;
