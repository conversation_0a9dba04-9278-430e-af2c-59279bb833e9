import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { IValidationRuleWithResponse } from '@/interfaces/production';

interface NextStepInfo {
  nextStepName: string;
  triggeredByRule?: string;
  isConditional: boolean;
}

interface NextStepBannerProps {
  data?: any; // Work order process data
  compact?: boolean; // For table view
}

const NextStepBanner = ({ data, compact = false }: NextStepBannerProps) => {
  const { accessToken } = useAuthStore();
  const router = useRouter();
  const [nextStepInfo, setNextStepInfo] = useState<NextStepInfo | null>(null);

  // Fetch validation rules with responses for current step
  const { data: validationData } = useFetch<{
    validation_rules: IValidationRuleWithResponse[];
  }>(
    accessToken,
    router.query.workOrderId && router.query.stepId
      ? `production/work-orders/${router.query.workOrderId}/process-steps/${router.query.stepId}/checklist-item-with-validation`
      : undefined,
  );

  // Use provided data or fetch work order steps
  const { data: fetchedWorkOrderData } = useFetch<{
    record: {
      steps: Array<{
        id: string;
        name: string;
        sequence_no: number;
        status: string;
        is_enabled?: boolean;
      }>;
    };
  }>(
    accessToken,
    !data && router.query.workOrderId
      ? `production/work-orders/${router.query.workOrderId}/process`
      : undefined,
  );

  const workOrderData = data || fetchedWorkOrderData;

  useEffect(() => {
    if (!workOrderData) return;

    const allSteps = workOrderData.record?.steps || [];

    // For table view, compute next step from enabled TODO steps to reflect backend logic
    if (compact) {
      const todoEnabled = allSteps.filter(
        (s: any) => s.status === 'Yet to start' && s.is_enabled,
      );

      if (todoEnabled.length > 0) {
        // If multiple enabled TODO (shouldn't happen now), pick the one with lowest sequence
        const next = todoEnabled.reduce((min: any, cur: any) =>
          (min.sequence_no ?? 1e9) < (cur.sequence_no ?? 1e9) ? min : cur,
        );
        setNextStepInfo({ nextStepName: next.name, isConditional: false });
        return;
      }

      // If none enabled, look for the first TODO after the highest completed, as fallback
      const completedSteps = allSteps.filter(
        (step: any) => step.status === 'Completed',
      );
      if (completedSteps.length === 0) {
        setNextStepInfo(null);
        return;
      }
      const lastCompleted = completedSteps.reduce((latest: any, cur: any) =>
        (cur.sequence_no ?? 0) > (latest.sequence_no ?? 0) ? cur : latest,
      );
      const seqNext = allSteps.find(
        (s: any) => s.sequence_no === (lastCompleted.sequence_no ?? 0) + 1,
      );
      setNextStepInfo({
        nextStepName: seqNext ? seqNext.name : 'Process Complete',
        isConditional: false,
      });
      return;
    }

    // For individual step view
    const currentStepId = router.query.stepId as string;
    const currentStep = allSteps.find((step: any) => step.id === currentStepId);

    console.log('NextStepBanner Debug - Individual step view:', {
      currentStepId,
      currentStep: currentStep
        ? {
            name: currentStep.name,
            status: currentStep.status,
            sequence: currentStep.sequence_no,
          }
        : null,
      hasValidationData: !!validationData,
    });

    if (!currentStep) {
      setNextStepInfo(null);
      return;
    }

    // Only show banner if current step is completed
    if (currentStep.status !== 'Completed') {
      setNextStepInfo(null);
      return;
    }

    // Prefer the currently enabled TODO step, if any, to mirror backend activation
    const enabledTodo = allSteps.filter(
      (s: any) => s.status === 'Yet to start' && s.is_enabled,
    );

    if (enabledTodo.length > 0) {
      const next = enabledTodo.reduce((min: any, cur: any) =>
        (min.sequence_no ?? 1e9) < (cur.sequence_no ?? 1e9) ? min : cur,
      );
      setNextStepInfo({ nextStepName: next.name, isConditional: false });
      return;
    }

    // Otherwise, try to infer based on conditional response matching; if none, fall back to next sequential
    let conditionalNextStep: string | null = null;
    let triggeringRule: string | null = null;

    const allValidationRules: IValidationRuleWithResponse[] = [];
    if (validationData?.validation_rules) {
      allValidationRules.push(...validationData.validation_rules);
    }

    for (const ruleData of allValidationRules) {
      const rule = ruleData.rule;
      const userResponse = ruleData.user_response;
      const flowConditions = ruleData.flow_conditions || [];

      if (
        rule.type === 'ConditionalStep' &&
        userResponse &&
        flowConditions.length > 0
      ) {
        const matchingFlowCondition = flowConditions.find(
          (fc) =>
            (fc.condition || '').trim().toLowerCase() ===
            (userResponse.user_response || '').trim().toLowerCase(),
        );

        if (matchingFlowCondition && matchingFlowCondition.next_step) {
          const nextStep = allSteps.find(
            (step: any) => step.id === matchingFlowCondition.next_step,
          );

          if (nextStep) {
            conditionalNextStep = nextStep.name;
            triggeringRule = rule.rule_name || 'Conditional Rule';
            break;
          }
        }
      }
    }

    if (!conditionalNextStep) {
      const nextStepInSequence = allSteps.find(
        (step: any) => step.sequence_no === currentStep.sequence_no + 1,
      );

      if (nextStepInSequence) {
        setNextStepInfo({
          nextStepName: nextStepInSequence.name,
          isConditional: false,
        });
      } else {
        setNextStepInfo({
          nextStepName: 'Process Complete',
          isConditional: false,
        });
      }
    } else {
      setNextStepInfo({
        nextStepName: conditionalNextStep,
        triggeredByRule: triggeringRule || undefined,
        isConditional: true,
      });
    }
  }, [validationData, workOrderData, router.query.stepId, compact]);

  if (!nextStepInfo) return null;

  // Compact version for table view
  if (compact) {
    return (
      <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <svg
              className="h-4 w-4 text-blue-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <div className="flex items-center space-x-4">
              <h3 className="text-sm font-medium text-blue-800">
                Next Step Information
              </h3>
              <div className="text-sm text-blue-700">
                <span className="font-semibold">Next Step:</span>{' '}
                <span className="text-blue-900">
                  {nextStepInfo.nextStepName}
                </span>
              </div>
              {!nextStepInfo.isConditional &&
                nextStepInfo.nextStepName !== 'Process Complete' && (
                  <span className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                    Following standard process sequence
                  </span>
                )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Full version for individual step view
  return (
    <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <svg
            className="h-5 w-5 text-blue-400"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <div className="ml-3">
          <h3 className="text-sm font-medium text-blue-800">
            Next Step Information
          </h3>
          <div className="mt-2 text-sm text-blue-700">
            <p>
              <span className="font-semibold">Next Step:</span>{' '}
              {nextStepInfo.nextStepName}
            </p>
            {nextStepInfo.isConditional && nextStepInfo.triggeredByRule && (
              <p className="mt-1">
                <span className="font-semibold">Triggered by:</span>{' '}
                {nextStepInfo.triggeredByRule}
              </p>
            )}
            {!nextStepInfo.isConditional && (
              <p className="mt-1 text-xs">
                Following standard process sequence
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NextStepBanner;
