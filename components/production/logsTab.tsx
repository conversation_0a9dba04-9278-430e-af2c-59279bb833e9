import React from "react";
// import { useParams } from "react-router-dom";

// import useFetch from "../../hooks/useFetch";
// import LogList from "../List/LogList";

const LogTabs = () => {
  //   const param = useParams();
  //   const { data, isLoading, error, reFetch } = useFetch(
  //     `products/${param?.id}/logs`,
  //     {}
  //   );

  return (
    <div>
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-1">
        <div className="flex flex-col gap-9">
          <div className="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
            <div className="flex flex-col gap-5.5 p-6.5">
              {/* {data.records && <LogList logs={data} />} */}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LogTabs;
