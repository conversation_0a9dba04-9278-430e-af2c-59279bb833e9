import { useRouter } from 'next/router';

import Loader from '@/components/common/loader';
import PreviewFlow from '@/components/masterProduct/processTab/Flowchart/preview';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';
import { ReactFlowProvider } from '@xyflow/react';

import useFetch from '../../../hooks/useFetch';
import WorkOrderProcessTable from './wOProcessTable';

const WOProcessTab = ({ data }: { data: any }) => {
  const user = useAuthStore((state) => state.user);
  const accessToken = useAuthStore((state) => state.accessToken);

  const router = useRouter();
  const {
    data: processData,
    isLoading: dataLoading,
    reFetch,
  } = useFetch(
    accessToken,
    `production/work-orders/${router.query.workOrderId}/process`,
  );

  return (
    <div>
      {dataLoading ? (
        <Loader className="h-[50vh]" />
      ) : (
        <div className="w-300 h-90 gap-5 mb-20">
          <ReactFlowProvider>
            <div className="w-300 h-90 border border-[rgba(221,221,221,1)] relative rounded-lg">
              <PreviewFlow data={processData} />
            </div>
          </ReactFlowProvider>
          <WorkOrderProcessTable
            data={processData}
            reFetch={reFetch}
            edit={hasAccess(
              AccessActions.CanEditSpecificProduct,
              user,
              data?.assignees?.some(
                (assignee: any) => assignee.id === user?.id,
              ),
            )}
          />
        </div>
      )}
    </div>
  );
};

export default WOProcessTab;
