import { useRouter } from 'next/router';
import React, { useState } from 'react';

import EditIcon from '@/assets/outline/edit';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { TWorkOrder } from '@/interfaces/production';
import { hasAccess } from '@/utils/roleAccessConfig';
import { truncateWithEllipsis } from '@/utils/truncateText';

import { useDelete } from '../../hooks/useDelete';
import DeleteButton from '../common/button/deleteButton';
import SecondaryButton from '../common/button/secondaryButton';
import { Dialog, DialogTrigger } from '../common/dialog';
import { FileCard } from '../common/fileCard';
import { DetailsText } from '../common/infoDetail';
import DeleteModal from '../common/modals/deleteModal';
import AddWOModal from './modals/addWOModal';

interface User {
  id: string;
  full_name: string;
}

const WOInfoTabContent: React.FC<{ data: TWorkOrder; reFetch: any }> = ({
  data,
  reFetch,
}: {
  data: TWorkOrder;
  reFetch: any;
}) => {
  const [editWorkOrder, setEditWorkOrder] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const user = useAuthStore((state) => state.user);
  const { accessToken } = useAuthStore();
  const { deleteData, isLoading } = useDelete();
  const router = useRouter();

  const onDelete = () => {
    async function fetch() {
      await deleteData(
        accessToken as string,
        `production/work-orders/${data?.id}`,
      ).then(() => {
        router.push(`/production`);
      });
    }
    fetch();
  };

  return (
    <>
      <div className="flex border border-grey-100 bg-white items-start justify-between p-2 rounded-lg">
        {data && (
          <>
            <div className="p-2 flex flex-col gap-3">
              <DetailsText
                label="Work order no."
                value={truncateWithEllipsis(data.order_no, 20) || '-'}
              />
              <DetailsText label="UoM" value={data.uom || '-'} />
              <DetailsText
                label="Customer name"
                value={truncateWithEllipsis(data.customer_name, 20) || '-'}
              />
            </div>
            <div className="p-2 flex flex-col gap-3">
              <DetailsText
                label="Reference no."
                value={data.reference_no || '-'}
              />
              <DetailsText label="Manager" value={data.managers} multiValue />
              <DetailsText
                label="Expected start date"
                value={data.start_date || '-'}
              />
            </div>
            <div className="p-2 flex flex-col gap-3">
              <DetailsText
                label="Quantity"
                value={String(data.quantity) || '-'}
              />
              <DetailsText
                label="Priority level"
                value={data.priority || '-'}
              />
              <DetailsText
                label="Expected delivery date"
                value={data.delivery_date || '-'}
              />
            </div>
          </>
        )}
        {hasAccess(AccessActions.CanEditSpecificWorkOrder, user) && (
          <div className="flex items-center gap-3">
            <Dialog open={editWorkOrder} onOpenChange={setEditWorkOrder}>
              <DialogTrigger asChild>
                <SecondaryButton
                  size="medium"
                  icon={<EditIcon color="#016366" className="h-5 w-5" />}
                  text="Edit"
                  onClick={() => setEditWorkOrder(true)}
                />
              </DialogTrigger>
              <AddWOModal
                edit={true}
                orderData={data as unknown as TWorkOrder}
                reFetch={reFetch}
                setOpenEdit={setEditWorkOrder}
              />
            </Dialog>
            {hasAccess(AccessActions.CanDeleteSpecificWorkOrder, user) && (
              <Dialog open={showDeleteModal} onOpenChange={setShowDeleteModal}>
                <DialogTrigger asChild>
                  <DeleteButton />
                </DialogTrigger>
                <DeleteModal
                  title={`Delete work order`}
                  infoText={'Are you sure you want to delete this work order?'}
                  btnText={'Delete'}
                  onClick={onDelete}
                >
                  <div className="p-2 border flex flex-col gap-4 border-white-300 bg-white-100 px-2.5 py-2 rounded-lg">
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium leading-5 text-grey-300">
                        Work order no:
                      </div>
                      <div className="text-base font-medium leading-6 text-dark-300">
                        {data.order_no}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="text-sm font-medium leading-5 text-grey-300">
                        Work order name
                      </div>
                      <div className="text-base font-medium leading-6 text-dark-300">
                        {data.name}
                      </div>
                    </div>
                  </div>
                </DeleteModal>
              </Dialog>
            )}
          </div>
        )}
      </div>

      <div className="mt-4">
        <div className="text-base leading-6 font-medium text-dark-100 mb-3">
          Linked files
        </div>
        {data?.attachments && data?.attachments?.length > 0 ? (
          <div className="flex items-center flex-wrap gap-2">
            {data?.attachments?.map((file, index) => (
              <FileCard
                key={index}
                filepath={file.file_path}
                file_extension={file.file_extension}
              />
            ))}
          </div>
        ) : (
          <>-- No files attached --</>
        )}
      </div>
    </>
  );
};

export default WOInfoTabContent;
