import { useRouter } from 'next/router';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';

const AddNoteModal = ({
  edit,
  closeModal,
  note,
  reFetch,
}: {
  edit?: boolean;
  closeModal: any;
  note?: string;
  reFetch: () => void;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const router = useRouter();

  const { postData, isLoading: isLoadingPost } = usePost();

  const { putData, isLoading: isLoadingPut } = usePut();

  const handleSubmit = async (event: any) => {
    try {
      event.preventDefault();

      const formData = new FormData(event.target);

      const body = {
        instruction: formData.get('note'),
      };

      accessToken &&
        (await putData(
          accessToken,
          `production/work-orders/${router.query.workOrderId}/process-steps/${router.query.stepId}`,
          body,
        ));

      event.target.reset();
      reFetch();
      closeModal();
    } catch (error) {
      console.error('Error during operation:', error);
    }
  };

  return (
    <>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{edit ? 'Edit' : 'Add a'} note</DialogTitle>
        </DialogHeader>

        <div className="w-full flex justify-center">
          <form onSubmit={handleSubmit} className="w-full">
            <div className="flex flex-col gap-9">
              <div className=" bg-white rounded-sm">
                <div className="p-6.5">
                  <div className="flex flex-col gap-2.5">
                    <Label
                      htmlFor="note"
                      className="text-base font-medium leading-6 text-dark-100 "
                    >
                      Note<span className="text-red-500">*</span>
                    </Label>
                    <Textarea
                      rows={5}
                      name="note"
                      required
                      placeholder="Add here..."
                      defaultValue={note}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-0 bg-white mx-0 pt-4">
              <PrimaryButton
                size="medium"
                text="Submit"
                isLoading={isLoadingPost || isLoadingPut}
              />
            </div>
          </form>
        </div>
      </DialogContent>
    </>
  );
};

export default AddNoteModal;
