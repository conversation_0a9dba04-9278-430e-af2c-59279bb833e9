import { useRouter } from 'next/router';
import { useEffect, useMemo, useState } from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import { RadioGroup, RadioGroupItem } from '@/components/common/radio-group';
import UploadComponent from '@/components/common/uploadComponent';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import { IAttachment } from '@/interfaces/misc';
import {
  IFlowCondition,
  IValidationRule,
  IValidationRuleWithResponse,
  IWorkInstruction,
} from '@/interfaces/production';

const UpdateInstructionModal = ({
  closeModal,
  instruction,
  reFetch,
}: {
  closeModal: () => void;
  instruction: IWorkInstruction;
  reFetch: () => void;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const router = useRouter();
  const [addedFiles, setAddedFiles] = useState<IAttachment[]>([]);
  const [remarks, setRemarks] = useState('');
  const [validationResponses, setValidationResponses] = useState<
    Record<string, string>
  >({});

  const [isSubmitting, setIsSubmitting] = useState(false);

  const { postData, isLoading: isLoadingPost } = usePost();
  const { putData, isLoading: isLoadingPut } = usePut();

  // Fetch validation rules for this checklist item
  const { data: validationData } = useFetch<{
    validation_rules: IValidationRuleWithResponse[];
  }>(
    accessToken,
    router.query.workOrderId && router.query.stepId && instruction.id
      ? `production/work-orders/${router.query.workOrderId}/process-steps/${router.query.stepId}/checklist-item/${instruction.id}/validation-rules`
      : undefined,
  );

  const validationRules = useMemo(
    () => validationData?.validation_rules || [],
    [validationData?.validation_rules],
  );

  // Initialize validation responses from existing data
  useEffect(() => {
    if (validationRules.length > 0) {
      const responses: Record<string, string> = {};
      validationRules.forEach((ruleData) => {
        if (ruleData.user_response) {
          responses[ruleData.rule.id] = ruleData.user_response.user_response;
        }
      });
      setValidationResponses(responses);
    }
  }, [validationRules]);

  // Removed real-time validation - will validate only on submit

  const validateResponse = (
    userResponse: string,
    rule: IValidationRule,
    flowConditions?: IFlowCondition[],
  ): boolean => {
    try {
      if (rule.type === 'ValueMatch') {
        return (
          userResponse.trim().toLowerCase() ===
          rule.condition.trim().toLowerCase()
        );
      } else if (rule.type === 'ValueRange') {
        // For range validation, we expect the rule.condition to be in "min-max" format
        // and the user should provide a single number that falls within that range
        if (rule.condition.includes('-')) {
          const [min, max] = rule.condition
            .split('-')
            .map((v) => parseFloat(v.trim()));

          // Check if user entered a range (which is not allowed for range validation)
          if (userResponse.trim().includes('-')) {
            return false; // User should not enter a range, only a single number
          }

          const userValue = parseFloat(userResponse.trim());

          // Check if parsing was successful and value is within range
          if (isNaN(userValue)) {
            return false;
          }

          return userValue >= min && userValue <= max;
        } else {
          // Single value condition
          const userValue = parseFloat(userResponse.trim());
          return (
            !isNaN(userValue) && userValue === parseFloat(rule.condition.trim())
          );
        }
      } else if (rule.type === 'ConditionalStep') {
        // For conditional steps, check if user response matches predefined options
        let validOptions: string[] = [];

        if (flowConditions && flowConditions.length > 0) {
          // Use flow conditions as valid options
          validOptions = flowConditions
            .filter((fc) => fc.condition)
            .map((fc) => fc.condition as string);
        } else if (rule.condition) {
          // Fallback to rule condition if no flow conditions
          validOptions = rule.condition.split(',').map((opt) => opt.trim());
        }

        // Check if user response matches any of the valid predefined options
        return validOptions.some(
          (option) =>
            option.toLowerCase() === userResponse.trim().toLowerCase(),
        );
      }
      return false;
    } catch {
      return false;
    }
  };

  // Calculate overall compliance based on all validation rules and evidence requirement
  const calculateOverallCompliance = (): {
    isCompliant: boolean;
    message: string;
  } => {
    // Check evidence requirement first
    const requiresEvidence = instruction.work_instruction?.requires_evidence;
    if (requiresEvidence && (!addedFiles || addedFiles.length === 0)) {
      return {
        isCompliant: false,
        message: 'Evidence is required but not provided - Not Compliant',
      };
    }

    if (validationRules.length === 0) {
      // If no validation rules but evidence is required and provided
      if (requiresEvidence && addedFiles && addedFiles.length > 0) {
        return {
          isCompliant: true,
          message:
            'Evidence provided and no validation rules defined - Compliant',
        };
      }
      // If no validation rules and no evidence required
      if (!requiresEvidence) {
        return {
          isCompliant: true,
          message: 'No validation rules defined - Compliant by default',
        };
      }
    }

    const rulesWithResponses = validationRules.filter((ruleData) => {
      const userResponse = validationResponses[ruleData.rule.id];
      return userResponse && userResponse.trim() !== '';
    });

    if (rulesWithResponses.length === 0) {
      return {
        isCompliant: false,
        message: 'No responses provided for validation rules - Not Compliant',
      };
    }

    const compliantRules = rulesWithResponses.filter((ruleData) => {
      const userResponse = validationResponses[ruleData.rule.id];
      return validateResponse(
        userResponse,
        ruleData.rule,
        ruleData.flow_conditions,
      );
    });

    const isValidationCompliant =
      compliantRules.length === rulesWithResponses.length;
    const totalRules = validationRules.length;
    const respondedRules = rulesWithResponses.length;
    const passedRules = compliantRules.length;

    // Check if all requirements are met (validation rules + evidence if required)
    const allValidationsPassed =
      isValidationCompliant && respondedRules === totalRules;
    const evidenceRequirementMet =
      !requiresEvidence || (addedFiles && addedFiles.length > 0);

    const isCompliant = allValidationsPassed && evidenceRequirementMet;

    if (isCompliant) {
      let message = `All validation rules passed (${passedRules}/${totalRules})`;
      if (requiresEvidence) {
        message += ` and evidence provided`;
      }
      message += ` - Compliant`;
      return { isCompliant: true, message };
    } else if (respondedRules < totalRules) {
      return {
        isCompliant: false,
        message: `Incomplete responses (${respondedRules}/${totalRules} answered, ${passedRules} passed) - Not Compliant`,
      };
    } else if (!evidenceRequirementMet) {
      return {
        isCompliant: false,
        message: `Validation rules passed but evidence required - Not Compliant`,
      };
    } else {
      return {
        isCompliant: false,
        message: `Validation rule failed (${passedRules}/${respondedRules} passed) - Not Compliant`,
      };
    }
  };

  const handleValidationResponseChange = (ruleId: string, response: string) => {
    setValidationResponses((prev) => ({ ...prev, [ruleId]: response }));
    // No API call here - will be submitted all at once when user clicks submit
  };

  // Helper function to format expected value text based on rule type
  const formatExpectedValueText = (
    rule: IValidationRule,
  ): { label: string; value: string } => {
    if (rule.type === 'ValueRange' && rule.condition) {
      if (rule.condition.includes('-')) {
        const [min, max] = rule.condition.split('-').map((v) => v.trim());
        return {
          label: 'Expected range:',
          value: `Between ${min} and ${max}`,
        };
      } else {
        // Single value for range type
        return {
          label: 'Expected value:',
          value: `Exactly ${rule.condition}`,
        };
      }
    } else if (rule.type === 'ValueMatch') {
      return {
        label: 'Expected answer:',
        value: rule.condition,
      };
    } else {
      return {
        label: 'Expected value:',
        value: rule.condition,
      };
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Calculate compliance on submit
      const compliance = calculateOverallCompliance();

      // Prepare validation responses for submission
      const validationResponsesData = validationRules.map((ruleData) => ({
        validation_rule_id: ruleData.rule.id,
        user_response: validationResponses[ruleData.rule.id] || '',
        is_valid: validationResponses[ruleData.rule.id]
          ? validateResponse(
              validationResponses[ruleData.rule.id],
              ruleData.rule,
              ruleData.flow_conditions,
            )
          : false,
      }));

      const body = {
        remark: remarks,
        compliant: compliance.isCompliant,
        ...(addedFiles?.length > 0 && {
          evidence: addedFiles[0]?.file_path || addedFiles[0]?.id,
        }),
      };

      // Update checklist item with compliant status
      await putData(
        accessToken as string,
        `production/work-orders/${router.query.workOrderId}/process-steps/${router.query.stepId}/checklist-item/${instruction.id}`,
        body,
      );

      // Submit validation responses separately
      for (const responseData of validationResponsesData) {
        if (responseData.user_response.trim() !== '') {
          await postData(
            accessToken as string,
            `production/work-orders/${router.query.workOrderId}/process-steps/${router.query.stepId}/checklist-item/${instruction.id}/validation-response`,
            {
              validation_rule_id: responseData.validation_rule_id,
              user_response: responseData.user_response,
            },
          );
        }
      }

      // Handle file attachments if any
      if (addedFiles?.length > 0) {
        await postData(accessToken as string, 'attachments/', {
          attachment_for: 'work_order_step_checklist',
          record_id: instruction.id,
          attachments: addedFiles,
        });
      }

      closeModal();
      reFetch();
    } catch (error) {
      console.error('Error during operation:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderValidationRule = (ruleData: IValidationRuleWithResponse) => {
    const rule = ruleData.rule;
    const userResponse = validationResponses[rule.id] || '';
    const flowConditions = ruleData.flow_conditions || [];
    const isValid = userResponse
      ? validateResponse(userResponse, rule, flowConditions)
      : null;

    if (rule.type === 'ConditionalStep') {
      // For conditional steps, use flow conditions to determine options
      let options: string[] = [];

      if (flowConditions.length > 0) {
        // Use flow conditions as options
        options = flowConditions
          .filter((fc) => fc.condition)
          .map((fc) => fc.condition as string);
      } else if (rule.condition) {
        // Fallback to rule condition if no flow conditions
        options = rule.condition.split(',').map((opt) => opt.trim());
      }

      return (
        <div
          key={rule.id}
          className="mb-4 p-4 border border-gray-200 rounded-lg bg-white shadow-sm"
        >
          <div className="flex items-start justify-between mb-2">
            <Label className="text-base font-semibold text-gray-800 block">
              {rule.rule_name || 'Conditional Rule'}
            </Label>
            {isValid !== null && (
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full ${
                  isValid
                    ? 'bg-green-100 text-green-600'
                    : 'bg-red-100 text-red-600'
                }`}
              >
                <span className="text-sm font-bold">{isValid ? '✓' : '✗'}</span>
              </div>
            )}
          </div>
          <RadioGroup
            value={options.includes(userResponse) ? userResponse : 'other'}
            onValueChange={(value) => {
              if (value === 'other') {
                // Keep the current userResponse if it's already a custom value
                if (!options.includes(userResponse)) {
                  return; // Don't change anything, keep the custom value
                }
                handleValidationResponseChange(rule.id, '');
              } else {
                handleValidationResponseChange(rule.id, value);
              }
            }}
            className="mt-2 gap-0"
          >
            {options.map((option, index) => (
              <div
                key={index}
                className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded"
              >
                <RadioGroupItem value={option} id={`${rule.id}-${index}`} />
                <Label
                  htmlFor={`${rule.id}-${index}`}
                  className="text-gray-700 cursor-pointer flex-1"
                >
                  {option}
                </Label>
              </div>
            ))}
            <div className="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded">
              <RadioGroupItem value="other" id={`${rule.id}-other`} />
              <Label
                htmlFor={`${rule.id}-other`}
                className="text-gray-700 cursor-pointer flex-1"
              >
                Other
              </Label>
            </div>
          </RadioGroup>
          {!options.includes(userResponse) && (
            <Input
              placeholder="Please specify your answer..."
              className={`mt-2 ${
                isValid === false
                  ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
                  : isValid === true
                  ? 'border-green-500 focus:border-green-500 focus:ring-green-500'
                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
              }`}
              value={userResponse}
              onChange={(e) => {
                const value = e.target.value;
                handleValidationResponseChange(rule.id, value);
              }}
            />
          )}

          {/* Show error message for invalid conditional responses */}
          {isValid === false && !options.includes(userResponse) && (
            <p className="text-sm text-red-600 mt-1">
              {rule.error_message ||
                'Please select one of the predefined options above.'}
            </p>
          )}
        </div>
      );
    }

    return (
      <div
        key={rule.id}
        className="mb-4 p-4 border border-gray-200 rounded-lg bg-white shadow-sm"
      >
        <div className="flex items-start justify-between mb-2">
          <div className="flex-1">
            <Label className="text-base font-semibold text-gray-800 block mb-1">
              {rule.rule_name || 'Validation Rule'}
            </Label>
            {rule.condition && (
              <div className="text-sm text-gray-600 mb-2">
                {(() => {
                  const expectedText = formatExpectedValueText(rule);
                  return (
                    <>
                      <span className="font-medium">{expectedText.label} </span>
                      <span className="bg-gray-100 px-2 py-1 rounded text-gray-700">
                        {expectedText.value}
                      </span>
                    </>
                  );
                })()}
              </div>
            )}
          </div>
          {isValid !== null && (
            <div
              className={`flex items-center justify-center w-8 h-8 rounded-full ${
                isValid
                  ? 'bg-green-100 text-green-600'
                  : 'bg-red-100 text-red-600'
              }`}
            >
              <span className="text-sm font-bold">{isValid ? '✓' : '✗'}</span>
            </div>
          )}
        </div>
        <Input
          placeholder={`Enter ${
            rule.type === 'ValueRange' ? 'numeric value' : 'response'
          }`}
          value={userResponse}
          onChange={(e) =>
            handleValidationResponseChange(rule.id, e.target.value)
          }
          className={`${
            isValid === false
              ? 'border-red-500 focus:border-red-500 focus:ring-red-500'
              : isValid === true
              ? 'border-green-500 focus:border-green-500 focus:ring-green-500'
              : 'border-gray-300 focus:border-blue-500 focus:ring-blue-500'
          }`}
        />
        {isValid === false && (
          <p className="text-sm text-red-600 mt-1">{rule.error_message}</p>
        )}
      </div>
    );
  };

  // Initialize remarks from instruction
  useEffect(() => {
    setRemarks(instruction.remark || '');
    setAddedFiles(instruction?.attachments || []);
  }, [instruction]);

  return (
    <>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Work Instruction Validation</DialogTitle>
        </DialogHeader>

        {/* Remarks Section */}
        <div className="flex flex-col gap-2.5 ">
          <Label className="text-base font-medium leading-6 text-dark-100">
            Remarks
          </Label>
          <Input
            placeholder="Enter remarks"
            id="remarks"
            type="text"
            name="remarks"
            value={remarks}
            onChange={(e) => setRemarks(e.target.value)}
          />
        </div>

        {/* Optional File Upload Section - Only show if evidence is not required */}
        {!instruction.work_instruction?.requires_evidence && (
          <div className="mb-6">
            <Label className="text-lg font-semibold text-dark-100 mb-2 block">
              Attachments (Optional)
            </Label>
            <UploadComponent
              setOpenUploadModal={closeModal}
              refetch={reFetch}
              addedFiles={addedFiles}
              setAddedFiles={setAddedFiles}
              documentFor="production_hub"
            />
          </div>
        )}

        {/* Validation Rules Section */}
        {validationRules.length > 0 && (
          <div className="">
            <Label className="text-lg font-semibold text-dark-100 mb-4 block">
              Validation Rules
            </Label>
            {validationRules.map(renderValidationRule)}
          </div>
        )}

        {/* Evidence Upload Section - Only show if evidence is required */}
        {instruction.work_instruction?.requires_evidence && (
          <div className="mb-6">
            <Label className="text-lg font-semibold text-dark-100 mb-2 block">
              Evidence Upload
              <span className="text-red-500 ml-1">*</span>
            </Label>
            <div className="text-sm text-gray-600 mb-3">
              Evidence is required for this work instruction. Please upload
              supporting documents or images.
            </div>
            <UploadComponent
              setOpenUploadModal={closeModal}
              refetch={reFetch}
              addedFiles={addedFiles}
              setAddedFiles={setAddedFiles}
              documentFor="production_hub"
              attachFileHeading={false}
            />
            {instruction.work_instruction?.requires_evidence &&
              (!addedFiles || addedFiles.length === 0) && (
                <p className="text-sm text-red-600 mt-1">
                  Evidence is required to mark this work instruction as
                  compliant.
                </p>
              )}
          </div>
        )}

        {/* Overall Compliance Banner - Only show if validation rules exist */}
        {validationRules.length > 0 &&
          (() => {
            const compliance = calculateOverallCompliance();
            return (
              <div
                className={`p-4 rounded-lg mb-2 border-2 ${
                  compliance.isCompliant
                    ? 'bg-green-50 border-green-300 text-green-800'
                    : 'bg-red-50 border-red-300 text-red-800'
                }`}
              >
                <div className="flex items-center">
                  <span
                    className={`mr-3 text-xl font-bold ${
                      compliance.isCompliant ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {compliance.isCompliant ? '✓' : '✗'}
                  </span>
                  <div>
                    <div className="font-semibold text-lg">
                      {compliance.isCompliant ? 'COMPLIANT' : 'NOT COMPLIANT'}
                    </div>
                    <div className="text-sm mt-1">{compliance.message}</div>
                  </div>
                </div>
              </div>
            );
          })()}

        {/* Submit Button */}
        <div className="flex justify-end mt-4 pt-4 border-t">
          <PrimaryButton
            size="medium"
            text="Submit"
            isLoading={isSubmitting || isLoadingPost || isLoadingPut}
            onClick={handleSubmit}
            // disabled={validationBanner?.type !== 'success'}
          />
        </div>
      </DialogContent>
    </>
  );
};

export default UpdateInstructionModal;
