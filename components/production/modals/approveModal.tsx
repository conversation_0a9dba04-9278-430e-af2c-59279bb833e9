import { useRouter } from 'next/router';
import { Dispatch, SetStateAction, useState } from 'react';

import PrimaryButton from '@/components/common/button/primaryButton';
import { DialogContent, DialogHeader, DialogTitle } from '@/components/common/dialog';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import { WorkOrderStepActions, WorkOrderStepStatus } from '@/constants/status';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePut } from '@/hooks/usePut';

const ApproveModal = ({
  openModal,
  stepId,
  reFetch,
  setStatus,
}: {
  openModal: Dispatch<SetStateAction<boolean>>;
  stepId?: string;
  reFetch: () => void;
  setStatus: Dispatch<SetStateAction<string | undefined>>;
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const router = useRouter();
  const [approveRemark, setApproveRemark] = useState<string | undefined>(
    undefined,
  );

  const {
    putData,
    response: responsePut,
    isLoading: isLoadingPut,
    error: errorPut,
  } = usePut();

  const handleSubmit = async () => {
    stepId &&
      accessToken &&
      (await putData(
        accessToken,
        `production/work-orders/${router.query.workOrderId}/process-steps/${stepId}`,
        {
          status: WorkOrderStepStatus.Completed,
          action: WorkOrderStepActions.Approved,
          remark: approveRemark,
        },
      ).then(() => {
        openModal(false);
        setStatus(WorkOrderStepStatus.Completed);
      }));
  };

  return (
    <>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Approve work instructions</DialogTitle>
        </DialogHeader>

        <div className="w-full flex justify-center">
          <div className="w-full">
            <div className="flex flex-col gap-9">
              <div className=" bg-white rounded-sm">
                <div className="p-6.5">
                  <div className="flex flex-col gap-2.5">
                    <Label
                      htmlFor="remarks"
                      className="text-base font-medium leading-6 text-dark-100 "
                    >
                      Remarks
                    </Label>
                    <Textarea
                      rows={5}
                      name="remarks"
                      required
                      placeholder="Add here..."
                      defaultValue={approveRemark}
                      onChange={(e) => {
                        setApproveRemark(e.target.value);
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end mt-0 bg-white mx-0 pt-4">
              <PrimaryButton
                size="medium"
                text="Submit"
                isLoading={isLoadingPut}
                onClick={handleSubmit}
              />
            </div>
          </div>
        </div>
      </DialogContent>
    </>
  );
};

export default ApproveModal;
