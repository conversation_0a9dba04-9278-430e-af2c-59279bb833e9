import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/styleUtils';

interface ToggleProps {
  initialState?: boolean;
  onChange?: (isActive: boolean) => void;
  size?: 'default' | 'sm' | 'lg';
  disabled?: boolean;
  label?: string;
  labelPosition?: 'left' | 'right';
  className?: string;
}

const sizeMap = {
  sm: {
    track: 'w-10 h-5',
    thumb: 'w-3.5 h-3.5',
    translate: 'translate-x-5',
  },
  default: {
    track: 'w-12 h-6',
    thumb: 'w-5 h-5',
    translate: 'translate-x-6',
  },
  lg: {
    track: 'w-14 h-7',
    thumb: 'w-6 h-6',
    translate: 'translate-x-7',
  },
};

export const ToggleSwitch = ({
  initialState = false,
  onChange,
  size = 'default',
  disabled = false,
  label,
  labelPosition = 'right',
  className,
}: ToggleProps) => {
  const [isActive, setIsActive] = useState(initialState);

  useEffect(() => {
    setIsActive(initialState);
  }, [initialState]);

  const handleToggle = () => {
    if (disabled) return;

    const newState = !isActive;
    setIsActive(newState);

    if (onChange) {
      onChange(newState);
    }
  };

  const dimensions = sizeMap[size];

  return (
    <div
      className={cn(
        'flex items-center',
        labelPosition === 'left' ? 'flex-row-reverse' : 'flex-row',
        className,
      )}
    >
      {label && (
        <span
          className={cn(
            'text-sm font-medium select-none',
            disabled ? 'text-gray-400' : 'text-gray-700',
            labelPosition === 'left' ? 'mr-3' : 'ml-3',
          )}
        >
          {label}
        </span>
      )}

      <button
        type="button"
        role="switch"
        aria-checked={isActive}
        onClick={handleToggle}
        disabled={disabled}
        className={cn(
          'relative inline-flex shrink-0 items-center rounded-full transition-colors duration-300 ease-in-out focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:ring-blue-500',
          dimensions.track,
          disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
          isActive ? 'bg-toggle-active' : 'bg-toggle-inactive',
        )}
      >
        <span className="sr-only">Toggle switch</span>
        <motion.span
          className={cn(
            'pointer-events-none absolute left-[2px] top-[2px] rounded-full bg-toggle-thumb shadow-sm ring-0',
            dimensions.thumb,
          )}
          initial={false}
          animate={{
            x: isActive ? (size === 'sm' ? 20 : size === 'lg' ? 28 : 24) : 0,
            transition: { type: 'spring', stiffness: 500, damping: 30 },
          }}
        />
      </button>
    </div>
  );
};

export default ToggleSwitch;
