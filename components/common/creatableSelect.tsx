import React, { useCallback, useEffect, useRef, useState } from 'react';
import { StylesConfig } from 'react-select';
import CreatableSelect from 'react-select/creatable';

import { useAuthStore } from '@/globalProvider/authStore';
import useSearch from '@/hooks/useSearch';

export type IOption = {
  value: string;
  label: string;
  // Optional extra metadata from API (e.g., units for materials)
  meta?: Record<string, unknown>;
};

interface CreatableSelectProps {
  selectedOption: IOption | undefined;
  placeholder?: string;
  onChange?: (selected: IOption) => void;
  isMulti?: boolean;
  endpoint: string;
  hasError?: boolean;
  isDisabled?: boolean;
  isCreatable?: boolean; // Add prop to control whether new options can be created
}

const customStyles = (hasError: boolean): StylesConfig<IOption, true> => ({
  control: (provided) => ({
    ...provided,
    backgroundColor: '#ffffff',
    borderColor: hasError ? '#F55D5D' : '#E1E1E1',
    borderRadius: '0.5rem',
    padding: '0.22rem',
    boxShadow: 'none',
    '&:hover': {
      borderColor: hasError ? '#F55D5D' : '#B9B9B9',
    },
  }),
  valueContainer: (provided) => ({
    ...provided,
    gap: '0.5rem',
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? '#F4F4F4'
      : state.isFocused
      ? '#F9F9F9'
      : '#ffffff',
    color: '#282828',
    padding: '0.625rem 0.5rem',
    fontSize: '1rem',
    fontWeight: 500,
    borderRadius: '0.5rem',
  }),
  multiValue: (provided) => ({
    ...provided,
    backgroundColor: '#F4F4F4',
    borderRadius: '0.5rem',
    padding: '0.25rem 0.5rem',
    gap: '2px',
    display: 'flex',
    alignItems: 'center',
    margin: '0',
    // marginRight: '0.5rem',
    // marginBottom: '0.5rem',
  }),
  multiValueLabel: (provided) => ({
    ...provided,
    color: '#282828',
    padding: '0',
    fontSize: '0.875rem',
    fontWeight: 500,
    lineHeight: '24px',
  }),
  multiValueRemove: (provided) => ({
    ...provided,
    color: '#282828',
    height: '20px',
    width: '20px',
    padding: 0,
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    '&:hover': {
      backgroundColor: '#F0F0F0',
      color: '#282828',
    },
  }),
  indicatorsContainer: (provided) => ({
    ...provided,
    display: 'none',
  }),
  input: (provided) => ({
    ...provided,
    fontFamily: 'Inter, sans-serif',
    fontWeight: 500,
    lineHeight: '1.5rem',
    fontSize: '1rem',
    color: '#282828',
    '&::placeholder': {
      color: '#B9B9B9',
    },
  }),
  menu: (provided) => ({
    ...provided,
    borderRadius: '0.5rem',
    boxShadow: '0px 0px 1px 0px #3031330D, 0px 4px 8px 0px #3031331A',
    padding: '0.25rem',
    backgroundColor: '#ffffff',
    border: '1px solid #E1E1E1',
  }),

  menuList: (provided) => ({
    ...provided,
    margin: 0,
    padding: 0,
    display: 'flex',
    flexDirection: 'column',
    gap: '0.25rem',
    width: '100%',
    maxHeight: '20vh',
    background: 'white',
  }),
  placeholder: (provided) => ({
    ...provided,
    color: '#B9B9B9',
    fontFamily: 'Inter, sans-serif',
    fontWeight: 500,
    lineHeight: '1.5rem',
    fontSize: '1rem',
  }),
  singleValue: (provided) => ({
    ...provided,
    fontFamily: 'Inter, sans-serif',
    fontWeight: 500,
    lineHeight: '1.5rem',
    fontSize: '1rem',
  }),
});
export const CreatableSingleSelect: React.FC<CreatableSelectProps> = ({
  selectedOption,
  placeholder = 'Select documents',
  onChange,
  isMulti = false,
  endpoint,
  hasError = false,
  isDisabled = false,
  isCreatable = true, // Default to allowing creation of new options
}) => {
  const accessToken = useAuthStore((state) => state.accessToken);
  const [inputValue, setInputValue] = useState<string | undefined>(
    selectedOption?.label,
  );
  const { search, isLoading } = useSearch<{
    records: { id: string; name: string; full_name?: string }[];
    Query: string;
  }>(endpoint);
  const [options, setOptions] = useState<IOption[]>([]);

  const debounceTimer = useRef<NodeJS.Timeout | null>(null);

  const handleChange = (selectedOptions: any, _actionMeta: any) => {
    if (onChange && selectedOptions) {
      onChange(selectedOptions as IOption);
      setInputValue(selectedOptions.label);
    }
  };

  useEffect(() => {
    setInputValue(selectedOption?.label);
  }, [selectedOption]);

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);

    // Debounce API call
    if (debounceTimer.current) {
      clearTimeout(debounceTimer.current);
    }

    debounceTimer.current = setTimeout(() => {
      if (newValue.trim() === '') {
        setOptions([]);
        return;
      }
      fetchOptions(newValue);
    }, 300);
  };

  const fetchOptions = useCallback(
    async (query: string) => {
      if (accessToken) {
        const result = await search(accessToken, query);
        const searchData = result?.records?.map((e: any) => ({
          label: e.name || e.full_name,
          value: e.id,
          meta: e, // carry entire record for downstream needs (e.g., units)
        })) as IOption[];
        setOptions(searchData);
      }
    },
    [accessToken, search],
  );

  useEffect(() => {
    fetchOptions('');
  }, []);

  // Function to determine if a new option can be created
  const isValidNewOption = (inputValue: string) => {
    if (!isCreatable) return false; // If creation is disabled, always return false
    return inputValue.trim().length > 0; // Otherwise, use default behavior
  };

  return (
    <CreatableSelect
      value={selectedOption}
      isLoading={isLoading}
      isClearable
      options={options}
      placeholder={placeholder}
      onChange={handleChange}
      styles={customStyles(hasError)}
      className="w-full"
      isMulti={isMulti ? true : undefined}
      onInputChange={handleInputChange}
      isDisabled={isDisabled}
      isValidNewOption={isValidNewOption} // Add this prop to control creation of new options
    />
  );
};

export default CreatableSingleSelect;
