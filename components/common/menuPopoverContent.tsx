import React from 'react';

import { cn } from '@/utils/styleUtils';

import { PopoverContent } from './popover';

export interface IMenuPopoverData {
  id: string;
  label: string;
  icon: React.ReactNode;
  selected: boolean;
  access?: boolean;
  onClick?: () => void;
}

interface IProps {
  className?: string;
  data: IMenuPopoverData[];
}
const MenuPopoverContent = ({ className = '', data }: IProps) => {
  return (
    <PopoverContent
      className={cn(
        'p-1 shadow-shadow-2 bg-white-100 rounded-lg w-fit min-w-56 border-0 ',
        className,
      )}
      align="end"
    >
      <div className="">
        {data.map(
          ({ id, label, icon, selected, access, onClick }) =>
            access && (
              <div
                className={cn(
                  'flex items-center gap-2.5 px-3 py-2.5 rounded-lg cursor-pointer text-base font-medium leading-6 bg-white-100 hover:bg-white-150',
                  selected ? 'bg-primary-200 hover:bg-primary-300' : '',
                )}
                key={id}
                onClick={onClick}
              >
                {icon && <div>{icon}</div>}
                {label}
              </div>
            ),
        )}
      </div>
    </PopoverContent>
  );
};

export default MenuPopoverContent;
