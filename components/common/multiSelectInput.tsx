import React from 'react';
import Select, { GroupBase, MultiValue, StylesConfig } from 'react-select';
import makeAnimated from 'react-select/animated';

export type IOption = {
  value: string;
  label: string;
  __isNew__?: boolean;
};
const animatedComponents = makeAnimated();

interface ReactSelectSingleProps {
  options: IOption[];
  placeholder?: string;
  onChange?: (selected: IOption | null) => void;
  value?: IOption;
  hasError?: boolean;
  isDisabled?: boolean;
  tabIndex?: number;
  onKeyDown?: (event: React.KeyboardEvent) => void;
}

interface ReactSelectMultiProps {
  options: IOption[];
  placeholder?: string;
  onChange?: (selected: IOption[]) => void;
  value?: IOption[];
  hasError?: boolean;
  isDisabled?: boolean;
  tabIndex?: number;
  onKeyDown?: (event: React.KeyboardEvent) => void;
}

const customStyles = <IsMulti extends boolean>(
  hasError: boolean,
): StylesConfig<IOption, IsMulti, GroupBase<IOption>> => ({
  control: (provided, state) => ({
    ...provided,
    backgroundColor: '#ffffff',
    borderColor: hasError ? '#F55D5D' : state.isFocused ? '#00797d' : '#E1E1E1',
    borderRadius: '0.5rem',
    padding: '0.22rem',
    boxShadow: 'none',
    '&:hover': {
      borderColor: hasError ? '#F55D5D' : '#B9B9B9',
    },
  }),
  valueContainer: (provided) => ({
    ...provided,
    gap: '0.5rem',
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? '#F4F4F4'
      : state.isFocused
      ? '#F9F9F9'
      : '#ffffff',
    color: '#282828',
    padding: '0.625rem 0.5rem',
    fontSize: '1rem',
    fontWeight: 500,
    borderRadius: '0.5rem',
  }),
  multiValue: (provided) => ({
    ...provided,
    backgroundColor: '#F4F4F4',
    borderRadius: '0.5rem',
    padding: '0.25rem 0.5rem',
    gap: '2px',
    display: 'flex',
    alignItems: 'center',
    margin: '0',
    // marginRight: '0.5rem',
    // marginBottom: '0.5rem',
  }),
  multiValueLabel: (provided) => ({
    ...provided,
    color: '#282828',
    padding: '0',
    fontSize: '0.875rem',
    fontWeight: 500,
    lineHeight: '24px',
  }),
  multiValueRemove: (provided) => ({
    ...provided,
    color: '#282828',
    height: '20px',
    width: '20px',
    padding: 0,
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    '&:hover': {
      backgroundColor: '#F0F0F0',
      color: '#282828',
    },
  }),
  indicatorsContainer: (provided) => ({
    ...provided,
    display: 'none',
  }),
  input: (provided) => ({
    ...provided,
    fontFamily: 'Inter, sans-serif',
    fontWeight: 500,
    lineHeight: '1.5rem',
    fontSize: '1rem',
    color: '#282828',
    '&::placeholder': {
      color: '#B9B9B9',
    },
  }),
  menu: (provided) => ({
    ...provided,
    borderRadius: '0.5rem',
    boxShadow: '0px 0px 1px 0px #3031330D, 0px 4px 8px 0px #3031331A',
    padding: '0.25rem',
    backgroundColor: '#ffffff',
    border: '1px solid #E1E1E1',
  }),

  menuList: (provided) => ({
    ...provided,
    margin: 0,
    padding: 0,
    display: 'flex',
    flexDirection: 'column',
    gap: '0.25rem',
    width: '100%',
    height: '20vh',
    background: 'white',
  }),
  placeholder: (provided) => ({
    ...provided,
    color: '#B9B9B9',
    fontFamily: 'Inter, sans-serif',
    fontWeight: 500,
    lineHeight: '1.5rem',
    fontSize: '1rem',
  }),
});

export const ReactSelectSingle: React.FC<ReactSelectSingleProps> = ({
  options,
  placeholder = 'Select option',
  onChange,
  value,
  hasError = false,
  isDisabled = false,
  tabIndex = 0,
}) => {
  const handleChange = (selectedOption: IOption | null) => {
    if (onChange) {
      onChange(selectedOption);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // When Tab is pressed
    if (e.key === 'Tab') {
      const allTabbableElements = Array.from(
        document.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
        ),
      );

      const currentIndex = allTabbableElements.indexOf(e.target as Element);
      const nextElement = allTabbableElements[currentIndex + 1] as HTMLElement;

      if (nextElement) {
        e.preventDefault();
        nextElement.focus();
      }
    }
  };

  return (
    <Select
      isMulti={false}
      isDisabled={isDisabled}
      value={value}
      options={options}
      placeholder={placeholder}
      onChange={handleChange}
      styles={customStyles(hasError)}
      className="w-full"
      components={animatedComponents}
      onKeyDown={handleKeyDown}
      tabIndex={tabIndex}
      aria-required="true"
    />
  );
};

export const ReactSelectMulti: React.FC<ReactSelectMultiProps> = ({
  options,
  placeholder = 'Select options',
  onChange,
  value = [],
  hasError = false,
  isDisabled = false,
  tabIndex = 0,
}) => {
  const handleChange = (selectedOptions: MultiValue<IOption>) => {
    if (onChange) {
      onChange(Array.from(selectedOptions));
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Tab') {
      const allTabbableElements = Array.from(
        document.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
        ),
      );

      const currentIndex = allTabbableElements.indexOf(e.target as Element);
      const nextElement = allTabbableElements[currentIndex + 1] as HTMLElement;

      if (nextElement) {
        e.preventDefault();
        nextElement.focus();
      }
    }
  };

  return (
    <Select
      isMulti={true}
      isDisabled={isDisabled}
      value={value}
      options={options}
      placeholder={placeholder}
      onChange={handleChange}
      styles={customStyles(hasError)}
      className="w-full"
      components={animatedComponents}
      onKeyDown={handleKeyDown}
      tabIndex={tabIndex}
      aria-required="true"
    />
  );
};
