import LoadingAnimation from '@/assets/loading.json';
import { cn } from '@/utils/styleUtils';
import dynamic from 'next/dynamic';

const Lottie = dynamic(() => import('react-lottie'), { ssr: false });

const Loader = ({ className = '' }: { className?: string }) => {
  const defaultOptions = {
    loop: true,
    autoplay: true,
    animationData: LoadingAnimation,
    rendererSettings: {
      preserveAspectRatio: 'xMidYMid slice',
    },
  };
  return (
    <div
      className={cn(
        'flex h-screen items-center justify-center bg-white',
        className,
      )}
    >
      <Lottie options={defaultOptions} height={64} width={64} />
    </div>
  );
};

export default Loader;
