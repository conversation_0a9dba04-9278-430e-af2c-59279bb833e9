import { ChevronDown } from 'lucide-react';
import * as React from 'react';

import { cn } from '@/utils/styleUtils';
import * as AccordionPrimitive from '@radix-ui/react-accordion';

const Accordion = AccordionPrimitive.Root;

const AccordionItem = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>
>(({ className, ...props }, ref) => (
  <AccordionPrimitive.Item
    ref={ref}
    className={cn('border border-grey-100 rounded-lg mb-3', className)}
    {...props}
  />
));
AccordionItem.displayName = 'AccordionItem';

interface IAccordionTrigger
  extends React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger> {
  triggerClassname?: string;
}
const AccordionTrigger = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Trigger>,
  IAccordionTrigger
>(({ className, children, triggerClassname, ...props }, ref) => (
  <AccordionPrimitive.Header
    className={cn(
      'flex data-[state=open]:border-b data-[state=open]:border-grey-100 border-collapse rounded-lg ',
      triggerClassname,
    )}
  >
    <AccordionPrimitive.Trigger
      ref={ref}
      className={cn(
        'flex flex-1 items-center justify-between text-base font-medium transition-all text-left [&[data-state=open]>svg]:rotate-180 py-2 px-3 text-dark-300 leading-6 bg-white-100 rounded-lg [&[data-state=closed]]:hover:shadow-shadow-1',
        className,
      )}
      {...props}
    >
      {children}
      <ChevronDown className="h-4 w-4 shrink-0 text-muted-foreground transition-transform duration-200" />
    </AccordionPrimitive.Trigger>
  </AccordionPrimitive.Header>
));
AccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName;

const AccordionContent = React.forwardRef<
  React.ElementRef<typeof AccordionPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>
>(({ className, children, ...props }, ref) => (
  <AccordionPrimitive.Content
    ref={ref}
    className="overflow-hidden text-sm data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down"
    {...props}
  >
    <div className={cn('pb-4 pt-0', className)}>{children}</div>
  </AccordionPrimitive.Content>
));
AccordionContent.displayName = AccordionPrimitive.Content.displayName;

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent };
