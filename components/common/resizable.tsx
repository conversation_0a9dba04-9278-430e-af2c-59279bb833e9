import { GripVertical } from 'lucide-react';
import * as ResizablePrimitive from 'react-resizable-panels';

import { cn } from '@/utils/styleUtils';

const ResizablePanelGroup = ({
  className,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.PanelGroup>) => (
  <ResizablePrimitive.PanelGroup
    className={cn(
      'flex h-full w-full data-[panel-group-direction=vertical]:flex-col',
      className,
    )}
    {...props}
  />
);

const ResizablePanel = ({
  className,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.Panel>) => (
  <ResizablePrimitive.Panel
    className={cn('relative h-full w-full overflow-hidden', className)}
    {...props}
  />
);

const ResizableHandle = ({
  withHandle,
  className,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle> & {
  withHandle?: boolean;
}) => (
  <ResizablePrimitive.PanelResizeHandle
    className={cn(
      'relative flex items-center justify-center bg-border transition-colors hover:bg-accent',
      'data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:absolute data-[panel-group-direction=vertical]:after:inset-y-0 data-[panel-group-direction=vertical]:after:left-1/2 data-[panel-group-direction=vertical]:after:w-1 data-[panel-group-direction=vertical]:after:-translate-x-1/2',
      'data-[panel-group-direction=horizontal]:w-px data-[panel-group-direction=horizontal]:h-full data-[panel-group-direction=horizontal]:after:absolute data-[panel-group-direction=horizontal]:after:inset-x-0 data-[panel-group-direction=horizontal]:after:top-1/2 data-[panel-group-direction=horizontal]:after:h-1 data-[panel-group-direction=horizontal]:after:-translate-y-1/2',
      className,
    )}
    {...props}
  ></ResizablePrimitive.PanelResizeHandle>
);

export { ResizablePanelGroup, ResizablePanel, ResizableHandle };
