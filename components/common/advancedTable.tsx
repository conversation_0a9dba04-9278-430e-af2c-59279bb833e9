import React, { useState, useRef, useEffect, ReactElement } from 'react';
import {
  ArrowDown,
  ArrowUp,
  ChevronLeft,
  ChevronRight,
  X,
  ListFilter,
  Tally1,
  ChevronsRight,
  ChevronsLeft,
} from 'lucide-react';
import PrimaryButton from '../common/button/primaryButton';
import { Input } from '../common/input';
import { createPortal } from 'react-dom';
import RowsPerPageDropdown from './rowsPerPage';

type Column<T> = {
  key: keyof T | string;
  label: string;
  sortable?: boolean;
  resizable?: boolean;
  showFilter?: boolean;
  width?: number;
  minWidth?: number;
  maxWidth?: number;
  render?: (value: any, row: T) => React.ReactNode;
};

type AdvancedTableProps<T> = {
  data: T[];
  columns: Column<T>[];
  defaultPageSize?: number;
  sortable?: boolean;
  resizable?: boolean;
  pagination?: boolean;
  searchRightSideElement?: ReactElement;
  loading?: boolean;
  handleRowClicked?: (row: T, index: number) => void; // ✅ Added
};

// ✅ Helper: Get nested value safely
const getValue = (obj: any, key: string): any => {
  if (!obj || !key) return undefined;
  return key.split('.').reduce((acc, part) => {
    if (acc && typeof acc === 'object' && part in acc) {
      return acc[part];
    }
    return undefined;
  }, obj);
};

// ✅ Helper: Flatten object for searching
const flattenRow = (row: any): string =>
  Object.entries(row)
    .map(([_, v]) =>
      typeof v === 'object' && v !== null ? flattenRow(v) : String(v || ''),
    )
    .join(' ');

export default function AdvancedTable<T extends Record<string, any>>({
  data = [],
  columns = [],
  defaultPageSize = 10,
  sortable = true,
  resizable = true,
  pagination = true,
  searchRightSideElement,
  loading = false,
  handleRowClicked,
}: AdvancedTableProps<T>) {
  const safeColumns = Array.isArray(columns) ? columns : [];
  const safeData = Array.isArray(data) ? data : [];

  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  }>({
    key: '',
    direction: 'asc',
  });
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(defaultPageSize || 10);
  const [searchTerm, setSearchTerm] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);

  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>(
    {},
  );
  const [openFilterCol, setOpenFilterCol] = useState<string | null>(null);
  const popoverRef = useRef<HTMLDivElement>(null);
  const [popoverPosition, setPopoverPosition] = useState<{
    top: number;
    left: number;
  }>({
    top: 0,
    left: 0,
  });

  // ✅ Initialize column widths
  useEffect(() => {
    if (safeColumns.length === 0 || !containerRef.current) return;
    const containerWidth = containerRef.current.offsetWidth;

    const fixedWidthTotal = safeColumns.reduce(
      (sum, col) => sum + (col.width || 0),
      0,
    );
    const flexibleColumns = safeColumns.filter((col) => !col.width);
    const flexibleWidth =
      flexibleColumns.length > 0
        ? (containerWidth - fixedWidthTotal) / flexibleColumns.length
        : 0;

    const initialWidths: Record<string, number> = {};
    safeColumns.forEach((col) => {
      const min = col.minWidth || 80;
      initialWidths[String(col.key)] = Math.max(
        col.width || flexibleWidth || 150,
        min,
      );
    });
    setColumnWidths(initialWidths);
  }, [safeColumns]);

  // ✅ Sorting
  const handleSort = (colKey: string) => {
    if (!sortable) return;
    const direction =
      sortConfig.key === colKey && sortConfig.direction === 'asc'
        ? 'desc'
        : 'asc';
    setSortConfig({ key: colKey, direction });
  };

  const sortedData = [...safeData].sort((a, b) => {
    if (!sortConfig.key) return 0;
    const valA = getValue(a, sortConfig.key) ?? '';
    const valB = getValue(b, sortConfig.key) ?? '';
    return (
      String(valA).localeCompare(String(valB), undefined, {
        numeric: true,
      }) * (sortConfig.direction === 'asc' ? 1 : -1)
    );
  });

  // ✅ Filtering
  const filteredData = sortedData.filter((row) =>
    Object.keys(activeFilters).every((filterKey) => {
      const selectedValues = activeFilters[filterKey];
      if (!selectedValues?.length) return true;

      const rowValue = getValue(row, filterKey);

      if (Array.isArray(rowValue)) {
        return rowValue.some((item) =>
          selectedValues.includes(item?.name || item),
        );
      }
      return selectedValues.includes(String(rowValue));
    }),
  );

  // ✅ Searching
  const searchedData = filteredData.filter((row) =>
    flattenRow(row).toLowerCase().includes(searchTerm.toLowerCase()),
  );

  // ✅ Pagination bounds
  useEffect(() => {
    const maxPage = Math.ceil(searchedData.length / rowsPerPage);
    if (currentPage > maxPage) setCurrentPage(maxPage || 1);
  }, [searchedData, rowsPerPage, currentPage]);

  const startIndex = (currentPage - 1) * rowsPerPage;
  const paginatedData = pagination
    ? searchedData.slice(startIndex, startIndex + rowsPerPage)
    : searchedData;

  // ✅ Resize column
  const handleResize = (
    colKey: string,
    e: React.MouseEvent,
    minWidth = 80,
    maxWidth = 500,
  ) => {
    if (!resizable) return;
    const startX = e.clientX;
    const currentWidth = columnWidths[colKey];

    const handleMouseMove = (moveEvent: MouseEvent) => {
      const deltaWidth = moveEvent.clientX - startX;
      const newWidth = Math.min(
        Math.max(currentWidth + deltaWidth, minWidth),
        maxWidth,
      );
      setColumnWidths((prev) => ({ ...prev, [colKey]: newWidth }));
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // ✅ Filters
  const toggleFilterValue = (filterKey: string, value: string) => {
    setActiveFilters((prev) => {
      const selected = prev[filterKey] || [];
      return {
        ...prev,
        [filterKey]: selected.includes(value)
          ? selected.filter((v) => v !== value)
          : [...selected, value],
      };
    });
  };

  const handleSelectAll = (colKey: string, options: string[]) => {
    const allSelected = (activeFilters[colKey] || []).length === options.length;
    setActiveFilters((prev) => ({
      ...prev,
      [colKey]: allSelected ? [] : options,
    }));
  };

  const getUniqueValues = (colKey: string): string[] => {
    if (!safeData.length) return [];
    const values = safeData.flatMap((row) => {
      const value = getValue(row, colKey);
      if (Array.isArray(value)) return value.map((v) => v?.name || String(v));
      return value ? [String(value)] : [];
    });
    return [...new Set(values)];
  };

  const handleFilterIconClick = (
    e: React.MouseEvent<SVGSVGElement, MouseEvent>,
    colKey: string,
  ) => {
    e.stopPropagation();
    const rect = e.currentTarget.getBoundingClientRect();
    setPopoverPosition({
      top: rect.bottom + window.scrollY + 4,
      left: rect.left + window.scrollX,
    });
    setOpenFilterCol(openFilterCol === colKey ? null : colKey);
  };

  const removeChip = (colKey: string, value: string) => {
    setActiveFilters((prev) => ({
      ...prev,
      [colKey]: prev[colKey].filter((v) => v !== value),
    }));
  };

  const clearAllFilters = () => setActiveFilters({});

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node)
      ) {
        setOpenFilterCol(null);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div ref={containerRef} className="w-full relative">
      {/* Search */}
      <div className="flex flex-col sm:flex-row justify-between mb-4 gap-2 mt-4">
        <div className="flex items-center justify-between relative w-full">
          <Input
            placeholder="Search"
            className="w-[40vw] bg-white flex-auto rounded-lg border border-grey-100 py-2.5 px-3 text-black outline-none transition focus:border-primary active:border-primary"
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
          />
          {searchRightSideElement}
        </div>
      </div>

      {/* Filter Chips */}
      {Object.entries(activeFilters).some(
        ([_, values]) => values.length > 0,
      ) && (
        <div className="flex flex-wrap gap-2 mb-3 items-center">
          {Object.entries(activeFilters).map(([colKey, values]) =>
            values.map((val) => (
              <div
                key={`${colKey}-${val}`}
                className="flex items-center bg-primary-100 text-primary-600 px-3 py-1 rounded-full text-sm"
              >
                <span className="mr-1">
                  {safeColumns.find((c) => String(c.key) === colKey)?.label ||
                    colKey}
                  : {val}
                </span>
                <X
                  size={14}
                  className="cursor-pointer"
                  onClick={() => removeChip(colKey, val)}
                />
              </div>
            )),
          )}
          <button
            onClick={clearAllFilters}
            className="text-sm text-red-500 underline ml-2"
          >
            Clear All
          </button>
        </div>
      )}

      {/* Table */}
      <div className="w-full overflow-x-auto">
        <table className="table-fixed w-full bg-white rounded-t-md overflow-hidden">
          <colgroup>
            {safeColumns.map((col, idx) => {
              const baseWidth = columnWidths[col.key as string];
              if (idx === safeColumns.length - 1) {
                return (
                  <col
                    key={String(col.key)}
                    style={{
                      width: 'auto',
                      minWidth: `${col.minWidth || 80}px`,
                    }}
                  />
                );
              }
              return (
                <col
                  key={String(col.key)}
                  style={{
                    width: `${baseWidth}px`,
                    minWidth: `${col.minWidth || 80}px`,
                  }}
                />
              );
            })}
          </colgroup>
          <thead>
            <tr className="bg-white-200 text-left border border-grey-100">
              {safeColumns.map((col) => (
                <th
                  key={String(col.key)}
                  className="px-3 py-3 relative whitespace-nowrap overflow-hidden text-ellipsis"
                  style={{
                    minWidth: `${col.minWidth || 80}px`,
                    maxWidth: `${col.maxWidth || 500}px`,
                  }}
                >
                  <div className="flex justify-between items-center text-base font-semibold text-grey-300 overflow-hidden">
                    <div
                      className={`flex items-center gap-1 truncate ${
                        col.sortable !== false
                          ? 'cursor-pointer hover:text-dark-300'
                          : ''
                      }`}
                      style={{ maxWidth: '70%' }}
                      onClick={() =>
                        col.sortable !== false && handleSort(String(col.key))
                      }
                    >
                      <span className="truncate">{col.label}</span>
                      {sortable &&
                        sortConfig.key === col.key &&
                        (sortConfig.direction === 'asc' ? (
                          <ArrowUp size={16} />
                        ) : (
                          <ArrowDown size={16} />
                        ))}
                    </div>
                    <div className="flex items-center gap-4 pr-5">
                      {' '}
                      {/* leaves space for resize */}
                      {col.showFilter && (
                        <ListFilter
                          size={16}
                          className="text-grey-400 cursor-pointer hover:text-dark-300"
                          onClick={(e) =>
                            handleFilterIconClick(e, String(col.key))
                          }
                        />
                      )}
                    </div>
                    {col.resizable !== false && (
                      <div
                        className="absolute top-0 right-0 h-full flex items-center justify-center cursor-col-resize px-1"
                        onMouseDown={(e) =>
                          handleResize(
                            String(col.key),
                            e,
                            col.minWidth || 80,
                            col.maxWidth || 500,
                          )
                        }
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Tally1 size={18} className="text-grey-300" />
                      </div>
                    )}
                  </div>
                </th>
              ))}
            </tr>
          </thead>

          <tbody>
            {loading ? (
              [...Array(rowsPerPage)].map((_, i) => (
                <tr
                  key={`skeleton-${i}`}
                  className="animate-pulse border border-grey-100"
                >
                  {safeColumns.map((_, idx) => (
                    <td key={idx} className="p-3">
                      <div className="h-4 bg-grey-200 rounded w-3/4"></div>
                    </td>
                  ))}
                </tr>
              ))
            ) : paginatedData.length > 0 ? (
              paginatedData.map((row, rowIndex) => (
                <tr
                  key={rowIndex}
                  className={`hover:bg-white-200 border border-grey-100 ${
                    handleRowClicked ? 'cursor-pointer' : ''
                  }`}
                  onClick={() =>
                    handleRowClicked && handleRowClicked(row, rowIndex)
                  }
                >
                  {safeColumns.map((col) => {
                    let value = getValue(row, String(col.key));
                    if (Array.isArray(value)) {
                      value = value
                        .map((item) => item?.name || String(item))
                        .join(', ');
                    }
                    if (value === undefined || value === null || value === '') {
                      value = '-';
                    }

                    return (
                      <td
                        key={String(col.key)}
                        className="p-3 text-dark-300 text-base whitespace-nowrap overflow-hidden text-ellipsis"
                      >
                        {(() => {
                          try {
                            return col.render ? col.render(value, row) : value;
                          } catch (e) {
                            console.error('Render error:', e);
                            return value || '-';
                          }
                        })()}
                      </td>
                    );
                  })}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={safeColumns.length}
                  className="text-center py-4 text-grey-400"
                >
                  No data available
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {pagination && (
        <div className="bg-white border-grey-100 border-l border-b border-r rounded-b-md px-4 py-3 flex flex-col sm:flex-row justify-end items-center gap-10">
          <div className="flex items-center gap-2">
            <label className="text-sm text-black font-normal">Page Size:</label>
            <RowsPerPageDropdown
              value={rowsPerPage}
              onChange={(val) => {
                setRowsPerPage(val);
                setCurrentPage(1);
              }}
            />
          </div>
          <div className="text-sm text-dark-300 font-medium">
            {searchedData.length > 0
              ? `${(currentPage - 1) * rowsPerPage + 1} to ${Math.min(
                  currentPage * rowsPerPage,
                  searchedData.length,
                )} of ${searchedData.length}`
              : '0 results'}
          </div>
          <div className="flex items-center gap-2">
            <PrimaryButton
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(1)}
              className="flex items-center gap-1 !px-1 !py-2"
              icon={<ChevronsLeft size={16} />}
              text=""
            />
            <PrimaryButton
              disabled={currentPage === 1}
              onClick={() => setCurrentPage((p) => p - 1)}
              className="flex items-center gap-1 !px-1 !py-2"
              icon={<ChevronLeft size={16} />}
              text=""
            />
            <span className="text-sm text-dark-300 font-medium">
              Page {currentPage} of{' '}
              {Math.ceil(searchedData.length / rowsPerPage)}
            </span>
            <PrimaryButton
              disabled={currentPage * rowsPerPage >= searchedData.length}
              onClick={() => setCurrentPage((p) => p + 1)}
              className="flex items-center gap-1 !px-1 !py-2"
              icon={<ChevronRight size={16} />}
              text=""
            />
            <PrimaryButton
              disabled={currentPage * rowsPerPage >= searchedData.length}
              onClick={() =>
                setCurrentPage(Math.ceil(searchedData.length / rowsPerPage))
              }
              className="flex items-center gap-1 !px-1 !py-2"
              icon={<ChevronsRight size={16} />}
              text=""
            />
          </div>
        </div>
      )}

      {/* Filter Popover */}
      {openFilterCol &&
        createPortal(
          <div
            ref={popoverRef}
            className="absolute z-[9999] bg-white shadow-lg border border-grey-100 rounded-md p-3"
            style={{
              top: `${popoverPosition.top}px`,
              left: `${popoverPosition.left}px`,
              minWidth: '200px',
            }}
          >
            <div className="mb-2 font-semibold text-dark-300">
              Filter{' '}
              {safeColumns.find((c) => String(c.key) === openFilterCol)
                ?.label || openFilterCol}
            </div>
            <label className="flex items-center space-x-2 mb-2">
              <input
                type="checkbox"
                checked={
                  (activeFilters[openFilterCol] || []).length ===
                  getUniqueValues(openFilterCol).length
                }
                onChange={() =>
                  handleSelectAll(openFilterCol, getUniqueValues(openFilterCol))
                }
              />
              <span>Select All</span>
            </label>
            <div className="max-h-40 overflow-auto space-y-1">
              {getUniqueValues(openFilterCol).map((value) => (
                <label key={value} className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={(activeFilters[openFilterCol] || []).includes(
                      value,
                    )}
                    onChange={() => toggleFilterValue(openFilterCol, value)}
                  />
                  <span>{value}</span>
                </label>
              ))}
            </div>
          </div>,
          document.body,
        )}
    </div>
  );
}
