import axios from 'axios';
import { useParams } from 'next/navigation';
import React, { useCallback, useEffect, useState } from 'react';
import { useDropzone } from 'react-dropzone';

import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { IAttachment } from '@/interfaces/misc';

import TertiaryButton from './button/tertiaryButton';
import { FileCard } from './fileCard';

// const acceptFileTypes = {
//   "application/pdf": [".pdf"],
//   "application/vnd.openxmlformats-officedocument.wordprocessingml.document": [
//     ".docx",
//     ".doc",
//   ],
//   "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
//     ".xlsx",
//   ],
//   "application/vnd.ms-excel": [".xls", ".csv"],
// };

interface IProps {
  isMulti?: boolean;
  setOpenUploadModal?: React.Dispatch<React.SetStateAction<boolean>>;
  documentFor: string;
  refetch?: () => void;
  addedFiles: IAttachment[];
  setAddedFiles: React.Dispatch<React.SetStateAction<IAttachment[]>>;
  attachFileHeading?: boolean;
}
const UploadComponent = ({
  isMulti = false,
  setOpenUploadModal,
  refetch,
  documentFor,
  setAddedFiles,
  addedFiles,
  attachFileHeading = true,
}: IProps) => {
  const [uploading, setUploading] = useState(false);
  const [versionNumber, setVersionNumber] = useState<string | null>('1');
  const onDrop = useCallback((acceptedFiles: File[]) => {
    handleUpload(acceptedFiles);
  }, []);
  const { accessToken } = useAuthStore();
  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: isMulti,
    // accept: acceptFileTypes,
  });
  const param = useParams();
  const { postData, response, error } = usePost();

  const handleUpload = (acceptedFiles: File[]) => {
    setUploading(true);
    const formData = new FormData();
    const baseUrl = process.env.NEXT_PUBLIC_URL;
    const productVersion = process.env.NEXT_PUBLIC_VERSION;
    const url = `${baseUrl}/${productVersion}/file/upload?document_for=${documentFor}&sub_path=v${versionNumber}`;

    const orgId =
      typeof window !== 'undefined'
        ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
        : null;

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${accessToken}`,
        ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
      },
      onUploadProgress: () => {
        setUploading(true);
      },
    };
    acceptedFiles.forEach((acceptedFile) => {
      formData.append('file', acceptedFile as unknown as Blob);
      axios.post(url, formData, config).then((response) => {
        setAddedFiles((prev) => [
          ...prev,
          {
            file_path: response.data.file_path,
            file_extension: response.data.file_ext,
          },
        ]);
        setUploading(false);
      });
    });

    setUploading(false);
  };

  const handleDelete = (filepath: string) => {
    let updatedFiles = [...addedFiles];
    updatedFiles = updatedFiles.filter((file) => file.file_path !== filepath);
    setAddedFiles(updatedFiles);
  };

  useEffect(() => {
    if (response || error) {
      setOpenUploadModal && setOpenUploadModal(false);
      refetch && refetch();
    }
  }, [response, error]);

  return (
    <div>
      {attachFileHeading ? (
        <div className="text-base leading-6 font-medium text-dark-100 mb-2.5">
          Attach files
        </div>
      ) : (
        ''
      )}
      <div>
        <div
          className=" min-h-28 bg-white-100 border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2"
          {...getRootProps()}
        >
          {!(addedFiles?.length && addedFiles?.length > 0) && (
            <div className="text-sm font-medium leading-5 text-[#49474E]">
              Upload or Drag and drop to upload your file
            </div>
          )}
          <input {...getInputProps()} />
          <div className="flex justify-center items-center flex-wrap gap-2">
            {addedFiles?.map((file, index) => (
              <FileCard
                key={index}
                filepath={file.file_path}
                handleDelete={handleDelete}
                file_extension={file.file_extension}
              />
            ))}
          </div>
          <TertiaryButton
            text={
              isMulti
                ? 'Add files'
                : addedFiles?.length && addedFiles?.length > 0
                ? 'Replace'
                : 'Upload file'
            }
            size="small"
            isLoading={uploading}
          />
        </div>
      </div>

      {/* <div className="text-base leading-6 font-medium text-dark-100 mb-2.5 mt-2.5">
          Version<span className="text-red-200">*</span>
        </div> */}
      {/* <Input
          type="number"
          placeholder="Version Number"
          value={versionNumber || ""}
          onChange={(e) => setVersionNumber(e.target.value)}
        /> */}
    </div>
  );
};

export default UploadComponent;
