import * as React from "react";

import { cn } from "@/utils/styleUtils";

interface TextAreaProps extends React.ComponentProps<"textarea"> {
  errorMsg?: string | null;
}

const Textarea = React.forwardRef<HTMLTextAreaElement, TextAreaProps>(
  ({ className, errorMsg, ...props }, ref) => {
    return (
      <>
        <textarea
          className={cn(
            "flex min-h-[80px] w-full  rounded-lg border border-grey-100 bg-white px-3 py-2 hover:border-grey-200   text-base    focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed   font-medium leading-[1.6rem] placeholder:text-grey-200 text-dark-300",
            className,
            errorMsg ? " !border-red-200" : ""
          )}
          ref={ref}
          {...props}
        />
        {errorMsg ? (
          <div className="text-xs font-semibold leading-5 text-left text-red-200">
            {errorMsg}
          </div>
        ) : (
          ""
        )}
      </>
    );
  }
);
Textarea.displayName = "Textarea";

export { Textarea };
