import React from 'react';
import { ToastContainer } from 'react-toastify';

import WarningIcon from '@/assets/outline/warning';
import Tick from '@/assets/solid/tick';

const ToastWrapper = () => {
  const handleToastIcon = ({ type }: { type: string }) => {
    switch (type) {
      case 'info':
        return <WarningIcon height="24" width="24" color="#FFA931" />;
      case 'error':
        return <WarningIcon height="24" width="24" color="#F55D5D" />;
      case 'success':
        return <Tick />;
      case 'warning':
        return <WarningIcon height="24" width="24" color="#FFA931" />;
      default:
        return null;
    }
  };

  return (
    <ToastContainer
      position="top-right"
      autoClose={10000}
      hideProgressBar={false}
      progressClassName={'toast-progress'}
      icon={handleToastIcon}
    />
  );
};

export default ToastWrapper;
