import React, { useEffect, useState } from 'react';
import Select, { SingleValue, StylesConfig } from 'react-select';
import CreatableSelect from 'react-select/creatable';

export type IOption = {
  value: string;
  label: string;
};

interface SingleSelectProps {
  selectedOption?: IOption | null;
  placeholder?: string;
  onChange?: (selected: IOption | null) => void;
  hasError?: boolean;
  isDisabled?: boolean;
  options: IOption[];
}

// Important styles to ensure dropdown visibility and proper interaction
const customStyles = (hasError: boolean): StylesConfig<IOption, false> => ({
  control: (provided) => ({
    ...provided,
    backgroundColor: '#ffffff',
    borderColor: hasError ? '#F55D5D' : '#E1E1E1',
    borderRadius: '0.5rem',
    padding: '0.22rem',
    boxShadow: 'none',
    '&:hover': {
      borderColor: hasError ? '#F55D5D' : '#B9B9B9',
    },
  }),
  valueContainer: (provided) => ({
    ...provided,
    gap: '0.5rem',
  }),
  option: (provided, state) => ({
    ...provided,
    backgroundColor: state.isSelected
      ? '#F4F4F4'
      : state.isFocused
      ? '#F9F9F9'
      : '#ffffff',
    color: '#282828',
    padding: '0.625rem 0.5rem',
    fontSize: '1rem',
    fontWeight: 500,
    borderRadius: '0.5rem',
  }),
  multiValue: (provided) => ({
    ...provided,
    backgroundColor: '#F4F4F4',
    borderRadius: '0.5rem',
    padding: '0.25rem 0.5rem',
    gap: '2px',
    display: 'flex',
    alignItems: 'center',
    margin: '0',
    // marginRight: '0.5rem',
    // marginBottom: '0.5rem',
  }),
  multiValueLabel: (provided) => ({
    ...provided,
    color: '#282828',
    padding: '0',
    fontSize: '0.875rem',
    fontWeight: 500,
    lineHeight: '24px',
  }),
  multiValueRemove: (provided) => ({
    ...provided,
    color: '#282828',
    height: '20px',
    width: '20px',
    padding: 0,
    cursor: 'pointer',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    '&:hover': {
      backgroundColor: '#F0F0F0',
      color: '#282828',
    },
  }),
  indicatorsContainer: (provided) => ({
    ...provided,
    display: 'none',
  }),
  input: (provided) => ({
    ...provided,
    fontFamily: 'Inter, sans-serif',
    fontWeight: 500,
    lineHeight: '1.5rem',
    fontSize: '1rem',
    color: '#282828',
    '&::placeholder': {
      color: '#B9B9B9',
    },
  }),
  menu: (provided) => ({
    ...provided,
    borderRadius: '0.5rem',
    boxShadow: '0px 0px 1px 0px #3031330D, 0px 4px 8px 0px #3031331A',
    padding: '0.25rem',
    backgroundColor: '#ffffff',
    border: '1px solid #E1E1E1',
    zIndex: 99999,
  }),

  menuList: (provided) => ({
    ...provided,
    margin: 0,
    padding: 0,
    display: 'flex',
    flexDirection: 'column',
    gap: '0.25rem',
    width: '100%',
    maxHeight: '28vh',
    background: 'white',
    zIndex: '9999999999',
  }),
  placeholder: (provided) => ({
    ...provided,
    color: '#B9B9B9',
    fontFamily: 'Inter, sans-serif',
    fontWeight: 500,
    lineHeight: '1.5rem',
    fontSize: '1rem',
  }),
  singleValue: (provided) => ({
    ...provided,
    fontFamily: 'Inter, sans-serif',
    fontWeight: 500,
    lineHeight: '1.5rem',
    fontSize: '1rem',
  }),
});

const SingleSelect: React.FC<SingleSelectProps> = ({
  selectedOption,
  placeholder = 'Select an option',
  onChange,
  hasError = false,
  isDisabled = false,
  options,
}) => {
  const [inputValue, setInputValue] = useState('');
  const [filteredOptions, setFilteredOptions] = useState<IOption[]>([]);

  useEffect(() => {
    setFilteredOptions(options);
  }, [options]);

  useEffect(() => {
    setInputValue(selectedOption?.label || '');
  }, [selectedOption]);

  const handleInputChange = (newValue: string) => {
    setInputValue(newValue);
    const lower = newValue.toLowerCase();
    const filtered = options.filter((opt) =>
      opt.label.toLowerCase().includes(lower),
    );
    setFilteredOptions(filtered);
  };

  const handleChange = (option: SingleValue<IOption>) => {
    onChange?.(option);
    setInputValue(option?.label || '');
  };

  return (
    <Select<IOption, false>
      value={selectedOption}
      isClearable
      isSearchable
      isDisabled={isDisabled}
      options={filteredOptions}
      placeholder={placeholder}
      styles={customStyles(hasError)}
      className="w-full"
      //   inputValue={inputValue}
      onInputChange={handleInputChange}
      onChange={handleChange}
    />
  );
};

export default SingleSelect;
