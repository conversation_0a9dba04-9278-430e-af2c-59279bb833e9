import * as React from "react";

import TickIcon from "@/assets/outline/tick";
import WarningIcon from "@/assets/outline/warning";
import { cn } from "@/utils/styleUtils";

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  correct?: boolean;
  errorMsg?: string | null;
  containerClass?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, correct, containerClass, errorMsg, ...props }, ref) => {
    return (
      <>
        <div className={cn("relative", containerClass)}>
          <input
            type={type}
            className={cn(
              "flex h-11 w-full rounded-lg border bg-white-100 border-grey-100 hover:border-grey-200   px-3 py-2 text-base  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed   font-medium leading-[1.6rem] placeholder:text-grey-200 text-dark-300",
              className,
              errorMsg ? " !border-red-200" : ""
            )}
            ref={ref}
            {...props}
          />
          {errorMsg ? (
            <WarningIcon className="h-5 w-5 absolute right-3 top-[50%] transform -translate-y-1/2" />
          ) : correct ? (
            <TickIcon className="h-5 w-5 absolute right-3 top-[50%] transform -translate-y-1/2" />
          ) : (
            <></>
          )}
        </div>
        {errorMsg ? (
          <div className="text-xs font-semibold leading-5 text-left text-red-200">
            {errorMsg}
          </div>
        ) : (
          ""
        )}
      </>
    );
  }
);
Input.displayName = "Input";

export { Input };
