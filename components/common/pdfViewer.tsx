import '@react-pdf-viewer/core/lib/styles/index.css';

import React from 'react';

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Viewer, Worker } from '@react-pdf-viewer/core';
import { toolbarPlugin, ToolbarSlot } from '@react-pdf-viewer/toolbar';

import Loader from './loader';

const PdfViewer = ({ url }: { url: string }) => {
  const toolbarPluginInstance = toolbarPlugin();
  const { Toolbar } = toolbarPluginInstance;

  return (
    <div
      className="rpv-core__viewer"
      style={{
        border: '1px solid rgb(240 240 240 / var(--tw-border-opacity, 1))',
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
      }}
    >
      <div className="border-gray-100 border-b-2 bg-white-150 p-1 flex items-center">
        <Toolbar>
          {(props: ToolbarSlot) => {
            const {
              CurrentPageInput,
              EnterFullScreen,
              GoToNextPage,
              GoToPreviousPage,
              NumberOfPages,
              ZoomIn,
              ZoomOut,
            } = props;
            return (
              <>
                <div style={{ padding: '0px 2px' }}>
                  <ZoomOut />
                </div>
                <div style={{ padding: '0px 2px' }}>
                  <ZoomIn />
                </div>
                <div style={{ padding: '0px 2px', marginLeft: 'auto' }}>
                  <GoToPreviousPage />
                </div>
                <div style={{ padding: '0px 2px', width: '4rem' }}>
                  <CurrentPageInput />
                </div>
                <div style={{ padding: '0px 2px' }}>
                  / <NumberOfPages />
                </div>
                <div style={{ padding: '0px 2px' }}>
                  <GoToNextPage />
                </div>
                <div style={{ padding: '0px 2px', marginLeft: 'auto' }}>
                  <EnterFullScreen />
                </div>
              </>
            );
          }}
        </Toolbar>
      </div>
      <div className="flex-1 overflow-hidden my-4">
        <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.4.120/build/pdf.worker.min.js">
          <Viewer
            fileUrl={url}
            renderLoader={() => <Loader className="h-[300px]" />}
            plugins={[toolbarPluginInstance]}
            renderError={renderError}
          />
        </Worker>
      </div>
    </div>
  );
};

const renderError = (error: LoadError) => {
  let message = '';
  switch (error.name) {
    case 'InvalidPDFException':
      message = 'The document is invalid or corrupted';
      break;
    case 'MissingPDFException':
      message = 'The document is missing';
      break;
    case 'UnexpectedResponseException':
      message = 'Unexpected server response';
      break;
    default:
      message = 'Cannot load the document';
      break;
  }

  return (
    <div className="flex items-center justify-center">
      <div className="bg-red-100 text-red-300 p-2 m-2 rounded-md">
        {message}
      </div>
    </div>
  );
};

export default PdfViewer;
