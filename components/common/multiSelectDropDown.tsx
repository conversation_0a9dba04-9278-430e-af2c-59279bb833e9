import React, { useState, useEffect, ReactNode, useRef } from 'react';
import { ChevronDown, ChevronUp, Check, Loader2 } from 'lucide-react';
import { cn } from '@/utils/styleUtils';

export interface DropdownItem {
  id: string;
  label: string;
}

export interface DropdownGroup {
  id: string;
  name: string;
}

export interface MultiSelectDropdownProps<
  TGroup extends DropdownGroup,
  TItem extends DropdownItem,
> {
  // Data fetching
  fetchGroups: () => Promise<TGroup[]>;
  fetchItems: (groupId: string) => Promise<TItem[]>;

  // Display
  placeholder?: string;
  groupLabel?: string;
  itemLabel?: string;

  // Selection
  onSelectionChange?: (
    selectedItems: Array<{
      groupId: string;
      groupName: string;
      itemId: string;
      itemLabel: string;
    }>,
  ) => void;

  // Styling
  className?: string;
  maxDisplayItems?: number;
}

const MultiSelectDropdown = <
  TGroup extends DropdownGroup,
  TItem extends DropdownItem,
>({
  fetchGroups,
  fetchItems,
  placeholder = 'Select items...',
  groupLabel = 'Groups',
  itemLabel = 'Items',
  onSelectionChange,
  className,
  maxDisplayItems = 5,
}: MultiSelectDropdownProps<TGroup, TItem>) => {
  const [isOpen, setIsOpen] = useState(false);
  const [groups, setGroups] = useState<TGroup[]>([]);
  const [selectedGroup, setSelectedGroup] = useState<string>('');

  // Cache for items data to avoid repeated API calls
  const [itemsCache, setItemsCache] = useState<Record<string, TItem[]>>({});

  // Persist selected items for each group
  const [selectedItemsByGroup, setSelectedItemsByGroup] = useState<
    Record<string, Set<string>>
  >({});

  const [loadingGroups, setLoadingGroups] = useState(true);
  const [loadingItems, setLoadingItems] = useState(false);
  const [error, setError] = useState<string>('');

  // Ref for click outside detection
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get current items and selected items for the active group
  const currentItems = selectedGroup ? itemsCache[selectedGroup] || [] : [];
  const currentSelectedItems = selectedGroup
    ? selectedItemsByGroup[selectedGroup] || new Set()
    : new Set();

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  // Fetch groups on component mount
  useEffect(() => {
    const loadGroups = async () => {
      try {
        setLoadingGroups(true);
        setError('');
        const data = await fetchGroups();
        setGroups(data);
      } catch (err) {
        console.error('Error fetching groups:', err);
        setError('Failed to load groups');
      } finally {
        setLoadingGroups(false);
      }
    };

    loadGroups();
  }, [fetchGroups]);

  // Fetch items when group changes (only if not cached)
  useEffect(() => {
    if (!selectedGroup || itemsCache[selectedGroup]) {
      return;
    }

    const loadItems = async () => {
      try {
        setLoadingItems(true);
        setError('');
        const data = await fetchItems(selectedGroup);

        // Cache the items data
        setItemsCache((prev) => ({
          ...prev,
          [selectedGroup]: data,
        }));
      } catch (err) {
        console.error('Error fetching items:', err);
        setError('Failed to load items');
      } finally {
        setLoadingItems(false);
      }
    };

    loadItems();
  }, [selectedGroup, itemsCache, fetchItems]);

  const handleGroupSelect = (groupId: string) => {
    setSelectedGroup(groupId);

    // Initialize selected items for this group if not exists
    if (!selectedItemsByGroup[groupId]) {
      setSelectedItemsByGroup((prev) => ({
        ...prev,
        [groupId]: new Set(),
      }));
    }
  };

  const handleItemToggle = (itemId: string) => {
    if (!selectedGroup) return;

    setSelectedItemsByGroup((prev) => {
      const currentSelected = prev[selectedGroup] || new Set();
      const newSelected = new Set(currentSelected);

      if (newSelected.has(itemId)) {
        newSelected.delete(itemId);
      } else {
        newSelected.add(itemId);
      }

      const updated = {
        ...prev,
        [selectedGroup]: newSelected,
      };

      // Notify parent of selection change
      if (onSelectionChange) {
        const allSelected = getAllSelectedItems(updated);
        onSelectionChange(allSelected);
      }

      return updated;
    });
  };

  const getTotalSelectedItems = () => {
    return Object.values(selectedItemsByGroup).reduce(
      (total, items) => total + items.size,
      0,
    );
  };

  const getSelectedItemsText = () => {
    const totalSelected = getTotalSelectedItems();
    if (totalSelected === 0) return placeholder;
    if (totalSelected === 1)
      return <p className="text-black">{totalSelected} item selected</p>;
    return <p className="text-black">{totalSelected} items selected</p>;
  };

  const getAllSelectedItems = (selectedItems = selectedItemsByGroup) => {
    const allSelected: Array<{
      groupId: string;
      groupName: string;
      itemId: string;
      itemLabel: string;
    }> = [];

    Object.entries(selectedItems).forEach(([groupId, items]) => {
      const groupName = groups.find((g) => g.id === groupId)?.name || groupId;
      const groupItems = itemsCache[groupId] || [];

      items.forEach((itemId) => {
        const item = groupItems.find((i) => i.id === itemId);
        if (item) {
          allSelected.push({
            groupId,
            groupName,
            itemId,
            itemLabel: item.label,
          });
        }
      });
    });

    return allSelected;
  };

  return (
    <div ref={dropdownRef} className={cn('relative w-full', className)}>
      {/* Main Dropdown Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={cn(
          'flex h-11 w-full items-center justify-between rounded-lg border border-gray-200 bg-transparent px-4 py-2 ring-offset-background focus:outline-none focus-visible:border-blue-400 focus:ring-1 focus:ring-blue-500 disabled:cursor-not-allowed disabled:opacity-50 text-base leading-6 text-gray-900 hover:border-grey-200',
          'transition-all duration-200',
          isOpen && 'border-blue-100',
        )}
      >
        <span className="text-grey-200 text-base leading-6 data-[placeholder]:text-grey-200">
          {getSelectedItemsText()}
        </span>
        {isOpen ? (
          <ChevronUp className="h-4 w-4 text-gray-500" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-500" />
        )}
      </button>

      {/* Error Message */}
      {error && (
        <div className="absolute top-full left-0 right-0 mt-2 p-3 bg-red-50 border border-red-200 rounded-lg text-red-700 text-sm z-50">
          {error}
        </div>
      )}

      {/* Dropdown Content */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 overflow-hidden">
          <div className="flex">
            {/* Groups Panel */}
            <div className="w-1/3 border-r border-gray-200">
              <div className="p-4 bg-gray-50 border-b border-gray-200">
                <h3 className="text-sm font-semibold text-gray-700">
                  {groupLabel}
                </h3>
              </div>
              <div className="max-h-80 overflow-y-auto">
                {loadingGroups ? (
                  <div className="p-6 text-center">
                    <Loader2 className="h-5 w-5 animate-spin mx-auto text-gray-500" />
                    <p className="text-sm text-gray-500 mt-2">
                      Loading {groupLabel.toLowerCase()}...
                    </p>
                  </div>
                ) : (
                  groups.map((group) => {
                    const groupSelectedCount =
                      selectedItemsByGroup[group.id]?.size || 0;
                    return (
                      <button
                        key={group.id}
                        onClick={() => handleGroupSelect(group.id)}
                        className={cn(
                          'w-full text-left px-4 py-4 hover:bg-white-150 transition-colors duration-150',
                          selectedGroup === group.id
                            ? 'bg-white-150 text-blue-700 font-medium'
                            : 'text-gray-700',
                        )}
                      >
                        <div className="flex justify-between items-center">
                          <span className="text-base text-dark-300 leading-6">
                            {group.name}
                          </span>
                          {groupSelectedCount > 0 && (
                            <span className="text-xs bg-primary-300 text-white rounded-full px-2 py-1">
                              {groupSelectedCount}
                            </span>
                          )}
                        </div>
                      </button>
                    );
                  })
                )}
              </div>
            </div>

            {/* Items Panel */}
            <div className="w-2/3">
              <div className="p-4 bg-gray-50 border-b border-gray-200">
                <h3 className="text-sm font-semibold text-gray-700">
                  {selectedGroup
                    ? groups.find((group) => group.id === selectedGroup)?.name
                    : itemLabel}
                </h3>
              </div>
              <div className="max-h-80 overflow-y-auto p-4">
                {loadingItems ? (
                  <div className="text-center py-8">
                    <Loader2 className="h-5 w-5 animate-spin mx-auto text-gray-500" />
                    <p className="text-sm text-gray-500 mt-2">
                      Loading {itemLabel.toLowerCase()}...
                    </p>
                  </div>
                ) : selectedGroup ? (
                  currentItems.length > 0 ? (
                    <div className="flex flex-wrap gap-3">
                      {currentItems.map((item) => (
                        <label
                          key={item.id}
                          className="flex items-center px-4 py-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors duration-150 whitespace-nowrap"
                        >
                          <div className="relative flex items-center">
                            <input
                              type="checkbox"
                              checked={currentSelectedItems.has(item.id)}
                              onChange={() => handleItemToggle(item.id)}
                              className="sr-only"
                            />
                            <div
                              className={cn(
                                'w-4 h-4 border-2 rounded flex items-center justify-center transition-all duration-200',
                                currentSelectedItems.has(item.id)
                                  ? 'bg-primary-400 border-primary-400'
                                  : 'border-gray-300 bg-white hover:border-gray-300',
                              )}
                            >
                              {currentSelectedItems.has(item.id) && (
                                <Check className="h-3 w-3 text-white" />
                              )}
                            </div>
                          </div>
                          <span className="ml-3 text-gray-700 text-sm">
                            {item.label}
                          </span>
                        </label>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center text-gray-500 text-sm py-8">
                      No {itemLabel.toLowerCase()} available
                    </div>
                  )
                ) : (
                  <div className="text-center text-gray-500 text-sm py-8">
                    Select a {groupLabel.toLowerCase().slice(0, -1)} to view{' '}
                    {itemLabel.toLowerCase()}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Selected Items Summary */}
          {getTotalSelectedItems() > 0 && (
            <div className="border-t border-gray-200 p-4 bg-gray-50">
              <div className="flex flex-wrap gap-2">
                {getAllSelectedItems()
                  .slice(0, maxDisplayItems)
                  .map((item) => (
                    <span
                      key={`${item.groupId}-${item.itemId}`}
                      className="inline-flex items-center px-3 py-1 text-sm font-medium bg-primary-400 text-white rounded-md"
                    >
                      {item.itemLabel}
                    </span>
                  ))}
                {getTotalSelectedItems() > maxDisplayItems && (
                  <span className="inline-flex items-center px-3 py-1 text-sm font-medium bg-gray-100 text-gray-600 rounded-md">
                    +{getTotalSelectedItems() - maxDisplayItems} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MultiSelectDropdown;
