import React from 'react';

import { cn } from '@/utils/styleUtils';

export enum StatusType {
  PUBLISH = 'publish',
  UPCOMING = 'upcoming',
  DRAFT = 'draft',
  NOT_UPLOADED = 'not_uploaded',
  NOT_APPLICABLE = 'not_applicable',
  NOT_UPLOADED2 = 'not uploaded',
  APPROVED = 'approved',
  PENDING_REVIEW = 'Pending review',
  OPEN = 'open',
  CLOSED = 'closed',
  WORK_IN_PROGRESS = 'work in progress',
  YET_TO_START = 'yet to start',
  ONGOING = 'ongoing',
  PENDING_APPROVAL = 'pending approval',
  PENDING = 'pending',
  ONGOING_AUDIT = 'ongoing audit',
  UPCOMING_AUDIT = 'upcoming audit',
  PAST_DUE = 'past due',
  PAST_DUES = 'past_due',
  COMPLETED = 'completed',
  REJECTED = 'rejected',
  PASS = 'pass',
  FAIL = 'fail',
  OVER_DUE = 'over due',
  IN_PROGRESS = 'in_progress',
}

const Status = ({ type = StatusType.PUBLISH }: { type: string }) => {
  let statusClasses;

  if (
    type === StatusType.CLOSED ||
    type === StatusType.UPCOMING_AUDIT ||
    type === StatusType.DRAFT ||
    type === StatusType.UPCOMING ||
    type === StatusType.YET_TO_START ||
    type === StatusType.NOT_APPLICABLE
  ) {
    statusClasses = 'bg-white-200 text-dark-100'; // Grey
  } else if (
    type === StatusType.ONGOING_AUDIT ||
    type === StatusType.WORK_IN_PROGRESS ||
    type === StatusType.ONGOING ||
    type === StatusType.PENDING_APPROVAL ||
    type === StatusType.NOT_UPLOADED2 ||
    type === StatusType.PENDING
  ) {
    statusClasses = 'bg-yellow-100 text-yellow-200'; // Yellow
  } else if (
    type === StatusType.PAST_DUE ||
    type === StatusType.PAST_DUES ||
    type === StatusType.NOT_UPLOADED ||
    type === StatusType.REJECTED ||
    type === StatusType.FAIL
  ) {
    statusClasses = 'bg-red-100 text-red-300'; // Red
  } else {
    statusClasses = 'bg-green-100 text-green-200'; // green
  }

  return (
    <div
      className={cn(
        'py-1 px-4 rounded-full w-fit font-semibold leading-5  text-sm capitalize',
        statusClasses,
      )}
    >
      {type === StatusType.NOT_UPLOADED
        ? 'Not Uploaded'
        : type === StatusType.NOT_APPLICABLE
        ? 'Not Applicable'
        : type === StatusType.PAST_DUES
        ? 'Past Due'
        : type}
    </div>
  );
};

export default Status;
