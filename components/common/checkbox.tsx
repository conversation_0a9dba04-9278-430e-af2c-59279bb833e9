import * as React from 'react';

import CheckIcon from '@/assets/outline/check';
import { cn } from '@/utils/styleUtils';
import * as CheckboxPrimitive from '@radix-ui/react-checkbox';

interface ICheckbox
  extends React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root> {
  excluded?: boolean;
}

const Checkbox = React.forwardRef<
  React.ElementRef<typeof CheckboxPrimitive.Root>,
  ICheckbox
>(({ className, excluded, ...props }, ref) => (
  <CheckboxPrimitive.Root
    ref={ref}
    className={cn(
      'peer h-5 w-5 shrink-0 rounded-sm border-2 data-[state=unchecked]:border-grey-200  data-[state=unchecked]:hover:border-grey-300 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-400 data-[state=checked]:hover:bg-primary-500 data-[state=checked]:hover:border-primary-500 data-[state=checked]:text-white data-[state=checked]:border-primary-400',
      className,
      excluded ? '' : '',
    )}
    {...props}
  >
    <CheckboxPrimitive.Indicator
      className={cn('flex items-center justify-center text-current h-4 w-4')}
    >
      <CheckIcon className="h-4 w-4" />
    </CheckboxPrimitive.Indicator>
  </CheckboxPrimitive.Root>
));
Checkbox.displayName = CheckboxPrimitive.Root.displayName;

export { Checkbox };
