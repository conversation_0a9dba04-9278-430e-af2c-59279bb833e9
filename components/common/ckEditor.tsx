import { useState, useEffect, useRef, useMemo } from 'react';
import { CKEditor, useCKEditorCloud } from '@ckeditor/ckeditor5-react';

// Define types for CKEditor Cloud
interface CloudStatus {
  status: 'loading' | 'error' | 'success';
  CKEditor?: any;
  CKEditorPremiumFeatures?: any;
}

// Make editor config more flexible to accommodate CKEditor's internal types
interface EditorConfig {
  [key: string]: any; // Allow any additional properties that might be required by CKEditor
  toolbar: {
    items: string[];
    shouldNotGroupWhenFull: boolean;
  };
  plugins: any[];
  balloonToolbar: string[];
  blockToolbar: string[];
  exportInlineStyles: {
    stylesheets: string[];
  };
  exportPdf: {
    stylesheets: string[];
    fileName: string;
    converterOptions: {
      format: string;
      margin_top: string;
      margin_bottom: string;
      margin_right: string;
      margin_left: string;
      page_orientation: string;
    };
  };
  exportWord: {
    stylesheets: string[];
    fileName: string;
    converterOptions: {
      document: {
        orientation: string;
        size: string;
        margins: {
          top: string;
          bottom: string;
          right: string;
          left: string;
        };
      };
    };
  };
  fontFamily: {
    supportAllValues: boolean;
  };
  fontSize: {
    options: (number | string)[];
    supportAllValues: boolean;
  };
  fullscreen: {
    onEnterCallback: (container: HTMLElement) => void;
  };
  heading: {
    options: {
      model: string;
      view?: string;
      title: string;
      class: string;
    }[];
  };
  image: {
    toolbar: string[];
  };
  initialData: string;
  licenseKey: string;
  link: {
    addTargetToExternalLinks: boolean;
    defaultProtocol: string;
    decorators: {
      [key: string]: {
        mode: string;
        label: string;
        attributes: {
          [key: string]: string;
        };
      };
    };
  };
  list: {
    properties: {
      styles: boolean;
      startIndex: boolean;
      reversed: boolean;
    };
  };
  placeholder: string;
  table: {
    contentToolbar: string[];
  };
  template: {
    definitions: {
      title: string;
      description: string;
      icon: string;
      data: string;
    }[];
  };
}

// Define the license key
let LICENSE_KEY = '';

if (typeof process !== 'undefined' && process.env) {
  const env = process.env.NEXT_PUBLIC_ENVIRONMENT;

  if (env === 'dev' || env === 'qa') {
    LICENSE_KEY = process.env.CK_DEV || '';
  } else {
    LICENSE_KEY = process.env.CK_PROD || '';
  }
}

export default function CkEditor({
  disabled,
  url,
  editorData,
  setEditorData,
}: {
  disabled: boolean;
  url: string;
  editorData: string;
  setEditorData: React.Dispatch<React.SetStateAction<string>>;
}) {
  const editorContainerRef = useRef<HTMLDivElement>(null);
  const editorMenuBarRef = useRef<HTMLDivElement>(null);
  const editorToolbarRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<HTMLDivElement>(null);
  const [isLayoutReady, setIsLayoutReady] = useState(false);
  const cloud = useCKEditorCloud({
    version: '45.0.0',
    premium: true,
  }) as CloudStatus;

  useEffect(() => {
    setIsLayoutReady(true);

    return () => setIsLayoutReady(false);
  }, []);

  useEffect(() => {
    const fetchHtmlContent = async () => {
      try {
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(
            `Failed to fetch HTML content. Status: ${response.status}`,
          );
        }

        const htmlContent = await response.text();
        setEditorData(htmlContent ? `${htmlContent}` : '');
        // Set the fetched HTML content to CKEditor
      } catch (error) {
        return;
      }
    };

    if (url) fetchHtmlContent();
  }, [setEditorData, url]);

  const { DecoupledEditor, editorConfig } = useMemo(() => {
    if (cloud.status !== 'success' || !isLayoutReady) {
      return {} as { DecoupledEditor: any; editorConfig: any };
    }

    const {
      Base64UploadAdapter,
      DecoupledEditor,
      Alignment,
      Autoformat,
      AutoImage,
      AutoLink,
      Autosave,
      BalloonToolbar,
      BlockQuote,
      BlockToolbar,
      Bold,
      CloudServices,
      Code,
      Essentials,
      FindAndReplace,
      FontBackgroundColor,
      FontColor,
      FontFamily,
      FontSize,
      FullPage,
      Fullscreen,
      Heading,
      Highlight,
      HorizontalLine,
      ImageBlock,
      ImageCaption,
      ImageEditing,
      ImageInline,
      ImageInsertViaUrl,
      ImageResize,
      ImageStyle,
      ImageTextAlternative,
      ImageToolbar,
      ImageUpload,
      ImageUtils,
      Indent,
      IndentBlock,
      Italic,
      Link,
      LinkImage,
      List,
      ListProperties,
      PageBreak,
      Paragraph,
      PasteFromOffice,
      PlainTableOutput,
      RemoveFormat,
      SpecialCharacters,
      SpecialCharactersArrows,
      SpecialCharactersCurrency,
      SpecialCharactersEssentials,
      SpecialCharactersLatin,
      SpecialCharactersMathematical,
      SpecialCharactersText,
      Strikethrough,
      Subscript,
      Superscript,
      Table,
      TableCaption,
      TableCellProperties,
      TableColumnResize,
      TableLayout,
      TableProperties,
      TableToolbar,
      TextTransformation,
      TodoList,
      Underline,
    } = cloud.CKEditor;

    const {
      getEmailInlineStylesTransformations,
      CaseChange,
      ExportInlineStyles,
      ExportPdf,
      ExportWord,
      MultiLevelList,
      PasteFromOfficeEnhanced,
      Template,
    } = cloud.CKEditorPremiumFeatures;

    const config: EditorConfig = {
      toolbar: {
        items: [
          'fullscreen',
          '|',
          'heading',
          '|',
          'fontSize',
          'fontFamily',
          'fontColor',
          'fontBackgroundColor',
          '|',
          'bold',
          'italic',
          'underline',
          '|',
          'link',
          'insertTable',
          'insertTableLayout',
          'highlight',
          'blockQuote',
          '|',
          'alignment',
          '|',
          'bulletedList',
          'numberedList',
          'multiLevelList',
          'todoList',
          'outdent',
          'indent',
        ],
        shouldNotGroupWhenFull: false,
      },
      plugins: [
        Base64UploadAdapter,
        Alignment,
        Autoformat,
        AutoImage,
        AutoLink,
        Autosave,
        BalloonToolbar,
        BlockQuote,
        BlockToolbar,
        Bold,
        CaseChange,
        CloudServices,
        Code,
        Essentials,
        ExportInlineStyles,
        ExportPdf,
        ExportWord,
        FindAndReplace,
        FontBackgroundColor,
        FontColor,
        FontFamily,
        FontSize,
        FullPage,
        Fullscreen,
        Heading,
        Highlight,
        HorizontalLine,
        ImageBlock,
        ImageCaption,
        ImageEditing,
        ImageInline,
        ImageInsertViaUrl,
        ImageResize,
        ImageStyle,
        ImageTextAlternative,
        ImageToolbar,
        ImageUpload,
        ImageUtils,
        Indent,
        IndentBlock,
        Italic,
        Link,
        LinkImage,
        List,
        ListProperties,
        MultiLevelList,
        PageBreak,
        Paragraph,
        PasteFromOffice,
        PasteFromOfficeEnhanced,
        PlainTableOutput,
        RemoveFormat,
        SpecialCharacters,
        SpecialCharactersArrows,
        SpecialCharactersCurrency,
        SpecialCharactersEssentials,
        SpecialCharactersLatin,
        SpecialCharactersMathematical,
        SpecialCharactersText,
        Strikethrough,
        Subscript,
        Superscript,
        Table,
        TableCaption,
        TableCellProperties,
        TableColumnResize,
        TableLayout,
        TableProperties,
        TableToolbar,
        Template,
        TextTransformation,
        TodoList,
        Underline,
      ],
      balloonToolbar: [
        'bold',
        'italic',
        '|',
        'link',
        '|',
        'bulletedList',
        'numberedList',
      ],
      blockToolbar: [
        'fontSize',
        'fontColor',
        'fontBackgroundColor',
        '|',
        'bold',
        'italic',
        '|',
        'link',
        'insertTable',
        'insertTableLayout',
        '|',
        'bulletedList',
        'numberedList',
        'outdent',
        'indent',
      ],
      exportInlineStyles: {
        stylesheets: [
          './export-style.css',
          'https://cdn.ckeditor.com/ckeditor5/45.0.0/ckeditor5.css',
          'https://cdn.ckeditor.com/ckeditor5-premium-features/45.0.0/ckeditor5-premium-features.css',
        ],
      },
      exportPdf: {
        stylesheets: [
          './export-style.css',
          'https://cdn.ckeditor.com/ckeditor5/45.0.0/ckeditor5.css',
          'https://cdn.ckeditor.com/ckeditor5-premium-features/45.0.0/ckeditor5-premium-features.css',
        ],
        fileName: 'export-pdf-demo.pdf',
        converterOptions: {
          format: 'A4',
          margin_top: '20mm',
          margin_bottom: '20mm',
          margin_right: '12mm',
          margin_left: '12mm',
          page_orientation: 'portrait',
        },
      },
      exportWord: {
        stylesheets: [
          './export-style.css',
          'https://cdn.ckeditor.com/ckeditor5/45.0.0/ckeditor5.css',
          'https://cdn.ckeditor.com/ckeditor5-premium-features/45.0.0/ckeditor5-premium-features.css',
        ],
        fileName: 'export-word-demo.docx',
        converterOptions: {
          document: {
            orientation: 'portrait',
            size: 'A4',
            margins: {
              top: '20mm',
              bottom: '20mm',
              right: '12mm',
              left: '12mm',
            },
          },
        },
      },
      fontFamily: {
        supportAllValues: true,
      },
      fontSize: {
        options: [10, 12, 14, 'default', 18, 20, 22],
        supportAllValues: true,
      },
      fullscreen: {
        onEnterCallback: (container) =>
          container.classList.add(
            'editor-container',
            'editor-container_document-editor',
            'editor-container_include-fullscreen',
            'main-container',
          ),
      },
      heading: {
        options: [
          {
            model: 'paragraph',
            title: 'Paragraph',
            class: 'ck-heading_paragraph',
          },
          {
            model: 'heading1',
            view: 'h1',
            title: 'Heading 1',
            class: 'ck-heading_heading1',
          },
          {
            model: 'heading2',
            view: 'h2',
            title: 'Heading 2',
            class: 'ck-heading_heading2',
          },
          {
            model: 'heading3',
            view: 'h3',
            title: 'Heading 3',
            class: 'ck-heading_heading3',
          },
          {
            model: 'heading4',
            view: 'h4',
            title: 'Heading 4',
            class: 'ck-heading_heading4',
          },
          {
            model: 'heading5',
            view: 'h5',
            title: 'Heading 5',
            class: 'ck-heading_heading5',
          },
          {
            model: 'heading6',
            view: 'h6',
            title: 'Heading 6',
            class: 'ck-heading_heading6',
          },
        ],
      },
      image: {
        toolbar: [
          'toggleImageCaption',
          'imageTextAlternative',
          '|',
          'imageStyle:inline',
          'imageStyle:wrapText',
          'imageStyle:breakText',
          '|',
          'resizeImage',
        ],
      },
      initialData: '',
      licenseKey: LICENSE_KEY,
      link: {
        addTargetToExternalLinks: true,
        defaultProtocol: 'https://',
        decorators: {
          toggleDownloadable: {
            mode: 'manual',
            label: 'Downloadable',
            attributes: {
              download: 'file',
            },
          },
        },
      },
      list: {
        properties: {
          styles: true,
          startIndex: true,
          reversed: true,
        },
      },
      placeholder: 'Type or paste your content here!',
      table: {
        contentToolbar: [
          'tableColumn',
          'tableRow',
          'mergeTableCells',
          'tableProperties',
          'tableCellProperties',
        ],
      },
      template: {
        definitions: [
          {
            title: 'Introduction',
            description: 'Simple introduction to an article',
            icon: '<svg width="45" height="45" viewBox="0 0 45 45" fill="none" xmlns="http://www.w3.org/2000/svg">\n    <g id="icons/article-image-right">\n        <rect id="icon-bg" width="45" height="45" rx="2" fill="#A5E7EB"/>\n        <g id="page" filter="url(#filter0_d_1_507)">\n            <path d="M9 41H36V12L28 5H9V41Z" fill="white"/>\n            <path d="M35.25 12.3403V40.25H9.75V5.75H27.7182L35.25 12.3403Z" stroke="#333333" stroke-width="1.5"/>\n        </g>\n        <g id="image">\n            <path id="Rectangle 22" d="M21.5 23C21.5 22.1716 22.1716 21.5 23 21.5H31C31.8284 21.5 32.5 22.1716 32.5 23V29C32.5 29.8284 31.8284 30.5 31 30.5H23C22.1716 30.5 21.5 29.8284 21.5 29V23Z" fill="#B6E3FC" stroke="#333333"/>\n            <path id="Vector 1" d="M24.1184 27.8255C23.9404 27.7499 23.7347 27.7838 23.5904 27.9125L21.6673 29.6268C21.5124 29.7648 21.4589 29.9842 21.5328 30.178C21.6066 30.3719 21.7925 30.5 22 30.5H32C32.2761 30.5 32.5 30.2761 32.5 30V27.7143C32.5 27.5717 32.4391 27.4359 32.3327 27.3411L30.4096 25.6268C30.2125 25.451 29.9127 25.4589 29.7251 25.6448L26.5019 28.8372L24.1184 27.8255Z" fill="#44D500" stroke="#333333" stroke-linejoin="round"/>\n            <circle id="Ellipse 1" cx="26" cy="25" r="1.5" fill="#FFD12D" stroke="#333333"/>\n        </g>\n        <rect id="Rectangle 23" x="13" y="13" width="12" height="2" rx="1" fill="#B4B4B4"/>\n        <rect id="Rectangle 24" x="13" y="17" width="19" height="2" rx="1" fill="#B4B4B4"/>\n        <rect id="Rectangle 25" x="13" y="21" width="6" height="2" rx="1" fill="#B4B4B4"/>\n        <rect id="Rectangle 26" x="13" y="25" width="6" height="2" rx="1" fill="#B4B4B4"/>\n        <rect id="Rectangle 27" x="13" y="29" width="6" height="2" rx="1" fill="#B4B4B4"/>\n        <rect id="Rectangle 28" x="13" y="33" width="16" height="2" rx="1" fill="#B4B4B4"/>\n    </g>\n    <defs>\n        <filter id="filter0_d_1_507" x="9" y="5" width="28" height="37" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">\n            <feFlood flood-opacity="0" result="BackgroundImageFix"/>\n            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>\n            <feOffset dx="1" dy="1"/>\n            <feComposite in2="hardAlpha" operator="out"/>\n            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.29 0"/>\n            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1_507"/>\n            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1_507" result="shape"/>\n        </filter>\n    </defs>\n</svg>\n',
            data: "<h2>Introduction</h2><p>In today's fast-paced world, keeping up with the latest trends and insights is essential for both personal growth and professional development. This article aims to shed light on a topic that resonates with many, providing valuable information and actionable advice. Whether you're seeking to enhance your knowledge, improve your skills, or simply stay informed, our comprehensive analysis offers a deep dive into the subject matter, designed to empower and inspire our readers.</p>",
          },
        ],
      },
    };

    return { DecoupledEditor, editorConfig: config as any };
  }, [cloud, isLayoutReady]);

  useEffect(() => {
    if (editorConfig) {
      configUpdateAlert(editorConfig);
    }
  }, [editorConfig]);

  return (
    <div className="main-container">
      <div
        className="editor-container editor-container_document-editor editor-container_include-fullscreen"
        ref={editorContainerRef}
      >
        <div
          className="editor-container__menu-bar"
          ref={editorMenuBarRef}
          style={{
            opacity: disabled ? 0.5 : 1,
            pointerEvents: disabled ? 'none' : 'auto',
            transition: 'opacity 0.3s ease',
          }}
        />
        <div
          className="editor-container__toolbar"
          ref={editorToolbarRef}
          style={{
            opacity: disabled ? 0.5 : 1,
            pointerEvents: disabled ? 'none' : 'auto',
            transition: 'opacity 0.3s ease',
          }}
        />
        <div className="editor-container__editor-wrapper">
          <div className="editor-container__editor">
            <div ref={editorRef}>
              {DecoupledEditor && editorConfig && (
                <CKEditor
                  onReady={(editor: any) => {
                    if (
                      editorToolbarRef.current &&
                      editor.ui.view.toolbar.element
                    ) {
                      editorToolbarRef.current.appendChild(
                        editor.ui.view.toolbar.element,
                      );
                    }
                    if (
                      editorMenuBarRef.current &&
                      editor.ui.view.menuBarView.element
                    ) {
                      editorMenuBarRef.current.appendChild(
                        editor.ui.view.menuBarView.element,
                      );
                    }
                  }}
                  onAfterDestroy={() => {
                    if (editorToolbarRef.current) {
                      Array.from(editorToolbarRef.current.children).forEach(
                        (child) => child.remove(),
                      );
                    }
                    if (editorMenuBarRef.current) {
                      Array.from(editorMenuBarRef.current.children).forEach(
                        (child) => child.remove(),
                      );
                    }
                  }}
                  data={editorData}
                  onChange={(event, editor) => {
                    setEditorData(editor.getData());
                  }}
                  disabled={disabled}
                  editor={DecoupledEditor}
                  config={editorConfig}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * This function exists to remind you to update the config needed for premium features.
 * The function can be safely removed. Make sure to also remove call to this function when doing so.
 */
function configUpdateAlert(config: any) {
  // Use static property for tracking if alert was shown
  if ((configUpdateAlert as any).configUpdateAlertShown) {
    return;
  }

  const isModifiedByUser = (
    currentValue: any,
    forbiddenValue: string,
  ): boolean => {
    if (currentValue === forbiddenValue) {
      return false;
    }

    if (currentValue === undefined) {
      return false;
    }

    return true;
  };

  const valuesToUpdate: string[] = [];

  // Set static property
  (configUpdateAlert as any).configUpdateAlertShown = true;

  if (!isModifiedByUser(config.licenseKey, '<YOUR_LICENSE_KEY>')) {
    valuesToUpdate.push('LICENSE_KEY');
  }

  if (valuesToUpdate.length) {
    window.alert(
      [
        'Please update the following values in your editor config',
        'to receive full access to Premium Features:',
        '',
        ...valuesToUpdate.map((value) => ` - ${value}`),
      ].join('\n'),
    );
  }
}
