import { useAuthStore } from '@/globalProvider/authStore';
import { useState, useEffect } from 'react';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import { Building2, ChevronDown } from 'lucide-react';
import { GET_ORGANIZATION, UPDATE_CURRENT_USER } from '@/utils/api';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import { IOrganization } from '@/interfaces/org';
import axios, { AxiosRequestConfig } from 'axios';
import useFetch from '@/hooks/useFetch';
import CheckIcon from '@/assets/outline/check';

export default function OrgSwitcher() {
  const { accessToken, user, organizations, setOrganizations } = useAuthStore();

  const [currentOrg, setCurrentOrg] = useState<IOrganization | null>(null);

  useEffect(() => {
    const fetchOrgs = async () => {
      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      if (organizations.length === 0 && accessToken) {
        try {
          const headers = {
            Authorization: `Bearer ${accessToken}`,
            ...(orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
          };

          const response = await axios.get<{ records: IOrganization[] }>(
            `${baseUrl}/${productVersion}/orgs`,
            { headers },
          );
          setOrganizations(response.data.records);
        } catch (err) {
          console.error('Failed to fetch organizations:', err);
        }
      }
    };
    fetchOrgs();
  }, [accessToken, organizations, setOrganizations]);

  useEffect(() => {
    const userCurrentCompanyId = sessionStorage.getItem('oid');
    const orgDbId = userCurrentCompanyId || user?.company?.id;

    if (organizations.length > 0 && orgDbId) {
      const selectedOrg = organizations.find(
        (org) => String(org.id) === String(orgDbId),
      );
      setCurrentOrg(selectedOrg ?? null);
    }
  }, [organizations]);

  const handleSelect = (org: IOrganization) => {
    window.open(`${window.location.origin}/standard?_t=${org.id}`, '_blank');
  };

  return (
    <Menu as="div" className="relative inline-block text-left">
      <div>
        <MenuButton className="inline-flex w-full items-center gap-4 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
          <Building2 className="h-5 w-5 text-gray-500 mt-0.5" />
          <div className="flex flex-col gap-0.5 items-start text-left">
            <span className="text-xs font-normal text-gray-500">
              Account Name
            </span>
            <span className="text-sm text-gray-900 font-medium">
              {currentOrg?.name || '---'}
            </span>
          </div>
          <ChevronDown className="ml-auto h-5 w-5 text-gray-400" />
        </MenuButton>
      </div>

      {organizations.length > 0 && (
        <MenuItems className="w-fit min-w-56 max-h-52 overflow-auto absolute right-0 z-10 mt-2 origin-top-right rounded-md bg-white-100 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="p-1 py-1 rounded-lg shadow-shadow-2">
            {organizations.map((org) => {
              const isActive =
                String(org.id) ===
                String(sessionStorage.getItem(ORGANIZATION_SESSION_KEY));
              return (
                <MenuItem key={org.id}>
                  {({ active }) => (
                    <button
                      onClick={() => !isActive && handleSelect(org)}
                      disabled={isActive}
                      className={`text-base flex justify-between font-medium leading-6 rounded-lg w-full text-left items-center px-4 py-2 ${
                        isActive
                          ? 'text-gray-400 cursor-not-allowed bg-gray-50'
                          : active
                          ? 'bg-gray-100 text-gray-900'
                          : 'text-gray-700'
                      }`}
                    >
                      {org.name}
                      {isActive && (
                        <span className="ml-2 text-xs text-green-500 font-semibold">
                          <CheckIcon className="h-5 w-5" color="#22c55a" />
                        </span>
                      )}
                    </button>
                  )}
                </MenuItem>
              );
            })}
          </div>
        </MenuItems>
      )}
    </Menu>
  );
}
