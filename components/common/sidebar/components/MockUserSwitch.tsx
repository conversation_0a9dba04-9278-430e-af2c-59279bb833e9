import { useAuthStore } from "@/globalProvider/authStore";
import useFetch from '@/hooks/useFetch';
import { useState, useEffect } from "react";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { ChevronDown } from "lucide-react";
import { IUser } from '@/interfaces/user';

export default function MockUserSwitcher() {
	const { accessToken } = useAuthStore();

	const [mounted, setMounted] = useState(false);
	const [mockUserValue, setMockUserValue] = useState<string | null>(null);

	const { data: users } = useFetch<{
		records: IUser[];
	}>(accessToken, `users`, {});

	useEffect(() => {
		const mockUser = localStorage.getItem("x-mock-user");
		setMockUserValue(mockUser);
		setMounted(true);
	}, []);

	const handleSelect = (value: "reset" | string) => {
		if (value === "reset") {
			localStorage.removeItem("x-mock-user");
			setMockUserValue(null);
		} else {
			localStorage.setItem("x-mock-user", value);
			setMockUserValue(value);
		}
		window.location.reload();
	};

	const getLabel = () => {
		if (mockUserValue) {
			const selectedUser = users?.records?.find(
				(user) => user.email === mockUserValue
			);
			return selectedUser ? selectedUser.full_name : "---";
		}
		return "---";
	};

	if (!mounted) return null;

	return (
		<Menu as="div" className="relative inline-block text-left">
			<div>
				<MenuButton className="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">
					{getLabel()}
					<ChevronDown
						aria-hidden="true"
						className="-mr-1 h-5 w-5 text-gray-400"
					/>
				</MenuButton>
			</div>

			{users && users.records && (
				<MenuItems
					transition
					className="h-60 overflow-scroll absolute right-0 z-10 mt-2 w-56 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in">
					<div className="py-1">
						<MenuItem>
							{({ active }) => (
								<a
									href="#"
									onClick={() => handleSelect("reset")}
									className={`block px-4 py-2 text-sm text-gray-700 ${
										active ? "bg-gray-100 text-gray-900" : ""
									}`}>
									---
								</a>
							)}
						</MenuItem>

						{users.records.map((user) => (
							<MenuItem key={user.email}>
								{({ active }) => (
									<a
										href="#"
										onClick={() => handleSelect(user.email)}
										className={`block px-4 py-2 text-sm text-gray-700 ${
											active ? "bg-gray-100 text-gray-900" : ""
										}`}>
										{user.full_name}
										<br />
										<span>[{user.roles?.join(",")}]</span>
									</a>
								)}
							</MenuItem>
						))}
					</div>
				</MenuItems>
			)}
		</Menu>
	);
}
