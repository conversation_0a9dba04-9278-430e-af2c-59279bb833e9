import React, { useEffect, useState } from 'react';
import { useAuthStore } from '@/globalProvider/authStore';
import { useRouter } from 'next/router';
import Header from './header';
import Sidebar from './sidebar';
import { hasOnlyPeopleViewRole } from '@/utils/roleAccessConfig';
import {
  ORGANIZATION_SESSION_KEY,
  ORGANIZATION_HEADER_KEY,
} from '@/constants/common';
import axios from 'axios';
const Layout = ({ children }: { children: React.ReactNode }) => {
  const { accessToken, user, isLoading } = useAuthStore((state) => state);
  const [isAuthChecked, setIsAuthChecked] = useState(false);
  const [currentEmployeeData, setCurrentEmployeeData] = useState<{
    record: { id: string };
  } | null>(null);
  const [currentEmployeeLoading, setCurrentEmployeeLoading] = useState(false);
  const router = useRouter();
  // Fetch employee data only for PEOPLEVIEW-only users
  useEffect(() => {
    const fetchEmployeeData = async () => {
      if (accessToken && user && hasOnlyPeopleViewRole(user)) {
        setCurrentEmployeeLoading(true);
        try {
          const orgId =
            typeof window !== 'undefined'
              ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
              : null;
          const response = await axios.get(
            `${process.env.NEXT_PUBLIC_URL}/${process.env.NEXT_PUBLIC_VERSION}/employees/me/`,
            {
              headers: {
                Authorization: `Bearer ${accessToken}`,
                ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
              },
            },
          );
          setCurrentEmployeeData(response.data);
        } catch (error) {
          console.error('Failed to fetch employee data:', error);
        } finally {
          setCurrentEmployeeLoading(false);
        }
      }
    };
    // Only fetch if user has PEOPLEVIEW role
    if (user && hasOnlyPeopleViewRole(user) && !currentEmployeeData) {
      fetchEmployeeData();
    }
  }, [accessToken, user, currentEmployeeData]);
  useEffect(() => {
    if (accessToken && user?.id) {
      // Check if user has only PEOPLEVIEW role
      const isPeopleViewOnly = hasOnlyPeopleViewRole(user);
      if (isPeopleViewOnly) {
        // If employee data is still loading, wait for it
        if (currentEmployeeLoading) {
          return;
        }

        const allowedPeopleViewRoutes = [
          '/people/directory/',
          '/people/training/',
        ];

        const isOnAllowedRoute = allowedPeopleViewRoutes.some((route) =>
          router.pathname.startsWith(route),
        );

        // Only redirect if we have a valid employee ID
        if (currentEmployeeData?.record?.id) {
          // Only redirect if not already on an allowed route
          if (!isOnAllowedRoute) {
            router.push(`/people/directory/${currentEmployeeData.record.id}`);
            return;
          }
        } else if (!currentEmployeeLoading) {
          console.error('PEOPLEVIEW user has no employee ID');
          return;
        }
      }
      // If the user is authenticated, check for stored redirect URL
      const redirectTo = localStorage.getItem('redirectTo');
      if (redirectTo) {
        // Redirect to the stored URL if available
        localStorage.removeItem('redirectTo');
        router.push(redirectTo);
      } else {
        setIsAuthChecked(true);
      }
    } else if (!isLoading) {
      // If not authenticated, store the page URL and redirect to login
      if (router.pathname !== '/login') {
        localStorage.setItem('redirectTo', router.asPath);
      }
      router.push('/login');
    }
  }, [
    accessToken,
    user,
    isLoading,
    router,
    currentEmployeeData,
    currentEmployeeLoading,
  ]);
  if (isLoading || !isAuthChecked) {
    return;
  }
  const isPeopleViewOnly = hasOnlyPeopleViewRole(user);
  return (
    <div className="relative flex w-screen h-screen overflow-hidden">
      {!isPeopleViewOnly && <Sidebar />}
      <div
        className={`flex-1 flex flex-col overflow-x-hidden overflow-y-auto px-5 py-6 ${
          isPeopleViewOnly ? 'w-full' : ''
        }`}
      >
        <Header />
        {children}
      </div>
    </div>
  );
};
export default Layout;
