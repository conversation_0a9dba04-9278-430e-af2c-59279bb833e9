import React from 'react';

import { useAuthStore } from '@/globalProvider/authStore';

import MockUserSwitcher from './components/MockUserSwitch';
import OrgSwitcher from './components/OrgSwitch';
import OrganizationIcon from '@/assets/outline/organization';
import { useRouter } from 'next/router';

import { hasOnlyPeopleViewRole } from '@/utils/roleAccessConfig';
import { ORGANIZATION_SESSION_KEY } from '@/constants/common';
import SecondaryButton from '@/components/common/button/secondaryButton';
import { LogOut } from 'lucide-react';
const Header = () => {
  const { user, setIsLoading } = useAuthStore((state) => state);
  const router = useRouter();
  const handleLogout = async () => {
    setIsLoading(true);
    window.location.href = '/api/auth/logout';
    sessionStorage.removeItem(ORGANIZATION_SESSION_KEY);
    router.push('/login');
  };
  return (
    <div className="flex justify-between items-center pb-4 border-b border-grey-100">
      <div className="flex items-center gap-2.5 h-9">
        <OrganizationIcon />
        <span className="font-medium text-2xl leading-9 text-dark-300">
          {user?.company.name}
        </span>
      </div>
      <div className="flex gap-8 items-center">
        {(process.env.NEXT_PUBLIC_ENVIRONMENT === 'dev' ||
          process.env.NEXT_PUBLIC_ENVIRONMENT === 'local') && (
          <MockUserSwitcher />
        )}
        {!hasOnlyPeopleViewRole(user) && <OrgSwitcher />}
        {user && hasOnlyPeopleViewRole(user) && (
          <SecondaryButton
            size="medium"
            text="Logout"
            icon={<LogOut size={16} />}
            iconPosition="left"
            onClick={handleLogout}
          />
        )}
      </div>
    </div>
  );
};
export default Header;
