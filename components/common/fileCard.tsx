import { DownloadIcon, X } from 'lucide-react';
import { useState } from 'react';

import DocumentIcon from '@/assets/outline/document';
import { useAuthStore } from '@/globalProvider/authStore';
import { handleDownloadDocument } from '@/utils/download';
import { fileNameEllipsis } from '@/utils/truncateText';

import { Dialog, DialogTrigger } from './dialog';
import DocumentViewModal from './modals/documentViewModal';

export const FileCard = ({
  filepath,
  file_extension,
  handleDelete,
}: {
  filepath: string;
  file_extension: string;
  handleDelete?: (filepath: string) => void;
}) => {
  const { accessToken } = useAuthStore();
  const filename: string = filepath?.split('/')?.pop() || '';
  const [hover, setHover] = useState(false);

  return (
    <div
      className="p-1.5 rounded-md bg-white-100 border border-white-300 flex items-center gap-2 relative group"
      onClick={(e) => e.stopPropagation()}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      {handleDelete && (
        <div
          className="h-4 w-4 rounded-full bg-dark-300 items-center justify-center absolute -top-1.5 -right-1.5 hidden group-hover:flex cursor-pointer"
          onClick={() => handleDelete && handleDelete(filepath)}
        >
          <X className="h-3 w-3" color="#fff" />
        </div>
      )}
      <div className="h-8 w-8 flex items-center justify-center rounded bg-[#5A91FF]">
        {hover ? (
          <div
            onClick={() =>
              handleDownloadDocument(
                accessToken as string,
                filepath,
                filename,
                1,
              )
            }
            className="cursor-pointer"
          >
            <DownloadIcon color="white" height="20" width="20" />
          </div>
        ) : (
          <DocumentIcon height="20" width="20" color="#fff" />
        )}
      </div>
      <div>
        {['pdf', 'docx', 'doc', 'html', 'jpg', 'png', 'jpeg'].includes(
          file_extension,
        ) ? (
          <Dialog>
            <DialogTrigger className="">
              <div>{fileNameEllipsis(filename, 20)}</div>
            </DialogTrigger>
            <DocumentViewModal
              title={''}
              filePath={filepath}
              extension={
                file_extension as
                  | 'html'
                  | 'pdf'
                  | 'docx'
                  | 'doc'
                  | 'png'
                  | 'jpeg'
                  | 'jpg'
              }
              dialogClass="min-w-[95%]"
            />
          </Dialog>
        ) : (
          fileNameEllipsis(filename, 20)
        )}
      </div>
    </div>
  );
};
