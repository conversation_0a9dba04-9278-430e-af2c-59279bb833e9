import React from 'react';

import InfoCircle from '@/assets/outline/infoCircle';

import PrimaryButton from '../button/primaryButton';
import { DialogContent, DialogHeader, DialogTitle } from '../dialog';

interface IProps {
  title: string;
  infoText: string;
  btnText: string;
  onClick: () => void;
  children?: React.ReactNode;
  btnLoading?: boolean;
  dialogClass?: string;
  btnDisabled?: boolean;
}

const ConfirmModal = ({
  title,
  infoText,
  btnText,
  onClick,
  children,
  btnLoading = false,
  dialogClass = '',
  btnDisabled = false,
}: IProps) => {
  return (
    <DialogContent className={dialogClass}>
      <DialogHeader>
        <DialogTitle>{title}</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        {infoText && (
          <div className="flex items-center gap-2 text-sm font-medium leading-5 text-dark-300 p-3 rounded-lg bg-white-150 mb-2">
            <div className="h-9 w-9 bg-[#91909A29] flex items-center justify-center rounded-full">
              <InfoCircle />
            </div>
            {infoText}
          </div>
        )}

        {children}
        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text={btnText}
            onClick={onClick}
            isLoading={btnLoading}
            disabled={btnDisabled}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default ConfirmModal;
