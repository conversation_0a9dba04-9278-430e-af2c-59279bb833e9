import { X } from 'lucide-react';

import DocumentIcon from '@/assets/outline/document';

const FileCard = ({
  file,
  setAddedFile,
  prefillFileName = undefined,
  onRemove,
}: {
  file?: File;
  setAddedFile?: React.Dispatch<React.SetStateAction<File[] | null>>;
  prefillFileName?: string;
  onRemove?: (file?: File) => void;
}) => {
  return (
    <div
      className="p-1.5 rounded-md bg-white-100 border border-white-300 flex items-center gap-2 relative group"
      onClick={(e) => e.stopPropagation()}
    >
      {!prefillFileName ? (
        <div
          className="h-4 w-4 rounded-full bg-dark-300 items-center justify-center absolute -top-1.5 -right-1.5 hidden group-hover:flex cursor-pointer"
          onClick={() => {
            setAddedFile &&
              setAddedFile((pre) => {
                return pre ? pre?.filter((e) => e.name !== file?.name) : null;
              });
            onRemove?.(file);
          }}
        >
          <X className="h-3 w-3" color="#fff" />
        </div>
      ) : (
        ''
      )}

      <div className="h-8 w-8 flex items-center justify-center rounded bg-[#5A91FF]">
        <DocumentIcon height="20" width="20" color="#fff" />
      </div>
      <div>{file?.name || prefillFileName}</div>
    </div>
  );
};

export default FileCard;
