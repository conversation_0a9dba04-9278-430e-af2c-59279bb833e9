import React, { useState, useRef } from 'react';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { RotateCcw, ZoomInIcon, ZoomOutIcon } from 'lucide-react';

const ZoomableImage = ({ url, title }: { url: string; title: string }) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageDimensions, setImageDimensions] = useState({
    width: 0,
    height: 0,
  });
  const imgRef = useRef<HTMLImageElement>(null);

  const handleImageLoad = () => {
    if (imgRef.current) {
      const { naturalWidth, naturalHeight } = imgRef.current;
      setImageDimensions({
        width: naturalWidth,
        height: naturalHeight,
      });
      setImageLoaded(true);
    }
  };

  return (
    <TransformWrapper
      initialScale={1}
      initialPositionX={0}
      initialPositionY={0}
    >
      {({ zoomIn, zoomOut, resetTransform, ...rest }) => (
        <React.Fragment>
          <div className="tools flex gap-4 bg-white-150 border border-gray-100 py-2 px-4">
            <button onClick={() => zoomOut()}>
              <ZoomOutIcon strokeWidth={1} size={20} />
            </button>
            <button onClick={() => zoomIn()}>
              <ZoomInIcon strokeWidth={1} size={20} />
            </button>
            <button onClick={() => resetTransform()}>
              <RotateCcw strokeWidth={1} size={20} />
            </button>
          </div>
          <TransformComponent
            wrapperStyle={{
              width: '100%',
              border:
                '1px solid rgb(240 240 240 / var(--tw-border-opacity, 1))',
            }}
          >
            <img
              ref={imgRef}
              src={url}
              alt={title}
              onLoad={handleImageLoad}
              className="max-w-[100vw] max-h-[75vh] w-auto h-auto object-contain"
            />
          </TransformComponent>
        </React.Fragment>
      )}
    </TransformWrapper>
  );
};

export default ZoomableImage;
