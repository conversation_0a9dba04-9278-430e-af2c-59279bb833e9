import React from 'react';

import DeleteIcon from '@/assets/outline/delete';

import DeleteButton from '../button/deleteButton';
import { DialogContent, DialogHeader, DialogTitle } from '../dialog';

interface IProps {
  title: string;
  infoText: string;
  btnText: string;
  onClick: () => void;
  children?: React.ReactNode;
  dialogContentClass?: string;
  btnLoading?: boolean;
}

const DeleteModal = ({
  title,
  infoText,
  btnText,
  onClick,
  children,
  dialogContentClass = '',
  btnLoading = false,
}: IProps) => {
  return (
    <DialogContent className={dialogContentClass}>
      <DialogHeader>
        <DialogTitle>{title}</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="flex items-center gap-2 text-sm font-medium leading-5 text-[#F04040] p-3 rounded-lg bg-[#FDE8E866] mb-2">
          <div className="h-9 w-9 min-h-9 min-w-9 bg-[#F040401F] flex items-center justify-center rounded-full">
            <DeleteIcon color="#F04040" height="20" width="20" />
          </div>
          {infoText}
        </div>
        {children}
        <div className="flex justify-end mt-5">
          <DeleteButton
            text={btnText}
            onClick={onClick}
            isLoading={btnLoading}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default DeleteModal;
