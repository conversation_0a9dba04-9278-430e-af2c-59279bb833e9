import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown } from 'lucide-react';
import { createPortal } from 'react-dom';

interface RowsPerPageDropdownProps {
  value: number;
  options?: number[];
  onChange: (value: number) => void;
}

export default function RowsPerPageDropdown({
  value,
  options = [5, 10, 20, 50],
  onChange,
}: RowsPerPageDropdownProps) {
  const [open, setOpen] = useState(false);
  const [position, setPosition] = useState<{
    top: number;
    left: number;
    placeAbove: boolean;
  }>({
    top: 0,
    left: 0,
    placeAbove: false,
  });
  const buttonRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const toggleDropdown = () => {
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      const dropdownHeight = options.length * 32 + 8;
      const spaceBelow = window.innerHeight - rect.bottom;
      const placeAbove = spaceBelow < dropdownHeight;

      setPosition({
        top: placeAbove
          ? rect.top - dropdownHeight - 4 + window.scrollY
          : rect.bottom + 4 + window.scrollY,
        left: rect.left + window.scrollX,
        placeAbove,
      });
    }
    setOpen((prev) => !prev);
  };

  return (
    <>
      <button
        ref={buttonRef}
        onClick={toggleDropdown}
        className="flex items-center justify-between w-16 border border-grey-100 rounded-lg px-2 py-1 text-sm text-dark-300 bg-white hover:border-grey-200 focus:outline-none"
      >
        {value}
        <ChevronDown size={16} className="ml-1 text-grey-400" />
      </button>

      {open &&
        createPortal(
          <div
            ref={dropdownRef}
            className="absolute z-[9999] bg-white border border-grey-100 rounded-lg shadow-md"
            style={{
              top: `${position.top}px`,
              left: `${position.left}px`,
              width: '65px',
            }}
          >
            {options.map((size) => (
              <div
                key={size}
                onClick={() => {
                  onChange(size);
                  setOpen(false);
                }}
                className={`px-2 py-2 text-sm cursor-pointer transition-colors ${
                  value === size
                    ? 'bg-primary-200 text-primary-600 font-medium'
                    : 'text-dark-300 hover:bg-primary-100'
                }`}
              >
                {size}
              </div>
            ))}
          </div>,
          document.body,
        )}
    </>
  );
}
