'use client';
import Link from 'next/link';
import React from 'react';

interface IBradcrumbData {
  name: string;
  link: string;
  query?: Record<string, string | string[] | undefined>;
}

const Breadcrumb = ({ data }: { data: IBradcrumbData[] }) => {
  return (
    <div>
      <div className="text-sm font-semibold leading-5 text-grey-200 mb-3 flex">
        {data?.map((e, i) => (
          <div key={i} className="flex">
            {e.link !== '#' ? (
              <Link
                className="cursor-pointer"
                href={{
                  pathname: e.link,
                  query: e.query,
                }}
              >
                {e.name}
              </Link>
            ) : (
              e.name
            )}
            &nbsp;
            {i === data.length - 1 ? null : <span> / </span>}&nbsp;
          </div>
        ))}
      </div>
    </div>
  );
};

export default Breadcrumb;
