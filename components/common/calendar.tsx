// 'use client';

import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import moment from 'moment';
import * as React from 'react';
import DatePicker from 'react-datepicker';
import { DayPicker } from 'react-day-picker';

dayjs.extend(utc);
export type CalendarProps = React.ComponentProps<typeof DayPicker>;

interface TProps {
  selectedDate: string;
  onDateChange: (date: any) => void;
  disabled?: boolean;
  className?: string;
  allowPastDates?: boolean;
  disableFutureDate?: boolean;
}

const Calendar = ({
  selectedDate,
  onDateChange,
  disabled,
  className,
  allowPastDates = false,
  disableFutureDate = false,
}: TProps) => {
  return (
    <DatePicker
      selected={
        selectedDate?.length > 0 ? parseLocalDate(selectedDate) : undefined
      }
      onChange={(value) => onDateChange(toDateOffset(value))}
      className={`flex h-11 w-full rounded-lg border bg-white-100 border-grey-100 hover:border-grey-200 px-3 py-2 text-base  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground  focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed font-medium leading-[1.6rem] placeholder:text-grey-200 text-dark-300 ${
        className ? className : ''
      }`}
      dateFormat="MMM d, yyyy"
      popperClassName="z-50"
      wrapperClassName="w-full h-11 relative"
      placeholderText="Select date"
      calendarClassName="!bg-white !border !border-gray-300 !rounded-lg !shadow-lg p-4 !dark:bg-gray-800 !dark:border-gray-600 absolute h-full z-100"
      dayClassName={(date: Date) => {
        if (!selectedDate || selectedDate.length === 0) {
          return '!w-7 h-7 font-inter flex items-center justify-center rounded-full !text-xs !leading-7 !text-gray-300 !hover:bg-primary !hover:text-white !!text-grey-300 !!hover:bg-grey-100 !!hover:text-white';
        }
        return `!w-7 h-7 font-inter flex items-center justify-center rounded-full !text-xs !leading-7 !text-gray-300 !hover:bg-primary !hover:text-white ${
          parseLocalDate(selectedDate).toDateString() === date.toDateString()
            ? '!!bg-primary-200 !!text-dark-300'
            : '!!text-grey-300 !!hover:bg-grey-100 !!hover:text-white'
        }`;
      }}
      disabled={disabled}
      minDate={allowPastDates ? undefined : new Date()}
      maxDate={disableFutureDate ? new Date() : undefined}
    />
  );
};

function parseLocalDate(dateStr: string): Date {
  return moment(dateStr, 'YYYY-MM-DD').toDate();
}

export function toDateOffset(date: Date | null) {
  if (!date) return null;
  return moment(date).local().utc().toDate();
}

export default Calendar;
