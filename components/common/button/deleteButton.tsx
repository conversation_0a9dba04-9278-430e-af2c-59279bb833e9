import React from 'react';

import { cn } from '@/utils/styleUtils';

const DeleteButton = ({
  text,
  onClick,
  isLoading = false,
  width,
}: {
  text?: string;
  onClick?: () => void;
  isLoading?: boolean;
  width?: string;
}) => {
  const widthClass = width === 'auto' ? 'w-auto' : `w-[${width}]`;

  return (
    <button
      className={cn(
        'py-2 px-5 text-base font-medium leading-6 w-auto text-white bg-[#F55D5D] cursor-pointer rounded-lg hover:bg-[#DF5353] active:bg-[#C84040] ',
        widthClass,
        isLoading && 'opacity-50 cursor-not-allowed',
      )}
      onClick={onClick}
      disabled={isLoading}
    >
      {isLoading ? (
        <span className="animate-pulse min-w-14 relative block w-14">•••</span>
      ) : (
        text || 'Delete'
      )}
    </button>
  );
};

export default DeleteButton;
