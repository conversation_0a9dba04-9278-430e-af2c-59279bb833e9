import React from 'react';

import { cn } from '@/utils/styleUtils';

type TButtonSize = 'small' | 'medium' | 'large';
type TIconPosition = 'left' | 'right';

interface TTertiaryButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  size?: TButtonSize;
  isLoading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: TIconPosition;
  text?: string;
  width?: string;
  buttonClasses?: string;
}

const TertiaryButton: React.FC<TTertiaryButtonProps> = ({
  size = 'large',
  isLoading = false,
  icon = null,
  iconPosition = 'left',
  text = 'Button',
  width = 'auto',
  buttonClasses = '',
  ...props
}) => {
  // Define size classes
  const sizeClasses = {
    small: 'px-3 py-1 text-sm leading-5',
    medium: 'px-5 py-2 text-base leading-6',
    large: 'px-7 py-3 text-lg leading-7',
  };

  // Define width classes
  const widthClass = width === 'auto' ? 'w-auto' : `w-${width}`;

  // Conditional classes for loading state
  const loadingClasses = isLoading
    ? 'opacity-50 cursor-not-allowed'
    : 'hover:text-primary-500 hover:border-primary-500 ';

  return (
    <button
      className={`bg-white-100 text-primary-400 border border-primary-400 font-medium rounded-lg ${sizeClasses[size]} ${widthClass} ${loadingClasses} flex items-center justify-center disabled:bg-white-100 disabled:border-grey-200 disabled:text-grey-200 disabled:cursor-not-allowed transition-colors ${buttonClasses}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {/* Loading State */}
      {isLoading ? (
        <span className="animate-pulse min-w-14">•••</span>
      ) : (
        <>
          {icon && iconPosition === 'left' && (
            <span className={cn(!text || text === '' ? '' : 'mr-2')}>
              {icon}
            </span>
          )}

          {text}

          {icon && iconPosition === 'right' && (
            <span className={cn(!text || text === '' ? '' : 'ml-2')}>
              {icon}
            </span>
          )}
        </>
      )}
    </button>
  );
};

export default TertiaryButton;
