import React from 'react';

import { cn } from '@/utils/styleUtils';

type TButtonSize = 'small' | 'medium' | 'large';
type TIconPosition = 'left' | 'right';

interface TLinkButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  size?: TButtonSize;
  isLoading?: boolean;
  icon?: React.ReactNode;
  iconPosition?: TIconPosition;
  text?: string;
  width?: string;
  buttonClasses?: string;
}

const LinkButton: React.FC<TLinkButtonProps> = ({
  size = 'large',
  isLoading = false,
  icon = null,
  iconPosition = 'left',
  text = 'Button',
  width = 'auto',
  buttonClasses = '',
  ...props
}) => {
  // Define size classes
  const sizeClasses = {
    small: 'text-sm leading-5',
    medium: 'text-base leading-6',
    large: 'text-lg leading-7',
  };

  // Define width classes
  const widthClass = width === 'auto' ? 'w-auto' : `w-${width}`;

  // Conditional classes for loading state
  const loadingClasses = isLoading
    ? 'opacity-50 cursor-not-allowed'
    : 'hover:text-primary-500  ';

  return (
    <button
      className={`bg-transparent text-primary-400  font-medium rounded-lg ${sizeClasses[size]} ${widthClass} ${loadingClasses} flex items-center justify-center disabled:bg-white-100 disabled:border-grey-200 disabled:text-grey-200 disabled:cursor-not-allowed transition-colors ${buttonClasses}`}
      disabled={isLoading || props.disabled}
      {...props}
    >
      {/* Loading State */}
      {isLoading ? (
        <span className="animate-pulse min-w-14">•••</span>
      ) : (
        <>
          {icon && iconPosition === 'left' && (
            <span className={cn(!text || text === '' ? '' : 'mr-2')}>
              {icon}
            </span>
          )}

          {text}

          {icon && iconPosition === 'right' && (
            <span className={cn(!text || text === '' ? '' : 'ml-2')}>
              {icon}
            </span>
          )}
        </>
      )}
    </button>
  );
};

export default LinkButton;
