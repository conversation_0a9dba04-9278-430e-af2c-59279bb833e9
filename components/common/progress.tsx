

const Progress = ({ percent, title }: { percent: number; title: string }) => {
  let progress;

  if (percent > 50) {
    progress = 'bg-[#00797D] text-[#E05252]';
  } else {
    progress = 'bg-[#00797D] text-[#E05252]';
  }

  return (
    <div className="flex flex-wrap items-center flex-1">
      <div>
        <p className="text-black text-md">{title}</p>
      </div>
      <div className="w-3/12 mx-2">
        <div className="w-full bg-[#E5F6F6] rounded-full h-3">
          <div
            className={`${progress + ' h-3 rounded-full'}`}
            style={{
              width: `${percent}%`,
            }}
          ></div>
        </div>
      </div>
      <div>
        <p className="text-black text-md">{percent}%</p>
      </div>
    </div>
  );
};

export default Progress;
