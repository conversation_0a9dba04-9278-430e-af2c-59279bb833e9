import { DownloadIcon } from 'lucide-react';

import View from '@/assets/outline/view';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import DocumentViewModal from '@/components/common/modals/documentViewModal';
import { useAuthStore } from '@/globalProvider/authStore';
import { handleDownloadDocument } from '@/utils/download';

type TableAttachmentProps = {
  obj: Record<string, any>;
  backendKey: string;
};

const TableAttachment: React.FC<TableAttachmentProps> = ({
  obj,
  backendKey,
}) => {
  const { accessToken } = useAuthStore();

  // Early return if the obj or backendKey is not valid
  if (!obj || !obj.hasOwnProperty(backendKey)) {
    return (
      <div className="h-auto flex  items-center justify-start gap-1 text-grey-200 cursor-pointer">
        <View color="#B9B9B9" /> View
      </div>
    );
  }

  const attachment = obj[backendKey]?.[0];

  // Check if the attachment is valid
  if (
    !attachment ||
    attachment === null ||
    JSON.stringify(attachment) === JSON.stringify('') ||
    JSON.stringify(attachment) === JSON.stringify([])
  ) {
    return (
      <div className="h-full flex  items-center justify-start gap-1 text-grey-200 cursor-pointer">
        <View color="#B9B9B9" /> View
      </div>
    );
  }

  const filename = attachment.file_path?.split('/').pop() || '';
  const filePath = attachment.file_path;
  const fileExtension = attachment.file_extension;

  const handleDownload = () => {
    if (accessToken && filePath) {
      handleDownloadDocument(accessToken, filePath, filename, 1);
    }
  };

  // Render based on file extension
  if (
    ['pdf', 'docx', 'doc', 'html', 'jpg', 'png', 'jpeg'].includes(fileExtension)
  ) {
    return (
      <Dialog>
        <DialogTrigger className="h-full flex items-center justify-start gap-1 text-primary-500 cursor-pointer">
          <View color="#00797D" /> View
        </DialogTrigger>
        <DocumentViewModal
          title=""
          filePath={filePath}
          extension={
            fileExtension as
              | 'html'
              | 'pdf'
              | 'docx'
              | 'doc'
              | 'png'
              | 'jpeg'
              | 'jpg'
          }
          dialogClass="min-w-[95%]"
        />
      </Dialog>
    );
  }

  return (
    <div
      className="h-full flex items-center justify-start gap-1 text-primary-500 cursor-pointer"
      onClick={handleDownload}
    >
      <DownloadIcon color="#00797D" /> Download
    </div>
  );
};

export default TableAttachment;
