import { Tooltip, TooltipContent, TooltipTrigger } from './tooltip';

export interface IDetailsText {
  name: string;
  full_name?: string;
  id: string;
  newLine?: boolean;
}
export const DetailsText = ({
  label,
  value,
  multiValue = false,
  newLine = false,
}: {
  label: string;
  value: string | IDetailsText[];
  multiValue?: boolean;
  newLine?: boolean;
}) => {
  return (
    <div
      className={`text-grey-300 font-medium text-base leading-6 flex ${
        newLine ? 'flex-col gap-1 mb-2' : 'items-center gap-2'
      }`}
    >
      {label}:{' '}
      {multiValue ? (
        <div className="flex items-center gap-2">
          <span className="text-dark-300">
            {(value as IDetailsText[])?.[0]?.name}
          </span>

          {(value as IDetailsText[]).slice(1).length > 0 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="py-1 px-2.5 rounded-full text-sm leading-5 font-semibold text-dark-300 bg-white-200">
                  +{(value as IDetailsText[]).slice(1).length}
                </span>
              </TooltipTrigger>
              <TooltipContent
                side="right"
                sideOffset={4}
                className="bg-white-100 rounded-lg p-1 shadow-shadow-2"
              >
                {(value as IDetailsText[]).slice(1).map((item, key) => (
                  <div className="flex flex-col gap-1 " key={key}>
                    <div className="py-1.5 px-3 text-base leading-6 text-dark-300 font-medium">
                      {item.name || item.full_name}
                    </div>
                  </div>
                ))}
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      ) : (
        <span className="text-dark-300">{value as string}</span>
      )}
    </div>
  );
};

export const DetailsTextNew = ({
  label,
  value,
  multiValue = false,
}: {
  label: string;
  value: string | IDetailsText[];
  multiValue?: boolean;
}) => {
  return (
    <div className="text-grey-300 font-medium text-sm leading-5 flex flex-col gap-0.5 items-start ">
      {label}:{' '}
      {multiValue ? (
        <div className="flex  items-center gap-2">
          <span className="text-dark-300 text-base leading-6">
            {(value as IDetailsText[])?.[0]?.name}
          </span>

          {(value as IDetailsText[]).slice(1).length > 0 && (
            <Tooltip>
              <TooltipTrigger asChild>
                <span className="py-1 px-2.5 rounded-full text-sm leading-5 font-semibold text-dark-300 bg-white-200">
                  +{(value as IDetailsText[]).slice(1).length}
                </span>
              </TooltipTrigger>
              <TooltipContent
                side="right"
                sideOffset={4}
                className="bg-white-100 rounded-lg p-1 shadow-shadow-2"
              >
                {(value as IDetailsText[]).slice(1).map((item, key) => (
                  <div className="flex flex-col gap-1 " key={key}>
                    <div className="py-1.5 px-3 text-base leading-6 text-dark-300 font-medium">
                      {item.name || item.full_name}
                    </div>
                  </div>
                ))}
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      ) : (
        <span className="text-dark-300 text-base leading-6">
          {value as string}
        </span>
      )}
    </div>
  );
};
