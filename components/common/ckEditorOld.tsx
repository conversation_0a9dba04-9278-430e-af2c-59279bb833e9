import 'ckeditor5/ckeditor5.css';

import {
  AccessibilityHelp,
  Alignment,
  Autoformat,
  AutoImage,
  AutoLink,
  Autosave,
  BlockQuote,
  Bold,
  CKBox,
  CKBoxImageEdit,
  EditorConfig as CKEditorConfig,
  ClassicEditor,
  CloudServices,
  Code,
  Essentials,
  FindAndReplace,
  FontBackgroundColor,
  FontColor,
  FontFamily,
  FontSize,
  GeneralHtmlSupport,
  Heading,
  Highlight,
  HorizontalLine,
  ImageBlock,
  ImageCaption,
  ImageInline,
  ImageInsert,
  ImageInsertViaUrl,
  ImageResize,
  ImageStyle,
  ImageTextAlternative,
  ImageToolbar,
  ImageUpload,
  Indent,
  IndentBlock,
  Italic,
  Link,
  LinkImage,
  List,
  ListProperties,
  Mention,
  PageBreak,
  Paragraph,
  PasteFromOffice,
  PictureEditing,
  RemoveFormat,
  SelectAll,
  SpecialCharacters,
  SpecialCharactersArrows,
  SpecialCharactersCurrency,
  SpecialCharactersEssentials,
  SpecialCharactersLatin,
  SpecialCharactersMathematical,
  SpecialCharactersText,
  Strikethrough,
  Style,
  Subscript,
  Superscript,
  Table,
  TableCaption,
  TableCellProperties,
  TableColumnResize,
  TableProperties,
  TableToolbar,
  TextTransformation,
  TodoList,
  Underline,
  Undo,
} from 'ckeditor5';
import {
  CaseChange,
  ExportPdf,
  ExportWord,
  FormatPainter,
  MergeFields,
  MultiLevelList,
  PasteFromOfficeEnhanced,
  SlashCommand,
  TableOfContents,
  Template,
} from 'ckeditor5-premium-features';
import { useEffect, useRef, useState } from 'react';

import { CKEditor } from '@ckeditor/ckeditor5-react';

import Loader from './loader';

interface EditorConfig extends CKEditorConfig {
  configUpdateAlertShown?: boolean;
}

// import { useParams } from "react-router-dom";
// import Notification from "../../notifications/Notification";
// import CreateDocumentVersion from "../Modals/CreateDocumentVersion";

const LICENSE_KEY = process.env.NEXT_PUBLIC_LICENSE_KEY;
const CKBOX_TOKEN_URL = process.env.NEXT_PUBLIC_CKBOX_TOKEN_URL;

export default function DocumentEditor({
  disabled,
  url,
  editorData,
  setEditorData,
}: {
  disabled: boolean;
  url: string;
  editorData: string;
  setEditorData: React.Dispatch<React.SetStateAction<string>>;
}) {
  const editorContainerRef = useRef(null);

  const editorRef = useRef<ClassicEditor | null>(null);
  const editorWrapperRef = useRef<HTMLDivElement>(null);
  const [isLayoutReady, setIsLayoutReady] = useState(false);

  useEffect(() => {
    const fetchHtmlContent = async () => {
      try {
        const response = await fetch(url);

        if (!response.ok) {
          throw new Error(
            `Failed to fetch HTML content. Status: ${response.status}`,
          );
        }

        const htmlContent = await response.text();
        setEditorData(htmlContent ? `${htmlContent}` : '');
        // Set the fetched HTML content to CKEditor
      } catch (error) {
        return;
      }
    };

    if (url) fetchHtmlContent();
  }, [setEditorData, url]);

  const editorConfig: EditorConfig = {
    toolbar: {
      items: [
        'undo',
        'redo',
        '|',
        'heading',
        'style',
        '|',
        'fontSize',
        'fontFamily',
        'fontColor',
        'fontBackgroundColor',
        '|',
        'bold',
        'italic',
        'underline',
        '|',
        'exportPdf',
        // 'importWord',
        '|',
        'link',
        'insertImage',
        'insertTable',
        'highlight',
        'blockQuote',
        '|',
        'alignment',
        '|',
        'bulletedList',
        'numberedList',
        'multiLevelList',
        'todoList',
        'outdent',
        'indent',
      ],
      shouldNotGroupWhenFull: false,
    },
    plugins: [
      AccessibilityHelp,
      Alignment,
      Autoformat,
      AutoImage,
      AutoLink,
      Autosave,
      BlockQuote,
      Bold,
      CaseChange,
      CKBox,
      CKBoxImageEdit,
      CloudServices,
      Code,
      Essentials,
      ExportPdf,
      ExportWord,
      FindAndReplace,
      FontBackgroundColor,
      FontColor,
      FontFamily,
      FontSize,
      FormatPainter,
      GeneralHtmlSupport,
      Heading,
      Highlight,
      HorizontalLine,
      ImageBlock,
      ImageCaption,
      ImageInline,
      ImageInsert,
      ImageInsertViaUrl,
      ImageResize,
      ImageStyle,
      ImageTextAlternative,
      ImageToolbar,
      ImageUpload,
      // ImportWord,
      Indent,
      IndentBlock,
      Italic,
      Link,
      LinkImage,
      List,
      ListProperties,
      Mention,
      MergeFields,
      MultiLevelList,
      PageBreak,
      Paragraph,
      PasteFromOffice,
      PasteFromOfficeEnhanced,
      PictureEditing,
      RemoveFormat,
      SelectAll,
      SlashCommand,
      SpecialCharacters,
      SpecialCharactersArrows,
      SpecialCharactersCurrency,
      SpecialCharactersEssentials,
      SpecialCharactersLatin,
      SpecialCharactersMathematical,
      SpecialCharactersText,
      Strikethrough,
      Style,
      Subscript,
      Superscript,
      Table,
      TableCaption,
      TableCellProperties,
      TableColumnResize,
      TableOfContents,
      TableProperties,
      TableToolbar,
      Template,
      TextTransformation,
      TodoList,
      Underline,
      Undo,
    ],
    extraPlugins: [],
    ckbox: {
      tokenUrl: CKBOX_TOKEN_URL,
    },
    exportPdf: {
      stylesheets: [
        'https://cdn.ckeditor.com/ckeditor5/43.3.1/ckeditor5.css',
        'https://cdn.ckeditor.com/ckeditor5-premium-features/43.3.1/ckeditor5-premium-features.css',
      ],
      fileName: 'BPRHub_Document.pdf',
      converterOptions: {
        format: 'Tabloid',
        margin_top: '20mm',
        margin_bottom: '20mm',
        margin_right: '24mm',
        margin_left: '24mm',
        page_orientation: 'portrait',
      },
    },
    exportWord: {
      stylesheets: [
        'https://cdn.ckeditor.com/ckeditor5/43.3.1/ckeditor5.css',
        'https://cdn.ckeditor.com/ckeditor5-premium-features/43.3.1/ckeditor5-premium-features.css',
      ],
      fileName: 'document.docx',
      converterOptions: {
        document: {
          orientation: 'portrait',
          size: 'Tabloid',
          margins: {
            top: '20mm',
            bottom: '20mm',
            right: '24mm',
            left: '24mm',
          },
        },
      },
    },
    fontFamily: {
      supportAllValues: true,
    },
    fontSize: {
      options: [10, 12, 14, 'default', 18, 20, 22],
      supportAllValues: true,
    },
    heading: {
      options: [
        {
          model: 'paragraph',
          title: 'Paragraph',
          class: 'ck-heading_paragraph',
        },
        {
          model: 'heading1',
          view: 'h1',
          title: 'Heading 1',
          class: 'ck-heading_heading1',
        },
        {
          model: 'heading2',
          view: 'h2',
          title: 'Heading 2',
          class: 'ck-heading_heading2',
        },
        {
          model: 'heading3',
          view: 'h3',
          title: 'Heading 3',
          class: 'ck-heading_heading3',
        },
        {
          model: 'heading4',
          view: 'h4',
          title: 'Heading 4',
          class: 'ck-heading_heading4',
        },
        {
          model: 'heading5',
          view: 'h5',
          title: 'Heading 5',
          class: 'ck-heading_heading5',
        },
        {
          model: 'heading6',
          view: 'h6',
          title: 'Heading 6',
          class: 'ck-heading_heading6',
        },
      ],
    },
    htmlSupport: {
      allow: [
        {
          name: /^.*$/,
          styles: true,
          attributes: true,
          classes: true,
        },
      ],
    },
    image: {
      toolbar: [
        'toggleImageCaption',
        'imageTextAlternative',
        '|',
        'imageStyle:inline',
        'imageStyle:wrapText',
        'imageStyle:breakText',
        '|',
        'resizeImage',
        '|',
        'ckboxImageEdit',
      ],
    },
    licenseKey: LICENSE_KEY,
    link: {
      addTargetToExternalLinks: true,
      defaultProtocol: 'https://',
      decorators: {
        toggleDownloadable: {
          mode: 'manual',
          label: 'Downloadable',
          attributes: {
            download: 'file',
          },
        },
      },
    },
    list: {
      properties: {
        styles: true,
        startIndex: true,
        reversed: true,
      },
    },
    menuBar: {
      isVisible: false,
    },
    placeholder: 'Type or paste your content here!',
    style: {
      definitions: [
        {
          name: 'Article category',
          element: 'h3',
          classes: ['category'],
        },
        {
          name: 'Title',
          element: 'h2',
          classes: ['document-title'],
        },
        {
          name: 'Subtitle',
          element: 'h3',
          classes: ['document-subtitle'],
        },
        {
          name: 'Info box',
          element: 'p',
          classes: ['info-box'],
        },
        {
          name: 'Side quote',
          element: 'blockquote',
          classes: ['side-quote'],
        },
        {
          name: 'Marker',
          element: 'span',
          classes: ['marker'],
        },
        {
          name: 'Spoiler',
          element: 'span',
          classes: ['spoiler'],
        },
        {
          name: 'Code (dark)',
          element: 'pre',
          classes: ['fancy-code', 'fancy-code-dark'],
        },
        {
          name: 'Code (bright)',
          element: 'pre',
          classes: ['fancy-code', 'fancy-code-bright'],
        },
      ],
    },
    table: {
      contentToolbar: [
        'tableColumn',
        'tableRow',
        'mergeTableCells',
        'tableProperties',
        'tableCellProperties',
      ],
    },
  };

  // configUpdateAlert(editorConfig);

  return (
    <>
      <div className="w-full">
        <div className="main-container ">
          <div
            className="editor-container editor-container_classic-editor editor-container_include-annotations editor-container_include-style"
            ref={editorContainerRef}
          >
            <div className="editor-container__editor-wrapper rounded-lg">
              <div ref={editorWrapperRef}>
                <div className="min-h-[400px]">
                  {!isLayoutReady ? <Loader className="h-[300px]" /> : null}
                  <CKEditor
                    data={editorData}
                    editor={ClassicEditor}
                    config={editorConfig}
                    onReady={(editor) => {
                      editorRef.current = editor;
                      setIsLayoutReady(true);
                    }}
                    onChange={(event, editor) => {
                      setEditorData(editor.getData());
                    }}
                    disabled={disabled}
                  />
                </div>
              </div>
            </div>
            {/* <pre>{editorData}</pre> */}
          </div>
        </div>
      </div>
    </>
  );
}

/**
 * This function exists to remind you to update the config needed for premium features.
 * The function can be safely removed. Make sure to also remove call to this function when doing so.
 */
function configUpdateAlert(config: EditorConfig) {
  if (config.configUpdateAlertShown) {
    return;
  }

  const isModifiedByUser = (
    currentValue: string | (() => Promise<string>) | undefined,
    forbiddenValue: string | undefined,
  ) => {
    if (currentValue === forbiddenValue) {
      return false;
    }

    if (currentValue === undefined) {
      return false;
    }

    return true;
  };

  const valuesToUpdate = [];

  config.configUpdateAlertShown = true;

  if (!isModifiedByUser(config.licenseKey, LICENSE_KEY)) {
    valuesToUpdate.push('LICENSE_KEY');
  }

  if (!isModifiedByUser(config.ckbox?.tokenUrl, CKBOX_TOKEN_URL)) {
    valuesToUpdate.push('CKBOX_TOKEN_URL');
  }

  if (valuesToUpdate.length) {
    window.alert(
      [
        'Please update the following values in your editor config',
        'in order to receive full access to the Premium Features:',
        '',
        ...valuesToUpdate.map((value) => ` - ${value}`),
      ].join('\n'),
    );
  }
}
