import React from 'react';
import { Mention, MentionsInput } from 'react-mentions';

interface IProps {
  comment: string;
  setComment: React.Dispatch<React.SetStateAction<string>>;
  data?: { id: string; display: string }[];
}

const ReactMention = ({ comment, setComment, data }: IProps) => {
  // const [comment, setComment] = useState("");
  // const [addedUser, setAddedUser] = useState<{ id: string | number }[]>([]);
  const mentionInputStyle = {
    input: {
      padding: '10px',
      height: '105px',
      border: '1px solid #E1E1E1',
      borderRadius: '8px',
      fontSize: '16px',
      lineHeight: '24px',
      color: '#282828',
      fontWeight: 500,
    },
    suggestions: {
      maxHeight: '150px',
      border: '1px solid #E1E1E1',
      borderRadius: '8px',
      boxShadow: '0px 0px 1px 0px #3031330D, 0px 4px 8px 0px #3031331A',
      padding: '0.25rem',
      background: 'white',
      overflow: 'auto',
      // left: "30px",
      item: {
        padding: '0.5rem 0.75rem',
        '&focused': {
          backgroundColor: '#01636633',
          borderRadius: '6px',
        },
      },
    },
    highlighter: {
      padding: '10px',
      maxHeight: '105px',
    },
  };

  return (
    <div>
      <div className="mb-4.5 mention-input-wrapper">
        <MentionsInput
          maxLength={3000}
          placeholder={"Mention people using '@'"}
          value={comment}
          rows={1}
          style={mentionInputStyle}
          onChange={(e) => {
            setComment(e.target.value);
          }}
        >
          <Mention
            data={data || []}
            style={{
              backgroundColor: '#01636633',
              padding: '2px 1px',
              borderRadius: '2px',
            }}
            trigger="@"
            displayTransform={(id, full_name) => `@${full_name}`}
            //rehan fix
            // onAdd={(id) => setAddedUser((prevState) => [...prevState, { id }])}
          />
        </MentionsInput>
        {/* <p className="text-red-500 text-sm text-right">
															{comment.length > 3000 &&
																"Max character length allowed 3000"}
														</p> */}
      </div>
    </div>
  );
};

export default ReactMention;
