import { useRouter } from 'next/router';
import React from 'react';

import { ISettingData } from '@/interfaces/setting';

const Card = ({ data }: { data: ISettingData }) => {
  const router = useRouter();
  return (
    data.access && (
      <div
        className="p-5 rounded-lg border border-gray-200 bg-white-100 hover:border-grey-200 hover:shadow-[0px_0px_1px_0px_#3031330D,_0px_2px_4px_0px_#3031331A] cursor-pointer transition-all"
        onClick={() => router.push(`/setting/${data.link}/`)}
        key={data.id}
      >
        <div>
          <div className="text-dark-100 text-base leading-6 font-medium mb-3">
            {data.icon}
          </div>
          <div className="text-dark-300 font-semibold leading-7 text-xl mb-3">
            {data.title}
          </div>
          <div className="text-gray-400 text-base leading-6 font-medium mb-2">
            {data.description}
          </div>
        </div>
      </div>
    )
  );
};

export default Card;
