import UserIcon from '@/assets/outline/user';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { hasAccess } from '@/utils/roleAccessConfig';

import Breadcrumb from '../common/breadcrumb';
import Layout from '../common/sidebar/layout';
import Card from './card';
import { Settings2 } from 'lucide-react';

const Settings = () => {
  const { user } = useAuthStore();

  const breadcrumbData = [
    {
      name: 'Settings',
      link: '#',
    },
  ];

  const data = [
    {
      id: 1,
      icon: <UserIcon color={'#016366'} width="32" height="32" />,
      title: 'User management',
      description:
        'Manage employee roles and access permissions from this page',
      link: 'users',
      access: hasAccess(AccessActions.CanCreateUser, user),
    },
    {
      id: 2,
      icon: <Settings2 color={'#016366'} width="32" height="32" />,
      title: 'Workflow',
      description:
        'Manage all platform settings and configurations from this page',
      link: 'workflow',
      access: hasAccess(AccessActions.CanCreateUser, user),
    },
  ];

  return (
    <div>
      <Layout>
        <div className=" my-5">
          <div className="flex flex-col">
            <Breadcrumb data={breadcrumbData} />
            <div className="text-dark-300 font-semibold text-3xl leading-10">
              Settings
            </div>
          </div>
          <div className="grid grid-cols-3 gap-5 mt-6">
            {data?.map((e, i) => (
              <Card key={i} data={e} />
            ))}
          </div>
        </div>
      </Layout>
    </div>
  );
};

export default Settings;
