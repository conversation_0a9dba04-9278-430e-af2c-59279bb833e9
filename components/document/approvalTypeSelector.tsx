import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/utils/styleUtils';

export type ApprovalType = 'Sequential' | 'Parallel';

interface ApprovalTypeSelectorProps {
  value: ApprovalType;
  onChange: (value: ApprovalType) => void;
  className?: string;
}

const ApprovalTypeSelector: React.FC<ApprovalTypeSelectorProps> = ({
  value,
  onChange,
  className,
}) => {
  const handleChange = (type: ApprovalType) => {
    onChange(type);
  };

  return (
    <div className={cn('space-y-4 w-full max-w-4xl', className)}>
      <h3 className="text-base font-medium text-approval-text-primary">
        Approval type
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <ApprovalOption
          type="Sequential"
          title="Sequential Approval"
          description="Approvers will be notified one after another in the order you specify"
          selected={value === 'Sequential'}
          onChange={handleChange}
        />
        <ApprovalOption
          type="Parallel"
          title="Parallel Approval"
          description="All approvers will be notified simultaneously"
          selected={value === 'Parallel'}
          onChange={handleChange}
        />
      </div>
    </div>
  );
};

interface ApprovalOptionProps {
  type: ApprovalType;
  title: string;
  description: string;
  selected: boolean;
  onChange: (type: ApprovalType) => void;
}

const ApprovalOption: React.FC<ApprovalOptionProps> = ({
  type,
  title,
  description,
  selected,
  onChange,
}) => {
  return (
    <motion.div
      whileTap={{ scale: 0.98 }}
      className={cn(
        'approval-option px-6 py-5 flex items-start gap-4 rounded-xl cursor-pointer',
        'bg-gray-50 hover:bg-gray-100/80 border border-approval-border',
        'transition-colors duration-200 ease-out',
      )}
      onClick={() => onChange(type)}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
    >
      <div className="relative flex-shrink-0 mt-[3px]">
        <div
          className={cn(
            'w-5 h-5 rounded-full border-2 flex items-center justify-center',
            selected ? 'border-approval-selected' : 'border-gray-300',
          )}
        >
          {selected && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="w-2.5 h-2.5 rounded-full bg-approval-selected"
              transition={{ duration: 0.2, ease: [0.175, 0.885, 0.32, 1.1] }}
            />
          )}
        </div>
      </div>
      <div className="space-y-1 flex-grow">
        <h4
          className={cn(
            'approval-title text-base font-medium leading-tight',
            selected
              ? 'text-approval-text-primary'
              : 'text-approval-text-primary',
          )}
        >
          {title}
        </h4>
        <p className="text-sm text-approval-text-secondary leading-relaxed">
          {description}
        </p>
      </div>
    </motion.div>
  );
};

export default ApprovalTypeSelector;
