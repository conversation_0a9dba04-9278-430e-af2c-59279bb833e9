import { ILogs } from '@/interfaces/document';
import { formatDate } from '@/utils/time';

const LogsCard = ({ logs }: { logs: ILogs }) => {
  return (
    <div className="flex gap-4 justify-between items-start p-4 border border-white-300 rounded-lg mb-3">
      <div className="flex-shrink-0 w-[70%]">
        <div className="text-base font-medium leading-6 text-grey-300 mb-1">
          Description:{' '}
          <span
            onClick={(e) => e.preventDefault()}
            className="text-base font-medium leading-6 text-dark-300"
          >
            {logs.description}
          </span>
        </div>
        {logs.comment ? (
          <div className="text-base font-medium leading-6 text-grey-300">
            Comment:{' '}
            <span className="text-dark-300">
              {
                <p
                  onClick={(e) => e.preventDefault()}
                  dangerouslySetInnerHTML={{
                    __html: logs.comment,
                  }}
                  className="text-base leading-6 font-medium text-dark-300"
                />
              }
            </span>
          </div>
        ) : (
          ''
        )}
      </div>
      <div className="flex-grow text-sm font-medium text-gray-700 text-right">
        <div className="text-sm font-medium leading-5 text-grey-300 mb-1">
          Updated by:{' '}
          <span className="text-dark-300">{logs.created_by.full_name}</span>
        </div>
        <div className="text-sm font-medium leading-5 text-grey-300">
          Updated on:{' '}
          <span className="text-dark-300">
            {formatDate(logs.created_on, true)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default LogsCard;
