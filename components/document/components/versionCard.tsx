import LinkButton from '@/components/common/button/linkButton';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import DocumentViewModal from '@/components/common/modals/documentViewModal';
import { IDocumentVersion } from '@/interfaces/document';

const VersionCard = ({
  versionData,
  handleDownloadDocument,
}: {
  versionData: IDocumentVersion;
  handleDownloadDocument: (
    path: string,
    documentTitle: string,
    versionNumber: any,
    extension?: any,
  ) => void;
}) => {
  return (
    <div className="p-4 border border-white-300 rounded-lg mb-3">
      <div className="flex justify-between items-center mb-1.5">
        <div className="text-base leading-6 font-medium text-grey-300">
          Doc name: <span className="text-dark-300">{versionData.title}</span>
        </div>
        <div className="text-sm leading-5 font-medium text-grey-300">
          Version no.:{' '}
          <span className="text-dark-300">{versionData.version_number}</span>
        </div>
      </div>

      {versionData?.file_path.split('.').pop() === 'html' ? (
        <div className="flex gap-2">
          <Dialog>
            <DialogTrigger>
              <LinkButton text="View" size="medium" />
            </DialogTrigger>
            <DocumentViewModal
              title={versionData.title}
              filePath={versionData?.file_path}
              extension={
                versionData?.file_path.split('.').pop() as
                  | 'html'
                  | 'pdf'
                  | 'png'
                  | 'jpeg'
                  | 'jpg'
              }
              dialogClass="min-w-[95%]"
            />
          </Dialog>
          {versionData?.file_path.split('.').pop() === 'html' ? (
            <LinkButton
              text="Download"
              size="medium"
              onClick={() =>
                handleDownloadDocument(
                  versionData.file_path,
                  versionData.title,
                  versionData.version_number,
                  versionData?.file_path.split('.').pop(),
                )
              }
            />
          ) : (
            ''
          )}
        </div>
      ) : (
        <LinkButton
          text="Download"
          size="medium"
          onClick={() =>
            handleDownloadDocument(
              versionData.file_path,
              versionData.title,
              versionData.version_number,
            )
          }
        />
      )}
    </div>
  );
};

export default VersionCard;
