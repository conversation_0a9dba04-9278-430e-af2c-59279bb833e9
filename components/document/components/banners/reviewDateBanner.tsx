import { getDaysUntilReview } from '@/utils/getDaysUntilReview';
import { formatDate } from '@/utils/time';
import { CircleAlert } from 'lucide-react';
import React from 'react';

interface ReviewDateBannerProps {
  reviewDate: Date | string;
}

const ReviewDateBanner: React.FC<ReviewDateBannerProps> = ({ reviewDate }) => {
  const diffInDays = getDaysUntilReview(reviewDate);

  const banner = (message: string, textColor: string, bgColor: string) => (
    <div className="flex items-center gap-2 border-l-6 bg-opacity-[15%] rounded-md">
      <div
        className={`${bgColor} h-9 w-9 flex items-center justify-center rounded-full`}
      >
        <CircleAlert
          height="20"
          width="20"
          className={`${textColor}`}
          aria-hidden="true"
        />
      </div>
      <p className={`${textColor} text-sm font-medium`}>{message}</p>
    </div>
  );

  if (diffInDays === null) return null;

  if (diffInDays <= 0) {
    return banner(
      'Review date passed! This document is due for review.',
      'text-red-600',
      'bg-red-50',
    );
  } else if (diffInDays < 30) {
    return banner(
      `The next review date is on ${formatDate(
        typeof reviewDate === 'string' ? reviewDate : reviewDate.toISOString(),
      )}. Please review the document soon!`,
      'text-yellow-300',
      'bg-orange-50',
    );
  } else {
    return banner(
      `The next review date is on ${formatDate(
        typeof reviewDate === 'string' ? reviewDate : reviewDate.toISOString(),
      )}.`,
      'text-gray-600',
      'bg-gray-100',
    );
  }
};

export default ReviewDateBanner;
