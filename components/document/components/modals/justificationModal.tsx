import { useParams } from 'next/navigation';
import React, { useEffect } from 'react';

import { Textarea } from '@/components/common/textarea';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';

import PrimaryButton from '../../../common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../common/dialog';

interface IProps {
  approvalId: string;
  setOpenJustificationModal: React.Dispatch<React.SetStateAction<boolean>>;
  refetch: () => void;
  handleRejectMfa: (data: any) => void;
}
const JustificationModal = ({
  approvalId,
  setOpenJustificationModal,
  handleRejectMfa,
  refetch,
}: IProps) => {
  const [justification, setJustification] = React.useState('');
  const {
    postData: postComment,
    response: postCommentResponse,
    isLoading: postCommentLoading,
  } = usePost();
  const param = useParams();
  const { accessToken } = useAuthStore();

  const handleCommentSubmit = () => {
    const body = {
      remark: justification,
      action: 'Rejected',
    };

    async function fetch() {
      await postComment(
        accessToken as string,
        `approvals/${approvalId}/action`,
        body,
      );
    }
    fetch();
  };

  useEffect(() => {
    if (postCommentResponse) {
      setJustification('');
      setOpenJustificationModal(false);
      handleRejectMfa(postCommentResponse);
    }
  }, [postCommentResponse]);
  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>Justification </DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Description <span className="text-[#F55D5D]">*</span>
        </div>
        <Textarea
          placeholder="Start Typing..."
          value={justification}
          onChange={(e) => setJustification(e.target.value)}
        />
        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text="Submit"
            isLoading={postCommentLoading}
            onClick={handleCommentSubmit}
            disabled={justification === ''}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default JustificationModal;
