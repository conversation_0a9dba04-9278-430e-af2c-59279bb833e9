import dynamic from "next/dynamic";
import { useParams } from "next/navigation";
import React, { useEffect } from "react";

import { useAuthStore } from "@/globalProvider/authStore";
import useFetch from "@/hooks/useFetch";
import { usePost } from "@/hooks/usePost";
import { IUser } from "@/interfaces/user";

import PrimaryButton from "../../../common/button/primaryButton";
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "../../../common/dialog";

const ReactMention = dynamic(() => import("../../../react-mention"), {
  ssr: false, // Disable server-side rendering for this component
});

interface IProps {
  setOpenResponse: React.Dispatch<React.SetStateAction<boolean>>;
  refetchComments: () => void;
}
const AddResponseModal = ({ setOpenResponse, refetchComments }: IProps) => {
  const [comment, setComment] = React.useState("");
  const {
    postData: postComment,
    response: postCommentResponse,
    isLoading: postCommentLoading,
  } = usePost();
  const param = useParams();
  const { accessToken } = useAuthStore();
  const { data: users, isLoading: usersLoading } = useFetch<{
    records: IUser[];
  }>(accessToken, `users`, {});

  const handleCommentSubmit = () => {
    const body = {
      description: comment,
    };

    async function fetch() {
      await postComment(
        accessToken as string,
        `documents/${param?.documentId}/comments`,
        body
      );
    }
    fetch();
  };

  useEffect(() => {
    if (postCommentResponse) {
      setComment("");
      setOpenResponse(false);
      setTimeout(() => {
        refetchComments();
      }, 2000);
    }
  }, [postCommentResponse]);
  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>Add response </DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Response <span className="text-[#F55D5D]">*</span>
        </div>
        <ReactMention
          comment={comment}
          setComment={setComment}
          data={users?.records?.map((user) => ({
            id: user.id,
            display: user.full_name,
          }))}
        />
        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text="Add"
            isLoading={postCommentLoading}
            onClick={handleCommentSubmit}
            disabled={comment === ""}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default AddResponseModal;
