import React, { useEffect, useState } from 'react';

import PrimaryButton from '../../../common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../common/dialog';
import { Label } from '@/components/common/label';
import { OtpInput } from 'reactjs-otp-input';
import { Checkbox } from '@/components/common/checkbox';
import LinkButton from '@/components/common/button/linkButton';
import { usePost } from '@/hooks/usePost';
import { useAuthStore } from '@/globalProvider/authStore';
import { toast } from 'react-toastify';
import { MaskEmail } from '@/utils/maskEmail';
import { AxiosError } from 'axios';

interface IProps {
  setOtpModal: React.Dispatch<React.SetStateAction<boolean>>;
  sessionId: string;
  refetchDocumentData: () => void;
}

interface ErrorResponse {
  error: string;
}

const OtpModal = ({ setOtpModal, sessionId, refetchDocumentData }: IProps) => {
  const [otp, setOtp] = useState('');
  const [isChecked, setIsChecked] = useState(false);

  const user = useAuthStore((state) => state.user);

  const { postData, response, isLoading, error } = usePost();
  const {
    postData: otpPostData,
    response: otpPostResponse,
    isLoading: otpPostIsLoading,
    error: otpPostError,
  } = usePost();

  const { accessToken } = useAuthStore();

  const handleSubmit = () => {
    const body = {
      session_id: sessionId,
      code: otp,
    };
    async function fetch() {
      await postData(accessToken as string, `mfa/validate`, body);
    }
    fetch();
  };

  const handleChange = (otp: any) => setOtp(otp);

  const handleResendOtp = () => {
    const body = {
      session_id: sessionId,
    };

    async function fetch() {
      await otpPostData(accessToken as string, `mfa/resend`, body);
    }
    fetch();
  };

  useEffect(() => {
    if (response) {
      setOtpModal(false);
      toast.success((response as { msg: string })?.msg);
      refetchDocumentData();
    }
    if (error) {
      toast.error(
        ((error as AxiosError).response?.data as ErrorResponse)?.error ||
          'An error occurred',
      );
    }
  }, [response, error]);

  useEffect(() => {
    if (otpPostResponse) {
      toast.success((otpPostResponse as { message: string })?.message);
    }
    if (otpPostError) {
      toast.error(
        ((otpPostError as AxiosError).response?.data as ErrorResponse)?.error ||
          'An error occurred',
      );
    }
  }, [otpPostResponse, otpPostError]);

  return (
    <DialogContent
      onClick={(e) => e.preventDefault()}
      className="min-w-[45.438rem]"
    >
      <DialogHeader>
        <DialogTitle>Verify OTP</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <Label
          htmlFor="origin"
          className="text-base font-medium leading-6 text-dark-100"
        >
          We have sent an OTP to your email{' '}
          <MaskEmail email={user?.email || ''} />. Please verify the OTP
        </Label>
        <OtpInput
          containerStyle={'mt-4'}
          inputStyle={
            'border-b-2 mx-2 border-grey-100 focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed   font-medium leading-[1.6rem] placeholder:text-grey-200 text-dark-300'
          }
          value={otp}
          onChange={handleChange}
          numInputs={6}
          separator={<span className="text-grey-100">-</span>}
        />

        <div className="flex items-center gap-4 mt-6">
          <Checkbox
            id="notify"
            checked={isChecked}
            onCheckedChange={(checked) => setIsChecked(checked as boolean)}
          />
          <label
            htmlFor="notify"
            className="text-sm font-medium leading-6 text-dark-300"
          >
            By clicking ‘Submit’ I, <strong>{user?.full_name}</strong>, affirm
            that this action constitutes my electronic signature and is legally
            binding under FDA 21 CFR Part 11.
          </label>
        </div>
        <div className="flex items-center justify-end gap-8 mt-2">
          <div>
            <LinkButton
              text="Resend OTP"
              size={'small'}
              isLoading={otpPostIsLoading}
              onClick={() => handleResendOtp()}
            />
          </div>
          <div>
            <PrimaryButton
              size="medium"
              text="Submit"
              disabled={otp.length < 6 || !isChecked}
              isLoading={isLoading}
              onClick={handleSubmit}
            />
          </div>
        </div>
      </div>
    </DialogContent>
  );
};

export default OtpModal;
