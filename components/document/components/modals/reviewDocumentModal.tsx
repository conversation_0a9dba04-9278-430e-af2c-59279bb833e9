import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { motion } from 'framer-motion';

import PrimaryButton from '../../../common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../common/dialog';
import { cn } from '@/utils/styleUtils';
import { Textarea } from '@/components/common/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import moment from 'moment';
import DocumentIcon from '@/assets/outline/document';
import Link from 'next/link';
import UploadIcon from '@/assets/outline/upload';
import { toast } from 'react-toastify';

const periodMap: Record<
  'Monthly' | 'Quarterly' | 'Half Yearly' | 'Annually',
  number
> = {
  Monthly: 1,
  Quarterly: 3,
  'Half Yearly': 6,
  Annually: 12,
};

type ReviewPeriod = keyof typeof periodMap;

interface IProps {
  setReviewPeriodModal: React.Dispatch<React.SetStateAction<boolean>>;
  refetchDocumentData: () => void;
  title: string;
  version: number;
  extension: string;
  filePath: string;
  documentVersionId: string;
  publishDate: string;
}
const ReviewDocumentModal = ({
  setReviewPeriodModal,
  refetchDocumentData,
  title,
  version,
  extension,
  filePath,
  documentVersionId,
  publishDate,
}: IProps) => {
  const [reviewComments, setReviewComments] = React.useState('');
  const [selectedOption, setSelectionOption] = React.useState('No');

  const [reviewPeriod, setReviewPeriod] = useState('');
  const { postData, response, isLoading } = usePost();
  const param = useParams();
  const { accessToken } = useAuthStore();

  const handleSubmit = () => {
    const body = {
      document_version_id: documentVersionId,
      review_period: reviewPeriod,
      comment: reviewComments,
    };

    async function fetch() {
      await postData(
        accessToken as string,
        `documents/${param?.documentId}/review`,
        body,
      );
    }
    fetch();
  };

  useEffect(() => {
    if (response) {
      setReviewComments('');
      setReviewPeriod('');
      setReviewPeriodModal(false);
      toast.success('Document Reviewed successfully');
      setTimeout(() => {
        refetchDocumentData();
      }, 2000);
    }
  }, [response]);

  const handleChange = (value: string) => {
    setSelectionOption(value);
  };

  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>
          Reviewing Document <strong>{title}</strong>
        </DialogTitle>
      </DialogHeader>
      <div className={cn('space-y-4 w-full max-w-4xl mt-2')}>
        <h3 className="text-base font-medium text-dark-100">
          Does this document require changes?
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <RadioOption
            type="No"
            title="No changes needed"
            selected={selectedOption === 'No'}
            onChange={handleChange}
          />
          <RadioOption
            type="Yes"
            title="Yes, requires changes"
            selected={selectedOption === 'Yes'}
            onChange={handleChange}
          />
        </div>
      </div>

      <div>
        {selectedOption === 'Yes' ? (
          <>
            <div className="mt-2">
              <div className="flex gap-4">
                <Link
                  href={`${param?.documentId}/upload-document`}
                  className="w-64 h-36 flex-1"
                >
                  <div className="w-full h-full flex flex-col items-center justify-center rounded-2xl bg-gray-50 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                    <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4">
                      <UploadIcon className="w-6 h-6 text-gray-300" />
                    </div>
                    <h3 className="text-base font-medium text-dark-300 mb-1">
                      Upload document
                    </h3>
                    <p className="text-xs font-semibold text-gray-400 mt-1 text-center">
                      PDF, DOCX, XLSX, PPTX (Max 10MB)
                    </p>
                  </div>
                </Link>

                <Link
                  href={
                    (version ?? 0) > 0 && extension === 'html'
                      ? `/document/${param.documentId}/create-document?file_path=${filePath}`
                      : `/document/${param?.documentId}/create-document`
                  }
                  className="w-64 h-36 flex-1"
                >
                  <div className="w-full h-full flex flex-col items-center justify-center rounded-2xl bg-gray-50 shadow-sm hover:shadow-md transition-shadow cursor-pointer">
                    <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 mb-4">
                      <DocumentIcon className="w-6 h-6 text-gray-300" />
                    </div>
                    <h3 className="text-base font-medium text-dark-300 mb-1">
                      Create with editor
                    </h3>
                    <p className="text-xs font-semibold text-gray-400 mt-1 text-center">
                      Create a document in our inbuilt editor
                    </p>
                  </div>
                </Link>
              </div>
            </div>
          </>
        ) : (
          <>
            <div>
              <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
                Review Comments{' '}
              </div>
              <Textarea
                placeholder="Comments"
                value={reviewComments}
                onChange={(e) => setReviewComments(e.target.value)}
              />
            </div>

            <div className="mt-3">
              <div className="mb-3 flex gap-4 justify-between text-base text-dark-300 leading-6 font-medium">
                <p className="text-dark-100 font-medium text-base leading-6 whitespace-nowrap">
                  Review Period
                  <span className="text-red-200">*</span>
                </p>
                <p className="text-dark-100 text-base font-medium">
                  Next review date:{' '}
                  {reviewPeriod ? (
                    <span className="text-grey-300">
                      {moment()
                        .add(
                          periodMap[reviewPeriod as ReviewPeriod] || 0,
                          'months',
                        )
                        .format('MMM D YYYY')}{' '}
                    </span>
                  ) : (
                    '---'
                  )}
                </p>
              </div>
              <Select
                value={reviewPeriod}
                onValueChange={(value: ReviewPeriod) => {
                  setReviewPeriod(value);
                }}
              >
                <SelectTrigger id="review">
                  <SelectValue placeholder="---" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Monthly">Monthly</SelectItem>
                  <SelectItem value="Quarterly">Quarterly</SelectItem>
                  <SelectItem value="Half Yearly">Half Yearly</SelectItem>
                  <SelectItem value="Annually">Annually</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="mt-2">
              <div className="flex justify-end mt-6">
                <PrimaryButton
                  size="medium"
                  text="Submit"
                  isLoading={isLoading}
                  onClick={handleSubmit}
                />
              </div>
            </div>
          </>
        )}
      </div>
    </DialogContent>
  );
};

export type OptionType = 'Yes' | 'No';

interface RadioOptionProps {
  type: OptionType;
  title: string;
  selected: boolean;
  onChange: (type: OptionType) => void;
}

const RadioOption: React.FC<RadioOptionProps> = ({
  type,
  title,
  selected,
  onChange,
}) => {
  return (
    <motion.div
      whileTap={{ scale: 0.98 }}
      className={cn(
        'approval-option px-6 py-5 flex items-end gap-4 rounded-xl cursor-pointer',
        'bg-gray-50 hover:bg-gray-100/80 border border-approval-border',
        'transition-colors duration-200 ease-out',
      )}
      onClick={() => onChange(type)}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, ease: 'easeOut' }}
    >
      <div className="relative flex-shrink-0 mt-[3px]">
        <div
          className={cn(
            'w-5 h-5 rounded-full border-2 flex items-center justify-center',
            selected ? 'border-approval-selected' : 'border-gray-300',
          )}
        >
          {selected && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="w-2.5 h-2.5 rounded-full bg-approval-selected"
              transition={{ duration: 0.2, ease: [0.175, 0.885, 0.32, 1.1] }}
            />
          )}
        </div>
      </div>
      <div className="space-y-1 flex-grow">
        <h4
          className={cn(
            'approval-title text-base font-medium leading-tight',
            selected
              ? 'text-approval-text-primary'
              : 'text-approval-text-primary',
          )}
        >
          {title}
        </h4>
      </div>
    </motion.div>
  );
};

export default ReviewDocumentModal;
