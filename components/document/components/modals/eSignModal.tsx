import React, { useEffect, useState } from 'react';

import PrimaryButton from '../../../common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../common/dialog';
import { Label } from '@/components/common/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { Checkbox } from '@/components/common/checkbox';

interface IProps {
  setESignModal: React.Dispatch<React.SetStateAction<boolean>>;
  setOtpModal: React.Dispatch<React.SetStateAction<boolean>>;
  documentName: string;
  created: string;
  documentId: string;
}
const ESignModal = ({
  setESignModal,
  setOtpModal,
  documentName,
  created,
  documentId,
}: IProps) => {
  const [isChecked, setIsChecked] = useState(false);
  const [reason, setReason] = useState('');

  const handleSubmit = () => {
    console.log(reason, isChecked);

    setESignModal(false);
    setOtpModal(true);
  };

  return (
    <DialogContent className="min-w-[28.5rem]">
      <DialogHeader>
        <DialogTitle>
          Do you want to securely E-Sign this Document?{' '}
        </DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="p-2 border flex flex-col gap-4 border-white-300 bg-white-100 px-2.5 py-2 rounded-lg">
          <div className="flex justify-between items-center">
            <div className="text-sm font-medium leading-5 text-grey-300">
              ID
            </div>
            <div className="text-base font-medium leading-6 text-dark-300">
              {documentId || '-'}
            </div>
          </div>
          <div className="flex justify-between items-center">
            <div className="text-sm font-medium leading-5 text-grey-300">
              Document Name
            </div>
            <div className="text-base font-medium leading-6 text-dark-300">
              {documentName || '-'}
            </div>
          </div>
          <div className="flex justify-between items-center">
            <div className="text-sm font-medium leading-5 text-grey-300">
              Created By
            </div>
            <div className="text-base font-medium leading-6 text-dark-300">
              {created || '-'}
            </div>
          </div>
        </div>

        <div className="mt-4">
          <Label
            htmlFor="reason"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Signing Reason
          </Label>
          <Select
            value={reason}
            onValueChange={(value) => {
              setReason(value);
            }}
          >
            <SelectTrigger className={'my-2'} id="reason">
              <SelectValue placeholder="Reason" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="1">I want to approve this document</SelectItem>
              <SelectItem value="2">I have reviewed this document</SelectItem>
              <SelectItem value="3">I am the owner this document</SelectItem>
              <SelectItem value="4">I want to reject this document</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="flex items-center justify-end gap-8 mt-6">
          <div className="flex items-center gap-2">
            <Checkbox
              id="notify"
              checked={isChecked}
              onCheckedChange={(checked) => setIsChecked(checked as boolean)}
            />
            <label
              htmlFor="notify"
              className="text-base font-medium leading-6 text-dark-300"
            >
              I agree, lorem ipusum{' '}
            </label>
          </div>
          <div>
            <PrimaryButton
              size="medium"
              text="Send OTP"
              disabled={isChecked === false || reason === ''}
              onClick={handleSubmit}
            />
          </div>
        </div>
      </div>
    </DialogContent>
  );
};

export default ESignModal;
