import React, { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  IOption,
  ReactSelectMulti,
} from '@/components/common/multiSelectInput';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
import { ICategory } from '@/interfaces/category';
import { IDepartment } from '@/interfaces/department';
import { IDocumentDetails } from '@/interfaces/document';
import { IProcess } from '@/interfaces/process';
import { IUser } from '@/interfaces/user';
import { transformList } from '@/utils/general';
import { removeEmptyFields } from '@/utils/removeEmptyFields';
import Calendar from '@/components/common/calendar';
import moment from 'moment';

const optionSchema = z.object({
  label: z.string().optional(),
  value: z.string(),
});

export const createDocumentSchema = {
  title: z.string().nonempty('Title is required'),
  processes: z.array(optionSchema).min(1, 'At least one process is required'),
  assignees: z.array(optionSchema).min(1, 'At least one assignee is required'),
};

interface IData extends Record<string, unknown> {
  doc_id: string;
  title: string;
  department: string;
  category: string;
  processes: IOption[];
  assignees: IOption[];
  approvers: IOption[];
  origin: string;
  review_period: string;
}

const CreateDocumentModal = ({
  edit = false,
  prefill,
  open,
  setOpenEdit,
  reFetch,
}: {
  edit?: boolean;
  prefill?: IDocumentDetails;
  open: boolean;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  reFetch?: () => void;
}) => {
  const { accessToken } = useAuthStore();
  const [titleAlreadyExists, setTitleAlreadyExists] = useState(false);
  const [data, setData] = useState<IData>({
    doc_id: '',
    title: '',
    department: '',
    category: '',
    processes: [],
    assignees: [],
    approvers: [],
    origin: '',
    review_period: '',
  });

  const { data: users } = useFetch<
    { records: IUser[] },
    { can_document_edit: boolean }
  >(accessToken, `users`, {
    can_document_edit: true,
  });
  const { data: departments } = useFetch<{
    records: IDepartment[];
  }>(accessToken, `departments`, {});
  const { data: categories } = useFetch<{
    records: ICategory[];
  }>(accessToken, `categories`, {});
  const { data: processes } = useFetch<{
    records: IProcess[];
  }>(accessToken, `processes`, {});
  const {
    putData,
    isLoading: editLoading,
    response: updateResponse,
    error: updateError,
  } = usePut();

  const {
    postData,
    isLoading: submitLoading,
    error: submitError,
    response: submitResponse,
  } = usePost<{ response: boolean }, any>();

  const userData = users?.records?.map((e) => ({
    label: e.full_name,
    value: e.id,
  })) as IOption[];

  const processesData = processes?.records?.map((e) => ({
    label: e.name,
    value: e.id,
  })) as IOption[];
  const { validationErrors, startValidation, reset } = useValidators({
    schemas: createDocumentSchema,
    values: data,
  });

  const handleSubmit = async () => {
    const { hasValidationErrors } = await startValidation();

    if (!hasValidationErrors) {
      console.log('Form submitted:', data);
      setTitleAlreadyExists(false);
      if (edit && prefill) {
        const payload = {
          document_info: {
            doc_id: data.doc_id,
            title: data.title,
            department: data.department,
            category: data.category,
            origin: data.origin,
            review_period: data.review_period,
          },
          processes: data.processes.map((option) => option.value),
          assignees: data.assignees.map((option) => option.value),
          approvers: data.approvers.map((option) => option.value),
        };
        await putData(
          accessToken as string,
          `documents/${prefill.id}`,
          payload,
        );
      } else {
        const payload = {
          ...data,
          processes: data.processes.map((option) => option.value),
          assignees: data.assignees.map((option) => option.value),
          approvers: data.approvers.map((option) => option.value),
        };
        await postData(
          accessToken as string,
          'documents',
          removeEmptyFields(payload),
        );
      }
    } else {
    }
  };

  useEffect(() => {
    if (edit && prefill && open) {
      setData({
        doc_id: prefill?.doc_id,
        title: prefill?.title,
        department: prefill?.department?.id,
        category: prefill?.category?.id,
        processes: transformList(prefill?.processes),
        assignees: transformList(prefill?.assignees),
        approvers: transformList(prefill?.approvers),
        origin: prefill?.origin,
        review_period: prefill?.review_period?.toString(),
      });
    }
  }, [prefill, edit, open]);

  useEffect(() => {
    if (
      (updateError || submitError) &&
      (updateError?.response?.status === 400 ||
        submitError?.response?.status === 400)
    ) {
      setTitleAlreadyExists(true);
    }
  }, [updateError, submitError]);

  useEffect(() => {
    if (submitResponse || updateResponse) {
      if (reFetch) reFetch();
      if (setOpenEdit) setOpenEdit(false);
      setTitleAlreadyExists(false);
    }
  }, [submitResponse, updateResponse]);

  useEffect(() => {
    if (!open) {
      setData({
        doc_id: '',
        title: '',
        department: '',
        category: '',
        processes: [],
        assignees: [],
        approvers: [],
        origin: '',
        review_period: '',
      });
      setTitleAlreadyExists(false);
      reset();
    }
  }, [open]);

  return (
    <DialogContent className="min-w-[55vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle>{edit ? 'Edit' : 'Create'} Document</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="documentId"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Document ID
            </Label>
            <Input
              placeholder="Document ID"
              autoFocus={false}
              id="documentId"
              value={data?.doc_id}
              onChange={(e) =>
                setData((pre) => ({ ...pre, doc_id: e.target.value }))
              }
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="title"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Title<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Title"
              id="title"
              value={data?.title}
              onChange={(e) => {
                setData((pre) => ({ ...pre, title: e.target.value }));
                if (titleAlreadyExists) setTitleAlreadyExists(false);
              }}
              errorMsg={
                validationErrors?.title[0] ||
                (titleAlreadyExists
                  ? `Document having title ${data?.title} already exists`
                  : undefined)
              }
            />
          </div>
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="department"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Department
            </Label>
            <Select
              value={data?.department}
              onValueChange={(value) =>
                setData((pre) => ({ ...pre, department: value }))
              }
            >
              <SelectTrigger className={''} id="department">
                <SelectValue placeholder="Department type" />
              </SelectTrigger>
              <SelectContent>
                {departments?.records?.map((e, i) => (
                  <SelectItem value={e.id} key={i}>
                    {e.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="category"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Category
            </Label>
            <Select
              value={data?.category}
              onValueChange={(value) =>
                setData((pre) => ({ ...pre, category: value }))
              }
            >
              <SelectTrigger className={''} id="category">
                <SelectValue placeholder="Category type" />
              </SelectTrigger>
              <SelectContent>
                {categories?.records?.map((e, i) => (
                  <SelectItem value={e.id} key={i}>
                    {e.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="origin"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Origin
            </Label>
            <Select
              value={data.origin}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, origin: value }));
              }}
            >
              <SelectTrigger className={''} id="origin">
                <SelectValue placeholder="Origin type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Internal">Internal</SelectItem>
                <SelectItem value="External">External</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="flex flex-col gap-2.5 flex-1 mb-5">
          <Label
            htmlFor="title"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Process<span className="text-red-200">*</span>
          </Label>
          <ReactSelectMulti
            value={data.processes}
            options={processesData}
            placeholder="Select processes"
            onChange={(value) => {
              setData((pre) => ({
                ...pre,
                processes: value as IOption[],
              }));
            }}
            hasError={Boolean(validationErrors?.processes[0])}
          />
          {validationErrors?.processes[0] ? (
            <div className="text-xs font-semibold leading-5 text-left text-red-200">
              Process is required
            </div>
          ) : (
            <></>
          )}
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="assignees"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Assignees<span className="text-red-200">*</span>
            </Label>
            <ReactSelectMulti
              value={data.assignees}
              options={userData}
              placeholder="Select Assignees"
              onChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  assignees: value as IOption[],
                }));
              }}
              hasError={Boolean(validationErrors?.assignees[0])}
            />
            {validationErrors?.assignees[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                Assignee is required
              </div>
            ) : (
              <></>
            )}
          </div>
          {/* <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="approvers"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Approvers
            </Label>
            <ReactSelectMulti
              value={data.approvers}
              options={userData}
              placeholder="Select Approvers"
              onChange={(value) => {
                setData((pre) => ({
                  ...pre,
                  approvers: value as IOption[],
                }));
              }}
            />
          </div> */}
        </div>
        {/* <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="origin"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Origin
            </Label>
            <Select
              value={data.origin}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, origin: value }));
              }}
            >
              <SelectTrigger className={''} id="origin">
                <SelectValue placeholder="Origin type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Internal">Internal</SelectItem>
                <SelectItem value="External">External</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="review"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Review period
            </Label>
            <Select
              value={data.review_period}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, review_period: value }));
              }}
            >
              <SelectTrigger className={''} id="review">
                <SelectValue placeholder="Review type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1">Monthly</SelectItem>
                <SelectItem value="3">Quarterly</SelectItem>
                <SelectItem value="6">Half Yearly</SelectItem>
                <SelectItem value="12">Annually</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div> */}

        <div className="flex justify-end mt-6">
          <PrimaryButton
            size="medium"
            text="Submit"
            onClick={handleSubmit}
            isLoading={submitLoading || editLoading}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default CreateDocumentModal;
