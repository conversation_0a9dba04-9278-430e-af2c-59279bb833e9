import axios from 'axios';
import dynamic from 'next/dynamic';
import { useParams, useSearchParams } from 'next/navigation';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';

import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { IDocumentDetails } from '@/interfaces/document';
import BackButton from '@/assets/backButton.svg';

import Breadcrumb from '../common/breadcrumb';
import PrimaryButton from '../common/button/primaryButton';
import SecondaryButton from '../common/button/secondaryButton';
import { Dialog, DialogTrigger } from '../common/dialog';
import { Input } from '../common/input';
import Loader from '../common/loader';
import ConfirmModal from '../common/modals/confirmModal';
// import DocumentEditor from "../common/ckEditor";
import Layout from '../common/sidebar/layout';
import Status from '../common/status';
import { Tooltip, TooltipContent, TooltipTrigger } from '../common/tooltip';
import Link from 'next/link';
import Image from 'next/image';
import { Label } from '../common/label';
import moment from 'moment';
import InfoCircle from '@/assets/outline/infoCircle';
import ToggleSwitch from '../common/toogleSwitch';
import AddApprovers from './addApprovers';
import ApprovalTypeSelector, { ApprovalType } from './approvalTypeSelector';
import Calendar from '../common/calendar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../common/select';
import CheckIcon from '@/assets/outline/check';
import { cn } from '@/utils/styleUtils';
import { toast } from 'react-toastify';
import { z } from 'zod';
import useValidators from '@/hooks/useValidator';
import CkEditor from '../common/ckEditor';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import { usePut } from '@/hooks/usePut';

const periodMap: Record<
  'Monthly' | 'Quarterly' | 'Half Yearly' | 'Annually',
  number
> = {
  Monthly: 1,
  Quarterly: 3,
  'Half Yearly': 6,
  Annually: 12,
};

type ReviewPeriod = keyof typeof periodMap;

export const uploadDocumentSchema = (currentVersion?: number) => ({
  version: z
    .string()
    .nonempty({ message: 'Version is required' })
    .refine(
      (val) => {
        const num = parseFloat(val);
        return !isNaN(num) && num >= 0;
      },
      {
        message: 'Version must be a valid number',
      },
    )
    .refine(
      (val) => {
        const num = parseFloat(val);
        if (isNaN(num)) return false;
        if (currentVersion == null) return true;
        return num > currentVersion;
      },
      {
        message:
          currentVersion != null
            ? `Version must be greater than ${currentVersion}`
            : 'Version must be 0 or greater',
      },
    ),
});

export const draftDocumentSchema = () => ({
  version: z
    .string()
    .optional()
    .refine(
      (val) => {
        if (!val || val === '') return true;
        const num = parseFloat(val);
        return !isNaN(num) && num >= 0;
      },
      {
        message: 'Version must be a valid number',
      },
    ),
});

const CreateDocumentComponent = () => {
  const param = useParams();
  const searchParam = useSearchParams();
  const router = useRouter();
  const [editorData, setEditorData] = useState('');
  const [versionNumber, setVersionNumber] = useState('');
  const [loading, setLoading] = useState(false);
  const [draftLoading, setDraftLoading] = useState(false);
  const [showVersionModal, setShowVersionModal] = useState(false);
  const { postData, response, isLoading, error } = usePost();
  const [versionError, setVersionError] = useState<string | undefined>();
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  const [approvalType, setApprovalType] = useState<ApprovalType>('Sequential');
  const [publishDate, setPublishDate] = useState(moment().format('YYYY-MM-DD'));
  const [nextReviewDate, setNextReviewDate] = useState<string>('');
  const [requireApproval, setRequireApproval] = useState<boolean>(true);
  const [version, setVersion] = useState('');
  const [approversData, setApproversData] = useState([]);
  const [existingApprovers, setExistingApprovers] = useState([]);
  const [reviewPeriod, setReviewPeriod] = useState('');
  const [customReviewDate, setCustomReviewDate] = useState(false);

  const [editorError, setEditorError] = useState(false);

  const [reviewPeriodError, setReviewPeriodError] = useState(false);
  const [approverError, setApproverError] = useState(false);

  const { accessToken } = useAuthStore();
  const { data, isLoading: documentLoading } = useFetch<{
    record: IDocumentDetails;
  }>(accessToken, `documents/${param?.documentId}`, {});
  const filePath = searchParam.get('file_path');

  const { data: fileData, reFetch } = useFetch<{
    expires_in: number;
    file_path: string;
    url: string;
  }>(
    accessToken,
    filePath
      ? `file/presigned-url?file_path=${encodeURIComponent(
          filePath,
        )}&expiration=800`
      : undefined,
    {},
  );

  const {
    putData,
    isLoading: isPutting,
    response: putResponse,
    error: putError,
  } = usePut<any, Record<string, string>>();

  const breadcrumbData = [
    {
      name: 'Document Hub',
      link: '/document',
    },
    {
      name: 'Document View',
      link: `/document/${param?.documentId}`,
    },
    {
      name: 'Create new document',
      link: '#',
    },
  ];

  const baseUrl = process.env.NEXT_PUBLIC_URL;
  const productVersion = process.env.NEXT_PUBLIC_VERSION;

  const [formState, setFormState] = useState({
    version: '',
    editorData: '',
  });

  const currentVersion =
    data?.record?.document_version?.version_number ?? undefined;

  const { validationErrors, startValidation, reset } = useValidators({
    schemas: uploadDocumentSchema(currentVersion),
    values: formState,
  });

  const {
    validationErrors: draftValidationErrors,
    startValidation: startDraftValidation,
  } = useValidators({
    schemas: draftDocumentSchema(),
    values: formState,
  });

  // Track unsaved changes
  useEffect(() => {
    if (
      editorData ||
      formState.version ||
      approversData.length > 0 ||
      reviewPeriod
    ) {
      setHasUnsavedChanges(true);
    }
  }, [editorData, formState.version, approversData, reviewPeriod]);

  // Prevent navigation with unsaved changes
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue =
          'You have unsaved changes. Are you sure you want to leave?';
        return 'You have unsaved changes. Are you sure you want to leave?';
      }
    };

    const handleRouteChange = (url: string) => {
      if (hasUnsavedChanges && !url.includes('/document')) {
        const confirmLeave = window.confirm(
          'You have unsaved changes. Are you sure you want to leave?',
        );
        if (!confirmLeave) {
          router.events.emit('routeChangeError');
          throw 'Route change aborted';
        }
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    router.events.on('routeChangeStart', handleRouteChange);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      router.events.off('routeChangeStart', handleRouteChange);
    };
  }, [hasUnsavedChanges, router]);

  const handleSaveAsDraft = async () => {
    const { hasValidationErrors } = await startDraftValidation();

    if (!hasValidationErrors) {
      setDraftLoading(true);
      try {
        let filePath = '';
        let fileExtension = '';

        // Only upload file if there's editor data
        if (editorData && editorData.trim()) {
          const blob = new Blob([editorData], { type: 'text/html' });
          const formData = new FormData();

          const url = `${baseUrl}/${productVersion}/file/upload?document_for=document_hub&sub_path=draft`;
          const orgId =
            typeof window !== 'undefined'
              ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
              : null;
          formData.append(
            'file',
            new File([blob], `${param?.documentId}_draft.html`, {
              type: 'text/html',
            }),
          );

          const response = await axios.post(url, formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: `Bearer ${accessToken}`,
              ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
            },
          });

          filePath = response.data.file_path;
          fileExtension = response.data.file_ext;
        }

        // Save draft with minimal required data
        const body = {
          ...(filePath && {
            file_path: filePath,
            file_extension: fileExtension,
          }),
          ...(formState.version && { version_number: formState.version }),
          ...(requireApproval &&
            approversData.length > 0 && {
              approval_request: {
                flow: approvalType,
                approvers: approversData.map((user: any, index: number) => ({
                  sequence_number: index + 1,
                  user_id: user?.user_id,
                })),
              },
            }),
          ...(!requireApproval &&
            reviewPeriod && {
              review_period: reviewPeriod,
              ...(nextReviewDate && { next_review_date: nextReviewDate }),
            }),
        };

        await putData(
          accessToken as string,
          `documents/${param.documentId}/save-draft`,
          body as Record<string, string>,
        );

        setHasUnsavedChanges(false);
        toast.success('Document saved as draft successfully');
        router.push(`/document/${param.documentId}`);
      } catch (error) {
        console.error('Error saving draft:', error);
        toast.error('Failed to save draft');
      } finally {
        setDraftLoading(false);
      }
    }
  };

  const handleSendForApproval = async () => {
    const { hasValidationErrors } = await startValidation();

    // Reset all validation errors first
    setApproverError(false);
    setReviewPeriodError(false);
    setEditorError(false);

    let hasError = false;

    // Validate required fields for approval/publish
    if (!editorData || editorData.trim() === '') {
      setEditorError(true);
      hasError = true;
    }

    if (!requireApproval && (!reviewPeriod || reviewPeriod.trim() === '')) {
      setReviewPeriodError(true);
      hasError = true;
    }

    if (requireApproval && (!approversData || approversData.length === 0)) {
      setApproverError(true);
      hasError = true;
    }

    if (!hasValidationErrors && !hasError) {
      setLoading(true);
      try {
        if (!editorData.trim()) {
          return;
        }

        // Create a Blob for the HTML file
        const blob = new Blob([editorData], { type: 'text/html' });

        const formData = new FormData();

        const url = `${baseUrl}/${productVersion}/file/upload?document_for=document_hub&sub_path=v${formState.version}`;
        const orgId =
          typeof window !== 'undefined'
            ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
            : null;
        formData.append(
          'file',
          new File([blob], `${param?.documentId}.html`, { type: 'text/html' }),
        );

        // Upload the HTML file to the server
        const response = await axios.post(url, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
            Authorization: `Bearer ${accessToken}`,
            ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
          },
        });
        if (requireApproval) {
          submitWithApprovers(response.data, formState.version);
        } else {
          submitWithOutApprovers(response.data, formState.version);
        }
      } catch (error) {
        console.error('Error uploading file:', error);
        setLoading(false);
      }
    }
  };

  const submitWithApprovers = (
    url: { file_path: string; file_ext: string },
    version: string,
  ) => {
    const body = {
      file_path: url.file_path,
      file_extension: url.file_ext,
      version: version,
      approval_request: {
        flow: approvalType,
        approvers: approversData.map((user: any, index: number) => ({
          sequence_number: index + 1,
          user_id: user?.user_id,
        })),
      },
    };

    async function fetch() {
      await postData(
        accessToken as string,
        `documents/${param.documentId}/send-for-approval`,
        body,
      );
    }
    fetch();
  };

  const submitWithOutApprovers = (
    url: { file_path: string; file_ext: string },
    version: string,
  ) => {
    const body = {
      file_path: url.file_path,
      file_extension: url.file_ext,
      version: formState.version,
      review_period: reviewPeriod,
      ...(data?.record && !data.record.document_version
        ? { next_review_date: nextReviewDate }
        : {}),
    };

    async function fetch() {
      await postData(
        accessToken as string,
        `documents/${param.documentId}/publish`,
        body,
      );
    }
    fetch();
  };

  const handleBackNavigation = () => {
    if (hasUnsavedChanges) {
      const confirmLeave = window.confirm(
        'You have unsaved changes. Are you sure you want to leave?',
      );
      if (confirmLeave) {
        setHasUnsavedChanges(false);
        router.push(`/document/${param.documentId}`);
      }
    } else {
      router.push(`/document/${param.documentId}`);
    }
  };

  useEffect(() => {
    setApproverError(false);
    setReviewPeriodError(false);
    setEditorError(false);
  }, [approversData, reviewPeriod, editorData]);

  useEffect(() => {
    if (error) {
      setLoading(false);
      setShowVersionModal(false);
    }
    if (response) {
      setLoading(false);
      setShowVersionModal(false);
      setEditorData('');
      setHasUnsavedChanges(false);

      toast.success('Document uploaded successfully');
      router.push(`/document/${param.documentId}`);
    }
  }, [response, error]);

  useEffect(() => {
    if (filePath && !fileData) {
      reFetch();
    }
  }, [filePath]);

  useEffect(() => {
    if (data && !documentLoading) {
      setExistingApprovers(data?.record?.approval?.approvers || []);
      setApprovalType(
        (data?.record?.approval?.flow as ApprovalType) || 'Sequential',
      );
      setFormState((pre) => ({
        ...pre,
        version:
          pre.version ||
          (() => {
            // If document_version exists, increment it by 1
            if (data?.record?.document_version?.version_number != null) {
              return String(data.record.document_version.version_number + 1);
            }

            // If approval payload version exists, use it
            if (data?.record?.approval?.payload?.version != null) {
              return String(data.record.approval.payload.version);
            }

            // If draft version exists, use it
            if (data?.record?.draft?.version_number != null) {
              return String(data.record.draft.version_number);
            }

            // Default to version 1 if nothing exists
            return '1';
          })(),
      }));
    }
  }, [data]);

  function transformApprovers(approvers: any) {
    return approvers?.map((approver: any, index: number) => ({
      sequence_number: index + 1,
      user: { ...approver },
    }));
  }

  useEffect(() => {
    if (
      existingApprovers.length === 0 &&
      (data?.record?.approvers?.length ?? 0) > 0
    ) {
      const exisitng = transformApprovers(data?.record?.approvers);
      setExistingApprovers(exisitng);
    }
  }, [data]);

  return (
    <Layout>
      {documentLoading ? (
        <Loader />
      ) : (
        <div className="flex flex-col flex-1">
          <div className="my-5">
            <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-5">
              <Tooltip>
                <TooltipTrigger>
                  <button
                    className="w-10 h-10 flex items-center justify-center bg-white-200 rounded-full hover:bg-white-300 cursor-pointer"
                    onClick={handleBackNavigation}
                  >
                    <Image src={BackButton} alt="" />
                  </button>
                </TooltipTrigger>
                <TooltipContent>
                  <div className="text-sm text-dark-300">Back</div>
                </TooltipContent>
              </Tooltip>
              {data?.record?.approval
                ? data?.record?.title
                : 'Create new document'}
            </div>
          </div>

          <div className="flex w-full items-start gap-6 mb-5">
            <div className="w-[100%]">
              <CkEditor
                disabled={false}
                url={fileData?.url || ''}
                editorData={editorData}
                setEditorData={setEditorData}
              />
              {editorError ? (
                <div className="text-xs mt-2 font-semibold leading-5 text-left text-red-200">
                  Content is required
                </div>
              ) : (
                <></>
              )}
            </div>
          </div>
          <div className="flex flex-col gap-2.5 mb-5">
            <Label
              htmlFor="version"
              className="text-base flex justify-between items-center font-medium leading-6 text-dark-100"
            >
              <Label
                htmlFor="version"
                className="text-base font-medium leading-6 text-dark-100"
              >
                Version
                {requireApproval && <span className="text-red-200">*</span>}
              </Label>
              <p className="text-grey-300">
                {data?.record?.approval
                  ? `Currently approval version: ${
                      data?.record?.approval?.payload?.version ?? 'NA'
                    }`
                  : `Currently published version: ${
                      data?.record?.document_version?.version_number ?? 'NA'
                    }`}
              </p>
            </Label>
            <Input
              placeholder="Version"
              id="version"
              type="number"
              value={formState.version}
              onChange={(e) => {
                setFormState((pre) => ({ ...pre, version: e.target.value }));
              }}
              errorMsg={
                validationErrors?.version?.[0] ||
                draftValidationErrors?.version?.[0]
              }
            />
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-16 justify-between mb-5 bg-gray-50 px-6 py-5 rounded-xl">
              <div>
                <div className="flex items-center gap-2 flex-nowrap">
                  <h3 className="text-base font-medium text-approval-text-primary">
                    Requires document approval
                  </h3>

                  <Tooltip>
                    <TooltipTrigger>
                      <div
                        className="w-10 h-10 flex items-center justify-centerrounded-full cursor-pointer"
                        onClick={() => console.log('first')}
                      >
                        <InfoCircle />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div className="text-sm text-dark-300">
                        Optionally, set a specific date for the next review. If
                        selected, the recurring review cycle will be counted
                        from this date instead of the document’s publish date.
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </div>
                <p className="text-grey-300 mt-1">
                  Enable if this document requires approval workflow
                </p>
              </div>
              <div>
                <ToggleSwitch
                  initialState={requireApproval}
                  onChange={(state) => {
                    setRequireApproval(state);
                    setPublishDate('');
                    setReviewPeriod('');
                    setCustomReviewDate(false);
                    if (!state) {
                      setPublishDate(moment().format('YYYY-MM-DD'));
                      setReviewPeriod('');
                    }
                  }}
                />
              </div>
            </div>
          </div>
          {requireApproval && (
            <>
              <div className="mb-5">
                <div className="w-full">
                  <ApprovalTypeSelector
                    value={approvalType}
                    onChange={setApprovalType}
                  />
                </div>
              </div>

              <AddApprovers
                approvalType={approvalType}
                assignees={data?.record?.assignees}
                approversData={(data: any) => setApproversData(data)}
                existingApprovers={existingApprovers}
              />
              {approverError ? (
                <div className="text-xs font-semibold leading-5 text-left text-red-200">
                  Atleast one approver is required
                </div>
              ) : (
                <></>
              )}
            </>
          )}
          {!requireApproval && (
            <div className="p-2 bg-white-100 border border-white-300 rounded-lg">
              <div className="flex items-center p-2 pb-4 border-b border-white-300">
                Set Review Schedule{' '}
                <Tooltip>
                  <TooltipTrigger>
                    <div
                      className="w-10 h-10 flex items-center justify-center rounded-full cursor-pointer"
                      onClick={() => console.log('first')}
                    >
                      <InfoCircle />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <div className="text-sm text-dark-300">
                      Set the recurring time period after which the document
                      should be reviewed.
                    </div>
                  </TooltipContent>
                </Tooltip>
              </div>
              <div className="py-4 px-3">
                <div className="flex gap-4 items-center text-base text-dark-300 leading-6 font-medium">
                  <p className="text-grey-300 font-medium text-base leading-6 whitespace-nowrap">
                    Review Period
                    {!requireApproval && (
                      <span className="text-red-200">*</span>
                    )}
                  </p>
                  <div>
                    <Select
                      value={reviewPeriod}
                      onValueChange={(value: ReviewPeriod) => {
                        setReviewPeriod(value);
                        setNextReviewDate(
                          moment(publishDate)
                            .add(periodMap[value], 'months')
                            .format('YYYY-MM-DD'),
                        );
                      }}
                    >
                      <SelectTrigger className={'w-[240px]'} id="review">
                        <SelectValue placeholder="---" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Monthly">Monthly</SelectItem>
                        <SelectItem value="Quarterly">Quarterly</SelectItem>
                        <SelectItem value="Half Yearly">Half Yearly</SelectItem>
                        <SelectItem value="Annually">Annually</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                {reviewPeriodError ? (
                  <div className="text-xs mt-1 font-semibold leading-5 text-left text-red-200">
                    Review period required
                  </div>
                ) : (
                  <></>
                )}
              </div>
              {!data?.record?.document_version && (
                <div className="flex items-center gap-4 justify-between pt-4 pb-2 px-3 border-t border-white-300">
                  <div className="flex gap-4 text-base text-dark-300 leading-6 font-medium">
                    <div className="flex items-center">
                      <Tooltip>
                        <TooltipTrigger>
                          <div
                            className="w-10 h-10 flex items-center justify-center rounded-fullcursor-pointer"
                            onClick={() => console.log('first')}
                          >
                            <InfoCircle />
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-sm text-dark-300">
                            Optionally, set a specific date for the next review.
                            If selected, the recurring review cycle will be
                            counted from this date instead of the document’s
                            publish date.
                          </div>
                        </TooltipContent>
                      </Tooltip>
                      <h3 className="text-base mt-0.5 font-medium text-approval-text-primary">
                        Pick a specific date for the next review
                      </h3>
                    </div>

                    <ToggleSwitch
                      initialState={customReviewDate}
                      onChange={(state) => {
                        setCustomReviewDate(state);

                        if (!state) {
                          setPublishDate(moment().format('YYYY-MM-DD'));
                        }
                      }}
                    />
                  </div>
                  <div className="flex gap-4 items-center text-base text-dark-300 leading-6 font-medium">
                    <p className="text-grey-300 font-medium text-base leading-6 whitespace-nowrap">
                      Next Review Date
                    </p>
                    <Calendar
                      className={`${
                        !customReviewDate ? 'bg-white-150' : 'bg-slate-50'
                      } w-full`}
                      selectedDate={(customReviewDate && nextReviewDate) || ''}
                      disabled={!customReviewDate}
                      onDateChange={(date) => {
                        if (date) {
                          setNextReviewDate(
                            moment(date as string).format('YYYY-MM-DD'),
                          );
                        } else {
                          setNextReviewDate('');
                        }
                      }}
                    />
                  </div>
                </div>
              )}
            </div>
          )}

          <div
            className={`flex ${
              !reviewPeriod ? 'justify-end' : 'justify-between'
            } gap-4 mt-5`}
          >
            {' '}
            {reviewPeriod && (
              <div className="flex-1 bg-green-50 rounded-md">
                <div className="flex gap-2 px-4 py-2 items-center text-base text-dark-300 leading-6 font-medium">
                  <div
                    className={cn(
                      'h-5 w-5 flex items-center justify-center bg-green-200 rounded-full',
                    )}
                  >
                    <CheckIcon height="16" width="16" />
                  </div>
                  <p className="text-base font-medium">
                    Next review scheduled for:{' '}
                    {reviewPeriod ? (
                      <>
                        {nextReviewDate
                          ? moment(nextReviewDate).format('MMM D YYYY')
                          : moment(publishDate)
                              .add(
                                periodMap[reviewPeriod as ReviewPeriod] || 0,
                                'months',
                              )
                              .format('MMM D YYYY')}
                        {} (repeats {reviewPeriod} from{' '}
                        {moment(publishDate).format('MMM D YYYY')})
                      </>
                    ) : (
                      '---'
                    )}
                  </p>
                </div>
              </div>
            )}
            <div className="flex gap-4 justify-end">
              <SecondaryButton
                text="Save as Draft"
                size="medium"
                onClick={handleSaveAsDraft}
                isLoading={draftLoading}
                disabled={!editorData}
              />

              <PrimaryButton
                text={requireApproval ? 'Send for Approval' : 'Publish'}
                size="medium"
                onClick={handleSendForApproval}
                isLoading={loading || isLoading}
                disabled={!editorData}
              />
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default CreateDocumentComponent;
