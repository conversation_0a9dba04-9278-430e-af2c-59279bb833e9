// components/risk/riskActions.tsx
import { useRouter } from 'next/router';
import EditIcon from '@/assets/outline/edit';
import DeleteIcon from '@/assets/outline/delete';
import PrimaryButton from '@/components/common/button/primaryButton';
import { AccessActions } from '@/constants/access';
import { hasAccess } from '@/utils/roleAccessConfig';

interface RiskActionsProps {
  riskId: string | string[] | undefined;
  currentUser: any;
  onDelete: () => void;
}

const RiskActions = ({ riskId, currentUser, onDelete }: RiskActionsProps) => {
  const router = useRouter();

  return (
    <div className="flex justify-end mb-2">
      {hasAccess(AccessActions.EDIT_RISK, currentUser) && (
        <PrimaryButton
          onClick={() => router.push(`/risk/${riskId}/edit`)}
          className="mr-2"
          text="Edit"
          icon={<EditIcon />}
        />
      )}

      {hasAccess(AccessActions.DELETE_RISK, currentUser) && (
        <PrimaryButton
          onClick={onDelete}
          text="Delete"
          icon={<DeleteIcon />}
        />
      )}
    </div>
  );
};

export default RiskActions;
