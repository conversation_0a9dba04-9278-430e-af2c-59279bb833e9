import { Check, X } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

import DeleteIcon from '@/assets/outline/delete';
import EditIcon from '@/assets/outline/edit';
import PlusIcon from '@/assets/outline/plus';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/common/accordion';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import { hasAccess } from '@/utils/roleAccessConfig';
import { cn } from '@/utils/styleUtils';

import Breadcrumb from '../common/breadcrumb';
import LinkButton from '../common/button/linkButton';
import { Dialog, DialogTrigger } from '../common/dialog';
import { Input } from '../common/input';
import Loader from '../common/loader';
import DeleteModal from '../common/modals/deleteModal';
import SideBarWrapper from '../common/sidebar/layout';

type IData = {
  value: string;
  id: string;
};

interface IRiskCategory {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

interface IRiskType {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

const RiskAdministrator = () => {
  const [riskCategories, setRiskCategories] = React.useState<IData[]>([]);
  const [riskTypes, setRiskTypes] = React.useState<IData[]>([]);
  const { accessToken, user } = useAuthStore();

  const {
    data: categories,
    reFetch: reFetchCategories,
    isLoading: categoryLoading,
  } = useFetch<{ records: IRiskCategory[] }>(
    accessToken,
    `risk-categories`,
    {},
  );

  const {
    data: types,
    reFetch: reFetchTypes,
    isLoading: typesLoading,
  } = useFetch<{ records: IRiskType[] }>(accessToken, `risk-types`, {});

  useEffect(() => {
    if (categories) {
      setRiskCategories(
        categories.records.map((item) => ({ value: item.name, id: item.id })),
      );
    }
  }, [categories]);

  useEffect(() => {
    if (types) {
      setRiskTypes(
        types.records.map((item) => ({ value: item.name, id: item.id })),
      );
    }
  }, [types]);

  const breadcrumbData = [
    {
      name: 'Risk Hub',
      link: '/risk',
    },
    {
      name: 'Risk Administration',
      link: '#',
    },
  ];

  return (
    <SideBarWrapper>
      <div className="flex flex-col flex-1">
        <div className=" my-5">
          <div>
            <Breadcrumb data={breadcrumbData} />
          </div>
          <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 ">
            Risk Administration
          </div>
        </div>

        <div>
          <Accordion type="multiple" className="w-full">
            <AccordionItem value="item-1" className="">
              <AccordionTrigger>
                View all Risk Categories{' '}
                {riskCategories.length > 0 ? `(${riskCategories.length})` : ''}
              </AccordionTrigger>
              <AccordionContent className="p-5">
                {categoryLoading ? (
                  <Loader className="h-[150px]" />
                ) : (
                  <div className="grid grid-cols-2 gap-5">
                    {riskCategories.map((item, index) => (
                      <EditedInput
                        key={index}
                        data={item}
                        title="risk-category"
                        setState={setRiskCategories}
                        index={index}
                        refetch={reFetchCategories}
                      />
                    ))}
                    <AddCategory
                      text="Add Risk Category"
                      onClick={() => {
                        setRiskCategories((pre) => [
                          ...pre,
                          { value: '', id: '' },
                        ]);
                      }}
                    />
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="item-2" className="">
              <AccordionTrigger>
                View all Risk Types{' '}
                {riskTypes.length > 0 ? `(${riskTypes.length})` : ''}
              </AccordionTrigger>
              <AccordionContent className="p-5">
                {typesLoading ? (
                  <Loader className="h-[150px]" />
                ) : (
                  <div className="grid grid-cols-2 gap-5">
                    {riskTypes.map((item, index) => (
                      <EditedInput
                        key={index}
                        data={item}
                        title="risk-type"
                        setState={setRiskTypes}
                        index={index}
                        refetch={reFetchTypes}
                      />
                    ))}
                    <AddCategory
                      text="Add Risk Type"
                      onClick={() => {
                        setRiskTypes((pre) => [...pre, { value: '', id: '' }]);
                      }}
                    />
                  </div>
                )}
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </SideBarWrapper>
  );
};

const mapUrl = (title: string) => {
  switch (title) {
    case 'risk-category':
      return 'risk-categories';
    case 'risk-type':
      return 'risk-types';
    default:
      return '';
  }
};

const EditedInput = ({
  data,
  title,
  setState,
  index,
  refetch,
}: {
  data: { value: string; id: string };
  title: string;
  setState: React.Dispatch<React.SetStateAction<IData[]>>;
  index: number;
  refetch: () => void;
}) => {
  const [isEdit, setIsEdit] = useState(false);
  const [name, setName] = useState(data.value);
  const [originalValue, setOriginalValue] = useState(data.value);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [openDeleteModal, setOpenDeleteModal] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const { accessToken } = useAuthStore();

  const { postData, response, error: postError } = usePost();
  const { putData, response: putResponse, error: putError } = usePut();
  const {
    deleteData,
    response: deletedResponse,
    error: deleteError,
    isLoading: deleteLoading,
  } = useDelete();

  useEffect(() => {
    if (isEdit && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEdit]);

  // Accessibility ENTER/ESC key
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && name.trim() !== '' && name !== originalValue) {
      handleSubmit(title);
    } else if (e.key === 'Escape') {
      handleCancel();
    }
  };

  const handleSubmit = async (title: string) => {
    const url = mapUrl(title);
    if (!url || name.trim() === '' || name === originalValue) return;

    setIsSubmitting(true);
    try {
      if (data.id === '') {
        // Create new item
        await postData(accessToken as string, url, { name: name.trim() });
      } else {
        // Update existing item
        await putData(accessToken as string, `${url}/${data.id}`, {
          name: name.trim(),
        });
      }
    } catch (error) {
      console.error('Error submitting:', error);
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (response && !postError) {
      // Handle successful creation
      setState((pre) => {
        const newState = [...pre];
        newState[index] = {
          id: (response as any)?.id || (response as any)?.data?.id || '',
          value: name,
        };
        return newState;
      });
      setOriginalValue(name);
      setIsSubmitting(false);
      setIsEdit(false);
      refetch();
    } else if (putResponse && !putError) {
      // Handle successful update
      setState((pre) => {
        const newState = [...pre];
        newState[index] = {
          id: data.id,
          value: name,
        };
        return newState;
      });
      setOriginalValue(name);
      setIsSubmitting(false);
      setIsEdit(false);
    } else if (postError != null) {
      setIsSubmitting(false);
      setIsEdit(true);
    } else if (
      putError != null &&
      typeof putError === 'object' &&
      Object.keys(putError).length > 0
    ) {
      setIsSubmitting(false);
      setIsEdit(true);
    }
  }, [response, putResponse, postError, putError]);

  useEffect(() => {
    if (data.value === '' && data.id === '') {
      setIsEdit(true);
      setOriginalValue('');
    }
    if (!isEdit && !isSubmitting) {
      setName(data.value);
      setOriginalValue(data.value);
    }
  }, [data, isEdit, isSubmitting]);

  useEffect(() => {
    if (deletedResponse && !deleteError) {
      refetch();
      setOpenDeleteModal(false);
    } else if (deleteError) {
      setOpenDeleteModal(false);
    }
  }, [deletedResponse, deleteError]);

  const handleDelete = async () => {
    const url = mapUrl(title);
    if (url && data.id) {
      try {
        await deleteData(accessToken as string, `${url}/${data.id}`);
      } catch (error) {
        console.error('Error deleting item:', error);
      }
    }
  };

  const handleCancel = () => {
    setName(originalValue);
    setIsEdit(false);

    if (data.id === '' && originalValue === '') {
      setState((pre) => pre.filter((_, i) => i !== index));
    }
  };

  return (
    <div className="flex items-center gap-2.5 flex-1">
      <Input
        placeholder={'Add ' + title.replace('-', ' ')}
        value={name}
        containerClass="flex-1"
        disabled={!isEdit || isSubmitting}
        ref={inputRef}
        onChange={(e) => setName(e.target.value)}
        onKeyDown={handleKeyDown}
      />
      {isEdit ? (
        <>
          <div
            className={cn(
              'h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer',
              name === originalValue || isSubmitting || name.trim() === ''
                ? 'cursor-not-allowed opacity-50'
                : '',
            )}
            onClick={() =>
              !isSubmitting &&
              name.trim() !== '' &&
              name !== originalValue &&
              handleSubmit(title)
            }
          >
            <Check className="h-5 w-5" />
          </div>
          <div
            className={cn(
              'h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer',
              isSubmitting ? 'cursor-not-allowed opacity-50' : '',
            )}
            onClick={() => !isSubmitting && handleCancel()}
          >
            <X className="h-5 w-5" />
          </div>
        </>
      ) : (
        <>
          <div
            className="h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer"
            onClick={() => {
              setIsEdit(true);
            }}
          >
            <EditIcon />
          </div>
          <Dialog open={openDeleteModal} onOpenChange={setOpenDeleteModal}>
            <DialogTrigger asChild>
              <div className="h-10 w-10 rounded-full bg-white-200 flex justify-center items-center hover:bg-white-300 cursor-pointer">
                <DeleteIcon height="20" width="20" />
              </div>
            </DialogTrigger>
            <DeleteModal
              title={'Delete '}
              infoText={'Are you sure you want to delete "' + data.value + '"'}
              btnText={'Delete'}
              onClick={handleDelete}
              btnLoading={deleteLoading}
            />
          </Dialog>
        </>
      )}
    </div>
  );
};

const AddCategory = ({
  text,
  onClick,
}: {
  text: string;
  onClick?: () => void;
}) => {
  return (
    <div className="flex items-center">
      <LinkButton
        size="large"
        text={text}
        icon={<PlusIcon />}
        iconPosition="left"
        onClick={onClick}
      />
    </div>
  );
};

export default RiskAdministrator;
