// components/risk/createRiskForm.tsx
import React, { useEffect, useState } from 'react';
import { z } from 'zod';
import moment from 'moment';
import { useDropzone } from 'react-dropzone';

import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import { Textarea } from '@/components/common/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import {
  IOption,
  ReactSelectMulti,
} from '@/components/common/multiSelectInput';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import useValidators from '@/hooks/useValidator';
import { IAttachment } from '@/interfaces/misc';
import { IUser } from '@/interfaces/user';
import { useRouter } from 'next/router';
import { FileCard } from '../common/fileCard';
import { ArrowLeft } from 'lucide-react';

// Define risk schema
const riskSchema = {
  // Section 1: Risk Identification
  risk_id: z.string().nonempty('Risk ID is required'),
  title: z.string().nonempty('Risk title is required'),
  description: z.string().nonempty('Risk description is required'),
  status: z.string().nonempty('Status is required'),
  departments: z
    .array(z.string())
    .min(1, 'At least one department is required'),
  types: z.array(z.string()).min(1, 'At least one type is required'),
  processes: z.array(z.string()).min(1, 'At least one process is required'),
  categories: z.array(z.string()).min(1, 'At least one category is required'),
  impact: z.string().nonempty('Impact is required'),

  // Section 2: Risk Calculation
  probability: z.number().min(1, 'Probability must be at least 1'),
  severity: z.number().min(1, 'Severity must be at least 1'),
  detectability: z.number().min(1, 'Detectability must be at least 1'),

  // Section 3: Mitigation Plan
  mitigation_description: z
    .string()
    .nonempty('Mitigation description is required'),
  risk_owner_id: z.string().nonempty('Risk owner is required'),
  target_completion_date: z
    .string()
    .nonempty('Target completion date is required'),
};

interface ICreateRiskFormProps {
  riskData?: any;
  isEdit?: boolean;
  onSuccess: () => void;
}

interface IData {
  risk_id: string;
  title: string;
  description: string;
  status: string;
  departments: string[];
  types: string[];
  processes: string[];
  categories: string[];
  impact: string;
  probability: number;
  severity: number;
  detectability: number;
  mitigation_description: string;
  risk_owner_id: string;
  target_completion_date: string;
}

const CreateRiskForm: React.FC<ICreateRiskFormProps> = ({
  riskData,
  isEdit = false,
  onSuccess,
}) => {
  const router = useRouter();
  const accessToken = useAuthStore((state) => state.accessToken);
  const [files, setFiles] = useState<File[]>([]);
  // const [attachments, setAttachments] = useState<IAttachment[]>([]);
  const [error, setError] = useState<string | undefined>();
  const [showDeleteFileModal, setShowDeleteFileModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<IAttachment | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initial data state
  const [data, setData] = useState<IData>({
    risk_id: '',
    title: '',
    description: '',
    status: 'Active', // Default status
    departments: [],
    types: [],
    processes: [],
    categories: [],
    impact: '',
    probability: 1,
    severity: 1,
    detectability: 1,
    mitigation_description: '',
    risk_owner_id: 'test',
    target_completion_date: '',
  });

  // Risk score calculation
  const riskScore =
    (data.probability * data.severity * data.detectability) / 100;

  // Risk level determination based on score
  const getRiskLevel = (score: number) => {
    if (score <= 0.27) return 'Low';
    if (score <= 0.64) return 'Medium';
    return 'High';
  };

  const riskLevel = getRiskLevel(riskScore);

  // Fetch departments, processes, categories, and users for dropdowns
  const { data: departments } = useFetch<{ records: any[] }>(
    accessToken,
    'departments',
  );

  const { data: types } = useFetch<{ records: any[] }>(
    accessToken,
    'risk-types',
  );

  const { data: processes } = useFetch<{ records: any[] }>(
    accessToken,
    'processes',
  );

  const { data: categories } = useFetch<{ records: any[] }>(
    accessToken,
    'risk-categories',
  );

  const { data: users } = useFetch<{ records: IUser[] }>(accessToken, 'users');

  // Transform data for select options
  const departmentOptions =
    departments?.records?.map((dept) => ({
      label: dept.name,
      value: dept.id,
    })) || [];

  const typeOptions =
    types?.records?.map((proc) => ({
      label: proc.name,
      value: proc.id,
    })) || [];

  const processOptions =
    processes?.records?.map((proc) => ({
      label: proc.name,
      value: proc.id,
    })) || [];

  const categoryOptions =
    categories?.records?.map((cat) => ({
      label: cat.name,
      value: cat.id,
    })) || [];

  // Filter users to only include risk admins and editors
  const riskOwnerOptions =
    users?.records
      ?.filter(
        (user) =>
          user.roles?.includes('Admin') ||
          user.roles?.includes('risk_editor') ||
          user.roles?.includes('super_admin'),
      )
      .map((user) => ({
        label: user.full_name,
        value: user.id,
      })) || [];

  // Form validation
  const { startValidation, validationErrors } = useValidators({
    schemas: riskSchema,
    values: data,
  });
  // API calls
  const { postData } = usePost();
  const { putData } = usePut();

  // File upload handling
  const onDrop = (acceptedFiles: File[]) => {
    setFiles((prev) => [...prev, ...acceptedFiles]);
  };

  const { getRootProps, getInputProps } = useDropzone({
    onDrop,
    accept: {
      'image/*': [],
      'application/pdf': [],
      'application/msword': [],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        [],
      'application/vnd.ms-excel': [],
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [],
    },
  });

  const removeFile = (index: number) => {
    setFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // Load risk data if editing
  useEffect(() => {
    if (isEdit && riskData && users?.records) {
  
      setData({
        risk_id: riskData.risk_id || '',
        title: riskData.title || '',
        description: riskData.description || '',
        departments: Array.isArray(riskData.departments)
          ? riskData.departments.map((dept: any) => dept.id)
          : [riskData.departments?.id],
        types: Array.isArray(riskData.types)
          ? riskData.types.map((type: any) => type.id)
          : [riskData.types?.id],
        processes: Array.isArray(riskData.processes)
          ? riskData.processes.map((process: any) => process.id)
          : [riskData.processes?.id],
        categories: Array.isArray(riskData.categories)
          ? riskData.categories.map((category: any) => category.id)
          : [riskData.categories?.id],
        status: riskData.status || 'Active',
        impact: riskData.impact || '',
        probability: riskData.probability || 1,
        severity: riskData.severity || 1,
        detectability: riskData.detectability || 1,
        mitigation_description: riskData.mitigation_description || '',
        risk_owner_id: riskData.risk_owner?.id || riskData.risk_owner_id || '',
        target_completion_date: riskData.target_completion_date || '',
      });

      // if (riskData.attachments) {
      //   setAttachments(riskData.attachments);
      // }
    }
  }, [isEdit, riskData, users]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(undefined);
    setIsSubmitting(true);
    const { hasValidationErrors } = await startValidation();
    if (hasValidationErrors) {
      setIsSubmitting(false);
      return;
    }

    try {
      // Create payload directly as an object (skip FormData approach)
      const payload: Record<string, any> = {
        risk_id: data.risk_id,
        title: data.title,
        description: data.description,
        status: data.status, // Ensure status is included
        departments: data.departments,
        types: data.types, // Ensure types array is properly included
        processes: data.processes,
        categories: data.categories,
        impact: data.impact,
        probability: Number(data.probability), // Ensure these are numbers
        severity: Number(data.severity),
        detectability: Number(data.detectability),
        mitigation_description: data.mitigation_description,
        risk_owner_id: data.risk_owner_id,
        target_completion_date: data.target_completion_date,
        risk_score: riskScore,
      };

      if (isEdit && riskData?.id) {
        await putData(accessToken as string, `risks/${riskData.id}`, payload);
      } else {
        await postData(accessToken as string, 'risks', payload);
      }

      onSuccess();
    } catch (err: any) {
      setError(err.message || 'An error occurred while saving the risk');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input changes
  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >,
  ) => {
    const { name, value } = e.target;
    setData((prev) => ({
      ...prev,
      [name]:
        name === 'probability' ||
        name === 'severity' ||
        name === 'detectability'
          ? Number(value)
          : value,
    }));
  };

  // Handle date selection
  const handleDateChange = (date: Date | undefined) => {
    if (date) {
      setData((prev) => ({
        ...prev,
        target_completion_date: moment(date).format('YYYY-MM-DD'),
      }));
    }
  };

  // Handle dropdown selection
  const handleSelectChange = (name: string, value: string) => {
    setData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // Handle multi-select changes
  const handleMultiSelectChange = (
    name: string,
    selectedOptions: IOption[] | null,
  ) => {
    setData((prev) => ({
      ...prev,
      [name]: selectedOptions
        ? selectedOptions.map((option) => option.value)
        : [],
    }));
  };

  return (
    <div className="max-w-7xl mx-auto p-8 bg-white min-h-screen">
      {/* Header with back button */}
      <div className="flex items-center mb-8">
        <button
          onClick={() => router.back()}
          className="flex items-center text-gray-600 hover:text-gray-800 mr-4"
        >
          <ArrowLeft className="h-5 w-5 mr-1" />
        </button>
        <h1 className="text-2xl font-semibold text-gray-900">
          {isEdit ? 'Edit Risk' : 'Add Risk'}
        </h1>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* Risk Identification Section */}
        <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold text-gray-900 mb-6 border-b border-gray-200 pb-2">
            Risk Identification
          </h2>
          <p className="text-sm text-gray-600 mb-6">
            Provide basic information about the risk
          </p>

          {/* First row: Risk title, Risk ID, Risk status */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <Label
                htmlFor="title"
                className="text-sm font-medium text-gray-700"
              >
                Risk title
              </Label>
              <Input
                id="title"
                name="title"
                value={data.title}
                onChange={handleChange}
                placeholder="Enter risk title"
                className="mt-1"
                errorMsg={validationErrors.title[0]}
              />
            </div>

            <div>
              <Label
                htmlFor="risk_id"
                className="text-sm font-medium text-gray-700"
              >
                Risk ID
              </Label>
              <Input
                id="risk_id"
                name="risk_id"
                value={data.risk_id}
                onChange={handleChange}
                placeholder="RSK112"
                className="mt-1"
                errorMsg={validationErrors.risk_id[0]}
              />
            </div>

            <div>
              <Label
                htmlFor="status"
                className="text-sm font-medium text-gray-700"
              >
                Risk status
              </Label>
              <Select
                value={data.status}
                onValueChange={(value) => handleSelectChange('status', value)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                  <SelectItem value="Closed">Closed</SelectItem>
                  <SelectItem value="Under Review">Under Review</SelectItem>
                  <SelectItem value="Mitigated">Mitigated</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Second row: Department, Category, Process */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <Label
                htmlFor="departments"
                className="text-sm font-medium text-gray-700"
              >
                Department
              </Label>
              <ReactSelectMulti
                options={departmentOptions}
                value={departmentOptions.filter((option) =>
                  data.departments.includes(option.value),
                )}
                onChange={(selectedOptions) =>
                  handleMultiSelectChange('departments', selectedOptions)
                }
                placeholder="Select Department"
              />
            </div>

            <div>
              <Label
                htmlFor="processes"
                className="text-sm font-medium text-gray-700"
              >
                Process
              </Label>
              <ReactSelectMulti
                options={processOptions}
                value={processOptions.filter((option) =>
                  data.processes.includes(option.value),
                )}
                onChange={(selectedOptions) =>
                  handleMultiSelectChange('processes', selectedOptions)
                }
                placeholder="Select Process"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <Label
                htmlFor="categories"
                className="text-sm font-medium text-gray-700"
              >
                Category
              </Label>
              <ReactSelectMulti
                options={categoryOptions}
                value={categoryOptions.filter((option) =>
                  data.categories.includes(option.value),
                )}
                onChange={(selectedOptions) =>
                  handleMultiSelectChange('categories', selectedOptions)
                }
                placeholder="Select Category"
              />
            </div>
            <div>
              <Label
                htmlFor="types"
                className="text-sm font-medium text-gray-700"
              >
                Type
              </Label>
              <ReactSelectMulti
                options={typeOptions}
                value={typeOptions.filter((option) =>
                  data.types.includes(option.value),
                )}
                onChange={(selectedOptions) =>
                  handleMultiSelectChange('types', selectedOptions)
                }
                placeholder="Select Type"
              />
            </div>
          </div>

          {/* Risk Description */}
          <div className="mb-6">
            <Label
              htmlFor="description"
              className="text-sm font-medium text-gray-700"
            >
              Risk Description
            </Label>
            <Textarea
              id="description"
              name="description"
              value={data.description}
              onChange={handleChange}
              rows={3}
              placeholder="Enter description"
              className="mt-1"
              errorMsg={validationErrors.description[0]}
            />
          </div>

          {/* Impact Description */}
          <div className="mb-6">
            <Label
              htmlFor="impact"
              className="text-sm font-medium text-gray-700"
            >
              Impact Description
            </Label>
            <Textarea
              id="impact"
              name="impact"
              value={data.impact}
              onChange={handleChange}
              rows={3}
              placeholder="Enter impact description"
              className="mt-1"
              errorMsg={validationErrors.impact[0]}
            />
          </div>

          {/* Attach files */}
        </div>

        {/* Risk Calculation Section */}
        <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold text-gray-900 mb-6 border-b border-gray-200 pb-2">
            Risk Calculation
          </h2>
          <p className="text-sm text-gray-600 mb-6">
            Assess risk severity using standardized formulas
          </p>

          {/* Risk factors */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div>
              <Label
                htmlFor="probability"
                className="text-sm font-medium text-gray-700"
              >
                Probability
              </Label>
              <Input
                id="probability"
                name="probability"
                type="number"
                min="1"
                max="10"
                value={data.probability}
                onChange={handleChange}
                className="mt-1"
                errorMsg={validationErrors.probability[0]}
              />
            </div>

            <div>
              <Label
                htmlFor="severity"
                className="text-sm font-medium text-gray-700"
              >
                Severity
              </Label>
              <Input
                id="severity"
                name="severity"
                type="number"
                min="1"
                max="10"
                value={data.severity}
                onChange={handleChange}
                className="mt-1"
                errorMsg={validationErrors.severity[0]}
              />
            </div>

            <div>
              <Label
                htmlFor="detectability"
                className="text-sm font-medium text-gray-700"
              >
                Detectability
              </Label>
              <Input
                id="detectability"
                name="detectability"
                type="number"
                min="1"
                max="10"
                value={data.detectability}
                onChange={handleChange}
                className="mt-1"
                errorMsg={validationErrors.detectability[0]}
              />
            </div>
          </div>

          {/* Risk Score Formula Display */}
          <div className="mt-4 mb-2 p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-700">
              Risk Score = ( Probability ({data.probability}) x Severity (
              {data.severity}) x Detectability ({data.detectability}) ) / 100 ={' '}
              {(
                (data.probability * data.severity * data.detectability) /
                100
              ).toFixed(2)}
            </p>
          </div>
        </div>

        {/* Mitigation Plans Section */}
        <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-sm">
          <h2 className="text-lg font-semibold text-gray-900 mb-6 border-b border-gray-200 pb-2">
            Mitigation Plans
          </h2>
          <p className="text-sm text-gray-600 mb-6">
            Define strategies to reduce or eliminate the risk
          </p>

          {/* Mitigation Description */}
          <div className="mb-6">
            <Label
              htmlFor="mitigation_description"
              className="text-sm font-medium text-gray-700"
            >
              Mitigation Description
            </Label>
            <Textarea
              id="mitigation_description"
              name="mitigation_description"
              value={data.mitigation_description}
              onChange={handleChange}
              rows={3}
              placeholder="Enter mitigation description"
              className="mt-1"
              errorMsg={validationErrors.mitigation_description[0]}
            />
          </div>

          {/* Risk Owner and Target completion date */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label
                htmlFor="risk_owner_id"
                className="text-sm font-medium text-gray-700"
              >
                Risk Owner
              </Label>
              <Select
                value={data.risk_owner_id}
                onValueChange={(value) => {
                  handleSelectChange('risk_owner_id', value);
                }}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Select Risk Owner" />
                </SelectTrigger>
                <SelectContent>
                  {riskOwnerOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {validationErrors.risk_owner_id && (
                <p className="text-red-500 text-sm mt-1">
                  {validationErrors.risk_owner_id}
                </p>
              )}
            </div>

            <div>
              <Label
                htmlFor="target_completion_date"
                className="text-sm font-medium text-gray-700"
              >
                Target completion date
              </Label>
              <Calendar
                selectedDate={data.target_completion_date}
                onDateChange={(date) => {
                  if (date) {
                    setData((prev) => ({
                      ...prev,
                      target_completion_date: moment(date as string).format(
                        'YYYY-MM-DD',
                      ),
                    }));
                  } else {
                    setData((prev) => ({
                      ...prev,
                      target_completion_date: '',
                    }));
                  }
                }}
                allowPastDates={false}
                className="mt-1"
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-6 border-t border-gray-200">
          <PrimaryButton
            type="submit"
            disabled={isSubmitting}
            text={
              isSubmitting
                ? 'Saving...'
                : isEdit
                ? 'Update Risk'
                : 'Create Risk'
            }
          />
        </div>
      </form>
    </div>
  );
};

export default CreateRiskForm;

{
  /* <div className="mb-6">
<Label className="text-sm font-medium text-gray-700 mb-2 block">
  Attach files
</Label>
<div
  {...getRootProps()}
  className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
>
  <input {...getInputProps()} />
  <div className="text-gray-500">
    <p className="mb-2">
      Upload or Drag and drop to upload your video
    </p>
    <button
      type="button"
      className="px-4 py-2 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
    >
      Select files
    </button>
  </div>
</div>

Display uploaded files
{files.length > 0 && (
  <div className="mt-4 space-y-2">
    {files.map((file, index) => (
      <div
        key={index}
        className="flex items-center justify-between p-2 bg-gray-100 rounded"
      >
        <span className="text-sm">{file.name}</span>
        <button
          type="button"
          onClick={() => removeFile(index)}
          className="text-red-500 hover:text-red-700"
        >
          Remove
        </button>
      </div>
    ))}
  </div>
)}
</div> */
}
