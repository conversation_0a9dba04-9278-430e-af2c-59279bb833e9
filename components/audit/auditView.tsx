import axios from 'axios';
import { Check, EditIcon, Minus, Search } from 'lucide-react';
import moment from 'moment';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import CheckIcon from '@/assets/outline/check';
import DocumentIcon from '@/assets/outline/document';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/components/common/accordion';
import { Dialog, DialogTrigger } from '@/components/common/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { AccessActions } from '@/constants/access';
import { useAuthStore } from '@/globalProvider/authStore';
import { useDelete } from '@/hooks/useDelete';
import useDownload from '@/hooks/useDownload';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import { usePut } from '@/hooks/usePut';
import {
  AuditRequirement,
  AuditStandard,
  Findings,
  IAuditDetails,
  NonConformity,
  Requirements,
  SubClause,
  User,
} from '@/interfaces/audit';
import { ILogs } from '@/interfaces/document';
import { IProcess } from '@/interfaces/process';
import { hasAccess } from '@/utils/roleAccessConfig';
import { cn } from '@/utils/styleUtils';
import { getValueOrDefault } from '@/utils/table';
import { formatDate } from '@/utils/time';

import Breadcrumb from '../common/breadcrumb';
import DeleteButton from '../common/button/deleteButton';
import PrimaryButton from '../common/button/primaryButton';
import SecondaryButton from '../common/button/secondaryButton';
import TertiaryButton from '../common/button/tertiaryButton';
import { DetailsText } from '../common/infoDetail';
import Loader from '../common/loader';
import ConfirmModal from '../common/modals/confirmModal';
import DeleteModal from '../common/modals/deleteModal';
import DocumentViewModal from '../common/modals/documentViewModal';
import Progress from '../common/progress';
import SideBarWrapper from '../common/sidebar/layout';
import Status from '../common/status';
import CommonTable from '../common/table';
import Tabs from '../common/tabs';
import LogsCard from '../document/components/logsCard';
import ProcessDocument from '../standard/processDocument';
import AcceptRequirementModal from './components/modals/acceptRequirementModal';
import AuditFindingResponseModal from './components/modals/auditFindingResponseModal';
import AuditRequirementResponseModal from './components/modals/auditRequirementResponseModal';
import CreateAuditByStandard from './components/modals/createAuditByStandard';
import FollowUpQueryModal from './components/modals/followUpQueryModal';
import FollowUpRequirementModal from './components/modals/followUpRequirementModal';
import NotifyAuditorModal from './components/modals/notifyAuditorModal';
import RaiseClauseStatusModal from './components/modals/raiseClauseStatusModal';
import RaiseRequirementModal from './components/modals/raiseRequirementModal';
import RejectFindingModal from './components/modals/rejectFindingModal';
import RejectRequirementModal from './components/modals/rejectRequirementModal';
import {
  filterClauses,
  transformClause,
  transformClauseRecords,
} from '@/utils/transformClause';
import { auditModes } from './constant';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';

const AuditView = () => {
  const [activeTab, setActiveTab] = useState<number>(0);
  const [activeStandardTab, setActiveStandardTab] = useState<number>(0);
  const [activeProcessTab, setActiveProcessTab] = useState<number>(0);
  const [activeLinkProcessTab, setActiveLinkProcessTab] = useState<number>(0);

  const [loading, setLoading] = useState<boolean>(false);

  const [showCloseAudit, setShowCloseAudit] = useState<boolean>(false);
  const [auditCloseRemark, setAuditCloseRemark] = useState<string>('');

  const [selectedStandardId, setSelectedStandardId] = useState<
    string | undefined
  >();
  const [selectedProcessId, setSelectedProcessId] = useState<
    string | undefined
  >();

  const [createRequirement, setCreateRequirement] = useState<boolean>(false);

  const [clauseStatusValue, setClauseStatusValue] = useState<string>('');
  const [clauseStatusModal, setClauseStatusModal] = useState<boolean>(false);
  const [selectedClauseStatus, setSelectedClauseStatus] = useState<string>('');
  const [selectedClauseId, setSelectedClauseId] = useState<string>('');

  const [rejectFindingModal, setRejectFindingModal] = useState<boolean>(false);
  const [requirementId, setRequirementId] = useState<string>('');
  const [findingId, setFindingId] = useState<string>('');

  const [modalTitle, setModalTitle] = useState<string>('');
  const [modalType, setModalType] = useState<string>('');
  const [commonId, setCommonId] = useState<string>('');
  const [capaDescription, setCapaDescription] = useState<string>('');

  const [showCapaModal, setShowCapaModal] = useState<boolean>(false);

  const [auditFindingResponse, setAuditFindingResponse] =
    useState<boolean>(false);

  const [auditRequirementResponseModal, setAuditRequirementResponseModal] =
    useState<boolean>(false);
  const [auditRequirementFollowUpModal, setAuditRequirementFollowUpModal] =
    useState<boolean>(false);
  const [auditRequirementRejectModal, setAuditRequirementRejectModal] =
    useState<boolean>(false);
  const [auditRequirementAcceptModal, setAuditRequirementAcceptModal] =
    useState<boolean>(false);
  const [transformedClauses, setTransformedClauses] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filteredData, setFilteredData] = useState<any[]>([]);

  const [editAudit, setEditAudit] = useState<boolean>(false);

  const [confirmModal, setConfirmModal] = useState<{
    show: boolean;
    action: string | null;
  }>({
    show: false,
    action: null,
  });

  const [auditFindingFollowUpModal, setAuditFindingFollowUpModal] =
    useState<boolean>(false);

  const [auditClauseData, setAuditClauseData] = useState<any>(null);

  const [requirmentNotifyModal, setRequirmentNotifyModal] =
    useState<boolean>(false);
  const [findingNotifyModal, setFindingNotifyModal] = useState<boolean>(false);

  const { accessToken, user } = useAuthStore();
  const { deleteData } = useDelete();
  const router = useRouter();

  const param = useParams();

  const {
    data: audit,
    isLoading: auditLoading,
    reFetch,
  } = useFetch<{
    record: IAuditDetails;
  }>(accessToken, `audits/${param?.auditId}`, {});

  // dynamic endpoints based on active tab
  const fetchRequirements =
    activeTab === 2 ? `audits/${param?.auditId}/requirements` : undefined;
  const fetchFindings =
    activeTab === 3 ? `audits/${param?.auditId}/non_conformities` : undefined;
  const fetchLogs =
    activeTab === 4 ? `audits/${param?.auditId}/logs` : undefined;

  const {
    data: requirements,
    isLoading: auditRequirementLoading,
    reFetch: reFetchRequirements,
  } = useFetch<Requirements>(accessToken, fetchRequirements, {});

  const {
    data: findings,
    isLoading: auditFindingsLoading,
    error,
    reFetch: reFetchfindings,
  } = useFetch<Findings>(accessToken, fetchFindings, {});

  const {
    data: logs,
    isLoading: auditLogsLoading,
    reFetch: reFetchLogs,
  } = useFetch<{
    records: ILogs[];
  }>(accessToken, fetchLogs, {});

  const { data: processes, isLoading: proccessLoading } = useFetch<{
    records: IProcess[];
  }>(accessToken, `processes`, {});

  const {
    putData,
    response: updateResponse,
    error: updateError,
    isLoading,
  } = usePut();

  const groupedData = useMemo(() => {
    if (!findings || !findings?.records || !Array.isArray(findings?.records))
      return {};

    return findings?.records?.reduce<Record<string | number, NonConformity[]>>(
      (accumulator, currentItem) => {
        const { category } = currentItem;
        if (!accumulator[category]) {
          accumulator[category] = [];
        }
        accumulator[category].push(currentItem);
        return accumulator;
      },
      {},
    );
  }, [findings]);

  const {
    postData: createCapa,
    response: createCapaResponse,
    isLoading: createCapaLoading,
  } = usePost();

  const {
    downloadFile,
    loading: downloadLoading,
    error: downloadError,
  } = useDownload();

  useEffect(() => {
    if (!audit?.record) return;

    const { create_mode, processes = [], standards = [] } = audit.record;

    if (create_mode === auditModes.FROM_PROCESS && processes.length > 0) {
      setSelectedProcessId(processes[0]?.id);
    } else if (standards.length > 0) {
      setSelectedStandardId(standards[0]?.id);
    }
  }, [audit]);

  useEffect(() => {
    if (selectedStandardId) {
      fetchClauseData(`clauses?standard_id=${selectedStandardId}`);
    }
    if (selectedProcessId) {
      fetchClauseData(`clauses?process_id=${selectedProcessId}`);
    }
  }, [selectedStandardId, selectedProcessId]);

  const fetchClauseData = async (endpoint: string) => {
    setLoading(true);
    if (endpoint) {
      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };

      const baseUrl = process.env.NEXT_PUBLIC_URL;
      const productVersion = process.env.NEXT_PUBLIC_VERSION;

      const url = `${baseUrl}/${productVersion}/audits/${param?.auditId}/${endpoint}`;
      try {
        const response = await axios.get(url, config);
        setAuditClauseData(response?.data);
        setLoading(false);
        return;
      } catch (error) {
        console.error('Error fetching clauses:', error);
        setLoading(false);
        return [];
      }
    }
  };

  useEffect(() => {
    const standards = audit?.record?.standards;
    if (!Array.isArray(standards) || standards.length === 0) return;
    const standardId = standards[activeStandardTab]?.id;
    setSelectedStandardId(standardId);
  }, [activeStandardTab]);

  useEffect(() => {
    const processes = audit?.record?.processes;
    if (!Array.isArray(processes) || processes.length === 0) return;
    const processId = processes[activeProcessTab]?.id;
    setSelectedProcessId(processId);
  }, [activeProcessTab]);

  const breadcrumbData = [
    {
      name: 'Audit Hub',
      link: '/audit',
    },
    {
      name: 'Audit View',
      link: '#',
    },
  ];

  const tabsData = [
    { name: 'Info', textColor: 'text-dark-100' },
    {
      name:
        audit?.record?.create_mode === auditModes.FROM_PROCESS
          ? 'Processes'
          : 'Standards',
      textColor: 'text-dark-100',
    },
    { name: 'Requirements', textColor: 'text-dark-100' },
    { name: 'Findings', textColor: 'text-dark-100' },
    { name: 'Logs', textColor: 'text-dark-100' },
  ];

  const linkedProcessTabsData = [
    { name: 'Linked Processes', textColor: 'text-dark-100' },
  ];

  const assetColumns: any = [
    {
      headerName: 'Document Id',
      field: 'doc_id',
      sortable: true,
      resizable: false,
      filter: true,
      cellRenderer: (params: any) => (
        <div className="text-primary-500 cursor-pointer">
          {params.data.doc_id}
        </div>
      ),
    },
    {
      headerName: 'Title',
      field: 'title',
      resizable: false,
      filter: true,
    },

    {
      headerName: 'Owner',
      field: 'assignee.full_name',
      sortable: false,
      resizable: true,
      valueFormatter: (params: any) =>
        getValueOrDefault(params.data.assignee, 'full_name'),
      filter: 'agMultiColumnFilter',
    },
    {
      headerName: 'Status',
      field: 'status',
      resizable: false,
      filter: false,
    },
    {
      headerName: 'Compliant',
      field: 'complaint',
      sortable: false,
      filter: false,
      cellRenderer: (params: any) => (
        <div className="text-primary-500 cursor-pointer h-5 w-5 flex items-center justify-center bg-green-200 rounded-full absolute top-[50%] left-[15%] translate-x-[-50%] translate-y-[-50%]">
          <Check className="h-3 w-3" color="#fff" />
        </div>
      ),
    },
  ];

  const handleChange = (value: string, id: string) => {
    console.log('Test', value, id);
    if (value === 'compliant') {
      return;
    } else if (value === 'requirement') {
      setSelectedClauseId(id);
    } else {
      setClauseStatusModal(true);
      setSelectedClauseStatus(value);
      setSelectedClauseId(id);
    }
  };

  const handleCreateCapa = () => {
    const body = {
      description: capaDescription,
      audit_info: {
        audit_id: param?.auditId,
        sub_clause_id: selectedClauseId,
        nc_id: findingId,
      },
    };

    async function fetch() {
      await createCapa(accessToken as string, `capas`, body);
    }
    fetch();
  };

  const handleAuditAction = useCallback(
    async (data: any) => {
      if (data?.status == 'closed') {
        setLoading(true);
        await putData(accessToken as string, `audits/${param?.auditId}`, {
          status: 'Closed',
          remark: auditCloseRemark,
        });
        setLoading(false);
      } else {
        setLoading(true);
        await putData(accessToken as string, `audits/${param?.auditId}`, {
          status: 'Start',
        });
        setLoading(false);
      }
    },
    [param?.auditId, putData],
  );

  useEffect(() => {
    if (error) {
      alert('Oops! something went wrong');
    }
    if (updateResponse) {
      reFetch();
    }
  }, [updateResponse, updateError]);

  useEffect(() => {
    if (error) {
      alert('Oops! something went wrong');
    }
    if (createCapaResponse) {
      router.push(`/improvement/${(createCapaResponse as { id: string }).id}`);
    }
  }, [createCapaResponse]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!transformedClauses) return;

    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    const searchItems = filterClauses(transformedClauses, term);
    setFilteredData(searchItems || []);
  };

  useEffect(() => {
    if (
      !Array.isArray(auditClauseData?.records) ||
      auditClauseData.records.length === 0
    ) {
      return;
    }

    const records = auditClauseData.records;

    const hasStandard = records[0]?.standard;

    let transformed: any[] = [];

    if (hasStandard) {
      // Handle format with standard
      transformed = records.flatMap((record: any) => {
        const standardTitle = record.standard?.title || 'Other';
        const standardId = record.standard?.id || '';

        return (record.clauses || []).map((clause: any) => {
          const transformedClause = transformClause(clause, 'parent');
          return {
            ...transformedClause,
            standardTitle,
            standardId,
          };
        });
      });
    } else {
      // Handle format without standard
      const clauses = records
        .filter((record: any) => Array.isArray(record.clauses))
        .flatMap((record: any) => record.clauses!);

      transformed =
        clauses.length > 0
          ? clauses.map((clause: any) => transformClause(clause, 'parent'))
          : records.map((clause: any) => transformClause(clause, 'parent'));
    }

    setTransformedClauses(transformed);
  }, [auditClauseData]);

  const renderTabRightSideElement = () => {
    let element = null;

    if (activeTab === 2) {
      if (
        audit?.record?.status === 'Ongoing audit' &&
        Array.isArray(audit?.record?.auditees) &&
        user?.id != null &&
        audit?.record?.auditees?.some((auditee) => auditee.id === user?.id) &&
        hasAccess(AccessActions.IsAuditee, user)
      ) {
        element = (
          <Dialog
            open={requirmentNotifyModal}
            onOpenChange={setRequirmentNotifyModal}
          >
            <DialogTrigger asChild>
              {requirements?.records && requirements?.records.length > 0 && (
                <PrimaryButton
                  text="Notify Auditor"
                  size="medium"
                  disabled={audit?.record?.status !== 'Ongoing audit'}
                />
              )}
            </DialogTrigger>
            <NotifyAuditorModal
              setOpenNotify={setRequirmentNotifyModal}
              title={'requirements'}
              type={'Requirement'}
              data={requirements || { records: [] }}
              reFetch={reFetch}
            />
          </Dialog>
        );
      }
    } else if (activeTab === 3) {
      if (
        audit?.record?.status === 'Ongoing audit' &&
        Array.isArray(audit?.record?.auditees) &&
        user?.id != null &&
        audit?.record?.auditees?.some((auditee) => auditee.id === user?.id) &&
        hasAccess(AccessActions.IsAuditee, user)
      ) {
        element = (
          <Dialog
            open={findingNotifyModal}
            onOpenChange={setFindingNotifyModal}
          >
            <DialogTrigger asChild>
              {findings?.records && findings?.records.length > 0 && (
                <PrimaryButton
                  text="Notify Auditor"
                  size="medium"
                  disabled={audit?.record?.status !== 'Ongoing audit'}
                />
              )}
            </DialogTrigger>
            <NotifyAuditorModal
              setOpenNotify={setFindingNotifyModal}
              title={'NC and OFIs'}
              type={'Non conformity'}
              data={findings || { records: [] }}
              reFetch={reFetch}
            />
          </Dialog>
        );
      }
    } else {
      const isAuditor =
        Array.isArray(audit?.record?.auditors) &&
        audit?.record?.auditors.some((auditor) => auditor.id == user?.id) &&
        hasAccess(AccessActions.CanEditDeleteCloseStartAudit, user);
      element = (
        <>
          {['Ongoing audit', null].includes(audit?.record?.status as string) &&
            isAuditor && (
              <Dialog
                open={confirmModal.show}
                onOpenChange={() =>
                  setConfirmModal({
                    show: !confirmModal.show,
                    action: 'closed',
                  })
                }
              >
                <DialogTrigger asChild>
                  <PrimaryButton
                    text="Close Audit"
                    size="medium"
                    onClick={() =>
                      setConfirmModal({ show: true, action: 'closed' })
                    }
                  />
                </DialogTrigger>
                <ConfirmModal
                  title={'Close Audit'}
                  infoText={
                    'Closing an audit is final. Please provide comments before confirming'
                  }
                  btnText={'Confirm'}
                  onClick={() => {
                    handleAuditAction({ status: confirmModal.action! });
                    setConfirmModal({ show: false, action: null });
                  }}
                  btnLoading={loading}
                  dialogClass="min-w-[45.438rem]"
                  btnDisabled={auditCloseRemark === '' ? true : false}
                >
                  <div>
                    <div className="text-base leading-6 font-medium text-dark-100 mb-2.5 mt-2.5">
                      Comments<span className="text-red-200">*</span>
                    </div>
                    <textarea
                      rows={4}
                      name="remark"
                      placeholder="Comments"
                      value={auditCloseRemark}
                      onChange={(e) => setAuditCloseRemark(e.target.value)}
                      className="text-black text-base w-full rounded-lg border bg-white-100 border-grey-100 hover:border-grey-200 bg-background px-3 py-2 file:border-0 file:bg-transparent file:text-medium file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed disabled:opacity-80 font-medium"
                    />
                  </div>
                </ConfirmModal>
              </Dialog>
            )}

          {audit?.record?.status === 'Closed' && (
            <PrimaryButton
              size="medium"
              isLoading={downloadLoading}
              onClick={() =>
                downloadFile(
                  accessToken as string,
                  `audits/${param?.auditId}/report`,
                  `${audit?.record?.name}-report.pdf`,
                )
              }
              text="Download Report"
            />
          )}

          {['Upcoming audit', 'Past due', null].includes(
            audit?.record?.status as string,
          ) &&
            isAuditor && (
              <Dialog
                open={confirmModal.show}
                onOpenChange={(open) =>
                  setConfirmModal((prevState) => ({ ...prevState, show: open }))
                }
              >
                <DialogTrigger asChild>
                  <PrimaryButton
                    text="Start Audit"
                    size="medium"
                    onClick={() =>
                      setConfirmModal({ show: true, action: 'Start' })
                    }
                  />
                </DialogTrigger>
                <ConfirmModal
                  title={'Start Audit'}
                  infoText={'Are you sure, you want to start this audit?'}
                  btnText={'Confirm'}
                  onClick={() => {
                    handleAuditAction({ status: confirmModal.action! });
                    setConfirmModal({ show: false, action: null });
                  }}
                  btnLoading={loading}
                  dialogClass="min-w-[28.5rem]"
                />
              </Dialog>
            )}
        </>
      );
    }

    // Ensure layout stability with a consistent container.
    return <div style={{ minHeight: '3rem' }}>{element}</div>;
  };
  const loadingData = auditLoading || proccessLoading;

  return (
    <SideBarWrapper>
      {loadingData ? (
        <Loader className="h-[80vh]" />
      ) : (
        <div className="flex flex-col flex-1">
          <div className="my-5">
            <div>
              <Breadcrumb data={breadcrumbData} />
              <div className="text-dark-300 font-semibold text-[1.75rem] leading-10 flex items-center gap-2.5">
                {audit?.record && audit?.record?.name}
                <Status
                  type={
                    audit?.record?.status
                      ?.toLowerCase()
                      .replace('_', ' ')
                      .replace(' audit', '') as string
                  }
                />
              </div>
              <div className="flex justify-between my-2">
                {['Upcoming audit', 'Past due', null].includes(
                  audit?.record?.status as string,
                ) && (
                  <Progress
                    title={'Audit Readiness: '}
                    percent={audit?.record?.percent_complete || 0}
                  />
                )}
              </div>
            </div>

            {audit?.record?.remark && (
              <div className="w-full bg-white-150 rounded-lg p-4 mt-4">
                <p className="text-base leading-6 font-medium">
                  <span className="text-grey-300">Remark: </span>
                  {audit?.record?.remark}
                </p>
              </div>
            )}

            {/* Info, standard, req, finding logs tabs */}
            <div className="mt-4">
              <Tabs
                tabsData={tabsData}
                activeTab={activeTab}
                setActiveTab={setActiveTab}
                tabGroupName="mainTabs"
                tabRightSideElement={renderTabRightSideElement()}
              />
            </div>
          </div>
          <div className="flex-1">
            {activeTab === 0 ? (
              <>
                <>
                  <div className="flex border border-grey-100 bg-white items-start justify-between p-2 rounded-lg">
                    <div className="p-2 flex flex-col gap-3">
                      <DetailsText
                        label="Audit Type"
                        value={audit?.record?.audit_type || '-'}
                      />
                      <DetailsText
                        label="Auditees"
                        value={
                          Array.isArray(audit?.record?.auditees)
                            ? audit?.record?.auditees.map((auditee: User) => ({
                                id: auditee.id,
                                name: auditee.name,
                              }))
                            : '-'
                        }
                        multiValue
                      />
                      <DetailsText
                        label="Auditors"
                        value={
                          Array.isArray(audit?.record?.auditors)
                            ? audit?.record?.auditors.map((auditor: User) => ({
                                id: auditor.id,
                                name: auditor.name,
                              }))
                            : '-'
                        }
                        multiValue
                      />
                    </div>
                    <div className="p-2 flex flex-col gap-3">
                      <DetailsText
                        label="Start Date"
                        value={formatDate(audit?.record?.start_date || '-')}
                      />
                      <DetailsText
                        label="Notification Date"
                        value={formatDate(
                          audit?.record?.notification_date || '-',
                        )}
                      />
                      {audit?.record?.create_mode ===
                        auditModes.FROM_STANDARD ||
                        (audit?.record?.create_mode ===
                          auditModes.FROM_CLAUSE && (
                          <DetailsText
                            label="Standards"
                            value={
                              audit?.record?.standards
                                .flatMap(
                                  (standard: AuditStandard) => standard.title,
                                )
                                .join(', ') || '-'
                            }
                          />
                        ))}

                      {audit?.record?.create_mode ===
                        auditModes.FROM_PROCESS && (
                        <DetailsText
                          label="Processes"
                          value={
                            audit?.record?.processes
                              .map((process: IProcess) => process.name)
                              .join(', ') || '-'
                          }
                        />
                      )}
                    </div>

                    <div className="flex items-center gap-3">
                      {(editAudit &&
                        Array.isArray(audit?.record?.auditors) &&
                        user?.id != null &&
                        audit.record.auditors.some(
                          (auditor: User) => auditor.id === user.id,
                        ) &&
                        hasAccess(
                          AccessActions.CanEditDeleteCloseStartAudit,
                          user,
                        )) ||
                      (Array.isArray(audit?.record?.auditees) &&
                        user?.id != null &&
                        audit.record.auditees.some(
                          (auditor: User) => auditor.id === user.id,
                        ) &&
                        hasAccess(
                          AccessActions.CanEditDeleteCloseStartAudit,
                          user,
                        )) ? (
                        <div>
                          <SecondaryButton
                            size="medium"
                            icon={
                              <EditIcon color="#016366" className="h-5 w-5" />
                            }
                            onClick={() => setEditAudit(true)}
                            text="Edit"
                          />

                          <Dialog open={editAudit} onOpenChange={setEditAudit}>
                            <CreateAuditByStandard
                              edit
                              auditData={audit?.record}
                              setOpenEdit={setEditAudit}
                              reFetch={reFetch}
                            />
                          </Dialog>
                        </div>
                      ) : (
                        ''
                      )}

                      {(Array.isArray(audit?.record?.auditors) &&
                        user?.id != null &&
                        audit.record.auditors.some(
                          (auditor: User) => auditor.id === user.id,
                        ) &&
                        hasAccess(
                          AccessActions.CanEditDeleteCloseStartAudit,
                          user,
                        )) ||
                      (Array.isArray(audit?.record?.auditees) &&
                        user?.id != null &&
                        audit.record.auditees.some(
                          (auditor: User) => auditor.id === user.id,
                        ) &&
                        hasAccess(
                          AccessActions.CanEditDeleteCloseStartAudit,
                          user,
                        )) ? (
                        <Dialog>
                          <DialogTrigger asChild>
                            <DeleteButton />
                          </DialogTrigger>
                          <DeleteModal
                            title="Delete"
                            infoText="Are you sure, you want to delete this audit?"
                            btnText="Delete"
                            onClick={() => {
                              async function fetch() {
                                await deleteData(
                                  accessToken as string,
                                  `audits/${param?.auditId}`,
                                );
                              }
                              fetch();
                              router.push('/audit');
                            }}
                          >
                            <div className="p-2 border flex flex-col gap-4 border-white-300 bg-white-100 px-2.5 py-2 rounded-lg">
                              <div className="flex justify-between items-center">
                                <div className="text-sm font-medium leading-5 text-grey-300">
                                  ID
                                </div>
                                <div className="text-base font-medium leading-6 text-dark-300">
                                  {audit?.record?.id}
                                </div>
                              </div>
                              <div className="flex justify-between items-center">
                                <div className="text-sm font-medium leading-5 text-grey-300">
                                  Audit name
                                </div>
                                <div className="text-base font-medium leading-6 text-dark-300">
                                  {audit?.record?.name}
                                </div>
                              </div>
                            </div>
                          </DeleteModal>
                        </Dialog>
                      ) : (
                        ''
                      )}
                    </div>
                  </div>
                </>
              </>
            ) : activeTab === 1 ? (
              <>
                <div className="py-2.5 px-3 mb-4 mt-0 bg-white flex items-center rounded-lg gap-1 border border-grey-100 max-w-[40rem] focus-within:border-primary-100">
                  <Search className="h-5 w-5" color="#B9B9B9" />
                  <input
                    type="text"
                    placeholder="Search by clause or number"
                    className="flex-1 focus-visible:outline-none"
                    value={searchTerm}
                    onChange={handleSearch}
                  />
                </div>

                {/* tabbed standard */}
                <Tabs
                  tabsData={(audit?.record?.standards ?? []).map(
                    (standard) => ({
                      name: standard.title,
                      textColor: 'text-dark-100',
                    }),
                  )}
                  tabGroupName="standardTabs"
                  activeTab={activeStandardTab}
                  setActiveTab={setActiveStandardTab}
                />
                <Tabs
                  tabsData={(audit?.record?.processes ?? []).map((process) => ({
                    name: process.name,
                    textColor: 'text-dark-100',
                  }))}
                  tabGroupName="processTabs"
                  activeTab={activeProcessTab}
                  setActiveTab={setActiveProcessTab}
                />

                {loading ? (
                  <Loader className="h-[40vh]" />
                ) : (
                  <div className="mt-4">
                    {Object.entries(
                      (
                        (filteredData?.length > 0 && filteredData) ||
                        transformedClauses
                      )?.reduce((acc: Record<string, any[]>, clause: any) => {
                        const title = clause.standardTitle || '';
                        if (!acc[title]) acc[title] = [];
                        acc[title].push(clause);
                        return acc;
                      }, {}),
                    ).map(([standardTitle, clauses]) => (
                      <div key={standardTitle} className="mb-6">
                        <h2 className="text-grey-300 font-medium text-base mb-2">
                          {standardTitle}
                        </h2>
                        <Accordion type="single" collapsible className="w-full">
                          {clauses.map((clause, index) => (
                            <RecursiveAccordion
                              key={`${clause.standardId}-${clause.id}`}
                              clause={clause}
                              processes={processes}
                              reFetch={reFetch}
                              user={user}
                              linkedProcessTabsData={linkedProcessTabsData}
                              activeLinkProcessTab={activeLinkProcessTab}
                              setActiveLinkProcessTab={setActiveLinkProcessTab}
                              audit={audit}
                              createRequirement={createRequirement}
                              setCreateRequirement={setCreateRequirement}
                              handleChange={handleChange}
                              selectedClauseId={selectedClauseId}
                              clauseStatusModal={clauseStatusModal}
                              setClauseStatusModal={setClauseStatusModal}
                              setClauseStatusValue={setClauseStatusValue}
                              selectedClauseStatus={selectedClauseStatus}
                              standardTitle={standardTitle}
                            />
                          ))}
                        </Accordion>
                      </div>
                    ))}
                  </div>
                )}
              </>
            ) : activeTab === 2 ? (
              auditRequirementLoading ? (
                <Loader className="h-[400px]" />
              ) : (
                <>
                  {requirements &&
                    requirements?.records?.map(
                      (requirement: AuditRequirement, index) => (
                        <Accordion
                          key={index}
                          type="single"
                          collapsible
                          className="w-full mb-2"
                        >
                          <AccordionItem value="requirements" className="">
                            <AccordionTrigger>
                              <div className="w-full flex justify-between items-center">
                                <div className="flex gap-3">
                                  <p className="text-grey-300 font-medium text-base">
                                    {requirement?.standard?.title}
                                  </p>
                                  <p className="text-black font-medium text-base">
                                    {requirement?.sub_clause?.clause_no +
                                      '. ' +
                                      requirement?.sub_clause?.title}
                                  </p>
                                </div>
                                <div className="px-4">
                                  <Status
                                    type={requirement?.status.toLowerCase()}
                                  />
                                </div>
                              </div>
                            </AccordionTrigger>
                            <AccordionContent className="py-4 px-5 flex flex-col gap-4">
                              <div>
                                <div className="w-full bg-white-150 rounded-lg p-4">
                                  <p className="text-base leading-6 font-medium">
                                    <span className="text-grey-300">
                                      Description:{' '}
                                    </span>
                                    {requirement?.description}
                                  </p>
                                </div>

                                {requirement?.remark && (
                                  <div className="mt-4 px-2 border border-stroke rounded-md p-4">
                                    <p className="text-base leading-6 font-medium">
                                      <span
                                        className={`${
                                          requirement?.action === 'Accepted'
                                            ? 'bg-[#37FF6329] text-[#49B380]'
                                            : 'bg-red-100 text-red-400'
                                        } px-4 py-2 text-sm mr-2 rounded-full`}
                                      >
                                        {requirement?.action}
                                      </span>{' '}
                                      {requirement?.remark}
                                    </p>
                                  </div>
                                )}

                                {requirement?.justification && (
                                  <div className="mt-5">
                                    <label className="text-base leading-6 mb-2.5 block font-medium text-black opacity-70">
                                      Justification
                                    </label>
                                    <textarea
                                      rows={4}
                                      name="description"
                                      disabled
                                      value={requirement?.justification}
                                      className="text-black text-base w-full rounded-lg border bg-white-100 border-grey-100 hover:border-grey-200 bg-background px-3 py-2 file:border-0 file:bg-transparent file:text-medium file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed disabled:opacity-80 font-medium"
                                    />
                                  </div>
                                )}

                                {requirement?.comments?.length > 0 && (
                                  <div className="mt-5 min-h-64 bg-white-150 rounded-lg p-5">
                                    <div>
                                      {requirement?.comments?.map(
                                        (comment, index) => (
                                          <div
                                            key={index}
                                            className="w-full bg-white-100 border border-white-300 rounded-lg p-4 mb-2"
                                          >
                                            <p className="text-base leading-6 font-medium mb-1">
                                              {comment?.created_by?.full_name}{' '}
                                              {comment?.created_by?.role && (
                                                <span className="text-grey-300 px-2">
                                                  {comment?.created_by?.role}
                                                </span>
                                              )}
                                            </p>
                                            <p className="text-grey-300 mb-1">
                                              <time
                                                className="font-medium"
                                                dateTime={comment?.created_on}
                                              >
                                                {moment(
                                                  comment?.created_on,
                                                ).format('h:mm, MMM Do YYYY')}
                                              </time>
                                            </p>
                                            <p
                                              onClick={(e) =>
                                                e.preventDefault()
                                              }
                                              dangerouslySetInnerHTML={{
                                                __html: comment?.description,
                                              }}
                                              className="text-base leading-6 font-medium mb-1"
                                            />

                                            {comment?.attachements.length > 0 &&
                                              comment?.attachements.map(
                                                (attachement, index) => (
                                                  <>
                                                    <Dialog>
                                                      <DialogTrigger asChild>
                                                        {[
                                                          'pdf',
                                                          'docx',
                                                          'doc',
                                                          'jpg',
                                                          'png',
                                                          'jpeg',
                                                        ].includes(
                                                          attachement?.file_extension,
                                                        ) && (
                                                          <div
                                                            className="hover:cursor-pointer w-fit mt-3 p-1.5 rounded-md bg-white-100 border border-white-300 flex items-center gap-2 relative group"
                                                            onClick={(e) =>
                                                              e.stopPropagation()
                                                            }
                                                          >
                                                            <div className="h-8 w-8 flex items-center justify-center rounded bg-[#5A91FF]">
                                                              <DocumentIcon
                                                                height="20"
                                                                width="20"
                                                                color="#fff"
                                                              />
                                                            </div>
                                                            <div>
                                                              {attachement?.file_path
                                                                .split('/')
                                                                .pop() || ''}
                                                            </div>
                                                          </div>
                                                        )}
                                                      </DialogTrigger>
                                                      <DocumentViewModal
                                                        title={'Test'}
                                                        filePath={
                                                          attachement?.file_path
                                                        }
                                                        extension={
                                                          attachement?.file_extension as
                                                            | 'html'
                                                            | 'pdf'
                                                            | 'png'
                                                            | 'jpeg'
                                                            | 'jpg'
                                                        }
                                                        dialogClass="min-w-[95%]"
                                                      />
                                                    </Dialog>
                                                  </>
                                                ),
                                              )}
                                          </div>
                                        ),
                                      )}
                                    </div>
                                  </div>
                                )}

                                {/* Checking if requirement is open */}
                                {requirement.status.toLowerCase() ===
                                  'open' && (
                                  <div>
                                    {Array.isArray(audit?.record?.auditors) &&
                                    user?.id != null &&
                                    audit?.record?.auditors?.some(
                                      (auditor: User) =>
                                        auditor.id === user?.id,
                                    ) &&
                                    hasAccess(AccessActions.IsAuditor, user) ? (
                                      <div className="flex justify-end items-center gap-4 mt-4">
                                        <Dialog
                                          open={auditRequirementRejectModal}
                                          onOpenChange={
                                            setAuditRequirementRejectModal
                                          }
                                        >
                                          <DialogTrigger asChild>
                                            <TertiaryButton
                                              text="Reject"
                                              buttonClasses="!px-5 !py-2"
                                              size="medium"
                                              onClick={() => {
                                                setRequirementId(
                                                  requirement.id,
                                                );
                                              }}
                                              disabled={
                                                audit?.record?.status ===
                                                'Ongoing audit'
                                                  ? false
                                                  : true
                                              }
                                            />
                                          </DialogTrigger>
                                          <RejectRequirementModal
                                            setOpenRequirement={
                                              setAuditRequirementRejectModal
                                            }
                                            requirementId={requirementId}
                                            subClauseId={
                                              requirement?.sub_clause?.id
                                            }
                                            reFetchRequirements={reFetch}
                                            reFetchFinding={reFetch}
                                          />
                                        </Dialog>

                                        <Dialog
                                          open={auditRequirementAcceptModal}
                                          onOpenChange={
                                            setAuditRequirementAcceptModal
                                          }
                                        >
                                          <DialogTrigger asChild>
                                            <SecondaryButton
                                              text="Accept"
                                              buttonClasses="!px-5 !py-2"
                                              size="medium"
                                              onClick={() => {
                                                setModalTitle('Requirement');
                                                setModalType('requirements');
                                                setCommonId(requirement.id);
                                              }}
                                              disabled={
                                                audit?.record?.status ===
                                                'Ongoing audit'
                                                  ? false
                                                  : true
                                              }
                                            />
                                          </DialogTrigger>
                                          <AcceptRequirementModal
                                            setOpenRequirement={
                                              setAuditRequirementAcceptModal
                                            }
                                            commonId={commonId}
                                            modalType={modalType}
                                            modalTitle={modalTitle}
                                            reFetch={reFetchRequirements}
                                          />
                                        </Dialog>

                                        <Dialog
                                          open={auditRequirementFollowUpModal}
                                          onOpenChange={
                                            setAuditRequirementFollowUpModal
                                          }
                                        >
                                          <DialogTrigger asChild>
                                            <PrimaryButton
                                              text="Follow-up query"
                                              buttonClasses="!px-5 !py-2"
                                              size="medium"
                                              onClick={() => {
                                                setRequirementId(
                                                  requirement.id,
                                                );
                                              }}
                                              disabled={
                                                audit?.record?.status ===
                                                'Ongoing audit'
                                                  ? false
                                                  : true
                                              }
                                            />
                                          </DialogTrigger>
                                          <FollowUpRequirementModal
                                            setOpenRequirement={
                                              setAuditRequirementFollowUpModal
                                            }
                                            requirementId={requirementId}
                                            reFetch={reFetchRequirements}
                                          />
                                        </Dialog>
                                      </div>
                                    ) : (
                                      ''
                                    )}

                                    {/* Actions belong to auditees */}
                                    {Array.isArray(audit?.record?.auditees) &&
                                    user?.id != null &&
                                    audit?.record?.auditees?.some(
                                      (auditee: User) =>
                                        auditee.id === user?.id,
                                    ) &&
                                    hasAccess(AccessActions.IsAuditee, user) ? (
                                      <div className="flex justify-end items-center gap-4 mt-4">
                                        <Dialog
                                          open={auditRequirementResponseModal}
                                          onOpenChange={
                                            setAuditRequirementResponseModal
                                          }
                                        >
                                          <DialogTrigger asChild>
                                            <PrimaryButton
                                              text="Add Response"
                                              buttonClasses="!px-5 !py-2"
                                              size="medium"
                                              onClick={() => {
                                                setRequirementId(
                                                  requirement.id,
                                                );
                                                setSelectedClauseStatus(
                                                  requirement.category,
                                                );
                                              }}
                                              disabled={
                                                audit?.record?.status ===
                                                'Ongoing audit'
                                                  ? false
                                                  : true
                                              }
                                            />
                                          </DialogTrigger>
                                          <AuditRequirementResponseModal
                                            setOpenRequirement={
                                              setAuditRequirementResponseModal
                                            }
                                            subClauseId={selectedClauseId}
                                            requirementId={requirementId}
                                            reFetch={reFetchRequirements}
                                          />
                                        </Dialog>
                                      </div>
                                    ) : (
                                      ''
                                    )}
                                  </div>
                                )}
                              </div>
                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      ),
                    )}

                  {requirements?.records?.length === 0 && (
                    <div className="w-full h-24 bg-white-100 border border-white-300 rounded-lg p-4">
                      <p className="text-base text-dark-300 leading-6 font-medium">
                        No requirements have been added yet
                      </p>
                    </div>
                  )}
                </>
              )
            ) : activeTab === 3 ? (
              auditFindingsLoading ? (
                <Loader className="h-[400px]" />
              ) : (
                <>
                  {Object.entries(groupedData).map(
                    ([category, items], index) => (
                      <div key={index}>
                        <h2 className=" text-grey-300 font-medium text-base">
                          {category}
                        </h2>
                        <div>
                          {items &&
                            items?.map((finding: NonConformity, index) => (
                              <Accordion
                                type="single"
                                key={index}
                                collapsible
                                className="w-full mt-2"
                              >
                                <AccordionItem value="findings" className="">
                                  <AccordionTrigger>
                                    <div className="w-full flex justify-between items-center">
                                      <div className="flex gap-3">
                                        <p className="text-grey-300 font-medium text-base">
                                          {finding?.standard?.title}
                                        </p>
                                        <p className="text-black font-medium text-base">
                                          {finding?.sub_clause?.clause_no +
                                            '. ' +
                                            finding?.sub_clause?.title}
                                        </p>
                                      </div>
                                      <div className="px-4">
                                        <Status
                                          type={finding?.status.toLowerCase()}
                                        />
                                      </div>
                                    </div>
                                  </AccordionTrigger>
                                  <AccordionContent className="py-4 px-5 flex flex-col gap-4">
                                    <div>
                                      <div className="w-full bg-white-150 rounded-lg p-4">
                                        <p className="text-base leading-6 font-medium">
                                          <span className="text-grey-300">
                                            NC ID:{' '}
                                          </span>
                                          {finding?.nc_id}
                                        </p>
                                      </div>

                                      {finding?.remark && (
                                        <div className="mt-4 px-2 border border-stroke rounded-md p-4">
                                          <p className="text-base leading-6 font-medium">
                                            <span
                                              className={`${
                                                finding?.action === 'Accepted'
                                                  ? 'bg-[#37FF6329] text-[#49B380]'
                                                  : 'bg-red-100 text-red-400'
                                              } px-4 py-2 text-sm mr-2 rounded-full`}
                                            >
                                              {finding?.action}
                                            </span>{' '}
                                            {finding?.remark}
                                          </p>
                                        </div>
                                      )}

                                      <div className="mt-5">
                                        <label className="text-base leading-6 mb-2.5 block font-medium text-black opacity-70">
                                          {finding?.category} Description
                                        </label>
                                        <textarea
                                          rows={4}
                                          name="description"
                                          disabled
                                          value={finding?.description}
                                          className="text-black text-base w-full rounded-lg border bg-white-100 border-grey-100 hover:border-grey-200 bg-background px-3 py-2 file:border-0 file:bg-transparent file:text-medium file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed disabled:opacity-80 font-medium"
                                        />
                                      </div>

                                      {finding?.justification && (
                                        <div className="mt-5">
                                          <label className="text-base leading-6 mb-2.5 block font-medium text-black opacity-70">
                                            Justification
                                          </label>
                                          <textarea
                                            rows={4}
                                            name="description"
                                            disabled
                                            value={finding?.justification}
                                            className="text-black text-base w-full rounded-lg border bg-white-100 border-grey-100 hover:border-grey-200 bg-background px-3 py-2 file:border-0 file:bg-transparent file:text-medium file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed disabled:opacity-80 font-medium"
                                          />
                                        </div>
                                      )}

                                      {finding?.comments?.length > 0 && (
                                        <div className="mt-5 min-h-64 bg-white-150 rounded-lg p-5">
                                          {finding?.comments?.map(
                                            (comment, index) => (
                                              <div
                                                key={index}
                                                className="w-full bg-white-100 border border-white-300 rounded-lg p-4 mt-2"
                                              >
                                                <p className="text-base leading-6 font-medium mb-1">
                                                  {
                                                    comment?.created_by
                                                      ?.full_name
                                                  }{' '}
                                                  {comment?.created_by
                                                    ?.role && (
                                                    <span className="text-grey-300 px-2">
                                                      {
                                                        comment?.created_by
                                                          ?.role
                                                      }
                                                    </span>
                                                  )}
                                                </p>
                                                <p className="text-grey-300 mb-1">
                                                  <time
                                                    className="font-medium"
                                                    dateTime={
                                                      comment?.created_on
                                                    }
                                                  >
                                                    {moment(
                                                      comment?.created_on,
                                                    ).format(
                                                      'h:mm, MMM Do YYYY',
                                                    )}
                                                  </time>
                                                </p>
                                                <p
                                                  onClick={(e) =>
                                                    e.preventDefault()
                                                  }
                                                  dangerouslySetInnerHTML={{
                                                    __html:
                                                      comment?.description,
                                                  }}
                                                  className="text-base leading-6 font-medium mb-1"
                                                />

                                                {comment?.attachements.length >
                                                  0 &&
                                                  comment?.attachements.map(
                                                    (attachement, index) => (
                                                      <>
                                                        <Dialog>
                                                          <DialogTrigger
                                                            asChild
                                                          >
                                                            {[
                                                              'pdf',
                                                              'docx',
                                                              'doc',
                                                              'jpg',
                                                              'png',
                                                              'jpeg',
                                                            ].includes(
                                                              attachement?.file_extension,
                                                            ) && (
                                                              <div
                                                                className="hover:cursor-pointer w-fit mt-3 p-1.5 rounded-md bg-white-100 border border-white-300 flex items-center gap-2 relative group"
                                                                onClick={(e) =>
                                                                  e.stopPropagation()
                                                                }
                                                              >
                                                                <div className="h-8 w-8 flex items-center justify-center rounded bg-[#5A91FF]">
                                                                  <DocumentIcon
                                                                    height="20"
                                                                    width="20"
                                                                    color="#fff"
                                                                  />
                                                                </div>
                                                                <div>
                                                                  {attachement?.file_path
                                                                    .split('/')
                                                                    .pop() ||
                                                                    ''}
                                                                </div>
                                                              </div>
                                                            )}
                                                          </DialogTrigger>
                                                          <DocumentViewModal
                                                            title={'Test'}
                                                            filePath={
                                                              attachement?.file_path
                                                            }
                                                            extension={
                                                              attachement?.file_extension as
                                                                | 'html'
                                                                | 'pdf'
                                                                | 'png'
                                                                | 'jpeg'
                                                                | 'jpg'
                                                            }
                                                            dialogClass="min-w-[95%]"
                                                          />
                                                        </Dialog>
                                                      </>
                                                    ),
                                                  )}
                                              </div>
                                            ),
                                          )}
                                        </div>
                                      )}

                                      {/* Checking if finding is open */}
                                      <div className="flex justify-end items-center gap-4 mt-4">
                                        {finding?.capa && (
                                          <TertiaryButton
                                            text="View linked CAPA"
                                            buttonClasses="!px-5 !py-2"
                                            size="medium"
                                            onClick={() =>
                                              router.push(
                                                `/improvement/${finding?.capa}`,
                                              )
                                            }
                                          />
                                        )}
                                        {finding?.status.toLowerCase() ==
                                          'open' &&
                                          !finding.capa && (
                                            <div>
                                              {Array.isArray(
                                                audit?.record?.auditors,
                                              ) &&
                                              user?.id != null &&
                                              audit?.record?.auditors?.some(
                                                (auditor: User) =>
                                                  auditor.id === user?.id,
                                              ) &&
                                              hasAccess(
                                                AccessActions.IsAuditor,
                                                user,
                                              ) ? (
                                                <div className="flex justify-end items-center gap-4">
                                                  <Dialog
                                                    open={rejectFindingModal}
                                                    onOpenChange={
                                                      setRejectFindingModal
                                                    }
                                                  >
                                                    <DialogTrigger asChild>
                                                      <TertiaryButton
                                                        text="Reject"
                                                        buttonClasses="!px-5 !py-2"
                                                        size="medium"
                                                        onClick={() => {
                                                          setFindingId(
                                                            finding.id,
                                                          );
                                                          setSelectedClauseStatus(
                                                            finding.category,
                                                          );
                                                        }}
                                                        disabled={
                                                          audit?.record
                                                            ?.status ===
                                                          'Ongoing audit'
                                                            ? false
                                                            : true
                                                        }
                                                      />
                                                    </DialogTrigger>
                                                    <RejectFindingModal
                                                      setOpenRequirement={
                                                        setRejectFindingModal
                                                      }
                                                      findingId={findingId}
                                                      selectedStatus={
                                                        selectedClauseStatus
                                                      }
                                                      reFetch={reFetchfindings}
                                                    />
                                                  </Dialog>

                                                  <Dialog
                                                    open={
                                                      auditRequirementAcceptModal
                                                    }
                                                    onOpenChange={
                                                      setAuditRequirementAcceptModal
                                                    }
                                                  >
                                                    <DialogTrigger asChild>
                                                      <SecondaryButton
                                                        text="Accept"
                                                        buttonClasses="!px-5 !py-2"
                                                        size="medium"
                                                        onClick={() => {
                                                          setModalTitle(
                                                            finding.category,
                                                          );
                                                          setModalType(
                                                            'non_conformities',
                                                          );
                                                          setCommonId(
                                                            finding.id,
                                                          );
                                                        }}
                                                        disabled={
                                                          audit?.record
                                                            ?.status ===
                                                          'Ongoing audit'
                                                            ? false
                                                            : true
                                                        }
                                                      />
                                                    </DialogTrigger>
                                                    <AcceptRequirementModal
                                                      setOpenRequirement={
                                                        setAuditRequirementAcceptModal
                                                      }
                                                      commonId={commonId}
                                                      modalType={modalType}
                                                      modalTitle={modalTitle}
                                                      reFetch={reFetchfindings}
                                                    />
                                                  </Dialog>

                                                  <Dialog
                                                    open={
                                                      auditFindingFollowUpModal
                                                    }
                                                    onOpenChange={
                                                      setAuditFindingFollowUpModal
                                                    }
                                                  >
                                                    <DialogTrigger asChild>
                                                      <PrimaryButton
                                                        text="Follow-up query"
                                                        buttonClasses="!px-5 !py-2"
                                                        size="medium"
                                                        onClick={() => {
                                                          setFindingId(
                                                            finding.id,
                                                          );
                                                        }}
                                                        disabled={
                                                          audit?.record
                                                            ?.status ===
                                                          'Ongoing audit'
                                                            ? false
                                                            : true
                                                        }
                                                      />
                                                    </DialogTrigger>
                                                    <FollowUpQueryModal
                                                      setOpenRequirement={
                                                        setAuditFindingFollowUpModal
                                                      }
                                                      findingId={findingId}
                                                      reFetch={reFetchfindings}
                                                    />
                                                  </Dialog>
                                                </div>
                                              ) : (
                                                ''
                                              )}

                                              {Array.isArray(
                                                audit?.record?.auditees,
                                              ) &&
                                              user?.id != null &&
                                              audit?.record?.auditees?.some(
                                                (auditee: User) =>
                                                  auditee.id === user?.id,
                                              ) &&
                                              hasAccess(
                                                AccessActions.IsAuditee,
                                                user,
                                              ) ? (
                                                <div className="flex justify-end items-center gap-4">
                                                  {/* Additional checks */}
                                                  {finding?.category !==
                                                    'Opportunity for Improvement' &&
                                                  !finding?.capa ? (
                                                    <Dialog
                                                      open={showCapaModal}
                                                      onOpenChange={
                                                        setShowCapaModal
                                                      }
                                                    >
                                                      <DialogTrigger asChild>
                                                        <SecondaryButton
                                                          text="Raise a CAPA"
                                                          size="medium"
                                                          onClick={() => {
                                                            setFindingId(
                                                              finding.id,
                                                            );
                                                            setSelectedClauseId(
                                                              finding
                                                                ?.sub_clause.id,
                                                            );
                                                            setCapaDescription(
                                                              finding.description,
                                                            );
                                                          }}
                                                          disabled={
                                                            audit?.record
                                                              ?.status ===
                                                            'Ongoing audit'
                                                              ? false
                                                              : true
                                                          }
                                                        />
                                                      </DialogTrigger>
                                                      <ConfirmModal
                                                        title={'Confirm'}
                                                        infoText={
                                                          'Are you sure, you want to raise a CAPA for this NC?'
                                                        }
                                                        btnText={'Confirm'}
                                                        onClick={() =>
                                                          handleCreateCapa()
                                                        }
                                                        btnLoading={
                                                          createCapaLoading
                                                        }
                                                        dialogClass="min-w-[28.5rem]"
                                                      ></ConfirmModal>
                                                    </Dialog>
                                                  ) : null}

                                                  {!finding?.capa && (
                                                    <Dialog
                                                      open={
                                                        auditFindingResponse
                                                      }
                                                      onOpenChange={
                                                        setAuditFindingResponse
                                                      }
                                                    >
                                                      <DialogTrigger asChild>
                                                        <PrimaryButton
                                                          text="Add Response"
                                                          buttonClasses="!px-5 !py-2"
                                                          size="medium"
                                                          onClick={() => {
                                                            setFindingId(
                                                              finding.id,
                                                            );
                                                            setSelectedClauseStatus(
                                                              finding.category,
                                                            );
                                                          }}
                                                          disabled={
                                                            audit?.record
                                                              ?.status ===
                                                            'Ongoing audit'
                                                              ? false
                                                              : true
                                                          }
                                                        />
                                                      </DialogTrigger>
                                                      <AuditFindingResponseModal
                                                        setOpenRequirement={
                                                          setAuditFindingResponse
                                                        }
                                                        subClauseId={
                                                          selectedClauseId
                                                        }
                                                        findingId={findingId}
                                                        reFetch={
                                                          reFetchfindings
                                                        }
                                                      />
                                                    </Dialog>
                                                  )}
                                                </div>
                                              ) : (
                                                ''
                                              )}
                                            </div>
                                          )}
                                      </div>
                                    </div>
                                  </AccordionContent>
                                </AccordionItem>
                              </Accordion>
                            ))}
                        </div>
                      </div>
                    ),
                  )}
                  {findings?.records?.length === 0 && (
                    <div className="w-full h-24 bg-white-100 border border-white-300 rounded-lg p-4">
                      <p className="text-base text-dark-300 leading-6 font-medium">
                        No findings have been added yet
                      </p>
                    </div>
                  )}
                </>
              )
            ) : activeTab === 4 ? (
              auditLogsLoading ? (
                <Loader className="h-[400px]" />
              ) : (
                <>
                  {logs?.records?.length && logs?.records?.length > 0 ? (
                    logs?.records.map((e, i) => <LogsCard logs={e} key={i} />)
                  ) : (
                    <>
                      <div className="w-full h-24 bg-white-100 border border-white-300 rounded-lg p-4">
                        <p className="text-base text-dark-300 leading-6 font-medium">
                          No logs have been added yet
                        </p>
                      </div>
                    </>
                  )}
                </>
              )
            ) : (
              ''
            )}
          </div>
        </div>
      )}
    </SideBarWrapper>
  );
};

const RecursiveAccordion = ({
  clause,
  processes,
  reFetch,
  user,
  linkedProcessTabsData,
  activeLinkProcessTab,
  setActiveLinkProcessTab,
  audit,
  createRequirement,
  setCreateRequirement,
  handleChange,
  selectedClauseId,
  clauseStatusModal,
  setClauseStatusModal,
  setClauseStatusValue,
  selectedClauseStatus,
  standardTitle,
  isChild,
}: {
  clause: any;
  processes: any;
  reFetch: any;
  user: any;
  linkedProcessTabsData: any;
  activeLinkProcessTab: any;
  setActiveLinkProcessTab: any;
  audit: any;
  createRequirement: any;
  setCreateRequirement: any;
  handleChange: any;
  selectedClauseId: any;
  clauseStatusModal: any;
  setClauseStatusModal: any;
  setClauseStatusValue: any;
  selectedClauseStatus: any;
  standardTitle?: string;
  isChild?: boolean;
}) => {
  return (
    <>
      <Accordion type="single" collapsible className="w-full mt-2">
        <AccordionItem value={`item-${clause.id}`}>
          <AccordionTrigger className="">
            <div className="flex flex-1 items-center justify-between mr-2">
              <div className="flex items-center gap-2">
                <span>{clause.clause_no}</span>
                <div className="text-dark-300 text-base leading-6 font-medium">
                  {clause.title}
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div
                  className={cn(
                    'rounded-full text-xs font-bold px-3 py-1',
                    !clause.is_active
                      ? 'bg-gray-50 text-gray-400'
                      : clause.is_compliant
                      ? 'bg-green-50 text-primary-400'
                      : 'bg-red-50 text-red-400',
                  )}
                >
                  {!clause.is_active ? (
                    <span>Out of scope</span>
                  ) : clause.is_compliant ? (
                    <span>Compliant</span>
                  ) : (
                    <span>Non-Compliant</span>
                  )}
                </div>
              </div>
            </div>
          </AccordionTrigger>

          <AccordionContent className="py-2 px-5 flex flex-col">
            {/* Render child clauses or sub-clauses */}
            {clause.children?.map((child: any, index: number) => {
              // Recursively render child elements (could be sub-clause or nested clauses)
              return (
                <RecursiveAccordion
                  key={child.id || index}
                  clause={child}
                  processes={processes}
                  reFetch={reFetch}
                  user={user}
                  linkedProcessTabsData={linkedProcessTabsData}
                  activeLinkProcessTab={activeLinkProcessTab}
                  setActiveLinkProcessTab={setActiveLinkProcessTab}
                  audit={audit}
                  createRequirement={createRequirement}
                  setCreateRequirement={setCreateRequirement}
                  handleChange={handleChange}
                  selectedClauseId={selectedClauseId}
                  clauseStatusModal={clauseStatusModal}
                  setClauseStatusModal={setClauseStatusModal}
                  setClauseStatusValue={setClauseStatusValue}
                  selectedClauseStatus={selectedClauseStatus}
                  isChild={
                    !!(
                      child.description ||
                      child.question ||
                      (child.processes && child.processes.length > 0)
                    )
                  }
                />
              );
            })}

            {clause.justification ? (
              <div className="mt-4">
                <div className="text-dark-300 text-lg font-semibold leading-7 mb-2">
                  Out of scope Justification:
                </div>
                <div className="text-dark-100 text-base leading-6 font-medium">
                  <TextWithLineBreaks text={clause.justification} />
                </div>
              </div>
            ) : (
              ''
            )}

            {clause.description && clause.is_active && (
              <div className="mt-2">
                {Array.isArray(audit?.record?.auditors) &&
                user?.id != null &&
                audit?.record?.auditors?.some(
                  (auditor: User) => auditor.id === user?.id,
                ) &&
                hasAccess(AccessActions.IsAuditor, user) ? (
                  <div className="flex justify-between items-center mt-2">
                    <Dialog
                      open={createRequirement}
                      onOpenChange={setCreateRequirement}
                    >
                      <DialogTrigger asChild>
                        <PrimaryButton
                          text="Raise a requirement"
                          buttonClasses="!px-5 !py-2"
                          size="medium"
                          disabled={
                            audit?.record?.status === 'Ongoing audit'
                              ? false
                              : true
                          }
                          onClick={() => {
                            handleChange('requirement', clause.id);
                          }}
                        />
                      </DialogTrigger>
                      <RaiseRequirementModal
                        setOpenRequirement={setCreateRequirement}
                        subClauseId={selectedClauseId}
                        reFetch={reFetch}
                      />
                    </Dialog>

                    <div>
                      <Dialog
                        open={clauseStatusModal}
                        onOpenChange={setClauseStatusModal}
                      >
                        <DialogTrigger asChild>
                          <Select
                            disabled={
                              audit?.record?.status === 'Ongoing audit'
                                ? false
                                : true
                            }
                            value={clause.status}
                            defaultValue={''}
                            onValueChange={(value) => {
                              setClauseStatusValue(value);
                              handleChange(value, clause.id);
                            }}
                          >
                            <SelectTrigger className="w-[300px]">
                              <SelectValue
                                className="font-medium"
                                placeholder="Status"
                              />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem
                                className="font-medium"
                                value="Compliant"
                              >
                                Compliant{' '}
                              </SelectItem>
                              <SelectItem
                                className="font-medium"
                                value="Minor Non-Conformity"
                              >
                                Minor Non-conformity
                              </SelectItem>
                              <SelectItem
                                className="font-medium"
                                value="Major Non-Conformity"
                              >
                                Major Non-conformity
                              </SelectItem>
                              <SelectItem
                                className="font-medium"
                                value="Opportunity for Improvement"
                              >
                                Opportunity for improvement
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </DialogTrigger>
                        <RaiseClauseStatusModal
                          setOpenRequirement={setClauseStatusModal}
                          subClauseId={selectedClauseId}
                          selectedStatus={selectedClauseStatus}
                          reFetch={reFetch}
                        />
                      </Dialog>
                    </div>
                  </div>
                ) : (
                  <p className="mb-2 text-base font-medium leading-6 text-primary-500">
                    {clause.status}
                  </p>
                )}
              </div>
            )}

            {clause.description && (
              <div className="mt-4">
                <div
                  className={`text-lg font-semibold leading-7 mb-2 ${
                    clause.is_active ? 'text-dark-300' : 'text-gray-300'
                  }`}
                >
                  Clause Description:
                </div>
                <div
                  className={`text-base leading-6 font-medium ${
                    clause.is_active ? 'text-dark-100' : 'text-gray-300'
                  }`}
                >
                  <TextWithLineBreaks text={clause.description} />
                </div>
              </div>
            )}

            {/* clause question */}
            {clause.question && (
              <div className="mt-4">
                <div
                  className={`text-lg font-semibold leading-7 mb-2 ${
                    clause.is_active ? 'text-dark-300' : 'text-gray-300'
                  }`}
                >
                  Clause Question:
                </div>
                <div
                  className={`text-base leading-6 font-medium ${
                    clause.is_active ? 'text-dark-100' : 'text-gray-300'
                  }`}
                >
                  <TextWithLineBreaks text={clause.question} />
                </div>
              </div>
            )}

            {clause.processes.length > 0 && (
              <div className="mt-4 py-2">
                <div className="flex items-center justify-between">
                  <Tabs
                    tabsData={linkedProcessTabsData}
                    activeTab={activeLinkProcessTab}
                    setActiveTab={setActiveLinkProcessTab}
                  />
                </div>
                <div>
                  {clause?.processes.map((process: any, y: number) => (
                    <ProcessDocument
                      key={y}
                      process={process}
                      canEdit={false}
                      subClause={clause}
                      reFetch={reFetch}
                      processes={processes}
                      index={y}
                    />
                  ))}
                </div>
              </div>
            )}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </>
  );
};

const TextWithLineBreaks = ({ text }: { text: string }) => {
  return (
    <div>
      {text.split('\n').map((line, index) => (
        <React.Fragment key={index}>
          {line}
          <br />
        </React.Fragment>
      ))}
    </div>
  );
};

export default AuditView;
