import DeleteIcon from '@/assets/outline/delete';
import EditIcon from '@/assets/outline/edit';
import PlusIcon from '@/assets/outline/plus';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import { useAuthStore } from '@/globalProvider/authStore';
import { AuditStandard } from '@/interfaces/audit';
import axios from 'axios';
import { useEffect, useState } from 'react';
import Select, { MultiValue, StylesConfig } from 'react-select';

interface Clause {
  clause_no: string;
  title: string;
  sub_clauses: { id: string; clause_no: string; title: string }[];
}

const AuditClauseForm = ({
  standards = [],
  onFormUpdate,
  hasError,
}: {
  standards?: AuditStandard[];
  onFormUpdate: (result: Record<string, string[]>) => void;
  hasError?: boolean;
}) => {
  const [selections, setSelections] = useState<
    {
      standard: any;
      clauses: { value: string; label: string }[];
      isConfirmed: boolean;
      isEditing?: boolean;
    }[]
  >([{ standard: null, clauses: [], isConfirmed: false, isEditing: false }]);

  const [availableClauses, setAvailableClauses] = useState<Clause[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const { accessToken } = useAuthStore();

  const fetchClauses = async (categoryId: string): Promise<Clause[]> => {
    setIsLoading(true);

    const orgId =
      typeof window !== 'undefined'
        ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
        : null;

    const config = {
      headers: {
        'Content-Type': 'multipart/form-data',
        Authorization: `Bearer ${accessToken}`,
        ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
      },
    };

    const baseUrl = process.env.NEXT_PUBLIC_URL;
    const productVersion = process.env.NEXT_PUBLIC_VERSION;
    const url = `${baseUrl}/${productVersion}/clauses/${categoryId}?include_detail=true`;

    try {
      const response = await axios.get(url, config);
      const records = response.data.records || [];

      const normalizedClauses: Clause[] = [];

      for (const record of records) {
        if (record.sub_clauses) {
          // Structure type 1
          normalizedClauses.push({
            clause_no: record.clause_no,
            title: record.title,
            sub_clauses: record.sub_clauses.map((sub: any) => ({
              id: sub.id,
              clause_no: sub.clause_no,
              title: sub.title,
            })),
          });
        } else if (record.clauses) {
          // Structure type 2
          for (const clause of record.clauses) {
            if (clause.sub_clauses) {
              normalizedClauses.push({
                clause_no: clause.clause_no,
                title: clause.title,
                sub_clauses: clause.sub_clauses.map((sub: any) => ({
                  id: sub.id,
                  clause_no: sub.clause_no,
                  title: sub.title,
                })),
              });
            }
          }
        }
      }

      setIsLoading(false);
      return normalizedClauses;
    } catch (error) {
      console.error('Error fetching clauses:', error);
      setIsLoading(false);
      return [];
    }
  };

  const handleCategoryChange = async (
    index: number,
    selectedCategory: { id: string; label: string } | null,
  ) => {
    const newSelections = [...selections];
    newSelections[index].standard = selectedCategory;
    newSelections[index].clauses = [];

    if (selectedCategory) {
      const fetchedClauses = await fetchClauses(selectedCategory.id);
      setAvailableClauses(fetchedClauses);
    }

    setSelections(newSelections);
  };

  const handleClausesChange = (
    index: number,
    selectedClauses: MultiValue<{ value: string; label: string }>,
  ) => {
    const newSelections = [...selections];
    newSelections[index].clauses = [...selectedClauses]; // Convert to mutable array
    setSelections(newSelections);
  };

  const addMore = () => {
    setSelections([
      ...selections,
      { standard: null, clauses: [], isConfirmed: false },
    ]);
  };

  const removeSelection = (index: number) => {
    const newSelections = [...selections];
    newSelections.splice(index, 1);
    setSelections(newSelections);
  };

  const confirmSelections = (index: number) => {
    const newSelections = [...selections];
    newSelections[index].isConfirmed = true;
    setSelections(newSelections);
    setAvailableClauses([]);
  };

  const areAllCategoriesSelected = () => {
    return selections.length >= standards.length;
  };

  const getAvailableCategories = () => {
    const selectedCategoryIds = selections
      .filter((sel) => sel.standard)
      .map((sel) => sel.standard?.id);

    return standards.filter(
      (standard) => !selectedCategoryIds.includes(standard.id),
    );
  };

  useEffect(() => {
    const result = selections.reduce<Record<string, string[]>>(
      (acc, selection) => {
        if (selection.standard && selection.clauses.length > 0) {
          acc[selection.standard.id] = selection.clauses.map(
            (clause) => clause.value,
          );
        }
        return acc;
      },
      {},
    );
    onFormUpdate(result);
  }, [selections]);

  const editSelection = async (index: number) => {
    const newSelections = [...selections];
    newSelections[index].isConfirmed = false;
    newSelections[index].isEditing = true;

    if (newSelections[index].standard) {
      const fetchedClauses = await fetchClauses(
        newSelections[index].standard.id,
      );
      setAvailableClauses(fetchedClauses);
    }

    setSelections(newSelections);
  };

  const customStyles: StylesConfig<any, boolean> = {
    control: (provided, state) => ({
      ...provided,
      display: 'flex',
      flexWrap: 'nowrap',
      padding: 3,
      borderColor: state.isFocused
        ? 'rgb(225 225 225 / var(--tw-border-opacity, 1))'
        : 'rgb(225 225 225 / var(--tw-border-opacity, 1))',
      boxShadow: 'none',
      zIndex: 99,
      borderRadius: '0.5rem',
    }),
    menu: (base) => ({
      ...base,
      borderRadius: '8px',
      marginTop: '5px',
      zIndex: 100,
    }),
    option: (base, state) => ({
      ...base,
      backgroundColor: state.isFocused ? '#F4F4F4' : 'white',
      color: 'black',
      padding: '10px',
      margin: '0 5px',
      borderRadius: state.isFocused ? '5px' : '0px',
    }),
  };

  return (
    <div className="rounded-lg relative z-20 w-full border border-stroke bg-transparent py-3 px-5 dark:border-form-strokedark dark:bg-form-input">
      {selections.map((selection, index) => (
        <div key={index} className="mb-2 flex gap-3 items-center">
          <Select
            options={getAvailableCategories().map((standard) => ({
              id: standard.id,
              label: standard.title,
            }))}
            value={selection.standard}
            onChange={(selectedCategory) =>
              handleCategoryChange(index, selectedCategory)
            }
            placeholder="Select Standard"
            isDisabled={selection.isConfirmed || !!selection.standard}
            classNamePrefix="select"
            styles={customStyles}
          />
          {selection.standard && (
            <div style={{ flex: 2 }}>
              <Select
                options={availableClauses.map((clause) => ({
                  label: (
                    <div style={{ fontWeight: 'bold' }}>
                      Clause {clause.clause_no}: {clause.title}
                    </div>
                  ),
                  options: clause?.sub_clauses.map((subClause) => ({
                    value: subClause.id,
                    label: `${subClause.clause_no} - ${subClause.title}`,
                  })),
                }))}
                isMulti
                value={selection.clauses}
                onChange={(selectedClauses) =>
                  handleClausesChange(
                    index,
                    selectedClauses as MultiValue<{
                      value: string;
                      label: string;
                    }>,
                  )
                }
                placeholder="Select Clauses"
                isDisabled={selection.isConfirmed}
                closeMenuOnSelect={false}
                styles={customStyles}
                isLoading={isLoading}
              />
            </div>
          )}
          {selection.isConfirmed || selection.clauses.length === 0 ? null : (
            <button
              type="button"
              onClick={() => confirmSelections(index)}
              className="rounded bg-primary px-4 py-1.5"
            >
              Confirm
            </button>
          )}
          {selection.isConfirmed && (
            <button
              onClick={() => editSelection(index)}
              className="h-5 w-5"
              aria-hidden="true"
            >
              <EditIcon />
            </button>
          )}
          {selections.length > 1 && selection.standard !== null && (
            <button
              type="button"
              onClick={() => removeSelection(index)}
              className="ml-2"
            >
              <DeleteIcon />
            </button>
          )}
        </div>
      ))}
      {selections.some((sel) => sel.isConfirmed) &&
        !areAllCategoriesSelected() && (
          <button
            onClick={addMore}
            type="button"
            className="mt-4 flex items-center gap-2 rounded bg-primary px-6 py-1.5"
          >
            <PlusIcon /> Add Standard
          </button>
        )}
    </div>
  );
};

export default AuditClauseForm;
