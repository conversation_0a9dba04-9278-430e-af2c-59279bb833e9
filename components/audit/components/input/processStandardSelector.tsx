import React from 'react';
import MultiSelectDropdown, {
  DropdownGroup,
  DropdownItem,
} from '@/components/common/multiSelectDropDown';
import { IProcess } from '@/interfaces/process';
import useFetch from '@/hooks/useFetch';
import { useAuthStore } from '@/globalProvider/authStore';
import { fetchData } from '@/utils/api';

interface Process extends DropdownGroup {
  id: string;
  name: string;
}

interface Standard extends DropdownItem {
  id: string;
  title: string;
}

interface ProcessStandardsSelectorProps {
  onSelectionChange?: (
    selectedStandards: Array<{
      processId: string;
      processName: string;
      standardId: string;
      standardLabel: string;
    }>,
  ) => void;
  className?: string;
}

const ProcessStandardsSelector: React.FC<ProcessStandardsSelectorProps> = ({
  onSelectionChange,
  className,
}) => {
  const { accessToken } = useAuthStore();

  const {
    data: processesResponse,
    error: processesError,
    reFetch: reFetchProcesses,
    isLoading: processesLoading,
  } = useFetch<{ records: IProcess[] }>(accessToken, `processes`, {});

  const fetchGroupsFromFetchedData = async (): Promise<Process[]> => {
    const processes = processesResponse?.records || [];
    return processes.map((p) => ({
      id: p.id,
      name: p.name,
    }));
  };

  const fetchStandards = async (processId: string) => {
    try {
      const response = await fetchData(
        accessToken as string,
        'standards/processes',
        { process_ids: processId },
      );

      const standards = response?.data?.records[0]?.standards || [];

      return standards.map((s: { id: string; title: string }) => ({
        id: s.id,
        label: s.title,
      }));
    } catch (err) {
      console.error('Error fetching standards:', err);
      return [];
    }
  };

  return (
    <MultiSelectDropdown
      fetchGroups={fetchGroupsFromFetchedData}
      fetchItems={fetchStandards}
      placeholder="Select Process"
      groupLabel="Processes"
      itemLabel="Standards"
      onSelectionChange={
        onSelectionChange
          ? (selectedItems) =>
              onSelectionChange(
                selectedItems.map((item) => ({
                  processId: item.groupId,
                  processName: item.groupName,
                  standardId: item.itemId,
                  standardLabel: item.itemLabel,
                })),
              )
          : undefined
      }
      className={className}
      maxDisplayItems={5}
    />
  );
};

export default ProcessStandardsSelector;
