import { useParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';

import PrimaryButton from '../../../common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../common/dialog';

interface IRecord {
  id: string;
  description: string;
}

interface IProps {
  setOpenNotify: React.Dispatch<React.SetStateAction<boolean>>;
  title: string;
  type: string;
  data: {
    records: IRecord[];
  };
  reFetch: () => void;
}

const NotifyAuditorModal = ({
  setOpenNotify,
  title,
  type,
  data,
  reFetch,
}: IProps) => {
  const { postData, response, isLoading } = usePost();

  const param = useParams();
  const { accessToken } = useAuthStore();

  // state for selectedItems and selectAll
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const handleCheckboxChange = (id: string) => {
    const newSelectedItems = [...selectedItems];
    if (newSelectedItems.includes(id)) {
      newSelectedItems.splice(newSelectedItems.indexOf(id), 1);
    } else {
      newSelectedItems.push(id);
    }
    setSelectedItems(newSelectedItems);
    setSelectAll(newSelectedItems.length === data?.records?.length); // Update "Select All" state
  };

  const handleSelectAllChange = () => {
    if (selectAll) {
      setSelectedItems([]);
    } else {
      setSelectedItems(
        data?.records?.map((requirement) => requirement.id) || [],
      );
    }
    setSelectAll(!selectAll);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const body = {
      nofity_for: type,
      record_ids: selectedItems,
    };

    async function fetch() {
      await postData(
        accessToken as string,
        `audits/${param?.auditId}/notify-auditors`,
        body,
      );
    }
    fetch();
  };

  useEffect(() => {
    if (response) {
      reFetch();
      setSelectedItems([]);
      setSelectAll(false);
      setOpenNotify(false);
    }
  }, [response]);

  useEffect(() => {
    setSelectedItems([]);
    setSelectAll(false);
  }, [setOpenNotify]);

  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>Notify Auditor</DialogTitle>
      </DialogHeader>

      <div className="mt-2">
        <p className="mb-4">
          Send email notification to the Auditors for the selected {title}:
        </p>
        <label
          className="border-stroke border px-2 py-2 mb-2 rounded-md"
          style={{ display: 'block' }}
        >
          <input
            type="checkbox"
            className="mr-2 capitalize"
            checked={selectAll}
            onChange={handleSelectAllChange}
          />
          <span>All {title}</span>
        </label>
        {data?.records?.map((item) => (
          <label
            className="border-stroke border px-2 py-2 mb-2 rounded-md"
            key={item.id}
            style={{ display: 'block' }}
          >
            <input
              type="checkbox"
              className="mr-2 capitalize"
              checked={selectedItems.includes(item.id)} // If item id is in selectedItems, it is checked
              onChange={() => handleCheckboxChange(item.id)}
            />
            <span className="text-black opacity-50">Description:</span>{' '}
            <span>{item.description}</span>
          </label>
        ))}
        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text="Submit"
            isLoading={isLoading}
            onClick={handleSubmit}
            disabled={selectedItems.length === 0}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default NotifyAuditorModal;
