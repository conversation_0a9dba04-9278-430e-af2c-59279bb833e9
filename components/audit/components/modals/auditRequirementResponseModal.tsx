import { useParams } from 'next/navigation';
import React, { useCallback, useEffect } from 'react';

import DocumentIcon from '@/assets/outline/document';
import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';
import { X } from 'lucide-react';

import TertiaryButton from '@/components/common/button/tertiaryButton';
import axios from 'axios';
import { useDropzone } from 'react-dropzone';
import PrimaryButton from '../../../common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../common/dialog';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';

const acceptFileTypes = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': [
    '.docx',
    '.doc',
  ],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
    '.xlsx',
  ],
  'application/vnd.ms-excel': ['.xls', '.csv'],
};

interface IProps {
  isMulti?: boolean;
  setOpenRequirement: React.Dispatch<React.SetStateAction<boolean>>;
  subClauseId: string;
  requirementId: string;
  reFetch: () => void;
}
const AuditRequirementResponseModal = ({
  setOpenRequirement,
  subClauseId,
  isMulti = false,
  requirementId,
  reFetch,
}: IProps) => {
  const [response, setResponse] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);
  const [addedFile, setAddedFile] = React.useState<File[] | null>(null);
  const onDrop = useCallback((acceptedFiles: File[]) => {
    setAddedFile(acceptedFiles);
  }, []);

  const {
    postData: postRequirement,
    response: postRequirementResponse,
    isLoading: postRequirementLoading,
  } = usePost();
  const param = useParams();
  const { accessToken } = useAuthStore();

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: isMulti,
    accept: acceptFileTypes,
  });

  const handleFilesUpload = async (requirementId: string, files: any) => {
    const responses: any = [];
    setIsLoading(true);
    if (files.length > 0) {
      await Promise.all(
        files.map(async (file: any) => {
          const formData = new FormData();
          formData.append('file', file);

          const baseUrl = process.env.NEXT_PUBLIC_URL;
          const productVersion = process.env.NEXT_PUBLIC_VERSION;

          const url = `${baseUrl}/${productVersion}/file/upload?document_for=audit_hub&sub_path=/${param?.auditId}/requirements/${requirementId}`;

          const orgId =
            typeof window !== 'undefined'
              ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
              : null;

          const config = {
            headers: {
              'Content-Type': 'multipart/form-data',
              Authorization: `Bearer ${accessToken}`,
              ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
            },
          };

          await axios.post(url, formData, config).then((response) => {
            if (response.status == 200) {
              responses.push({
                file_path: response.data.file_path,
                file_extension: response.data.file_ext,
              });
              setIsLoading(false);
            } else {
              setIsLoading(false);
              console.error('Error uploading file:', file.name);
            }
          });
        }),
      );
    }

    return responses;
  };

  const handleCommentSubmit = async () => {
    let uploadFiles = [];

    if (addedFile && addedFile.length > 0) {
      uploadFiles = await handleFilesUpload(requirementId, addedFile);
    }

    const body = {
      description: response,
      status: 'Ready for Review',
      attachments: uploadFiles.length > 0 ? uploadFiles : [],
      audit_requirement: requirementId,
    };

    console.log(body);

    async function fetch() {
      await postRequirement(
        accessToken as string,
        `audits/${param?.auditId}/requirements/comment`,
        body,
      );
    }
    fetch();
  };

  useEffect(() => {
    if (postRequirementResponse) {
      reFetch();
      setResponse('');
      setAddedFile([]);
      setOpenRequirement(false);
    }
  }, [postRequirementResponse]);
  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>Submit Response</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Response<span className="text-[#F55D5D]">*</span>
        </div>
        <textarea
          rows={4}
          name="description"
          onChange={(e) => setResponse(e.target.value)}
          className="text-black text-base w-full rounded-lg border bg-white-100 border-grey-100 hover:border-grey-200 bg-background px-3 py-2 file:border-0 file:bg-transparent file:text-medium file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed disabled:opacity-80 font-medium"
        />

        <div className="mt-2">
          <div className="text-base leading-6 font-medium text-dark-100 mb-2.5">
            Attach document
          </div>

          <div>
            <div
              className=" min-h-28 bg-white-100 border border-dashed border-[#C7C7CC] rounded-xl flex items-center justify-center flex-col gap-2 hover:bg-[#F8F8F8] p-2"
              {...getRootProps()}
            >
              {!(addedFile?.length && addedFile?.length > 0) && (
                <div className="text-sm font-medium leading-5 text-[#49474E]">
                  Upload or Drag and drop to upload your file
                </div>
              )}

              <input {...getInputProps()} />
              <div className="flex justify-center items-center flex-wrap gap-2">
                {addedFile?.map((file, index) => (
                  <FileCard
                    key={index}
                    file={file}
                    setAddedFile={setAddedFile}
                  />
                ))}
              </div>
              <TertiaryButton
                text={
                  isMulti
                    ? 'Add files'
                    : addedFile?.length && addedFile?.length > 0
                    ? 'Replace'
                    : 'Upload file'
                }
                size="small"
              />
            </div>
          </div>
        </div>
        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text="Submit"
            isLoading={postRequirementLoading || isLoading}
            onClick={handleCommentSubmit}
            disabled={response === ''}
          />
        </div>
      </div>
    </DialogContent>
  );
};

const FileCard = ({
  file,
  setAddedFile,
}: {
  file: File;
  setAddedFile: React.Dispatch<React.SetStateAction<File[] | null>>;
}) => {
  return (
    <div
      className="p-1.5 rounded-md bg-white-100 border border-white-300 flex items-center gap-2 relative group"
      onClick={(e) => e.stopPropagation()}
    >
      <div
        className="h-3 w-3 rounded-full bg-dark-300 items-center justify-center absolute -top-1.5 -right-1.5 hidden group-hover:flex cursor-pointer"
        onClick={() => {
          setAddedFile((pre) => {
            return pre ? pre?.filter((e) => e.name !== file.name) : null;
          });
        }}
      >
        <X className="h-2.5 w-2.5" color="#fff" />
      </div>
      <div className="h-8 w-8 flex items-center justify-center rounded bg-[#5A91FF]">
        <DocumentIcon height="20" width="20" color="#fff" />
      </div>
      <div>{file.name}</div>
    </div>
  );
};

export default AuditRequirementResponseModal;
