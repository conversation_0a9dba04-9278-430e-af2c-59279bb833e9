import React, { useEffect, useState } from 'react';
import { z } from 'zod';

import PrimaryButton from '@/components/common/button/primaryButton';
import Calendar from '@/components/common/calendar';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import { Input } from '@/components/common/input';
import { Label } from '@/components/common/label';
import {
  IOption,
  ReactSelectMulti,
} from '@/components/common/multiSelectInput';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/common/select';
import { useAuthStore } from '@/globalProvider/authStore';
import useFetch from '@/hooks/useFetch';
import { usePost } from '@/hooks/usePost';
import useValidators from '@/hooks/useValidator';
import { AuditStandard } from '@/interfaces/audit';
import { IDocumentDetails } from '@/interfaces/document';
import { IStandardData } from '@/interfaces/standard';
import { IUser } from '@/interfaces/user';
import moment from 'moment';
import { useParams } from 'next/navigation';
import { useRouter } from 'next/router';
import AuditClauseForm from '../forms/auditClauseForm';

const optionSchema = z.object({
  label: z.string().optional(),
  value: z.string(),
});

export const createAuditSchema = {
  name: z.string().nonempty('Name is required'),
  clauses: z
    .record(
      z
        .array(z.string())
        .min(1, 'Each clause must contain at least one standard'),
    )
    .refine((data) => Object.keys(data).length > 0, {
      message: 'At least one standard and one clause is required',
    }),
  audit_type: z.string().nonempty('Type of audit is required'),
  auditees: z.array(optionSchema).min(1, 'At least one auditee is required'),
  auditors: z.array(optionSchema).min(1, 'At least one auditor is required'),
  start_date: z.string().nonempty('Start date is required'),
  notification_date: z.string().nonempty('Notification date is required'),
};

interface IData extends Record<string, unknown> {
  name: string;
  audit_type: string;
  start_date: string;
  notification_date: string;
  auditees: IOption[];
  auditors: IOption[];
  clauses: any;
}

const CreateAuditByClause = ({
  edit = false,
  prefill,
  setOpenEdit,
  reFetch,
}: {
  edit?: boolean;
  prefill?: IDocumentDetails;
  setOpenEdit?: React.Dispatch<React.SetStateAction<boolean>>;
  reFetch?: () => void;
}) => {
  const { accessToken } = useAuthStore();
  const [data, setData] = useState<IData>({
    name: '',
    audit_type: '',
    start_date: '',
    notification_date: '',
    mode: 'FromClause',
    auditees: [],
    auditors: [],
    clauses: {},
  });

  const [selectedClausesData, setSelectedClausesData] = useState(null);

  const handleFormUpdate = (data: any) => {
    setData((pre) => ({ ...pre, clauses: data }));
  };

  const { data: users } = useFetch<
    { records: IUser[] },
    { can_audit_edit: boolean }
  >(accessToken, `users`, {
    can_audit_edit: true,
  });

  const { data: standards } = useFetch<{
    records: IStandardData[];
  }>(accessToken, `standards`, {});

  const router = useRouter();
  const param = useParams();

  const { postData, response, isLoading: submitLoading, error } = usePost();

  const userData = users?.records?.map((e) => ({
    label: e.full_name,
    value: e.id,
  })) as IOption[];

  const standardsData = standards?.records?.map((e) => ({
    label: e.title,
    value: e.id,
  })) as IOption[];

  const { validationErrors, startValidation } = useValidators({
    schemas: createAuditSchema,
    values: data,
  });

  const handleSubmit = async () => {
    const { hasValidationErrors } = await startValidation();
    if (!hasValidationErrors) {
      const payload = {
        ...data,
        auditees: data.auditees
          .map((auditee) => auditee.value.split(','))
          .flat(),
        auditors: data.auditors
          .map((auditor) => auditor.value.split(','))
          .flat(),
      };
      await postData(accessToken as string, 'audits', payload).then(reFetch);
    } else {
    }
  };

  useEffect(() => {
    if (error) {
    }
    if (response) {
      router.push(`/audit/${(response as { id: string }).id}`);
    }
  }, [response, error]);

  const isValidValue = (value: any): boolean => {
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    if (typeof value === 'object' && value !== null) {
      return (
        Object.keys(value).length > 0 &&
        Object.values(value).every(isValidValue)
      );
    }
    return value !== '';
  };
  const isFormFilled = Object.values(data).every(isValidValue);

  return (
    <DialogContent className="min-w-[65vw] max-h-[90vh] overflow-y-auto overflow-x-hidden">
      <DialogHeader>
        <DialogTitle>Create Audit by Clause</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="standardName"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Audit Name<span className="text-red-200">*</span>
            </Label>
            <Input
              placeholder="Audit Name"
              id="standardName"
              required
              value={data?.name}
              onChange={(e) =>
                setData((pre) => ({ ...pre, name: e.target.value }))
              }
              errorMsg={validationErrors?.name[0]}
            />
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="type_of_audit"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Type of Audit<span className="text-red-200">*</span>
            </Label>
            <Select
              value={data.audit_type}
              onValueChange={(value) => {
                setData((pre) => ({ ...pre, audit_type: value }));
              }}
              required
            >
              <SelectTrigger
                className={
                  validationErrors?.audit_type[0] ? 'border-red-200' : ''
                }
                id="type_of_audit"
              >
                <SelectValue placeholder="Type of Audit" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Internal Audit">Internal</SelectItem>
                <SelectItem value="External Audit">External</SelectItem>
              </SelectContent>
            </Select>
            {validationErrors?.audit_type[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.audit_type[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-2.5 flex-1 mb-5">
          <Label
            htmlFor="clauses"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Clauses<span className="text-red-200">*</span>
          </Label>
          <AuditClauseForm
            standards={(standards?.records || []) as unknown as AuditStandard[]}
            onFormUpdate={handleFormUpdate}
            hasError={Boolean(validationErrors?.clauses)}
          />
          {validationErrors?.clauses && validationErrors.clauses.length > 0 ? (
            <div className="text-xs font-semibold leading-5 text-left text-red-200">
              {validationErrors?.clauses[0]}
            </div>
          ) : null}
        </div>

        <div className="flex flex-col gap-2.5 flex-1 mb-5">
          <Label
            htmlFor="auditee"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Auditees<span className="text-red-200">*</span>
          </Label>
          <ReactSelectMulti
            value={data.auditees}
            options={userData}
            placeholder="Select Auditees"
            onChange={(value) => {
              setData((pre) => ({
                ...pre,
                auditees: value as IOption[],
              }));
            }}
            hasError={Boolean(validationErrors?.auditees[0])}
          />
          {validationErrors?.auditees[0] ? (
            <div className="text-xs font-semibold leading-5 text-left text-red-200">
              Auditee is required
            </div>
          ) : (
            <></>
          )}
        </div>
        <div className="flex flex-col gap-2.5 flex-1 mb-5">
          <Label
            htmlFor="auditor"
            className="text-base font-medium leading-6 text-dark-100"
          >
            Auditors<span className="text-red-200">*</span>
          </Label>
          <ReactSelectMulti
            value={data.auditors}
            options={userData}
            placeholder="Select Auditors"
            onChange={(value) => {
              setData((pre) => ({
                ...pre,
                auditors: value as IOption[],
              }));
            }}
            hasError={Boolean(validationErrors?.auditors[0])}
          />
          {validationErrors?.auditors[0] ? (
            <div className="text-xs font-semibold leading-5 text-left text-red-200">
              Auditor is required
            </div>
          ) : (
            <></>
          )}
        </div>

        <div className="flex gap-5 mb-5">
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="start_date"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Audit Start Date<span className="text-red-200">*</span>
            </Label>
            <Calendar
              selectedDate={data?.start_date}
              onDateChange={(date) => {
                if (date) {
                  setData((prev) => ({
                    ...prev,
                    start_date: moment(date as string).format('YYYY-MM-DD'),
                  }));
                } else {
                  setData((prev) => ({
                    ...prev,
                    start_date: '',
                  }));
                }
              }}
              className={
                validationErrors?.start_date[0] ? 'border !border-red-200' : ''
              }
            />
            {validationErrors?.start_date[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.start_date[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
          <div className="flex flex-col gap-2.5 flex-1">
            <Label
              htmlFor="notification_date"
              className="text-base font-medium leading-6 text-dark-100"
            >
              Audit Notification Date<span className="text-red-200">*</span>
            </Label>
            <Calendar
              selectedDate={data?.notification_date}
              onDateChange={(date) => {
                if (date) {
                  setData((prev) => ({
                    ...prev,
                    notification_date: moment(date as string).format(
                      'YYYY-MM-DD',
                    ),
                  }));
                } else {
                  setData((prev) => ({
                    ...prev,
                    notification_date: '',
                  }));
                }
              }}
              className={
                validationErrors?.notification_date[0]
                  ? 'border !border-red-200'
                  : ''
              }
            />
            {validationErrors?.notification_date[0] ? (
              <div className="text-xs font-semibold leading-5 text-left text-red-200">
                {validationErrors?.notification_date[0]}
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>

        <div className="flex justify-end mt-6">
          <PrimaryButton
            size="medium"
            text="Submit"
            onClick={handleSubmit}
            disabled={!isFormFilled}
            isLoading={submitLoading}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default CreateAuditByClause;
