import { useParams } from "next/navigation";
import React, { useEffect } from "react";

import { useAuthStore } from "@/globalProvider/authStore";
import { usePost } from "@/hooks/usePost";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/common/select";

import PrimaryButton from "../../../common/button/primaryButton";
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "../../../common/dialog";

interface IProps {
  setOpenRequirement: React.Dispatch<React.SetStateAction<boolean>>;
  requirementId: string;
  subClauseId: string;
  reFetchRequirements: () => void;
  reFetchFinding: () => void;
}
const RejectRequirementModal = ({
  setOpenRequirement,
  requirementId,
  subClauseId,
  reFetchRequirements,
  reFetchFinding,
}: IProps) => {
  const [justification, setJustification] = React.useState("");
  const [status, setStatus] = React.useState("");

  const {
    postData: requirementPostData,
    response: requirementPostResponse,
    isLoading: requirementPostIsLoading,
  } = usePost();
  const {
    postData: findingPostData,
    response: findingPostResponse,
    isLoading: findingPostIsLoading,
  } = usePost();

  const param = useParams();
  const { accessToken } = useAuthStore();

  const handleCommentSubmit = () => {
    const body = {
      action: "Rejected",
      comment: justification,
      remark: `The response to this requirement has
			resulted in a  ${status}.`,
      justification: justification,
    };

    console.log(body, param?.auditId, requirementId);

    async function fetch() {
      await requirementPostData(
        accessToken as string,
        `audits/${param?.auditId}/requirements/${requirementId}/action`,
        body,
      );
    }
    fetch();
  };

  const handleCreateNc = async () => {
    const body = {
      sub_clause_id: subClauseId,
      description: justification,
      category: status,
    };

    async function fetch() {
      await findingPostData(
        accessToken as string,
        `audits/${param?.auditId}/non_conformities`,
        body,
      );
    }
    fetch();
  };

  useEffect(() => {
    if (requirementPostResponse) {
      handleCreateNc();
    }
  }, [requirementPostResponse]);

  useEffect(() => {
    if (findingPostResponse) {
      reFetchRequirements();
      reFetchFinding();
      setJustification("");
      setOpenRequirement(false);
    }
  }, [findingPostResponse]);
  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>Reject </DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Select NC or OFI<span className="text-[#F55D5D]">*</span>
        </div>

        <Select
          value={status}
          onValueChange={(value) => {
            setStatus(value);
          }}
        >
          <SelectTrigger className={""} id="type_of_audit">
            <SelectValue
              className="font-medium"
              placeholder="Select type of finding"
            />
          </SelectTrigger>
          <SelectContent>
            <SelectItem className="font-medium" value="Minor Non-Conformity">
              Minor Non-Conformity
            </SelectItem>
            <SelectItem className="font-medium" value="Major Non-Conformity">
              Major Non-Conformity
            </SelectItem>
            <SelectItem
              className="font-medium"
              value="Opportunity for Improvement"
            >
              Opportunity for Improvement
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="mt-2">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Justification<span className="text-[#F55D5D]">*</span>
        </div>
        <textarea
          rows={4}
          name="description"
          placeholder="Provide Jusitification"
          onChange={(e) => setJustification(e.target.value)}
          className="text-black text-base w-full rounded-lg border bg-white-100 border-grey-100 hover:border-grey-200 bg-background px-3 py-2 file:border-0 file:bg-transparent file:text-medium file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed disabled:opacity-80 font-medium"
        />
        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text="Submit"
            isLoading={requirementPostIsLoading || findingPostIsLoading}
            onClick={handleCommentSubmit}
            disabled={justification === ""}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default RejectRequirementModal;
