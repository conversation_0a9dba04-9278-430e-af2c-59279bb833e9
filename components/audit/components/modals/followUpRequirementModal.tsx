import { useParams } from 'next/navigation';
import React, { useEffect } from 'react';

import { useAuthStore } from '@/globalProvider/authStore';
import { usePost } from '@/hooks/usePost';

import PrimaryButton from '../../../common/button/primaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '../../../common/dialog';

interface IProps {
  setOpenRequirement: React.Dispatch<React.SetStateAction<boolean>>;
  requirementId: string;
  reFetch: () => void;
}
const FollowUpRequirementModal = ({
  setOpenRequirement,
  requirementId,
  reFetch,
}: IProps) => {
  const [requirement, setRequirement] = React.useState('');
  const {
    postData: postRequirement,
    response: postRequirementResponse,
    isLoading: postRequirementLoading,
  } = usePost();
  const param = useParams();
  const { accessToken } = useAuthStore();

  const handleCommentSubmit = () => {
    const body = {
      description: requirement,
      status: 'Ready for Review',
      attachments: [],
      audit_requirement: requirementId,
    };

    async function fetch() {
      await postRequirement(
        accessToken as string,
        `audits/${param?.auditId}/requirements/comment`,
        body,
      );
    }
    fetch();
  };

  useEffect(() => {
    if (postRequirementResponse) {
      setRequirement('');
      reFetch();
      setOpenRequirement(false);
    }
  }, [postRequirementResponse]);
  return (
    <DialogContent className="min-w-[45.438rem]">
      <DialogHeader>
        <DialogTitle>Follow-up query</DialogTitle>
      </DialogHeader>
      <div className="mt-2">
        <div className="text-base font-medium leading-6 text-dark-100 mb-2.5">
          Query<span className="text-[#F55D5D]">*</span>
        </div>
        <textarea
          rows={4}
          name="description"
          onChange={(e) => setRequirement(e.target.value)}
          className="text-black text-base w-full rounded-lg border bg-white-100 border-grey-100 hover:border-grey-200 bg-background px-3 py-2 file:border-0 file:bg-transparent file:text-medium file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary-400 disabled:cursor-not-allowed disabled:opacity-80 font-medium"
        />
        <div className="flex justify-end mt-5">
          <PrimaryButton
            size="medium"
            text="Submit"
            isLoading={postRequirementLoading}
            onClick={handleCommentSubmit}
            disabled={requirement === ''}
          />
        </div>
      </div>
    </DialogContent>
  );
};

export default FollowUpRequirementModal;
