import axios from 'axios';
import { Download, Upload, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';

import PrimaryButton from '@/components/common/button/primaryButton';
import SecondaryButton from '@/components/common/button/secondaryButton';
import {
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/common/dialog';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import { useAuthStore } from '@/globalProvider/authStore';

interface BulkUploadError {
  row: number;
  field: string;
  message: string;
  value: string;
}

interface BulkUploadResponse {
  success: boolean;
  message: string;
  imported_count: number;
  errors?: BulkUploadError[];
}
const baseUrl = process.env.NEXT_PUBLIC_URL;
const productVersion = process.env.NEXT_PUBLIC_VERSION;

const BulkUploadModal = ({
  setOpenModal,
  onSuccess,
}: {
  setOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
  onSuccess?: () => void;
}) => {
  const { accessToken, user } = useAuthStore();
  const [file, setFile] = useState<File | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadResponse, setUploadResponse] =
    useState<BulkUploadResponse | null>(null);
  const [uploadStep, setUploadStep] = useState<
    'instructions' | 'upload' | 'results'
  >('instructions');

  // Check if user has admin permissions (only admins can bulk upload)
  const isAdmin =
    user?.roles?.includes('Admin') || user?.roles?.includes('SuperAdmin');

  // Reset modal state function
  const resetModalState = () => {
    setFile(null);
    setIsLoading(false);
    setUploadResponse(null);
    setUploadStep('instructions');
  };

  // Reset modal when it's opened/closed
  useEffect(() => {
    // Reset state when modal is opened
    resetModalState();
  }, []); // Empty dependency array means this runs once when component mounts

  const handleDownloadTemplate = async (format: 'xlsx') => {
    try {
      setIsLoading(true);

      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const response = await axios.get(
        `${baseUrl}/${productVersion}/employees/bulk-upload/template`,

        {
          params: { format },
          headers: {
            Authorization: `Bearer ${accessToken}`,
            ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
          },
          responseType: 'blob',
        },
      );

      // Create a URL for the blob
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `employee_bulk_upload_template.${format}`);
      document.body.appendChild(link);
      link.click();
      link.remove();

      toast.success(`Template downloaded successfully`);
    } catch (error) {
      console.error('Error downloading template:', error);
      toast.error('Failed to download template');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const selectedFile = e.target.files[0];
      const fileType = selectedFile.type;

      // Check file type (allow only XLSX)
      if (
        fileType !==
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' &&
        !selectedFile.name.endsWith('.xlsx')
      ) {
        toast.error('Please upload only XLSX files');
        return;
      }

      setFile(selectedFile);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      toast.error('Please select a file to upload');
      return;
    }

    try {
      setIsLoading(true);
      const formData = new FormData();
      formData.append('file', file);
      const orgId =
        typeof window !== 'undefined'
          ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
          : null;

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
          Authorization: `Bearer ${accessToken}`,
          ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
        },
      };
      const response = await axios.post(
        `${baseUrl}/${productVersion}/employees/bulk-upload`,
        formData,
        config,
      );

      setUploadResponse(response.data);
      setUploadStep('results');

      if (response.data.success) {
        toast.success(response.data.message);
        // Call onSuccess callback if provided to refresh the employee list
        if (onSuccess && response.data.imported_count > 0) {
          onSuccess();
        }
      } else {
        toast.error('Upload completed with errors. Please check the details.');
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      toast.error('Failed to upload file');
    } finally {
      setIsLoading(false);
    }
  };

  const renderInstructions = () => (
    <div className="space-y-6">
      <p className="text-dark-100">
        Follow these steps to bulk upload employees to the system:
      </p>

      <div className="flex flex-col gap-4">
        <div className="flex items-center gap-2">
          <div className="w-6 h-6 rounded-full bg-primary-600 text-white flex items-center justify-center text-sm">
            1
          </div>
          <p className="text-dark-100">Download the Excel template file</p>
        </div>

        <div className="ml-8 flex gap-2">
          <SecondaryButton
            text="Excel Template"
            icon={<Download className="h-4 w-4" />}
            onClick={() => handleDownloadTemplate('xlsx')}
            disabled={isLoading}
            size="medium"
          />
        </div>

        <div className="flex items-center gap-2">
          <div className="w-6 h-6 rounded-full bg-primary-600 text-white flex items-center justify-center text-sm">
            2
          </div>
          <p className="text-dark-100">
            Fill the template with your employee data
          </p>
        </div>

        <div className="flex items-center gap-2">
          <div className="w-6 h-6 rounded-full bg-primary-600 text-white flex items-center justify-center text-sm">
            3
          </div>
          <p className="text-dark-100">Upload the completed file</p>
        </div>

        <div className="ml-8">
          <p className="text-sm text-dark-75">
            <strong>Note:</strong> For departments and processes, ensure the
            names match existing entries or new ones will be created
            automatically. Use comma-separated values for multiple entries.
          </p>
        </div>
      </div>

      <div className="mt-8 flex justify-end">
        <PrimaryButton
          text="Next"
          onClick={() => setUploadStep('upload')}
          size="medium"
        />
      </div>
    </div>
  );

  const renderFileUpload = () => (
    <div className="space-y-6">
      <div
        className="border-2 border-dashed border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition"
        onClick={() => document.getElementById('fileInput')?.click()}
      >
        <Upload className="h-10 w-10 text-primary-600 mb-2" />
        <p className="text-dark-100 mb-1">
          {file ? file.name : 'Click to upload or drag and drop'}
        </p>
        <p className="text-sm text-dark-75">XLSX only (max 10MB)</p>
        <input
          type="file"
          id="fileInput"
          className="hidden"
          accept=".xlsx"
          onChange={handleFileChange}
          disabled={isLoading}
        />
      </div>

      {file && (
        <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
          <div className="flex items-center gap-2 overflow-hidden">
            <Download className="h-5 w-5 text-primary-600 flex-shrink-0" />
            <span className="text-sm truncate">{file.name}</span>
          </div>
          <button
            onClick={() => setFile(null)}
            className="text-dark-75 hover:text-dark-100"
            disabled={isLoading}
          >
            <X className="h-4 w-4" />
          </button>
        </div>
      )}

      <div className="mt-8 flex justify-between">
        <SecondaryButton
          text="Back"
          onClick={() => setUploadStep('instructions')}
          disabled={isLoading}
          size="medium"
        />
        <PrimaryButton
          text={isLoading ? 'Uploading...' : 'Import'}
          onClick={handleUpload}
          disabled={!file || isLoading}
          size="medium"
        />
      </div>
    </div>
  );

  const renderResults = () => {
    if (!uploadResponse) return null;

    return (
      <div className="space-y-6">
        {uploadResponse.success ? (
          <div className="bg-green-50 p-4 rounded-md border border-green-200">
            <p className="text-green-700">
              <strong>Success!</strong> {uploadResponse.message}
            </p>
          </div>
        ) : (
          <div className="bg-red-50 p-4 rounded-md border border-red-200">
            <p className="text-red-700 mb-2">
              <strong>Error!</strong> {uploadResponse.message}
            </p>
            <p className="text-sm text-dark-75">
              Please fix the errors below and try again.
            </p>
          </div>
        )}

        {uploadResponse.imported_count > 0 && (
          <div className="bg-gray-50 p-4 rounded-md border border-gray-200">
            <p className="text-dark-100">
              Successfully imported {uploadResponse.imported_count} employees.
            </p>
          </div>
        )}

        {uploadResponse.errors && uploadResponse.errors.length > 0 && (
          <div>
            <h3 className="text-lg font-medium mb-3">Errors Found</h3>
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead className="bg-gray-100">
                  <tr>
                    <th className="px-4 py-2 text-left">Row</th>
                    <th className="px-4 py-2 text-left">Field</th>
                    <th className="px-4 py-2 text-left">Value</th>
                    <th className="px-4 py-2 text-left">Error</th>
                  </tr>
                </thead>
                <tbody>
                  {uploadResponse.errors.map((error, index) => (
                    <tr key={index} className="border-b border-gray-200">
                      <td className="px-4 py-2">{error.row}</td>
                      <td className="px-4 py-2">{error.field}</td>
                      <td className="px-4 py-2">
                        <code className="bg-gray-100 px-1 py-0.5 rounded text-xs">
                          {error.value}
                        </code>
                      </td>
                      <td className="px-4 py-2 text-red-600">
                        {error.message}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        <div className="mt-8 flex justify-between">
          <SecondaryButton
            text="Upload Another File"
            onClick={() => {
              setFile(null);
              setUploadResponse(null);
              setUploadStep('upload');
            }}
            size="medium"
          />
          <PrimaryButton
            text="Done"
            onClick={() => {
              resetModalState();
              setOpenModal(false);
            }}
            size="medium"
          />
        </div>
      </div>
    );
  };

  if (!isAdmin) {
    return (
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Bulk Upload Employees</DialogTitle>
        </DialogHeader>
        <div className="p-6">
          <p className="text-red-600">
            You do not have permission to perform bulk uploads. Only Admin users
            can access this feature.
          </p>
        </div>
      </DialogContent>
    );
  }

  return (
    <DialogContent className="max-w-[65vw] min-w-[65vw]">
      <DialogHeader>
        <DialogTitle>Bulk Upload Employees</DialogTitle>
      </DialogHeader>
      <div className="p-6">
        {uploadStep === 'instructions' && renderInstructions()}
        {uploadStep === 'upload' && renderFileUpload()}
        {uploadStep === 'results' && renderResults()}
      </div>
    </DialogContent>
  );
};

export default BulkUploadModal;
