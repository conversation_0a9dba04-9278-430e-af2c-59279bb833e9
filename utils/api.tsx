import axios from 'axios';

export const USER_LOGIN = `${process.env.NEXT_PUBLIC_URL}/${process.env.NEXT_PUBLIC_VERSION}/login`;
export const GET_PRESIGNED_URL = `${process.env.NEXT_PUBLIC_URL}/${process.env.NEXT_PUBLIC_VERSION}/file/presigned-url`;
export const GET_ORGANIZATION = `${process.env.NEXT_PUBLIC_URL}/${process.env.NEXT_PUBLIC_VERSION}/orgs`;
export const GET_CURRENT_USER = `${process.env.NEXT_PUBLIC_URL}/${
  process.env.NEXT_PUBLIC_VERSION
}/${'users/me'}`;
export const UPDATE_CURRENT_USER = `${process.env.NEXT_PUBLIC_URL}/${
  process.env.NEXT_PUBLIC_VERSION
}/${'company/users'}`;

export const fetchData = async (
  accessToken: string,
  endpoint: string,
  query?: Record<string, string[] | string | undefined>,
) => {
  const baseUrl = process.env.NEXT_PUBLIC_URL;
  const productVersion = process.env.NEXT_PUBLIC_VERSION;
  try {
    const mockUser =
      typeof window !== 'undefined'
        ? localStorage.getItem('x-mock-user')
        : null;

    const orgId =
      typeof window !== "undefined"
        ? sessionStorage.getItem("oid")
        : null;

    const response = await axios.get(
      `${baseUrl}/${productVersion}/${endpoint}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`, // Include the token
          'Content-Type': 'application/json',
          ...(!!mockUser ? { 'x-mock-user': mockUser } : {}),
          ...(!!orgId ? { "x-org": orgId } : {}),
        },
        params: query,
      },
    );

    return response;
  } catch (error) {
    console.error('Error fetching data:', error);
  }
};
