export const getValueOrDefault = (obj: any, key: any) => {
  if (obj && obj.hasOwnProperty(key) && obj[key] !== null) {
    return obj[key];
  } else {
    return '--';
  }
};

export const getlastModified = (obj: any, key: any) => {
  if (
    obj &&
    obj.hasOwnProperty(key) &&
    obj[key] !== null &&
    JSON.stringify(obj[key]) != JSON.stringify('') && //checking empty array/string
    JSON.stringify(obj[key]) != JSON.stringify([])
  ) {
    return obj[key].full_name;
  } else {
    return '--';
  }
};
