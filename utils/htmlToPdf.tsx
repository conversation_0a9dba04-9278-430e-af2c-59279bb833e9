import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';

interface HtmlToPdfOptions {
  htmlContent: string;
  filename?: string;
  scale?: number;
  onProgress?: (status: string) => void;
  onError?: (error: string) => void;
  onSuccess?: (pdfBlob?: Blob) => void;
  returnBlob?: boolean;
  paperFormat?: 'a4' | 'letter' | 'legal';
  orientation?: 'portrait' | 'landscape';
  margin?: number;
}

export const htmlToPdf = async ({
  htmlContent,
  filename = 'document.pdf',
  onProgress,
  onError,
  onSuccess,
  returnBlob = false,
  paperFormat = 'a4',
  orientation = 'portrait',
  margin = 5,
}: HtmlToPdfOptions): Promise<Blob | void> => {
  if (!htmlContent) {
    const errorMsg = 'No HTML content provided';
    if (onError) onError(errorMsg);
    throw new Error(errorMsg);
  }

  if (onProgress) onProgress('Preparing HTML content');

  try {
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.width = '800px';
    container.style.padding = '20px';
    container.innerHTML = htmlContent;

    document.body.appendChild(container);

    if (onProgress) onProgress('Converting HTML to canvas');

    const canvas = await html2canvas(container, {
      useCORS: true,
      allowTaint: true,
      logging: false,
    });

    document.body.removeChild(container);

    if (onProgress) onProgress('Generating PDF');

    const pdf = new jsPDF({
      orientation,
      unit: 'mm',
      format: paperFormat,
    });

    const getPaperDimensions = () => {
      let width = 210;
      let height = 297;

      if (paperFormat === 'letter') {
        width = 215.9;
        height = 279.4;
      } else if (paperFormat === 'legal') {
        width = 215.9;
        height = 355.6;
      }

      return orientation === 'portrait'
        ? { width, height }
        : { width: height, height: width };
    };

    const { width: pageWidth, height: pageHeight } = getPaperDimensions();
    const contentWidth = pageWidth - margin * 2;

    const imgWidth = contentWidth;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    pdf.addImage(
      canvas.toDataURL('image/png'),
      'PNG',
      margin,
      margin,
      imgWidth,
      imgHeight,
    );

    let heightLeft = imgHeight;
    let position = 0;
    const pageContentHeight = pageHeight - margin * 2;

    if (heightLeft > pageContentHeight) {
      heightLeft -= pageContentHeight;
      position = pageContentHeight;

      while (heightLeft > 0) {
        pdf.addPage();
        pdf.addImage(
          canvas.toDataURL('image/png'),
          'PNG',
          margin,
          margin - position,
          imgWidth,
          imgHeight,
        );
        heightLeft -= pageContentHeight;
        position += pageContentHeight;
      }
    }

    if (onProgress) onProgress('PDF generated successfully');

    if (returnBlob) {
      const blob = pdf.output('blob');
      if (onSuccess) onSuccess(blob);
      return blob;
    } else {
      pdf.save(filename);
      if (onSuccess) onSuccess();
    }
  } catch (err) {
    const errorMsg = `Error generating PDF: ${
      err instanceof Error ? err.message : String(err)
    }`;
    if (onError) onError(errorMsg);
    throw new Error(errorMsg);
  }
};

// htmlToPdf({
//   htmlContent: htmlContent,
//   filename: 'report.pdf',
//   scale: 2,
//   orientation: 'landscape',
//   paperFormat: 'letter',
//  onProgress: (status) => console.log(status),
//   onError: (error) => console.error(error),
//   onSuccess: () => console.log('PDF downloaded successfully')
// });
