export const getValueOrDefault = (obj: any, key: any) => {
  if (obj && obj.hasOwnProperty(key) && obj[key] !== null) {
    return obj[key];
  } else {
    return '--';
  }
};

export const mapForMultiSelect = (
  input: string,
  array: {
    label: string;
    value: string;
  }[],
): {
  label: string;
  value: string;
}[] => {
  // Split the input string into an array of strings
  const inputLabels = input?.split(',').map((str) => str.trim());

  // Filter and map to create a new array
  return inputLabels
    ?.map((label) => {
      const match = array?.find((item) => item.label === label);
      return match ? { label: match.label, value: match.value } : null;
    })
    ?.filter(
      (
        item,
      ): item is {
        label: string;
        value: string;
      } => item !== null,
    ); // Type guard for non-null filtering
};

export const mapArrayForMultiSelect = (
  input: string[],
  array: {
    label: string;
    value: string;
  }[],
): {
  label: string;
  value: string;
}[] => {
  // Filter and map to create a new array
  return input
    ?.map((label) => {
      const match = array?.find((item) => item.label === label);
      return match ? { label: match.label, value: match.value } : null;
    })
    ?.filter(
      (
        item,
      ): item is {
        label: string;
        value: string;
      } => item !== null,
    ); // Type guard for non-null filtering
};

export const transformList = (
  list: {
    id: string;
    name: string;
  }[],
) => {
  return list?.map((item) => ({
    value: item?.id,
    label: item?.name,
  }));
};
