import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';
import axios from 'axios';

interface UploadedFile {
  file_path: string;
  file_extension: string;
  document_for: string;
  sub_path: string;
}

export const handleFilesUpload = async (
  files: File[],
  accessToken: string,
  sub_path: string,
  document_for: string,
): Promise<UploadedFile[]> => {
  if (!files || files.length === 0) return [];

  const baseUrl = process.env.NEXT_PUBLIC_URL;
  const productVersion = process.env.NEXT_PUBLIC_VERSION;

  const orgId =
    typeof window !== 'undefined'
      ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
      : null;

  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `Bearer ${accessToken}`,
      ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
    },
  };

  const results = await Promise.all(
    files.map(async (file) => {
      const formData = new FormData();
      formData.append('file', file);

      // avoid double slashes in sub_path
      const cleanSubPath = sub_path.startsWith('/') ? sub_path : `/${sub_path}`;
      const url = `${baseUrl}/${productVersion}/file/upload?document_for=${document_for}&sub_path=${cleanSubPath}`;

      try {
        const response = await axios.post(url, formData, config);

        if (response.status === 200) {
          return {
            file_path: response.data.file_path,
            file_extension: response.data.file_ext,
          } as UploadedFile;
        } else {
          console.error('Error uploading file:', file.name);
          return null;
        }
      } catch (error) {
        console.error('Upload failed for:', file.name, error);
        return null;
      }
    }),
  );

  return results.filter((r): r is UploadedFile => r !== null);
};
