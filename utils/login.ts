import { ORGANIZATION_SESSION_KEY } from "@/constants/common";


export const handleLogout = async (logout: (options?: any) => Promise<void>, returnTo: any) => {
    localStorage.removeItem("token");
    localStorage.removeItem("user_info");
    localStorage.removeItem("x-mock-user");
    sessionStorage.removeItem(ORGANIZATION_SESSION_KEY);
    await logout({ logoutParams: { returnTo: returnTo } });
}