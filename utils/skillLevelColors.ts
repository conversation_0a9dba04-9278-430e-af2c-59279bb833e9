export interface SkillLevelColor {
  bg: string;
  text: string;
  border: string;
  lightBg: string;
}

export const skillLevelColors: Record<string, SkillLevelColor> = {
  '1': {
    bg: 'bg-red-500',
    text: 'text-red-600',
    border: 'border-red-200',
    lightBg: 'bg-red-500',
  },
  '2': {
    bg: 'bg-yellow-500',
    text: 'text-yellow-600',
    border: 'border-yellow-200',
    lightBg: 'bg-yellow-500',
  },
  '3': {
    bg: 'bg-red-500',
    text: 'text-red-600',
    border: 'border-red-200',
    lightBg: 'bg-red-500',
  },
  '4': {
    bg: 'bg-green-500',
    text: 'text-green-600',
    border: 'border-green-200',
    lightBg: 'bg-green-500',
  },
  '5': {
    bg: 'bg-green-500',
    text: 'text-green-600',
    border: 'border-green-200',
    lightBg: 'bg-green-500',
  },
};

export const getSkillLevelColor = (level: string): SkillLevelColor => {
  return skillLevelColors[level] || skillLevelColors['1'];
};

export const getSkillLevelName = (level: string): string => {
  const levelNames: Record<string, string> = {
    '1': 'Beginner',
    '2': 'Intermediate',
    '3': 'Advanced',
    '4': 'Expert',
    '5': 'Master',
  };
  return levelNames[level] || 'Unknown';
};
