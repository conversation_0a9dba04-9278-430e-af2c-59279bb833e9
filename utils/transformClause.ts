export const transformClause = (node: any, level = 'parent') => {
  const children: any[] = [];

  const sortByClauseNo = (a: any, b: any) => {
    const itemA = a.clause_no.split('.').map(Number);
    const itemB = b.clause_no.split('.').map(Number);
    const len = Math.max(itemA.length, itemB.length);

    for (let i = 0; i < len; i++) {
      const numA = itemA[i] || 0;
      const numB = itemB[i] || 0;
      if (numA !== numB) return numA - numB;
    }

    return 0;
  };

  if (node.clauses) {
    node.clauses.sort(sortByClauseNo).forEach((clause: any) => {
      children.push(transformClause(clause, 'clause'));
    });
  }

  if (node.sub_clauses) {
    node.sub_clauses.sort(sortByClauseNo).forEach((subClause: any) => {
      children.push(transformClause(subClause, 'sub_clause'));
    });
  }

  if (children.length > 0) {
    children.sort(sortByClauseNo);
  }

  return {
    id: node.id,
    clause_no: node.clause_no,
    title: node.title,
    description: node.description,
    question: node.question,
    is_compliant: node.is_compliant,
    justification: node.justification,
    is_active: node.is_active,
    processes: node.processes || [],
    type: level,
    status: node.status,
    children: children.length > 0 ? children : undefined,
  };
};

export const filterClauses = (nodes: any[], query: string): any[] => {
  if (!query) return nodes;

  return nodes
    .map((node: any) => {
      const children = node.children ? filterClauses(node.children, query) : [];

      const isMatch =
        node.title.toLowerCase().includes(query.toLowerCase()) ||
        node.clause_no.toLowerCase().includes(query.toLowerCase());

      if (isMatch || children.length > 0) {
        return {
          ...node,
          children: children.length > 0 ? children : undefined,
        };
      }

      return null;
    })
    .filter(Boolean);
};
// used in audit hub
export const transformClauseRecords = (records: any[]): any[] => {
  return records.flatMap((record) => {
    const standardTitle = record.standard?.title || '';
    const standardId = record.standard?.id || '';

    if (Array.isArray(record.clauses)) {
      return record.clauses.map((clause: any) => {
        const transformed = transformClause(clause, 'parent');
        return {
          ...transformed,
          standardTitle,
          standardId,
        };
      });
    }

    return [];
  });
};
