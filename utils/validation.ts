/**
 * Validation utilities for form inputs and data validation
 */

/**
 * Validates if a string follows the min-max range format (e.g., "0-100", "10-50")
 * @param value - The string to validate
 * @returns boolean - true if valid range format, false otherwise
 */
export const isValidRangeFormat = (value: string): boolean => {
  if (!value || typeof value !== 'string') {
    return false;
  }

  // Regex pattern for min-max format:
  // - Allows integers or decimals
  // - Requires exactly one hyphen separator
  // - Min value must be less than or equal to max value
  const rangePattern = /^(-?\d+(?:\.\d+)?)-(-?\d+(?:\.\d+)?)$/;
  const match = value.trim().match(rangePattern);

  if (!match) {
    return false;
  }

  const minValue = parseFloat(match[1]);
  const maxValue = parseFloat(match[2]);

  // Check if min is less than or equal to max
  return minValue <= maxValue;
};

/**
 * Validates range format and returns error message if invalid
 * @param value - The string to validate
 * @returns string | null - Error message if invalid, null if valid
 */
export const validateRangeFormat = (value: string): string | null => {
  if (!value || value.trim() === '') {
    return 'Range value is required';
  }

  if (!isValidRangeFormat(value)) {
    return 'Invalid range format. Use min-max format (e.g., 0-100, 10.5-20.5)';
  }

  return null;
};

/**
 * Extracts min and max values from a valid range string
 * @param value - The range string (e.g., "0-100")
 * @returns {min: number, max: number} | null - Object with min/max values or null if invalid
 */
export const parseRangeValues = (value: string): { min: number; max: number } | null => {
  if (!isValidRangeFormat(value)) {
    return null;
  }

  const rangePattern = /^(-?\d+(?:\.\d+)?)-(-?\d+(?:\.\d+)?)$/;
  const match = value.trim().match(rangePattern);

  if (!match) {
    return null;
  }

  return {
    min: parseFloat(match[1]),
    max: parseFloat(match[2])
  };
};

/**
 * Formats a range object back to string format
 * @param min - Minimum value
 * @param max - Maximum value
 * @returns string - Formatted range string (e.g., "0-100")
 */
export const formatRangeString = (min: number, max: number): string => {
  return `${min}-${max}`;
};
