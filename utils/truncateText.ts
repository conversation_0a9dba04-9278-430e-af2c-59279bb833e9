export const truncateWithEllipsis = (text: string, maxLength: number) => {
  if (!text) return '';
  if (text.length > maxLength) {
    return text.slice(0, maxLength - 3) + '...';
  }
  return text;
};

export const fileNameEllipsis = (filepath: string, maxLength: number) => {
  if (!filepath) return '';
  if (filepath.length <= maxLength) return filepath;

  const parts = filepath.split('.');
  if (parts.length < 2) return filepath.slice(0, maxLength) + '...';

  const extension = parts.pop();
  const name = parts.join('.');

  if (!extension || extension.length + 3 >= maxLength) {
    return name.slice(0, maxLength) + '...';
  }

  const truncatedName = name.slice(0, maxLength - extension.length - 3);
  return `${truncatedName}...${extension}`;
};
