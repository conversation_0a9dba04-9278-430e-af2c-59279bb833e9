export function removeEmptyFields<T extends Record<string, any>>(
  obj: T,
): Partial<T> {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, value]) =>
      Array.isArray(value) ? value.length > 0 : value !== '',
    ),
  ) as Partial<T>;
}

export function nullEmptyFields<T extends Record<string, any>>(
  obj: T,
): Partial<T> {
  return Object.fromEntries(
    Object.entries(obj).map(([key, value]) => {
      const isEmptyObject =
        value &&
        typeof value === 'object' &&
        !Array.isArray(value) &&
        Object.keys(value).length === 0;
      const isEmpty =
        value === '' ||
        (Array.isArray(value) && value.length === 0) ||
        isEmptyObject;

      return [key, isEmpty ? null : value];
    }),
  ) as Partial<T>;
}
