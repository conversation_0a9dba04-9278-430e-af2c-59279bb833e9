import moment from 'moment-timezone';

export const formatDate = (inputDate: string, withTime?: boolean) => {
  if (inputDate == '--') {
    return inputDate;
  }

  try {
    // Check if the input date has time information or is in ISO format
    // This regex checks for common time formats like HH:MM, HH:MM:SS, or time with timezone
    const hasTimeInfo =
      /\d{1,2}[:T]\d{2}([:\.]\d{2})?([+-Z]|(\+|-)\d{2}:?\d{2})?/.test(
        inputDate,
      );

    if (hasTimeInfo) {
      // If the date has time information, use timezone conversion
      const localTimeZone = moment.tz.guess();
      return !!inputDate
        ? withTime
          ? moment.utc(inputDate).tz(localTimeZone).format('h:mm a, MMM D YYYY')
          : moment.utc(inputDate).tz(localTimeZone).format('D MMM YYYY')
        : '-';
    } else {
      // If the date doesn't have time information, parse it directly without timezone conversion
      // This prevents the date from shifting due to timezone differences
      return !!inputDate
        ? withTime
          ? moment(inputDate).format('h:mm a, MMM D YYYY')
          : moment(inputDate).format('D MMM YYYY')
        : '-';
    }
  } catch (error) {
    console.error('Error formatting date:', error);
    return '--';
  }
};

export const getSubscriptionPeriod = (months: number): string => {
  const periods: Record<number, string> = {
    1: 'Monthly',
    3: 'Quarterly',
    6: 'Half-yearly',
    12: 'Yearly',
  };

  return (
    periods[months] ??
    periods[
      [1, 3, 6, 12].reduce((prev, curr) =>
        Math.abs(curr - months) < Math.abs(prev - months) ? curr : prev,
      )
    ]
  );
};

export const getMonthsFromPeriod = (
  period: string | undefined,
): number | undefined => {
  const periods: Record<string, number> = {
    Monthly: 1,
    Quarterly: 3,
    'Half-yearly': 6,
    Yearly: 12,
  };

  return period ? periods[period] ?? 0 : 0; // Return null if not found
};

export const getPeriodFromMonth = (period: number | undefined): string => {
  const periods: Record<number, string> = {
    1: 'Monthly',
    3: 'Quarterly',
    6: 'Half-yearly',
    12: 'Yearly',
  };

  return period ? periods[period] ?? '--' : '--'; // Return undefined if not found
};
