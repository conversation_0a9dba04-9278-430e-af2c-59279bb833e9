const ALLOWED_EXTS = [
  'pdf',
  'docx',
  'csv',
  'xlsx',
  'xls',
  'jpeg',
  'jpg',
  'png',
  'html',
  'doc',
  'zip',
] as const;

const MIME_TO_EXT: Record<string, string> = {
  'application/pdf': 'pdf',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
    'docx',
  'application/msword': 'doc',
  'text/csv': 'csv',
  'application/vnd.ms-excel': 'xls',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'xlsx',
  'image/jpeg': 'jpg',
  'image/png': 'png',
  'text/html': 'html',
  'application/zip': 'zip',
  'application/x-zip-compressed': 'zip',
};

const sanitizeBase = (s: string) =>
  s
    .trim()
    .replace(/\.[a-z0-9]+$/i, '') // remove trailing extension
    .replace(/[^a-z0-9 _.-]/gi, '') // remove invalid chars
    .replace(/\s+/g, ' ') // collapse spaces
    .slice(0, 200) || 'file';

/**
 * Rename file to match `documentName` while keeping/ensuring a valid extension.
 */
export function renameFileWithExtension(
  documentName: string,
  file: File,
): File {
  const base = sanitizeBase(documentName);
  const extFromMime = MIME_TO_EXT[file.type];
  const originalExt = file.name.split('.').pop()?.toLowerCase();

  // Check if user provided extension is valid
  const userExtMatch = documentName
    .match(/\.([a-z0-9]+)$/i)?.[1]
    ?.toLowerCase();
  const userExtAllowed =
    userExtMatch && ALLOWED_EXTS.includes(userExtMatch as any);

  let finalName: string;

  if (userExtAllowed) {
    finalName = documentName;
  } else if (extFromMime && ALLOWED_EXTS.includes(extFromMime as any)) {
    finalName = `${base}.${extFromMime}`;
  } else if (originalExt && ALLOWED_EXTS.includes(originalExt as any)) {
    finalName = `${base}.${originalExt}`;
  } else {
    // fallback (rare case) — no valid extension
    finalName = base;
  }

  return new File([file], finalName, {
    type: file.type,
    lastModified: file.lastModified,
  });
}
