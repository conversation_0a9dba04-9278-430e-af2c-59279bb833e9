export interface IAnalyticsService {
 init: () => void;
 identifyUser: (userId: string, traits?: Record<string, any>) => void;
 setGroup: (groupKey: string, groupId: string | string[]) => void;
 setGroupProps: (groupKey: string, props: Record<string, any>) => void;
 pageView: (pageName: string, properties?: Record<string, any>) => void;
 trackEvent: (eventName: string, properties?: Record<string, any>) => void;
 registerSuperProperties: (properties: Record<string, any>) => void;
 clearSuperProperties: () => void;
 reset: () => void; // for logout
}
