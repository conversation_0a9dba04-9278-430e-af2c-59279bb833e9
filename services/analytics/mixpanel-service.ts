import mixpanel from 'mixpanel-browser';

const TOKEN = process.env.NEXT_PUBLIC_MIXPANEL_TOKEN as string;
const API_HOST = process.env.NEXT_PUBLIC_MIXPANEL_API_HOST || 'https://api.mixpanel.com';
const DEBUG = (process.env.NEXT_PUBLIC_MIXPANEL_DEBUG === 'false');
const REPLAY_SAMPLE = Number(process.env.NEXT_PUBLIC_MIXPANEL_REPLAY_SAMPLE || 0);
const HEATMAP = (process.env.NEXT_PUBLIC_MIXPANEL_HEATMAP === 'true');


export const mixpanelService = {
    init: () => {
        if (typeof window === 'undefined') return;

        mixpanel.init(TOKEN, {
            api_host: API_HOST,
            debug: DEBUG,
            autocapture: true, // optional but useful
            record_sessions_percent: REPLAY_SAMPLE, // Session Replay sampling
            // @ts-expect-error - not in types yet
            record_heatmap_data: HEATMAP, // Heatmaps require Replay enabled
            persistence: 'localStorage', // typical for SPAs
            secure_cookie: true,
        });
    },
    identifyUser: (userId: string, traits?: Record<string, any>) => {
        mixpanel.identify(userId);
        if (traits && Object.keys(traits).length) {
            // user profile properties
            // NOTE: people.* requires that profiles are enabled in project settings
            mixpanel.people.set(traits);
        }
    },
    setGroup: (groupKey: string, groupId: string | string[]) => {
        // B2B: group by account/org
        mixpanel.set_group(groupKey, groupId);
    },
    setGroupProps: (groupKey: string, props: Record<string, any>) => {
        // Sends group property updates via groups API
        // mixpanel.group_set(groupKey, props);
    },
    pageView: (pageName: string, properties?: Record<string, any>) => {
        mixpanel.track('Page Viewed', { 'Page Name': pageName, ...properties });
    },
    trackEvent: (eventName: string, properties?: Record<string, any>) => {
        mixpanel.track(eventName, properties);
    },
    registerSuperProperties: (properties: Record<string, any>) => {
        mixpanel.register(properties);
    },
    clearSuperProperties: () => {
        // mixpanel.unregister_all();
    },
    reset: () => {
        mixpanel.reset(); // clears id/super props; call on logout
    },
}