import axios, { AxiosError, AxiosRequestConfig } from 'axios';
import { useEffect, useState } from 'react';

import useLocalStorage from './useLocalStorage';
import { toast } from 'react-toastify';
import {
  ORGANIZATION_HEADER_KEY,
  ORGANIZATION_SESSION_KEY,
} from '@/constants/common';

const useFetch = <
  T = unknown,
  Q extends Record<string, unknown> = Record<string, never>,
>(
  accessToken: string | null,
  endpoint?: string,
  query?: Q,
) => {
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<AxiosError | null>(null);

  const baseUrl = process.env.NEXT_PUBLIC_URL;
  const productVersion = process.env.NEXT_PUBLIC_VERSION;

  const mockUser =
    typeof window !== 'undefined' ? localStorage.getItem('x-mock-user') : null;

  const orgId =
    typeof window !== 'undefined'
      ? sessionStorage.getItem(ORGANIZATION_SESSION_KEY)
      : null;

  const headers: Record<string, string> = {
    Authorization: `Bearer ${accessToken}`,
    'Content-Type': 'application/json',
    ...(!!mockUser ? { 'x-mock-user': mockUser } : {}),
    ...(!!orgId ? { [ORGANIZATION_HEADER_KEY]: orgId } : {}),
  };

  const options: AxiosRequestConfig = {
    method: 'GET',
    url: `${baseUrl}/${productVersion}/${endpoint}`,
    headers: headers,
    params: query,
  };

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const response = await axios.request<T>(options);
      setData(response.data);
    } catch (err) {
      const axiosError = err as AxiosError;
      if (axiosError.response) {
        if (axiosError.response.status === 401) {
          window.location.reload();
        } else if (axiosError.response.status === 404) {
          toast.error('Not found');
        } else if (axiosError.response.status === 403) {
          setError(axiosError);
          // toast.error(
          //   'Forbidden. You do not have permission to access this resource.',
          // );
        } else {
          toast.error(axiosError.message);
          setError(axiosError);
        }
      } else {
        // toast.error('Network error or request timeout.');
      }
      console.error('Oops! Something went wrong', axiosError.message);
      setError(axiosError);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    accessToken && endpoint && fetchData();
  }, [accessToken, endpoint]);

  const reFetch = () => {
    if (accessToken) fetchData();
  };

  return { data, isLoading, error, reFetch };
};

export default useFetch;
