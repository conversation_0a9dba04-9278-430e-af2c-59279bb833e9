import { useAnalytics } from "@/globalProvider/analyticsProvider";
import { analyticEvents } from "@/constants/analytic";

export function usePostLoginAnalytics(analytics: any,  user: any) {
    // Identify the user
    analytics.identifyUser(user.id, {
        $email: user.email,
        name: user.name,
    });
    
    // Super properties (auto-attached to all future events on this device)
    analytics.registerSuperProperties({
        org_name: user.company.name,
        org_id: user.company.id,
        roles: user.roles,
        data_source: 'web-app',
    });
    
    // Group Analytics (analyze by account/org)
    analytics.setGroup('org_id', user.company.id);
    analytics.setGroupProps('org_id', {
        org_name: user.company.name,
    });

    analytics.trackEvent(analyticEvents.USER_LOGGED_IN, {...user})
}